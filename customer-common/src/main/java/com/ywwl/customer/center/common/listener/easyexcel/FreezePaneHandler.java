package com.ywwl.customer.center.common.listener.easyexcel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;


/**
 * //TODO
 *
 * @ClassName:FreezePaneHandler
 * <AUTHOR>
 * @Date 2022/9/27
 */
public final class FreezePaneHandler implements SheetWriteHandler {

    /**
     * 行
     */
    private final Integer row;
    /**
     * 列
     */
    private final Integer col;

    /**
     * 冻结多少行多少列
     * @param row   行
     * @param col   列
     */
    public FreezePaneHandler(Integer col, Integer row) {
        this.col = col;
        this.row = row;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        writeSheetHolder.getSheet().createFreezePane(col, row);
    }

}

