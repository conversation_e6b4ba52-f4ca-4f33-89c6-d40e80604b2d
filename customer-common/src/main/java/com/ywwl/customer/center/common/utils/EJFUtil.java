package com.ywwl.customer.center.common.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayOutputStream;
import java.util.Base64;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Slf4j
public class EJFUtil {

    public static void process(Process p) {
        p.execute();
    }

    public interface Process {
        default void execute() {
            try {
                run();
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }
        }

        void run() throws Throwable;
    }
    /**
     * Byte转String
     * @param outputStream
     * @return
     */
    public static String byteToString(ByteArrayOutputStream outputStream) {
        byte[] binary = outputStream.toByteArray();
        if (ArrayUtils.isNotEmpty(binary)) {
            Base64.Encoder encoder = Base64.getEncoder();
            return encoder.encodeToString(binary);
        }
        return StringUtils.EMPTY;
    }
}
