package com.ywwl.customer.center.common.utils;


import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * http request response 过滤XSS SQL 数据工具类
 *
 * <AUTHOR>
 * @date 10:13 2018/2/14
 */
@Slf4j
public class RequestResponseUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestResponseUtil.class);
    private static final String STR_BODY = "body";


    /**
     * description 获取request中的body json 数据转化为map
     *
     * @param request 1
     * @return java.util.Map<java.lang.String, java.lang.String>
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> getRequestBodyMap(ServletRequest request) {
        Map<String, Object> dataMap = new HashMap<>(16);
        // 判断是否已经将 inputStream 流中的 body 数据读出放入 attribute
        if (request.getAttribute(STR_BODY) != null) {
            // 已经读出则返回attribute中的body
            return (Map<String, Object>) request.getAttribute(STR_BODY);
        } else {
            String login = StringUtils.EMPTY;
            try {
                login = IoUtil.readUtf8(request.getInputStream());
                Map<String, Object> maps = JSON.parseObject(login, Map.class);
                dataMap.putAll(maps);
                request.setAttribute(STR_BODY, dataMap);
            } catch (IOException e) {
                log.error("登录信息传输错误 {}", login, e);
            }
            return dataMap;
        }
    }




}
