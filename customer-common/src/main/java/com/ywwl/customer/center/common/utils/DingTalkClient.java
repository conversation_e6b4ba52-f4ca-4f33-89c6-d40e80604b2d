package com.ywwl.customer.center.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
* // 钉钉通知
*<AUTHOR>
*@date 2023/2/22
*/
@Component
@Slf4j
public class DingTalkClient {
    public static String secret;
    public static  String url ;
    // 乐群的通知服务
    public static String noticeUrl;
    private static String token;
    /**
     * 往钉钉群发送消息
     * <AUTHOR>
     * @date 2021/4/25 9:35
     * @param content 发送具体内容
     * @return void
     */
    public static void sendMessage(String content) {
        try {
            Long timestamp = System.currentTimeMillis();
            String sign = getSign(timestamp);
            StringBuilder stringBuilder = new StringBuilder(url).append("&timestamp=").append(timestamp).append("&sign=").append(sign);
            HttpUtil.doPost(stringBuilder.toString(),null,getMessage(content),null,null);
        } catch (Exception e) {
            log.error("调用钉钉出现异常,原因:{}", e.getMessage());
        }

    }

    public static Map<String,Object> getMessage(String content) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("msgtype", "text");
        String sendContent = "{\"content\":\"CONTENT\"}";
        map.put("text", sendContent.replace("CONTENT", content));
        map.put("at", "{\"isAtAll\":true}");
        return map;
    }

    public static String getSign(Long timestamp) throws Exception {
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        return sign;
    }

    public static void sendDingNoticeToUser(String tmsCode, String title, String message) {
        if (StringUtils.isBlank(tmsCode)||StringUtils.isBlank(title)||StringUtils.isBlank(message)){
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("opcode", "sendTextMsg");
        paramMap.put("tenantid", 1);
        paramMap.put("title", title);
        paramMap.put("usercode", tmsCode);
        paramMap.put("notifybydd", true);
        paramMap.put("content", message);
        EncoderHandler.ThirdValidateSignature sign = EncoderHandler.getSignature(token);
        Object timestamp = sign.getTimestamp();
        Object nonce = sign.getNonce();
        String signature = sign.getSignature();
        String apiUrl = noticeUrl + "?timestamp=" + timestamp + "&nonce=" + nonce + "&signature=" + signature;
        try {
            HttpUtil.doPost(apiUrl,paramMap);
        } catch (Exception e) {
            log.error("给指定用户发送钉钉通知异常,异常信息: {}", e.getMessage());
        }
    }

    @Value("${ding-talk.url}")
    public void setUrl(String dingTalkUrl){
        DingTalkClient.url=dingTalkUrl;
    }
    @Value("${ding-talk.sign}")
    public void setSecret(String dingTalkSecret){
        DingTalkClient.secret=dingTalkSecret;
    }

    @Value("${send-ding-notice.url}")
    public void setNoticeUrl(String noticeUrl){
        DingTalkClient.noticeUrl=noticeUrl;
    }

    @Value("${send-ding-notice.token}")
    public void setToken(String token) {
        DingTalkClient.token = token;
    }
}
