package com.ywwl.customer.center.common.converter;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.text.SimpleDateFormat;


/**
 * <AUTHOR>
 * @date 2018/1/3
 */
public class RedisJacksonObjectMapper extends ObjectMapper {
    public RedisJacksonObjectMapper() {
        this.enableDefaultTyping(DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        this.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS"));
        this.findAndRegisterModules();
    }
}
