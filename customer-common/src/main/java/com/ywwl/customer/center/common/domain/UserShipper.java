package com.ywwl.customer.center.common.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 用户发货账号关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-16
 */
@Data
public class UserShipper implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	/**
	 * 用户id
	 */
	private Long userId;
	/**
	 * 发货账号id
	 */
	private Long shipperId;

	/*** 发货账号 */
	private String accountCode;

	private String customerCode;
	private String pickWarehouseName;

	public UserShipper() {
	}

	// public UserShipper(Long userId, Long shipperId) {
	// this.userId = userId;
	// this.shipperId = shipperId;
	// }

	public UserShipper(Long userId, String accountCode) {
		this.userId = userId;
		this.accountCode = accountCode;
	}
}
