package com.ywwl.customer.center.common.listener.easyexcel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * @ClassName:EasyExcelHead
 * @Description 表头设置
 * <AUTHOR>
 * @Date 2022/6/1
 */

@Slf4j
@Data
public class EasyExcelHead implements SheetWriteHandler {
    private String message;
    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheet = workbook.getSheetAt(0);
        // 设置标题
        Row row2 = sheet.createRow(0);

        row2.setHeight((short) 800);

        Cell cell1 = row2.createCell(0);

        cell1.setCellValue(message);

        CellStyle cellStyle = workbook.createCellStyle();

        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        Font font = workbook.createFont();
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);

        // 设置 自动换行
        contentWriteCellStyle.setWrapped(false);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short)10);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        font.setBold(true);
        font.setColor(Font.COLOR_RED);
        font.setFontHeight((short) 400);

        cellStyle.setFont(font);

        cell1.setCellStyle(cellStyle);

        sheet.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, 8));
    }
}
