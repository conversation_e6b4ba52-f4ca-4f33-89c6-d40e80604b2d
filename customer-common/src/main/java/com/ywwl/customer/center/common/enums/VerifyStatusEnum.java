package com.ywwl.customer.center.common.enums;

import lombok.Getter;

/**
 * //以下状态才能进行操作
 *
 * <AUTHOR>
 * @date 2023/4/19
 */
@Getter
public enum VerifyStatusEnum {

    SIGNED(6, "完成");

    private Integer value;
    private String desc;

    VerifyStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static Integer getValue(Integer value) {
        for (VerifyStatusEnum verifyStatusEnum : VerifyStatusEnum.values()) {
            if (verifyStatusEnum.value.equals(value)) {
                return verifyStatusEnum.value;
            }
        }
        return null;
    }
}
