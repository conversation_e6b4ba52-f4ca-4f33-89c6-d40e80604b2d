package com.ywwl.customer.center.common.enums;

/**
 * // 接口地址
 *
 * <AUTHOR>
 * @date 2023/2/24
 */
public enum OPCodeEnum {
    PAYMENT_ATTACH_STATUS("/api/internal/merchant/payment/synPaymentAttachStatus", "同步Portal付款委托书状态到CRM"),
    PAYMENT_BANK_ID("/api/internal/merchant/payment/synBankId", "同步bankId"),
    PAYMENT_AUDIT_SNY("/api/internal/merchant/paymentAudit/sny", "非商户主体备案审核银行支付宝审核"),
    SEARCH_ALL_PAYMENT("/api/internal/merchant/payment/searchAllPayment?merchantCode=", "查询商户所有非[审核拒绝]且有效 的平台账号 "),
    COMMISSION_CALLBACK("/api/internal/merchant/payment/synPaymentAttach", "付款委托书回调"),
    BATCH_DELETE_ADDRESS("/api/customer/address/batchDelete","批量删除"),
    SEARCH_ADDRESS("/api/customer/address/search","查询多地址信息"),
    NEW_ADDRESS("/api/customer/address","新增/更新/删除多地址信息"),
    GET_RAIL ("inface/selectFenceListByCompanyNum", "根据仓号获取围栏数据"),
    SEARCH_PAYMENT_RECORD ("/api/internal/merchant/payment/searchPaymentRecord", "CRM查询备案数据"),
    OUTSIDEABNORMAL_List("outsideAbnormal/selectPage", "仓外异常件查询"),
    OUTSIDEABNORMAL_CUSTOMERHANDLE("outsideAbnormal/customerHandle", "仓外异常件单个处理"),
    OUTSIDEABNORMAL_CUSTOMERBATCHHANDLE("outsideAbnormal/customerBatchHandle", "仓外异常件多个处理"),
    OUTSIDEABNORMAL_GETABNORMALTYPE("outsideAbnormal/getAbnormalType", "仓外异常原因枚举"),
    MODIFY_PWD("modifyPassword", "更新密码"),
    COMPLIANCEMERCHANT_SELECTPAGE("/complianceMerchant/selectPage", "9610商户信息申请查询"),
    COMPLIANCE_LIST("/complianceProduct/selectPage", "9610商品列表"),
    EXCEL_LIST("/complianceProductExcel/selectPage", "9610导入数据查询列表"),
    COMPLIANCE_SAVE("/complianceProduct/save", "9610商品备案新增"),
    COMPLIANCE_EDIT("/complianceProduct/edit", "9610商品备案编辑"),
    COMPLIANCEFILE_LIST("/complianceFile/selectPage", "9610报关单列表"),
    SELECTSTATECOUNT_LIST("/complianceProduct/selectStateCount", "9610商品信息数量"),
    COMPLIANCE_BATCHDELETE("/complianceProduct/batchDelete", "9610商品备案列表删除"),
    COMPLIANCE_BATCHSAVE("/complianceProductExcel/upload", "9610商品备案批量新增"),
    COMPLIANCE_BATCHEDIT("/complianceProduct/batchEdit", "9610商品备案批量编辑"),
    COMPLIANCE_BATCHCHECK("/complianceProduct/batchCheck", "9610商品备案批量校验"),
    COMPLIANCEMERCHANT_EDIT("/complianceMerchant/edit", "9610商户信息申请再次编辑"),
    CUSTOMSDECLARATIONBATCHDOW("/complianceFile/customsDeclarationBatchDow", "9610批量报关单下载"),
    COMPLIANCEMERCHANT_APPAY("/complianceMerchant/save", "9610商户信息申请"),
    COMPLIANCE_DOWNLOAD("/complianceProductExcel/download?sourceType=0", "9610下载模板"),
    GET_USER_MENUS("menu/getsTheOptionalMenuBasedOnTheUserID", "查看用户权限菜单"),
    ADD_USER("addUser", "新增子用户"),
    UPDATE_USER("updateUser", "编辑子用户"),
    DELETE_USER("deleteUser", "删除子用户"),
    User_List("showAllUserList", "子用户列表"),
    GET_SUB_USER_LIST("user/child/getChildUserRoles", "子用户角色列表"),
    ADD_USER_ROLE("user/child/addChildRole", "新增子用户角色"),
    UPDATE_USER_ROLE("user/child/updateChildRole", "修改子用户角色"),
    DELETE_USER_ROLE("user/child/deleteChildRole", "删除子用户角色"),
    USER_ROLE_ROLE("user/child/getChildUserRole", "子用户角色详情"),
    OLD_ABNORMAL_LIST("portalAbnormal/list", "历史异常工单信息"),
    UPDATE_MERCHANT("updateMerchant", "修改管理员信息"),
    VERIFY_SMS_CODE("checkiplsms", "校验短信验证码"),
    CHECK_BIND_WECHAT("checkuservalid", "校验是否绑定微信"),
    OBTAIN_OPENID("getopenid", "根据用户userid获取openid"),
    BIND_WECHAT("binduser", "在portal进行微信绑定"),
    UNBIND_WECHAT("unbinduser", "解绑微信"),
    RESET_PWD("resetpwd", "重置密码"),
    RETRIEVE_PWD("retrievepwd", "申请找回密码"),
    GET_MOBILE("getUserMobile", "根据用户名获取手机号"),
    GET__FORGET_MOBILE("getForgetPasswordMobile", "根据用户名或者手机号获取手机号"),
    WAREHOUSE_SHIPPER("searchDeliveryAccountIncludeWarehouseName", "查询带有揽收仓名称的发货账号"),
    LIST_USER_PERMISSION_MENU("queryuserauthorityToMenu", "查看用户功能权限菜单信息返回"),
    SEARCHAUDITMERCHANT("internal/merchant/searchAuditMerchant ", "根据商户流水号查询商户审核状态"),
    GET_SLIDER_CAPTCHA("getSliderCaptcha", "获取滑动图片验证码"),
    SEARCH_BY_TMS_CODE("/api/employee/searchByTmsCode", "通过tmsCode获取员工信息"),
    INVOICEDMERCHANT("/invoiceApplication/invoicedMerchant", "开票商户"),

    LIST_USER_PERMISSION("queryuserauthority", "查看用户功能权限"),
    MERCHANT_INFO("selectMerchantByMerchantCodeOrNo", "查询商户信息"),
    GET_SUBUSER_INFO("showUser", "查看子用户信息"),
    LOGIN("loginuser", "登陆"),
    SCAN_LOGIN("getlogininfobyopenid", "微信扫码通过用户openid登录portal"),
    GENERATE_SMS_AND_VERIFY_MOBILE("genersmsAndVerifyMobile", "生成短信验证码(登录/找回密码使用)"),
    GENERATE_SMS_VERIFY("genersmsverify", "生成短信验证码(注册使用)"),
    REGISTER("registeruser", "用户注册"),
    CHECK_PIC_VERIFY("checkpicverify", "校验登录名和图片验证码"),
    GET_ACCOUNT_BY_USERID("/user/child/getAccountByChildUserId", "获取子用户所有有权限制单账号"),
    CHECK_SMS_VERIFY_CODE("checksmsverify", "校验短信验证码"),
    SHOW_ADMIN_USER("showAdminUser","获取主用户详细信息"),
    GET_WAREHOUSE_LIST("wechat/getcollectingPointInfoList", "根据仓号查询对应仓下的所有揽收点信息"),
    ENABLE_SAFE_LOGIN("enableSafeLogin","启用关闭安全提醒"),
    QUERY_SAFE_NOTICE_INFO("querySafeNotice","查询安全登录信息"),
    GET_USER_BY_EMAIL_OR_MOBILE("queryUserByMobileOrEmail","通过手机号或者邮箱获取用户"),
    ;
    private final String value;
    private final String desc;

    OPCodeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String value() {
        return value;
    }

    public String desc() {
        return desc;
    }
}
