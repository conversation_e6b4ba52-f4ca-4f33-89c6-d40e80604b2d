package com.ywwl.customer.center.common.interceptor;

import cn.hutool.core.util.NumberUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 请求超时拦截器
 *
 * <AUTHOR>
 * @date 2023/04/28 10:27
 **/
public class TimeoutInterceptor implements Interceptor {

    /**
     * 请求拦截器
     * @param chain 请求
     * @return  响应
     * @throws IOException  数据异常
     */
    @Override
    public Response intercept(Chain chain) throws IOException {
        final Request request = chain.request();
        // 如果请求头有特殊超时时间，则使用特殊超时时间
        final String specificTimeout = request.header(HttpUtil.SPECIFIC_TIMEOUT);
        if (StringUtils.isNotBlank(specificTimeout) && NumberUtil.isInteger(specificTimeout)) {
            final int timeout = Integer.parseInt(specificTimeout);
            return chain.withConnectTimeout(timeout, TimeUnit.SECONDS)
                    .withReadTimeout(timeout,TimeUnit.SECONDS)
                    .withWriteTimeout(timeout,TimeUnit.SECONDS)
                    .proceed(request);
        }
        return chain.proceed(request);
    }

}
