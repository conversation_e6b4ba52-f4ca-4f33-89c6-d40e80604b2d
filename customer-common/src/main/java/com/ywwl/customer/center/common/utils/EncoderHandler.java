package com.ywwl.customer.center.common.utils;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * MD5,SHA1
 *
 * <AUTHOR>
 */
@Component
public class EncoderHandler {

    private static final String ALGORITHM = "MD5";
    private static final String SHA1_ALGORITHM = "SHA1";

    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * encode string
     *
     * @param algorithm
     * @param str
     *
     * @return String
     */
    public static String encode(String algorithm, String str) {
        return digestMessage(algorithm, str);
    }


    /**
     * encode By MD5
     *
     * @param str
     *
     * @return String
     */
    public static String encodeByMD5(String str) {
        return digestMessage(ALGORITHM, str);
    }

    /**
     * encode By SHA1
     *
     * @param str
     *
     * @return String
     */
    public static String encodeBySHA1(String str) {
        return digestMessage(SHA1_ALGORITHM, str);
    }

    /**
     * 生成str信息摘要
     *
     * @param algorithm
     * @param str
     *
     * @return
     */
    private static String digestMessage(String algorithm, String str) {
        if (str == null) {
            return null;
        }
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance(algorithm);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        messageDigest.update(str.getBytes());
        return getFormattedText(messageDigest.digest());
    }

    /**
     * Takes the raw bytes from the digest and formats them correct.
     *
     * @param bytes the raw bytes from the digest.
     *
     * @return the formatted bytes.
     */
    private static String getFormattedText(byte[] bytes) {
        int len = bytes.length;
        StringBuilder buf = new StringBuilder(len * 2);
        // 把密文转换成十六进制的字符串形式
        for (int j = 0; j < len; j++) {
            buf.append(HEX_DIGITS[(bytes[j] >> 4) & 0x0f]);
            buf.append(HEX_DIGITS[bytes[j] & 0x0f]);
        }
        return buf.toString();
    }

    public static ThirdValidateSignature getSignature(String token) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = String.valueOf(RandomUtils.nextInt(1000, 10000));
        String[] str = {token, timestamp, nonce};
        Arrays.sort(str);
        String bigStr = str[0] + str[1] + str[2];
        String signature = EncoderHandler.encodeBySHA1(bigStr);
        ThirdValidateSignature result = new ThirdValidateSignature();
        result.setTimestamp(timestamp);
        result.setNonce(nonce);
        result.setSignature(signature);
        return result;
    }

    /**
     * 文件服务器校验
     *
     * @return
     * <AUTHOR>
    public Map<String, Object> getEncryptFileCopy() {
        Map<String, Object> dataMap = new HashMap<String, Object>();
        //取1000~9999之间随机数
        Random random = new Random();
        int max = 9999;
        int min = 1000;
        int s = random.nextInt(max) % (max - min + 1) + min;
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = String.valueOf(s);
        String token = "acdfbac3789183FE";
        String[] str = {token, timestamp, nonce};
        Arrays.sort(str);
        String bigStr = str[0] + str[1] + str[2];
        dataMap.put("timestamp", timestamp);
        dataMap.put("nonce", nonce);
        dataMap.put("signature", encode("SHA1", bigStr));
        return dataMap;
    }

    public String getEncryptUrl() {
        Map<String, Object> dataMap = getEncryptFileCopy();
        String url = "timestamp=" + dataMap.get("timestamp")
                + "&nonce=" + dataMap.get("nonce")
                + "&signature=" + dataMap.get("signature");
        return url;
    }

    @Data
    public static class ThirdValidateSignature {
        private String timestamp;
        private String nonce;
        private String signature;

        public Map<String, String> getQueryParams() {
            HashMap<String, String> params = Maps.newHashMap();
            params.put("timestamp", timestamp);
            params.put("nonce", nonce);
            params.put("signature", signature);
            return params;
        }
    }
}
