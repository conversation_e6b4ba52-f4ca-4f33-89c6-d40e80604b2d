// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.constant;

/**
 * <AUTHOR>
 * @Description 用户相关提示常量
 * @Date 2023/2/22 14:43
 * @ModifyDate 2023/2/22 14:43
 * @Version 1.0
 */
public class SysUserConstants {

    /**
     * 图形验证码验证失败
     */
    public static final String BASE_AUTH_ERROR = "验证失败，请刷新重试";

    /**
     * 图形验证码验证超时
     */
    public static final String BASE_AUTH_TIME_OUT = "验证超时,请刷新重试";
    /**
     * 短信不可用
     */
    public static final String NOTE_SERVICE_ERROR = "短信服务不可用,请稍后重试";
    /**
     * 短信失效
     */
    public static final String NOTE_VERIFY_ERROR = "短信验证码已失效. 请重新发送";
    /**
     * 页面
     */
    public static final String PAGE_AUTH_ERROR = "页面或者验证码过期,请刷新重试";


    /**
     * 登录超时
     */
    public static final String LOGIN_TIME_OUT = "登录超时，请重试";

    /**
     * 登录错误
     */
    public static final String LOGIN_ERROR = "登录请求错误,请重试";

    /**
     * 登录信息错误
     */
    public static final String LOGIN_MSG_ERROR = "登录信息有误，如验证码错误或失效，请确认后重试";

    /**
     * 登录参数缺失
     */
    public static final String LOGIN_MSG_MISS = "登录参数缺失";


    /**
     * 密码过于简单
     */
    public static final String PASSWORD_EASY = "密码过于简单，请修改密码";

    /**
     * 用户信息异常
     */
    public static final String USER_INFO_ERROR = "用户信息异常, 请联系客服";


    /**
     * 商户信息异常
     */
    public static final String MERCHANT_INFO_ERROR = "商户信息异常, 请联系客服";
    public static final String SUCCEED_STATUS = "获取成功";

    public static final String NO_MERCHANT_ATTRIBUTES = "noMerchantAttributes";

    public static final String PROMPT_INFORMATION = "用户ID:[%s],商户ID:[%s],不存在的商户属性:%s";

}
