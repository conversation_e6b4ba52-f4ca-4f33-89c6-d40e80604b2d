// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.utils;


import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.enums.TrackStatusEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/4/2 17:46
 * @ModifyDate 2021/4/2 17:46
 * @Version 1.0
 */
public class YwPointUtils {
    public static final List<Object> COLLECT = Arrays.asList("SC10");
    public static final List<Object> LEAVE = Arrays.asList("LH20", "LH21", "LH22", "LH30", "IC50", "IC60", "IC70", "LM10", "LM11", "LM12", "LM13", "LM15", "LM20"
            , "LM25", "LM30", "LM35", "LM40", "LM50", "LM60", "LM70", "LM75", "LM85", "LM90");
    public static final List<Object> ARRIVE = Arrays.asList("IC50", "IC60", "IC70", "LM10", "LM11", "LM12", "LM13", "LM15", "LM20", "LM25", "LM30", "LM35"
            , "LM40", "LM50", "LM60", "LM70", "LM75", "LM85", "LM90");
    public static final List<Object> FAIL = Arrays.asList("LM50", "LM75", "LM85", "LM90");



    public static final List<String> ORDER_DONE = Arrays.asList("OR15","PU30","PU01","OR10");
    public static final List<String> TRANSPORTING = Arrays.asList("LM09","IC61","GS16","LH23","LH16","LH15","LH13","LH12","LH08","SC29","SC64","LM40e","S304","S306","S305"
            ,"S303","S302","S301","S212","S213","S211","S210","S209","S208","S207","S206","S205","S204","S203","S202","S201","LM57","LM55","LH09","SC59","SC58","LH34","SC63"
            ,"SC90","LM03","LH38","LM20","LM13","LM12","LM15","LM11","LM10","SC40","IC80","IC60","IC50","LH33","LH45","LH40","LH30","LH22","LH21","LH35","LH20","LH11","LH10"
            ,"EC20","EC10","SC70","SC65","SC60","SC45","SC25","SC20","SC19","SC18","SC17","SC16","SC15","SC13","SC12","SC27","SC26","SC11","SC09","SC08","SC06","SC05","SC10"
            ,"SC03","SC00","SC01","SC04","SC02","PU10");
    public static final List<String> DELIVERYING = Arrays.asList("LM60","LM35","LM25");
    public static final List<String> ARRIVED_NOT_GET = Arrays.asList("LM30");
    public static final List<String> SEND_SUCCESS = Arrays.asList("LM40");
    public static final List<String> TRACK_OVER = Arrays.asList("SC61","OR35","OR30");
    public static final List<String> SEND_FAIL = Arrays.asList("LM54","LM85","LM75","LM90","LM70","LM50");
    public static final List<String> BAG_EXCEPTION = Arrays.asList("LH36","LH14","IC55","LH55","SC95","LM56","SC83","SC53","SC49","IC70","LH37","LH07","EC30","SC36","SC31","SC28"
            ,"SC23","SC22","SC21","SC07");
    public static final List<String> BAG_RETURN = Arrays.asList("SC62","SC81","SC85","SC80","SC75","SC57","SC42","SC41","SC39","SC38","SC37","SC34","SC35","SC33","SC32","SC30","SC24"
            ,"SC14");


    /**
     *
     * @param status
     * @return
     */
    public static String convertTransportStatus(String status){
        if(ORDER_DONE.contains(status)){
            return TrackStatusEnum.ORDER_DONE.getCode();
        }else if(TRANSPORTING.contains(status)){
            return TrackStatusEnum.TRANSPORTING.getCode();
        }else if(DELIVERYING.contains(status)){
            return TrackStatusEnum.DELIVERYING.getCode();
        }else if(ARRIVED_NOT_GET.contains(status)){
            return TrackStatusEnum.ARRIVED_NOT_GET.getCode();
        }else if(SEND_SUCCESS.contains(status)){
            return TrackStatusEnum.SEND_SUCCESS.getCode();
        }else if(TRACK_OVER.contains(status)){
            return TrackStatusEnum.TRACK_OVER.getCode();
        }else if(SEND_FAIL.contains(status)){
            return TrackStatusEnum.SEND_FAIL.getCode();
        }else if(BAG_EXCEPTION.contains(status)){
            return TrackStatusEnum.BAG_EXCEPTION.getCode();
        }else if(BAG_RETURN.contains(status)){
            return TrackStatusEnum.BAG_RETURN.getCode();
        }else {
            return TrackStatusEnum.NOT_FOUND.getCode();
        }
    }

    /**
     * 制单完成
     * @param billStatus
     * @return
     */
    public static boolean ywSubmitOrderDone(String billStatus){
        return billStatus.equals(TrackStatusEnum.ORDER_DONE.getCode());
    }


    /**
     * 运输途中
     *
     * @param billStatus
     * @return
     */
    public static boolean ywTransporting(String billStatus) {
        return billStatus.equals(TrackStatusEnum.TRANSPORTING.getCode());
    }

    /**
     * 正在派送
     *
     * @param billStatus
     * @return
     */
    public static boolean outOfDelivery(String billStatus) {
        return billStatus.equals(TrackStatusEnum.DELIVERYING.getCode());
    }


    /**
     * 到达待取
     *
     * @param billStatus
     * @return
     */
    public static boolean ywNotGet(String billStatus) {
        return billStatus.equals(TrackStatusEnum.ARRIVED_NOT_GET.getCode());
    }

    /**
     * 投递成功
     *
     * @param billStatus
     * @return
     */
    public static boolean ywSendSuccess(String billStatus) {
        return billStatus.equals(TrackStatusEnum.SEND_SUCCESS.getCode());
    }


    /**
     * 追踪结束
     *
     * @param billStatus
     * @return
     */
    public static boolean trackStop(String billStatus) {
        return billStatus.equals(TrackStatusEnum.TRACK_OVER.getCode());
    }


    /**
     * 投递失败
     *
     * @param billStatus
     * @return
     */
    public static boolean ywSendFail(String billStatus) {
        return billStatus.equals(TrackStatusEnum.SEND_FAIL.getCode());
    }


    /**
     * 包裹异常
     * @param billStatus
     * @return
     */
    public static boolean ywBagException(String billStatus){
        return billStatus.equals(TrackStatusEnum.BAG_EXCEPTION.getCode());
    }


    /**
     * 包裹退回
     * @param billStatus
     * @return
     */
    public static boolean ywBagReturn(String billStatus){
        return billStatus.equals(TrackStatusEnum.BAG_RETURN.getCode());
    }


    /**
     * 尝试投递
     *
     * @param status
     * @return
     */
//    public static boolean attemptSend(String status) {
//        List<String> datas = new ArrayList<>();
//        datas.add("LM35");
//        return datas.contains(status);
//    }




    /**
     * 过期失效
     *
     * @param status
     * @return
     */
    public static boolean ywFailureTime(String status) {
        List<String> datas = new ArrayList<>();
        datas.add("LM75");
        datas.add("LM80");
        return datas.contains(status);
    }


    /**
     * 燕文揽收
     *
     * @param status
     * @return
     */
    public static boolean ywPickUp(String status) {
        List<String> datas = new ArrayList<>();
        datas.add("PU10");
        return datas.contains(status);
    }





    /**
     * 离开始发国
     *
     * @param status
     * @return
     */
    public static boolean ywLeaved(String status) {
        List<String> datas = new ArrayList<>();
        datas.add("LH20");
        return datas.contains(status);
    }

    /**
     * 到达目的国
     *
     * @param status
     * @return
     */
    public static boolean ywArrived(String status) {
        List<String> datas = new ArrayList<>();
        datas.add("LM10");
        return datas.contains(status);
    }

    /**
     * 妥投
     *
     * @param status
     * @return
     */
    public static boolean ywSend(String status) {
        List<String> datas = new ArrayList<>();
        datas.add("LM40");
        return datas.contains(status);
    }

    /***
     * //重要节点;0:揽收 1:离开始发国;2:到达始发国:3妥投;4失败
     * <AUTHOR>
     * @date 2021/4/8 17:06
     * @param node
     * @return java.lang.String
     */
    public static String rangeNode(List<JSONObject> node, String status, String billStatus) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < node.size(); i++) {
            String statusCode = node.get(i).getString("tracking_status");
            list.add(statusCode);
        }
        if (FAIL.contains(status)) {
            return "4";
        } else if (ywSendSuccess(billStatus)) {
            return "3";
        } else if (ARRIVE.contains(status)) {
            return "2";
        } else if (LEAVE.contains(status)) {
            return "1";
        } else if (COLLECT.contains(status)) {
            return "0";
        } else {
            for (String code : list) {
                if (FAIL.contains(code)) {
                    return "4";
                } else if (ywSendSuccess(billStatus)) {
                    return "3";
                } else if (ARRIVE.contains(code)) {
                    return "2";
                } else if (LEAVE.contains(code)) {
                    return "1";
                } else if (COLLECT.contains(code)) {
                    return "0";
                }
            }
        }
        return "";
    }
}
