package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @date 2021/11/18 09:49
 */
public enum PageStateEnum {
    INIT_PASSWORD(-1, "初始化密码"),
    NORMAL_MERCHANT(0, "正常商户首页"),
    NET_ERROR_PAGE(500, "网络异常,错误页面"),
    MERCHANT_ERROR_PAGE(400, "商户信息状态异常"),
    NEW_AUTH(1, "新注册商户认证页面"),
    NEW_MERCHANT_INFO(2, "新注册商户信息页面"),
    NEW_COMPANY_BANK_INFO(3, "新注册企业商户银行卡信息页面"),
    NEW_COMPANY_BANK_VERIFY_AMOUNT(4, "新注册企业商户金额校验页面"),
    NEW_SHIPPER_INFO(5, "新注册商户发货账号信息页面"),
    NEW_WAIT_AUDIT(6, "新商户申请待审核"),
    NEW_WAITING_SIGN(7, "签约准备中"),
    SIGN(8, "待签约"),
    WAITING_SIGNED_RESULT(9, "等待签约结果"),
    // 以下为正式商户进行补齐资料等操作页面跳转
    MERCHANT_AUTH(10, "人脸识别"),
    PERSONAL_MERCHANT_INFO_UPDATE(11, "个人商户信息填写"),
    COMPANY_MERCHANT_INFO_UPDATE(12, "企业商户信息填写"),
    COMPANY_MERCHANT_BANK_INFO(13, "企业商户银行卡信息填写页面"),
    COMPANY_MERCHANT_BANK_VERIFY_AMOUNT(14, "企业商户银行卡打款金额校验页面"),
    MERCHANT_SHIPPER_INFO(15, "发货账号,联系人信息填写页面"),
    MERCHANT_WAITING_AUDIT(16, "等待审核页面"),
    MERCHANT_WAITING_CONTRACT_GEN(17, "合同准备中"),
    MERCHANT_CONTRACT_RESIGN(18, "合同补签"),
    MERCHANT_CERTIFICATE_UPDATE(19, "更新或者补齐证件照页面"),
    MERCHANT_CERTIFICATE_UPDATE_AUDITING(20, "更新或者补齐证件照等待审核"),
    MERCHANT_AGREEMENT(21, "补签协议"),
    MERCHANT_UPDATE_COMMISSION(22, "补齐委托书状态"),
    MERCHANT_CONFIRM_INFORMATION(23, "确认付款信息"),
    MERCHANT_WAITING_CONFIRM_PAYMENT_BOOK(24, "待确认付款委托书"),
    OVERSEA_COMPANY_MERCHANT_COMPLETE(25, "境外企业商户补齐资料"),
    NOT_ESIGN(26,"未实名认证"),
    NOT_SIGN_CONTRACT(27,"未签署e签宝合同"),
    OVERSEA_MERCHANT(28,"海外派商户"),
    ;

    private Integer pageState;
    private String desc;

    PageStateEnum(Integer pageState, String desc) {
        this.pageState = pageState;
        this.desc = desc;
    }

    public Integer getPageState() {
        return pageState;
    }

    public String getDesc() {
        return desc;
    }
}
