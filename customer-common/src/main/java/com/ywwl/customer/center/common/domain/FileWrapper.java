package com.ywwl.customer.center.common.domain;

import lombok.Data;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.util.StringJoiner;

/**
 * 下载文件
 *
 * <AUTHOR>
 * @date 2018/10/17
 */
@Data
public class FileWrapper {
	private byte[] content;

	private long contentLength;

	private String ContentDisposition;

	private String contentType;

    public static String HTML_CONTENT_TYPE = "text/html;charset=utf-8";

    public static String ERROR_TIPS = "账务中心接口异常";

	public static FileWrapper build(Response response) throws IOException {
		FileWrapper fileWrapper = new FileWrapper();
		if (response == null || !response.isSuccessful()) {
			return null;
		}

		ResponseBody body = response.body();
		if (body == null) {
			return null;
		}
		try {
			fileWrapper.setContent(body.bytes());
			fileWrapper.setContentDisposition(response.header("Content-Disposition"));
			fileWrapper.setContentType(response.header("Content-Type"));
		} finally {
			body.close();
		}

		if (fileWrapper.getContent() != null) {
			fileWrapper.setContentLength(fileWrapper.getContent().length);
		}
		return fileWrapper;
	}

	@Override
	public String toString() {
		return new StringJoiner(", ", FileWrapper.class.getSimpleName() + "[", "]")
				.add("contentLength=" + contentLength)
				.add("ContentDisposition='" + ContentDisposition + "'")
				.add("contentType='" + contentType + "'")
				.toString();
	}

    public static ResponseEntity<byte[]> getResponseEntity(FileWrapper fileWrapper) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (StringUtils.isNotBlank(fileWrapper.getContentDisposition())) {
            httpHeaders.setContentDisposition(org.springframework.http.ContentDisposition.parse(fileWrapper.getContentDisposition()));
        }
        if (StringUtils.isNotBlank(fileWrapper.getContentType())) {
            httpHeaders.setContentType(MediaType.parseMediaType(fileWrapper.getContentType()));
        }


        if (HTML_CONTENT_TYPE.equals(fileWrapper.getContentType())) {
            httpHeaders.setContentLength(ERROR_TIPS.getBytes().length);
            return new ResponseEntity<>(ERROR_TIPS.getBytes(), httpHeaders, HttpStatus.OK);
        } else {
            httpHeaders.setContentLength(fileWrapper.getContentLength());
            return new ResponseEntity<>(fileWrapper.getContent(), httpHeaders, HttpStatus.OK);
        }
    }
}
