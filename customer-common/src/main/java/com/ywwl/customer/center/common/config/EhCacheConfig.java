package com.ywwl.customer.center.common.config;

import cn.hutool.core.io.FileUtil;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import org.ehcache.PersistentCacheManager;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.expiry.ExpiryPolicy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;
import java.time.Duration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/5/9
 */
@Configuration
public class EhCacheConfig {

	@Value("${java.io.tmpdir}")
	String rootDirectory;
	@Value("${tmpdir}")
	String tmpDir;

	/**
	 * EhCache CacheManagers
	 * @return CacheManagers
	 */
	@Bean
	public PersistentCacheManager ehCacheCacheManager() {
		// 缓存目录
		final String directory = rootDirectory + "/" + tmpDir + "/ehcache/portal";
		// 删除缓存
		FileUtil.del(directory);
		final CacheManagerBuilder<PersistentCacheManager> builder = CacheManagerBuilder.newCacheManagerBuilder()
				// 设置主存储目录
				.with(CacheManagerBuilder.persistence(directory));
		final PersistentCacheManager build = builder.build(true);
		// 创建缓存Cache
		final CacheKeyEnum[] values = CacheKeyEnum.values();
		for (CacheKeyEnum value : values) {
			// 创建Build
            final ResourcePoolsBuilder poolsBuilder = ResourcePoolsBuilder.newResourcePoolsBuilder()
                    .heap(10000, EntryUnit.ENTRIES)
                    .offheap(10, MemoryUnit.MB)
                    .disk(20, MemoryUnit.MB, true);
            final CacheConfigurationBuilder<String, Serializable> cacheBuild =
					CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, Serializable.class, poolsBuilder);
            // 设置超时时间
			final Duration timeout = value.getTimeout();
			final ExpiryPolicy<Object, Object> expiry;
			if (Objects.nonNull(timeout)) {
				expiry = ExpiryPolicyBuilder.timeToIdleExpiration(timeout);
			}else {
				expiry = ExpiryPolicyBuilder.timeToIdleExpiration(Duration.ofDays(9999));
			}
			final CacheConfiguration<String, Serializable> cacheConfiguration =
					cacheBuild.withExpiry(expiry).build();
			build.createCache(value.key(), cacheConfiguration);
		}
		return build;
	}

}
