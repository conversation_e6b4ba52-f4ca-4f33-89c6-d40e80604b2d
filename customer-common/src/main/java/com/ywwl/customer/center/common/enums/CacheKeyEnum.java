package com.ywwl.customer.center.common.enums;

import lombok.Getter;

import java.time.Duration;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/23 16:30
 **/
@Getter
public enum CacheKeyEnum {
    IDEMPOTENT_ASPECT("idempotent", Duration.ofSeconds(2), "控制幂等性"),
    EJF("ejf", Duration.ofMinutes(30), "EJF数据"),
    PROVINCE("province", Duration.ofMinutes(30), "主数据城市信息获取"),
    MIS_CELL("misCell", Duration.ofMinutes(30), "计费项获取"),
    ACCOUNT("account", Duration.ofMinutes(60), "制单账号"),
    CHILD_ACCOUNT("child_account", Duration.ofMinutes(60), "子用户制单账号"),
    PLM_COUNTRY("plmCountry", Duration.ofMinutes(30), "PLM的城市获取"),
    NODE("node", Duration.ofMinutes(30), "节点信息获取"),
    PLM_PRODUCT("plmProduct", Duration.ofMinutes(30), "PLM的产品获取"),
    WECHAT_SCAN("BIND", Duration.ofMinutes(10), "微信扫码绑定"),
    GD_NUMBER("AutoNavi", null, "高德次数"),
    THIRD_PAYMENT_LIST("thirdPaymentList", null, "第三方使用的支付方式"),
    BANK("bank", null, "银行信息"),
    PACKET_BUSINESS_CODE("packetBusinessCode",Duration.ofMinutes(30),"小包业务账号"),
    FBA_BUSINESS_CODE("fbaBusinessCode",Duration.ofMinutes(30),"fba业务账号"),
    SINGLE_SIGN_ON("singleSignOn",Duration.ofMinutes(30),"单点登录"),
    WAREHOUSE("warehouse",Duration.ofMinutes(30),"交货仓缓存"),
    OVERSEAS_DELIVERY_LOCATION("overseasDeliveryLocation",Duration.ofMinutes(30),"海外交货地缓存"),
    API_TOKEN("apiToken",Duration.ofSeconds(3),"ApiToken瞬时缓存"),
    TAX_TYPE("taxType",Duration.ofMinutes(30),"税号类型"),
    COMMODITY_TYPE("commodityType",Duration.ofMinutes(30),"商品类型"),
    BATTERY_TYPE("batteryType",Duration.ofMinutes(30),"电池类型"),
    PACK_TYPE("packType",Duration.ofMinutes(30),"包裹类型"),
    BUSINESS("business",Duration.ofMinutes(30),"商业快递枚举统类"),
    ABNORMAL_TYPE("abnormalType",Duration.ofMinutes(30),"异常类型"),
    ;

    private final String key;
    /**
     * 默认超时时间，redis如果不配置超时时间，就默认取这个，默认单位秒
     */
    private final Duration timeout;
    private final String desc;

    CacheKeyEnum(String key, Duration timeout, String desc) {
        this.key = key;
        this.timeout = timeout;
        this.desc = desc;
    }

    public String key() {
        return key;
    }

}
