package com.ywwl.customer.center.common.enums;


/**
 * 申请类型状态
 *
 * <AUTHOR>
 * @since 2018年11月16日09:21:15
 */
public enum ApplyTypeEnum implements ConstantBaseEnum {
    MERCHANT_NEW(0, "新商户申请"),
    MERCHANT_FORMAL(1, "非正式转正商户"),
    PERSON_COMPANY(2, "个人转企业"),
    MERCHANT_INFO(3, "补齐资料"),
    CONTRACT_RESIGN(4, "合同补签"),
    APPLY_SUCCESS(5, "申请完成"),
    UPDATE_CERTIFICATE(6, "更新证件照"),
    UPDATE_DATA(7, "更新资料"),
    POLISHING_CERTIFICATE(8, "补齐证件照"),
    UPDATE_COMMISSION(9, "补齐委托书"),
    CONFIRM_INFORMATION(10, "确认付款信息"),
    AGREEMENT_RESIGN(11, "补签协议"),
    MERCHANT_ACTIVE(12, "商户激活"),
    UNFREEZE_MERCHANT(13, "长期未发货解冻");
    private int value;
    private String desc;

    ApplyTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    /**
     * @description 判断是否处于补齐资料或者非正式转正式
     * <AUTHOR>
     * @date 2022/4/11 14:57
     */
    public static Boolean validateCompleteStatus(Integer applyType) {
        if (applyType == null) {
            return false;
        }
        if (ApplyTypeEnum.MERCHANT_INFO.value() == applyType || ApplyTypeEnum.PERSON_COMPANY.value() == applyType) {
            return true;
        }
        return false;
    }

    @Override
    @EnumKey
    public int value() {
        return value;
    }

    @Override
    @EnumValue
    public String desc() {
        return desc;
    }

    /**
     * //TODO 获取Desc信息
     *
     * @ClassName:ApplyTypeEnum
     * <AUTHOR>
     * @Date 2022/10/24
     */
    public static String getDesc(Integer value) {
        for (ApplyTypeEnum applyTypeEnum : ApplyTypeEnum.values()) {
            if (applyTypeEnum.value == value) {
                return applyTypeEnum.desc();
            }
        }

        return null;
    }
}