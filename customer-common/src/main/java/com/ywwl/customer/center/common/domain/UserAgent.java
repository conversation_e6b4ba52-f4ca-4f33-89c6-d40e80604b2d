package com.ywwl.customer.center.common.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * //TODO 用户登录信息
 *
 * <AUTHOR>
 * @date 2023/2/20
 */
@Data
public class UserAgent implements Serializable {

    public static final String TAG = "userAgent";
    private static final long serialVersionUID = -392178874150895446L;
    /**
     * 用户ID
     */
    private Long userId ;
    /**
     * 商户流水号
     */
    private String merchantNo ;
    private String tokenType;
    private String accessToken;

    private String loginName;
    /**
     * 用户名称
     */
    private String username;

    private String email;
    private String phone;
    /**
     * 是否系统管理员
     */
    private boolean isAdmin = false;

    private boolean mobileVerify;

    private boolean emailVerify;

    /**
     * 客户类型 0:个人 1:企业
     */
    private Integer customerType;
    /**
     * 浏览器设备指纹
     */
    private String deviceNo;

    /**
     * 平台登录途径
     * pc-portal登录、wechat-微信登录、website-官网登录
     */
    private String dsource;

    /**
     * 是否需要强制修改密码
     */
    private boolean initPw;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * 销售手机号
     */
    private String sale;
    /**
     * <AUTHOR>
     * @description 用户代码
     * @date 2023/3/3 17:10
     **/
    private String userCode;
}
