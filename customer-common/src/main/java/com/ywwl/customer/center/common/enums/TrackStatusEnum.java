// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/24 15:47
 * @ModifyDate 2023/3/24 15:47
 * @Version 1.0
 */
@Getter
public enum TrackStatusEnum {
    NOT_FOUND("1","查询不到"),
    ORDER_DONE("2","制单完成"),
    TRANSPORTING("3","运输途中"),
    DELIVERYING("4","正在派送"),
    ARRIVED_NOT_GET("5","到达待取"),
    SEND_SUCCESS("6","投递成功"),
    TRACK_OVER("7","追踪结束"),
    SEND_FAIL("8","投递失败"),
    BAG_EXCEPTION("9","包裹异常"),
    BAG_RETURN("0","包裹退回");

    private String code;
    private String desc;


    TrackStatusEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

}
