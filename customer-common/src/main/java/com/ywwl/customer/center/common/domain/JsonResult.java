package com.ywwl.customer.center.common.domain;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;

/**
 * //TODO 公共返参类
 *<AUTHOR>
 *@date 2023/2/17
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JsonResult<T> extends HashMap<String, Object> implements Serializable {

	/**
	 * 状态码
	 */
	public static final String CODE_TAG = "code";

	/**
	 * 是否成功
	 */
	public static final String SUCCESS_TAG = "success";

	/**
	 * 是否成功
	 */
	public static final String WARN_TAG = "warn";

	/**
	 * 返回内容
	 */
	public static final String MSG_TAG = "message";

	/**
	 * 数据对象
	 */
	public static final String DATA_TAG = "data";

	/**
	 * 下面四种同成功响应
	 * @param <T>
	 * @return	响应结果类
	 */
	public static <T> JsonResult<T> success() {
		return success(ResponseCode.PORTAL_200.getMessage(), null);
	}
	public static <T> JsonResult<T> success(T data) {
		return success(ResponseCode.PORTAL_200.getMessage(), data);
	}
	//	public static <T> JsonResult<T> success(String message) {
//		return success(message, null);
//	}
	public static <T> JsonResult<T> success(String message ,T data) {
		return success(message, data, ResponseCode.PORTAL_200.getCode());
	}
	public static <T> JsonResult<T> success(ResponseCode response) {
		return success(response.getMessage(), null, response.getCode());
	}
	public static <T> JsonResult<T> success(ResponseCode response, T data) {
		return success(response.getMessage(), data, response.getCode());
	}
	public static <T> JsonResult<T> success(String message ,T data, String code) {
		JsonResult<T> result = new JsonResult<>();
		result.put(SUCCESS_TAG, true);
		result.put(MSG_TAG, message);
		result.put(CODE_TAG, code);
		result.put(DATA_TAG, data);
		return result;
	}

	/**
	 * 设置是否警告
	 * @param warn	是否警告
	 */
	public void setWarn(Boolean warn) {
		this.put(WARN_TAG, warn);
	}

	/**
	 * 下面四种同失败响应
	 * @param <T>
	 * @return	响应结果类
	 */
	public static <T> JsonResult<T> error() {
		return error(ResponseCode.PORTAL_500.getMessage(), null);
	}
	public static <T> JsonResult<T> error(T data) {
		return error(ResponseCode.PORTAL_500.getMessage(), data);
	}
	public static <T> JsonResult<T> error(String message) {
		return error(message, null);
	}
	public static <T> JsonResult<T> fError(CharSequence template, Object... paramsString) {
		return error(StrUtil.format(template, paramsString), null);
	}
	public static <T> JsonResult<T> error(String message ,T data) {
		return error(message, data, ResponseCode.PORTAL_500.getCode());
	}
	public static <T> JsonResult<T> error(ResponseCode response) {
		return error(response.getMessage(), null, response.getCode());
	}
	public static <T> JsonResult<T> error(ResponseCode response, T data) {
		return error(response.getMessage(), data, response.getCode());
	}
	public static <T> JsonResult<T> error(String message ,T data, String code) {
		JsonResult<T> result = new JsonResult<>();
		result.put(SUCCESS_TAG, false);
		result.put(MSG_TAG, message);
		result.put(CODE_TAG, code);
		result.put(DATA_TAG, data);
		return result;
	}

	/**
	 * 获取成功标志
	 */
	public Boolean getSuccess() {
		return (Boolean) get(SUCCESS_TAG);
	}
	/**
	 * 获取响应信息
	 */
	public String getMessage() {
		return (String) get(MSG_TAG);
	}
	/**
	 * 获取响应码
	 */
	public String getCode() {
		return (String) get(CODE_TAG);
	}
	/**
	 * 获取数据
	 */
	public T getData() {
		return (T) get(DATA_TAG);
	}

	/**
	 * 获取成功标志
	 */
	public void setSuccess(Boolean success) {
		put(SUCCESS_TAG, success);
	}
	/**
	 * 获取响应信息
	 */
	public void setMessage(String message) {
		put(MSG_TAG, message);
	}
	/**
	 * 获取响应码
	 */
	public void setCode(String code) {
		put(CODE_TAG, code);
	}
	/**
	 * 获取数据
	 */
	public void setData(T data) {
		put(DATA_TAG, data);
	}

	public <V> V getValue(String key) {
		return (V) get(key);
	}

}
