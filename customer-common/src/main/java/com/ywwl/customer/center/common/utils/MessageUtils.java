package com.ywwl.customer.center.common.utils;

import java.util.Random;

public class MessageUtils {
    public static String getCode() {
        Random random = new Random();
        String content = random.nextInt(899999) + 100000 + "";

        return content;
    }

    /**
     * 判断密码复杂度
     *
     * @param str
     * @return
     */
    public static boolean isPwComplex(String str) {
        String regex = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![_.!@#?]+$)[0-9A-Za-z_.!@#?]{9,20}$";
        return str.matches(regex);
    }

    /**
     * 判断哪些密码需要进行强制密码修改
     *
     * @param str
     * @return
     */
    public static boolean needModifyPw(String str) {
        String regex = "^[0-9]*$";
        if ((str.length() <= 9 && str.matches(regex)) || str.length() <= 6) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @Description 校验亚马逊平台账号
     * <AUTHOR>
     * @Date 2020/7/16 18:43
     */
    public static boolean virifyPlatform(String str) {
        String regex = "^[A-Z][A-Z0-9]*$";
        if (str != null && str.length() > 9 && str.length() <= 20 && str.matches(regex)) {
            return true;
        }
        return false;
    }

}
