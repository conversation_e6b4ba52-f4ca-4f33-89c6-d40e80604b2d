package com.ywwl.customer.center.common.listener.easyexcel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ywwl.customer.center.common.domain.ResponseCode;

import java.util.ArrayList;
import java.util.List;


/**
 * //TODO
 *
 * @ClassName:EasyExcelCompliance
 * <AUTHOR>
 * @Date 2022/9/27
 */
public final class EasyExcelCompliance<T> extends AnalysisEventListener<T> {
    /**
     * 自定义用于暂时存储data
     * 可以通过实例获取该值
     */
    private List<T> datas = new ArrayList<>();

    /**
     * 每解析一行都会回调invoke()方法
     *
     * @param object  读取后的数据对象
     * @param context 内容
     */
    @Override
    public void invoke(T object, AnalysisContext context) {
        T data = (T) object;
        //数据存储到list
        datas.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (datas.isEmpty()) {
            throw ResponseCode.PORTAL_9002.getError();
        }
    }

    /**
     * 返回数据
     *
     * @return 返回读取的数据集合
     **/
    public List<T> getDatas() {
        return datas;
    }

    /**
     * 设置读取的数据集合
     *
     * @param datas 设置读取的数据集合
     **/
    public void setDatas(List<T> datas) {
        this.datas = datas;
    }

}

