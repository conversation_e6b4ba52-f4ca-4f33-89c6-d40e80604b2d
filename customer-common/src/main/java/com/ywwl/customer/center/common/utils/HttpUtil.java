package com.ywwl.customer.center.common.utils;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.filter.PropertyFilter;
import com.ywwl.customer.center.common.domain.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.security.sasl.AuthenticationException;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.hutool.http.Header.CONTENT_DISPOSITION;
import static cn.hutool.http.Header.CONTENT_ENCODING;

@Slf4j(topic = "API")
@Component
public class HttpUtil extends RequestUtil {

    /**
     * Byte数组的过滤器
     */
    public static final PropertyFilter FILTER = (object, name, value) -> {
        // 判断属性类型是否为 byte[]
        return !(value instanceof byte[]);
    };
    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");
    private final static String MSG = "请求成功，但是响应为空";

    /**
     * 超时重试次数
     */
    public static final int COUNT = 3;
    /**
     * 下载
     */
    public static final String DOWNLOAD = "attachment";
    /**
     * 预览
     */
    public static final String REVIEW = "inline";
    /**
     * 编码
     */
    static final String ENCODE = "UTF-8";
    /**
     * 超时时间特殊设置
     */
    public static final String SPECIFIC_TIMEOUT = "specificTimeout";
    /**
     * 文件服务器
     */
    private static String token;

    private static OkHttpClient okHttpClient;

    public static final String SLICER = ".";

    public static final String VIEW_SUFFIX = "jpg,jpeg,png,pdf";

    /**
     * 忽略响应
     */
    private static final Set<String> ignoreResponse = SetUtils.hashSet("/api/dict/generalQuery");

    @Autowired
    public HttpUtil(OkHttpClient okHttpClient, @Value("${attachment-file-cloud.token}") String token) {
        HttpUtil.okHttpClient = okHttpClient;
        HttpUtil.token = token;
    }

    /**
     * 通用请求
     *
     * @param param 请求参数
     * @param <T>   响应类型
     * @return
     */
    public static <T> T request(@Valid RequestParam<T> param) {
        if (Objects.isNull(param.getMode())) {
            param.setMode(RequestMode.GET);
        }
        switch (param.getMode()) {
            case POST:
                return doPost(param);
            case GET:
            default:
                return doGet(param);
        }
    }

    public static <T> T doGet(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, MediaType mediaType) {
        return doGet(url, header, queryParams, body, clazz, null, mediaType);
    }

    public static <T> T doGet(String url, Integer timeout, Class<T> clazz) {
        return doGet(url, null, null, null, clazz, timeout, null);
    }

    public static <T> T doGet(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, Integer timeout, MediaType mediaType) {
        return doGet(url, header, queryParams, body, clazz, timeout, mediaType, false);
    }

    public static <T> T doGet(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, Integer timeout, MediaType mediaType, Boolean failureRetry) {
        try {
            return execute(url, header, queryParams, body, RequestMode.GET, clazz, mediaType, timeout, failureRetry);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doGet(RequestParam<T> param) {
        return doGet(param.getUrl(), param.getHeader(), param.getParams(), param.getBody(), param.getClazz(), param.getTimeout(), param.getMediaType(), param.getFailureRetry());
    }

    public static <T> T doGet(String url, Map<String, String> params, Class<T> clazz) {
        return doGet(url, null, params, null, clazz, null);
    }

    public static <T> T doGet(String url, Map<String, String> header, Map<String, String> params, Class<T> clazz) {
        return doGet(url, header, params, null, clazz, null);
    }

    public static <T> T doGet(String url, Class<T> clazz) {
        return doGet(url, null, null, null, clazz, null);
    }

    public static JSONObject doGet(String url) {
        return doGet(url, JSONObject.class);
    }

    public static JSONObject doPost(String url, Object body) {
        return doPost(url, null, body, JSONObject.class, null);
    }

    public static JSONObject doPost(String url, Object body, Integer timeout) {
        try {
            return execute(url, null, null, body, RequestMode.POST, JSONObject.class, null, timeout, false);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Object body, Class<T> clazz, MediaType mediaType) {
        return doPost(url, Collections.emptyMap(), body, clazz, mediaType);
    }

    public static <T> T doPost(String url, Object body, Class<T> clazz) {
        return doPost(url, body, clazz, false);
    }

    public static <T> T doPost(String url, Object body, Class<T> clazz, Integer timeout) {
        try {
            return execute(url, null, null, body, RequestMode.POST, clazz, null, timeout, false);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Object body, Class<T> clazz, Boolean failureRetry) {
        try {
            return execute(url, null, null, body, RequestMode.POST, clazz, null, null, failureRetry);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Object body, Class<T> clazz, MediaType mediaType) {
        try {
            return execute(url, header, null, body, RequestMode.POST, clazz, mediaType, null, false);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Object body, Class<T> clazz, MediaType mediaType, Boolean logReq, Boolean logRsp) {
        try {
            return execute(url, header, null, body, RequestMode.POST, clazz, mediaType, null, false, logReq, logRsp);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, MediaType mediaType) {
        try {
            return execute(url, header, queryParams, body, RequestMode.POST, clazz, mediaType, null, false);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, MediaType mediaType, Boolean logReq, Boolean logRsp) {
        try {
            return execute(url, header, queryParams, body, RequestMode.POST, clazz, mediaType, null, false, logReq, logRsp);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Map<String, String> queryParams, Object body, Class<T> clazz, Integer timeout, MediaType mediaType, Boolean failureRetry, Boolean logReq, Boolean logRsp) {
        try {
            return execute(url, header, queryParams, body, RequestMode.POST, clazz, mediaType, timeout, failureRetry, logReq, logRsp);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static <T> T doPost(String url, Map<String, String> header, Object body, Class<T> clazz) {
        return doPost(url, header, body, clazz, null);
    }

    public static <T> T doPost(RequestParam<T> param) {
        return doPost(param.getUrl(), param.getHeader(), param.getParams(), param.getBody(), param.getClazz(), param.getTimeout(), param.getMediaType(), param.getFailureRetry(), param.getLogReq(), param.getLogRsp());
    }

    /**
     * 文件下载并转发前端
     *
     * @param url    url
     * @param target response
     */
    public static void download(String url, HttpServletResponse target) {
        download(url, target, null, false);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url     url
     * @param target  response
     * @param timeout 超时时间
     */
    public static void download(String url, HttpServletResponse target, Integer timeout) {
        download(url, target, null, false, timeout);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url        url
     * @param target     response
     * @param fileSystem 是否文件系统
     */
    public static void download(String url, HttpServletResponse target, Boolean fileSystem) {
        download(url, target, null, fileSystem);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url      url
     * @param fileName 自定义文件名称
     * @param target   response
     */
    public static void download(String url, HttpServletResponse target, String fileName) {
        download(url, target, fileName, false);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url      url
     * @param fileName 自定义文件名称
     * @param target   response
     * @param timeout  超时时间
     */
    public static void download(String url, HttpServletResponse target, String fileName, Integer timeout) {
        download(url, target, fileName, false, timeout);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url        url
     * @param fileName   自定义文件名称
     * @param target     response
     * @param fileSystem 是否文件服务器
     */
    public static void download(String url, HttpServletResponse target, String fileName, Boolean fileSystem) {
        download(url, target, fileName, fileSystem, 3);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url        url
     * @param fileName   自定义文件名称
     * @param target     response
     * @param fileSystem 是否文件服务器
     * @param timeout    超时时间
     */
    public static void download(String url, HttpServletResponse target, String fileName, Boolean fileSystem, Integer timeout) {
        file(url, target, fileName, fileSystem, DOWNLOAD, timeout, RequestMode.GET, null, null);
    }

    /**
     * 文件下载并转发前端
     *
     * @param param 请求参数
     */
    public static void download(RequestParam<?> param) {
        String type = DOWNLOAD;
        if (Objects.equals(param.getReview(), Boolean.TRUE)) {
            type = REVIEW;
        }
        file(param.getUrl(), param.getResponse(), param.getFileName(), param.getFileSystem(), type, param.getTimeout(), param.getMode(), param.getBody(), param.getHeader());
    }

    /**
     * 文件下载并转发前端
     *
     * @param url        url
     * @param fileName   自定义文件名称
     * @param target     response
     * @param fileSystem 是否文件服务器
     */
    public static void file(String url, HttpServletResponse target, String fileName, Boolean fileSystem, String type) {
        file(url, target, fileName, fileSystem, type, 3, RequestMode.GET, null, null);
    }

    /**
     * 文件下载并转发前端
     *
     * @param url        url
     * @param fileName   自定义文件名称
     * @param target     response
     * @param fileSystem 是否文件服务器
     * @param type       文件类型
     * @param timeout    超时时间
     */
    public static void file(String url, HttpServletResponse target, String fileName, Boolean fileSystem, String type, Integer timeout, RequestMode mode, Object reqBody, Map<String, String> header) {
        // 判断是否文件系统
        Map<String, String> param = Collections.emptyMap();
        if (Objects.equals(Boolean.TRUE, fileSystem)) {
            // 对文件服务器进行添加后缀
            param = EncoderHandler.getSignature(token).getQueryParams();
        }
        // 构建请求参数
        final RequestParam<Response> requestParam = RequestParam.<Response>builder()
                .url(url)
                .params(param)
                .header(header)
                .mode(mode)
                .body(reqBody)
                .clazz(Response.class)
                .timeout(timeout)
                .build();
        try (okhttp3.Response source = request(requestParam)) {
            final ResponseBody body = source.body();
            // 获取类型
            String contentDisposition = getContentDisposition(url, source);
            if (Objects.nonNull(body)) {
                if (Objects.equals(type, DOWNLOAD)) {
                    setDownloadHeader(target, fileName, type, source.header(CONTENT_ENCODING.getValue()), contentDisposition);
                }
                if (Objects.equals(type, REVIEW)) {
                    String suffix = StringUtils.substring(contentDisposition, contentDisposition.lastIndexOf(SLICER) + 1, contentDisposition.length()).toLowerCase();
                    if (!VIEW_SUFFIX.contains(suffix)) {
                        throw ResponseCode.PORTAL_5061.getError();
                    }
                }
                // 流Copy
                IoUtil.copy(body.byteStream(), target.getOutputStream());
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw ResponseCode.PORTAL_5028.getError();
        }
    }

    private static String getContentDisposition(String url, Response source) throws UnsupportedEncodingException {
        String contentDisposition = source.header(CONTENT_DISPOSITION.getValue());
        if (StringUtils.isBlank(contentDisposition)) {
            contentDisposition = DOWNLOAD.concat(";filename=" + URLEncoder.encode(getPathFileName(url), ENCODE));
        }
        return contentDisposition;
    }

    /**
     * 获取路径文件名称
     *
     * @param url URL
     * @return 文件名称
     */
    private static String getPathFileName(String url) {
        // 获取URL
        HttpUrl httpUrl = HttpUrl.parse(url);
        String pathFileName = url;
        if (Objects.nonNull(httpUrl) && !CollectionUtils.isEmpty(httpUrl.pathSegments())) {
            pathFileName = httpUrl.pathSegments().get(httpUrl.pathSize() - 1);
        }
        return pathFileName;
    }

    /**
     * <AUTHOR>
     * @description 查看文件
     * @date 2023/3/29 17:14
     **/
    public static void view(String url, HttpServletResponse target, Boolean fileSystem) {
        file(url, target, null, fileSystem, REVIEW);
    }

    public static FileWrapper down(String url, Map<String, String> queryParams, Integer timeOut) {
        try (Response response = request(RequestParam.<Response>builder()
                .url(url)
                .timeout(timeOut)
                .clazz(Response.class)
                .params(queryParams)
                .mode(RequestMode.GET)
                .build())) {
            if (response != null && response.isSuccessful()) {
                return FileWrapper.build(response);
            }
            return null;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 请i去
     *
     * @param url          连接
     * @param header       请求头
     * @param body         请求体
     * @param mode         类型
     * @param clazz        响应类型
     * @param mediaType    请求类型
     * @param failureRetry 是否超时重试
     * @param <T>          响应类型
     * @return 响应结果
     * @throws IOException IO异常
     */
    private static <T> T execute(String url, Map<String, String> header, Map<String, String> queryParams, Object body, RequestMode mode, Class<T> clazz, MediaType mediaType, Integer timeout, Boolean failureRetry) throws IOException {
        return execute(url, header, queryParams, body, mode, clazz, mediaType, timeout, failureRetry, true, true);
    }


    /**
     * xml请求
     *
     * @param url       链接
     * @param header    头
     * @param mediaType 类型
     * @param body      请求体
     * @return  结果
     */
    public static Response post(String url, Map<String,String> header ,com.google.common.net.MediaType mediaType, String body) {
        Request request = new Request.Builder().url(url)
                .headers(Headers.of(header))
                .post(RequestBody.create(okhttp3.MediaType.parse(mediaType.toString()), body))
                .build();
        try {
            return okHttpClient.newCall(request).execute();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 请i去
     *
     * @param url          连接
     * @param header       请求头
     * @param body         请求体
     * @param mode         类型
     * @param clazz        响应类型
     * @param mediaType    请求类型
     * @param failureRetry 是否超时重试
     * @param <T>          响应类型
     * @return 响应结果
     * @throws IOException IO异常
     */
    private static <T> T execute(String url, Map<String, String> header, Map<String, String> queryParams, Object body, RequestMode mode, Class<T> clazz, MediaType mediaType, Integer timeout, Boolean failureRetry, Boolean logReq, Boolean logRsp) throws IOException {
        okhttp3.MediaType type = JSON_TYPE;
        if (Objects.nonNull(mediaType)) {
            type = okhttp3.MediaType.parse(mediaType.toString());
        }
        String bodyJson;
        if (Objects.nonNull(body) && body instanceof String) {
            bodyJson = String.valueOf(body);
        } else {
            bodyJson = JSON.toJSONString(body, JSONWriter.Feature.WriteByteArrayAsBase64);
        }
        RequestBody requestBody = RequestBody.create(type, bodyJson);
        Headers.Builder heads = new Headers.Builder();
        if (!CollectionUtils.isEmpty(header)) {
            for (String key : header.keySet()) {
                heads.add(key, header.get(key));
            }
        }
        // 添加特殊超时时间
        if (Objects.nonNull(timeout)) {
            heads.add(SPECIFIC_TIMEOUT, String.valueOf(timeout));
        }
        Headers head = heads.build();
        Request.Builder req = new Request.Builder().headers(head);
        HttpUrl httpUrl = HttpUrl.parse(url);
        if (Objects.isNull(httpUrl)) {
            throw new RuntimeException("URL有误请检查");
        }
        if (!CollectionUtils.isEmpty(queryParams)) {
            HttpUrl.Builder builder = httpUrl.newBuilder();
            for (String key : queryParams.keySet()) {
                builder.addQueryParameter(key, queryParams.get(key));
            }
            httpUrl = builder.build();
        }
        switch (mode) {
            case GET:
                req.get();
                break;
            case POST:
                req.post(requestBody);
                break;
            default:
                throw new RuntimeException("错误的调用方式 只支持GET/POST");
        }
        req.url(httpUrl);
        String uuid = IdUtil.fastSimpleUUID();
        if (Boolean.FALSE.equals(logReq)) {
            log.info("{} -REQUEST -X {} -H {} -d ignore {}", uuid, mode, head, httpUrl);
        } else {
            log.info("{} -REQUEST -X {} -H {} -d {} {}", uuid, mode, head, JSON.toJSONString(body, FILTER), httpUrl);
        }
        // 判断是否超时重试，次数为三次
        if (Boolean.TRUE.equals(failureRetry)) {
            return request(uuid, url, clazz,req, 0, logRsp);
        }
        return request(uuid, url, clazz, req, COUNT, logRsp);
    }


    /**
     * 请求
     *
     * @param requestParam          请求参数
     * @param <T>                   响应类型
     * @return 响应结果
     * @throws IOException IO异常
     */
    public static <T> T execute(RequestParam<T> requestParam) throws IOException {

        final MediaType mediaType = requestParam.getMediaType();
        final Object body = requestParam.getBody();
        final Map<String, String> header = requestParam.getHeader();
        final Integer timeout = requestParam.getTimeout();
        final String url = requestParam.getUrl();
        final Map<String, String> queryParams = requestParam.getParams();
        final RequestMode mode = requestParam.getMode();
        final Boolean logReq = requestParam.getLogReq();
        final Boolean logRsp = requestParam.getLogRsp();
        final Class<T> clazz = requestParam.getClazz();
        final Boolean failureRetry = requestParam.getFailureRetry();
        final List<MultipartBody.Part> part = requestParam.getFormPart();

        okhttp3.MediaType type = JSON_TYPE;
        if (Objects.nonNull(mediaType)) {
            type = okhttp3.MediaType.parse(mediaType.toString());
        }
        RequestBody requestBody;
        // from提交请求
        if (mediaType == MediaType.MULTIPART_FORM_DATA) {
            final MultipartBody.Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
            part.forEach(builder::addPart);
            requestBody = builder.build();
        }else {
            String bodyJson;
            if (Objects.nonNull(body) && body instanceof String) {
                bodyJson = String.valueOf(body);
            } else {
                bodyJson = JSON.toJSONString(body, JSONWriter.Feature.WriteByteArrayAsBase64);
            }
            requestBody = RequestBody.create(type, bodyJson);
        }
        Headers.Builder heads = new Headers.Builder();
        if (!CollectionUtils.isEmpty(header)) {
            for (String key : header.keySet()) {
                heads.add(key, header.get(key));
            }
        }
        // 添加特殊超时时间
        if (Objects.nonNull(timeout)) {
            heads.add(SPECIFIC_TIMEOUT, String.valueOf(timeout));
        }
        Headers head = heads.build();
        Request.Builder req = new Request.Builder().headers(head);
        HttpUrl httpUrl = HttpUrl.parse(url);
        if (Objects.isNull(httpUrl)) {
            throw new RuntimeException("URL有误请检查");
        }
        if (!CollectionUtils.isEmpty(queryParams)) {
            HttpUrl.Builder builder = httpUrl.newBuilder();
            for (String key : queryParams.keySet()) {
                builder.addQueryParameter(key, queryParams.get(key));
            }
            httpUrl = builder.build();
        }
        switch (mode) {
            case GET:
                req.get();
                break;
            case POST:
                req.post(requestBody);
                break;
            default:
                throw new RuntimeException("错误的调用方式 只支持GET/POST");
        }
        req.url(httpUrl);
        String uuid = IdUtil.fastSimpleUUID();
        if (Boolean.FALSE.equals(logReq)) {
            log.info("{} -REQUEST -X {} -H {} -d ignore {}", uuid, mode, head, httpUrl);
        } else {
            log.info("{} -REQUEST -X {} -H {} -d {} {}", uuid, mode, head, JSON.toJSONString(body, FILTER), httpUrl);
        }
        // 判断是否超时重试，次数为count次
        if (Boolean.TRUE.equals(failureRetry)) {
            return request(uuid, url, clazz,  req, 0, logRsp);
        }
        return request(uuid, url, clazz, req, COUNT, logRsp);
    }

    /**
     * 请求
     *
     * @param url      连接
     * @param clazz    响应类型
     * @param <T>      响应类型
     * @return 响应结果
     * @throws IOException IO异常
     */
    private static <T> T request(String uuid, String url,Class<T> clazz, Request.Builder req, Integer count, Boolean logRsp) throws IOException {
        long start = System.currentTimeMillis();
        Response rsp = null;
        try {
            rsp = HttpUtil.okHttpClient.newCall(req.build()).execute();
            // 请求失败
            if (!rsp.isSuccessful()) {
                final int code = rsp.code();
                if (code == 401) {
                    // 如果是身份验证错误则跳过
                    throw ResponseCode.PORTAL_9401.getError();
                }
                String msg = String.format("请求失败 %s，错误信息 %s", code, rsp.message());
                throw new RuntimeException(msg);
            }
            if (Objects.equals(clazz, Response.class)) {
                log.info("{} -RESPONSE SUCCESS Response {}", uuid, url);
                return (T) rsp;
            }
            ResponseBody responseBody = rsp.body();
            final long elapsedTime = System.currentTimeMillis() - start;
            if (Objects.nonNull(responseBody)) {
                if (Objects.equals(clazz, ResponseBody.class)) {
                    log.info("{} -RESPONSE SUCCESS ResponseBody {} elapsedTime {}ms", uuid, url, elapsedTime);
                    return (T) responseBody;
                }
                if (Objects.equals(clazz, InputStream.class)) {
                    final InputStream inputStream = responseBody.byteStream();
                    log.info("{} -RESPONSE SUCCESS InputStream {} elapsedTime {}ms", uuid, url, elapsedTime);
                    return (T) inputStream;
                }
                if (Objects.equals(clazz, Byte[].class) || Objects.equals(clazz, byte[].class)) {
                    final byte[] bytes = responseBody.bytes();
                    log.info("{} -RESPONSE SUCCESS bytes {} elapsedTime {}ms", uuid, url, elapsedTime);
                    rsp.close();
                    return (T) bytes;
                }
                final String rspStr = responseBody.string();
                rsp.close();
                // 是否忽略响应打印
                if (Boolean.FALSE.equals(logRsp) || ignoreResponse.stream().anyMatch(url::endsWith)) {
                    log.info("{} -RESPONSE SUCCESS ignore {} elapsedTime {}ms", uuid, url, elapsedTime);
                } else {
                    log.info("{} -RESPONSE SUCCESS {} {} elapsedTime {}ms", uuid, rspStr, url, elapsedTime);
                }
                if (StringUtils.isNotBlank(rspStr)) {
                    if (Objects.equals(clazz, String.class)) {
                        return (T) rspStr;
                    }
                    return JSONObject.parseObject(rspStr, clazz);
                }
            }
            // 响应成功但是响应为空
            log.warn(MSG);
            log.info("{} -RESPONSE SUCCESS empty {} elapsedTime {}ms", uuid, url, elapsedTime);
            return null;
        } catch (AuthenticationException ae) {
            final long elapsedTime = System.currentTimeMillis() - start;
            log.info("{} -RESPONSE ERROR {} {} elapsedTime {}ms", uuid, getStackTrace(ae), url, elapsedTime);
            if (rsp != null) {
                rsp.close();
            }
            throw ae;
        } catch (Throwable e) {
            final long elapsedTime = System.currentTimeMillis() - start;
            // 进行错误重试
            if (count < COUNT) {
                log.error("{} 网络调用异常，进行重试 第{}次 elapsedTime {}ms", url, count + 1, elapsedTime);
                return request(uuid, url, clazz, req, count + 1, logRsp);
            }
            log.info("{} -RESPONSE ERROR {} {} elapsedTime {}ms", uuid, getStackTrace(e), url, elapsedTime);
            if (rsp != null) {
                rsp.close();
            }
            throw e;
        }
    }

    /**
     * 获取堆栈信息打印
     *
     * @param throwable 异常
     * @return 异常信息
     */
    public static String getStackTrace(Throwable throwable) {
        return ExceptionUtils.getStackTrace(throwable);
    }

    public static FileDownVO localDownloadFile(String url, Integer timeout, RequestMode mode, Object reqBody, Boolean fileSystem, Map<String, String> header) {
        FileDownVO fileDownVO=new FileDownVO();
        // 判断是否文件系统
        Map<String, String> param = Collections.emptyMap();
        if (Objects.equals(Boolean.TRUE, fileSystem)) {
            // 对文件服务器进行添加后缀
            param = EncoderHandler.getSignature(token).getQueryParams();
        }
        // 构建请求参数
        final RequestParam<Response> requestParam = RequestParam.<Response>builder()
                .url(url)
                .params(param)
                .header(header)
                .mode(mode==null?RequestMode.GET:mode)
                .body(reqBody)
                .clazz(Response.class)
                .timeout(timeout==null?30:timeout)
                .build();
        try (okhttp3.Response source = request(requestParam)) {
            final ResponseBody body = source.body();
            // 获取类型
            String fileName = getFileName(url,source);
            fileDownVO.setSuccess(true);
            fileDownVO.setBytes(body.bytes());
            fileDownVO.setFileName(fileName);
        } catch (Exception e) {
            log.error("下载文件失败:", e);
           fileDownVO.setSuccess(false);
           fileDownVO.setMessage("下载文件失败");
        }
        return fileDownVO;
    }

    public static String getFileName(String url,Response source) throws UnsupportedEncodingException {
        // 获取类型
        String contentDisposition = getContentDisposition(url, source);
        if(contentDisposition==null){
            return null;
        }
        Pattern pattern = Pattern.compile(".*filename=\"?([^\"]+)\"?.*$");
        Matcher matcher = pattern.matcher(contentDisposition);
        String fileName = "";
        if (matcher.matches()) {
            fileName = matcher.group(1);
        }
        try {
            fileName= URLDecoder.decode(fileName, String.valueOf(StandardCharsets.UTF_8));
        } catch (UnsupportedEncodingException e) {

        }
        return fileName;
    }

}
