// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 15:08
 * @ModifyDate 2023/3/14 15:08
 * @Version 1.0
 */
public enum ReturnTypeEnum {

    DRIVER_GET(0, "司机带回"),
    SELF_GET(1, "客户自提"),
    EXPRESS_MAIL(2, "快递寄付"),
    EXPRESS_TO(3, "快递到付");

    private Integer type;
    private String desc;


    ReturnTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer type) {
        for (ReturnTypeEnum returnTypeEnum : ReturnTypeEnum.values()) {
            if (type.equals(returnTypeEnum.getType())) {
                return returnTypeEnum.getDesc();
            }
        }
        return null;
    }
}
