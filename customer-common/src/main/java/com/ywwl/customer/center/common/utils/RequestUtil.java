package com.ywwl.customer.center.common.utils;

import cn.hutool.http.ContentType;
import com.ywwl.customer.center.common.domain.RequestMode;
import com.ywwl.customer.center.common.domain.RequestParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

import static cn.hutool.http.Header.CONTENT_DISPOSITION;
import static cn.hutool.http.Header.CONTENT_ENCODING;

/**
 * 请求工具类
 *
 * <AUTHOR>
 * @date 2023/10/09 10:10
 **/
public class RequestUtil {

    /**
     * 请求地址
     *
     * @param url 请求地址
     * @param <T> T
     * @return RequestParam
     */
    public static <T> RequestParam<T> url(String url) {
        return RequestParam.<T>builder().url(url).build();
    }

    /**
     * 请求头
     *
     * @param header 请求头
     * @param <T>    T
     * @return RequestParam
     */
    public static <T> RequestParam<T> header(Map<String, String> header) {
        return RequestParam.<T>builder().header(header).build();
    }

    /**
     * 请求参数
     *
     * @param params 请求参数
     * @param <T>    T
     * @return RequestParam
     */
    public static <T> RequestParam<T> params(Map<String, String> params) {
        return RequestParam.<T>builder().params(params).build();
    }

    /**
     * 请求体
     *
     * @param body 请求体
     * @param <T>  T
     * @return RequestParam
     */
    public static <T> RequestParam<T> body(Object body) {
        return RequestParam.<T>builder().body(body).build();
    }

    /**
     * 请求类型
     *
     * @param mode 请求类型
     * @param <T>  T
     * @return RequestParam
     */
    public static <T> RequestParam<T> mode(RequestMode mode) {
        return RequestParam.<T>builder().mode(mode).build();
    }

    /**
     * 返回类型
     *
     * @param clazz 返回类型
     * @param <T>   T
     * @return RequestParam
     */
    public static <T> RequestParam<T> clazz(Class<T> clazz) {
        return RequestParam.<T>builder().clazz(clazz).build();
    }

    /**
     * 请求类型
     *
     * @param mediaType 请求类型
     * @param <T>       T
     * @return RequestParam
     */
    public static <T> RequestParam<T> mediaType(MediaType mediaType) {
        return RequestParam.<T>builder().mediaType(mediaType).build();
    }

    /**
     * 超时时间(s)
     *
     * @param timeout 超时时间(s)
     * @param <T>     T
     * @return RequestParam
     */
    public static <T> RequestParam<T> timeout(Integer timeout) {
        return RequestParam.<T>builder().timeout(timeout).build();
    }

    /**
     * 是否失败重试
     *
     * @param failureRetry 是否失败重试
     * @param <T>          T
     * @return RequestParam
     */
    public static <T> RequestParam<T> failureRetry(Boolean failureRetry) {
        return RequestParam.<T>builder().failureRetry(failureRetry).build();
    }

    /**
     * 打印请求
     *
     * @param logReq 打印请求
     * @param <T>    T
     * @return RequestParam
     */
    public static <T> RequestParam<T> logReq(Boolean logReq) {
        return RequestParam.<T>builder().logReq(logReq).build();
    }

    /**
     * 打印响应
     *
     * @param logRsp 打印响应
     * @param <T>    T
     * @return RequestParam
     */
    public static <T> RequestParam<T> logRsp(Boolean logRsp) {
        return RequestParam.<T>builder().logRsp(logRsp).build();
    }

	/**
	 * 设置下载头
	 * @param target    response
	 * @param fileName  文件名称
	 * @param type      类型
	 * @param sourceEncode     编码类型
	 * @param contentDisposition    文件名称
	 * @throws UnsupportedEncodingException
	 */
	public static void setDownloadHeader(HttpServletResponse target, String fileName, String type, String sourceEncode, String contentDisposition) throws UnsupportedEncodingException {
		// 配置文件下载
		target.setContentType(ContentType.OCTET_STREAM.getValue());
		// 设置文件名称
		target.addHeader(CONTENT_DISPOSITION.getValue(), contentDisposition);
		// 设置编码类型
		target.addHeader(CONTENT_ENCODING.getValue(), sourceEncode);
		// 设置文件下载名称
		if (StringUtils.isNotBlank(fileName)) {
			// 设置文件名称
			target.setHeader(CONTENT_DISPOSITION.getValue(), type.concat(";filename=" + URLEncoder.encode(fileName, HttpUtil.ENCODE)));
		}
	}
}