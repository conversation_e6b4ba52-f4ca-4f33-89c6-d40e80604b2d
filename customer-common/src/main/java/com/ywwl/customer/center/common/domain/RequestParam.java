package com.ywwl.customer.center.common.domain;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.File;
import java.io.IOException;
import java.util.*;

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class RequestParam<T> {
    /**
     * URL
     */
    @NotBlank(message = "url不能为空")
    private String url;
    /**
     * 请求头
     */
    private Map<String, String> header;
    /**
     * 请求参数
     */
    private Map<String, String> params;
    /**
     * 请求体
     */
    private Object body;
    /**
     * 请求类型
     */
    private RequestMode mode;
    /**
     * 返回类型
     */
    private Class<T> clazz;
    /**
     * 请求类型
     */
    private MediaType mediaType;
    /**
     * 超时时间(s)
     */
    private Integer timeout;
    /**
     * 是否失败重试
     */
    private Boolean failureRetry;
    /**
     * 打印请求
     */
    private Boolean logReq;
    /**
     * 打印响应
     */
    private Boolean logRsp;
    /**
     * 响应
     */
    private HttpServletResponse response;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 是否文件系统
     */
    private Boolean fileSystem;
    /**
     * 是否预览
     */
    private Boolean review;
    /**
     * from提交
     */
    private List<MultipartBody.Part> formPart;

    /**
     * 添加文件部分到请求参数中（用于多部分表单文件上传）
     *
     * @param name 表单字段名称（对应HTML表单中的name属性）
     * @param file 要上传的文件对象
     * @param <T> 泛型类型参数
     * @return 返回当前RequestParam对象，支持链式调用
     */
    public RequestParam<T> filePart(String name, File file) {
        // 如果表单部分尚未初始化，则创建一个新的ArrayList
        if (Objects.isNull(formPart)) {
            formPart = new ArrayList<>();
        }
        // 创建多部分表单数据并添加到表单部分集合中
        formPart.add(
                MultipartBody.Part.createFormData(
                        name,                       // 表单字段名称
                        file.getName(),              // 文件名
                        RequestBody.create(
                                okhttp3.MediaType.parse("application/octet-stream"),  // 使用通用的二进制流媒体类型
                                file                                                   // 文件内容
                        )
                )
        );
        // 返回当前对象以支持链式调用
        return this;
    }


    public RequestParam<T> filePart(String name,String fileName, byte[] bytes) {
        // 如果表单部分尚未初始化，则创建一个新的ArrayList
        if (Objects.isNull(formPart)) {
            formPart = new ArrayList<>();
        }
        // 创建多部分表单数据并添加到表单部分集合中
        formPart.add(
                MultipartBody.Part.createFormData(
                        name,                       // 表单字段名称
                        fileName,              // 文件名
                        RequestBody.create(
                                okhttp3.MediaType.parse("application/octet-stream"),  // 使用通用的二进制流媒体类型
                                bytes                                                   // 文件内容
                        )
                )
        );
        // 返回当前对象以支持链式调用
        return this;
    }

    public void formPart(List<MultipartBody.Part> formPart) {
        this.formPart = formPart;
    }

    public RequestParam<T> review(Boolean review) {
        this.review = review;
        return this;
    }

    public RequestParam<T> response(HttpServletResponse response) {
        this.response = response;
        return this;
    }

    public RequestParam<T> fileName(String fileName) {
        this.fileName = fileName;
        return this;
    }

    public RequestParam<T> fileSystem(Boolean fileSystem) {
        this.fileSystem = fileSystem;
        return this;
    }

    public RequestParam<T> url(String url) {
        this.url = url;
        return this;
    }

    public RequestParam<T> header(Map<String, String> header) {
        this.header = header;
        return this;
    }

    public RequestParam<T> params(Map<String, String> params) {
        this.params = params;
        return this;
    }

    public RequestParam<T> body(Object body) {
        this.body = body;
        return this;
    }

    public RequestParam<T> mode(RequestMode mode) {
        this.mode = mode;
        return this;
    }

    public RequestParam<T> clazz(Class<T> clazz) {
        this.clazz = clazz;
        return this;
    }

    public RequestParam<T> mediaType(MediaType mediaType) {
        this.mediaType = mediaType;
        return this;
    }

    public RequestParam<T> timeout(Integer timeout) {
        this.timeout = timeout;
        return this;
    }

    public RequestParam<T> failureRetry(Boolean failureRetry) {
        this.failureRetry = failureRetry;
        return this;
    }

    public RequestParam<T> logReq(Boolean logReq) {
        this.logReq = logReq;
        return this;
    }

    public RequestParam<T> logRsp(Boolean logRsp) {
        this.logRsp = logRsp;
        return this;
    }

    /**
     * 发送请求
     *
     * @return 响应
     */
    public T request() {
        try {
            return HttpUtil.execute(this);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * get请求
     *
     * @return 响应
     */
    public T get() {
        try {
            mode(RequestMode.GET);
            return HttpUtil.execute(this);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * post请求
     *
     * @return 响应
     */
    public T post() {
        try {
            mode(RequestMode.POST);
            return HttpUtil.execute(this);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 下载请求
     */
    public void download() {
        if (Objects.isNull(response)) {
            ExceptionUtil.wrapRuntimeAndThrow("response不能为空");
        }
        HttpUtil.download(this);
    }

}