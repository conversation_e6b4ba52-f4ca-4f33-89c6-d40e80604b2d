package com.ywwl.customer.center.common.utils;

import org.springframework.util.Assert;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2017/12/13
 */
public class DateUtil {

    public static long toEpochMilli(LocalDateTime dateTime) {
        return dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static long toEpochSecond(LocalDateTime dateTime) {
        Assert.notNull(dateTime, "non null dateTime required");
        return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    public static String stardDate(LocalDateTime dateTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
        return dateTimeFormatter.format(dateTime);
    }

    /**
     * 获取日期时间,如：2013-09-09 12:12:12
     */
    public static String getDateTime(LocalDateTime date) {
        DateTimeFormatter sdf = null;
        try {
            if (null != date) {
                sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return sdf.format(date);
            } else {
                return null;
            }
        } catch (Exception e) {
            return null;
        } finally {
            sdf = null;
        }
    }

    /**
     * 转换类型: Date-->LocalDateTime
     *
     * <AUTHOR>
     * @since 2018年3月26日 下午4:55:03
     */
    public static LocalDateTime change(Date date) {
        if (date != null) {
            try {
                Instant instant = date.toInstant();
                ZoneId zoneId = ZoneId.systemDefault();
                return instant.atZone(zoneId).toLocalDateTime();
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

}
