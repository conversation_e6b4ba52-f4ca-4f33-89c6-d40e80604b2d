package com.ywwl.customer.center.common.utils;

import cn.hutool.core.util.SerializeUtil;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * redis
 *
 * <AUTHOR>
 * @date 2023/03/23 15:23
 **/
@Component
public class EhCacheTemplate {

	@Resource
	CacheManager ehCacheCacheManager;

	/**
	 * 获取数据
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValue(CacheKeyEnum module, String key) {
		return getValue(module, key, null);
	}

	/**
	 * 获取数据
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValue(CacheKeyEnum module, String key, T defaultValue) {
		final Cache<String, Serializable> cache = getCache(module);
		final byte[] t = (byte[]) cache.get(key);
		if (Objects.nonNull(t)) {
			return SerializeUtil.deserialize(t);
		};
		return defaultValue;
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue) {
		return getValueAndCache(module ,key, defaultValue, true);
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param resultHandle	结果处理方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue ,boolean priorityCache, Consumer<T> resultHandle) {
		// 缓存优先
		if(priorityCache) {
			T value = getValue(module, key);
			if (Objects.isNull(value)) {
				value = defaultValue.get();
				if (Objects.nonNull(resultHandle)) {
					resultHandle.accept(value);
				}
			}
			return value;
		}
		// 值优先
		try {
			T result = defaultValue.get();
			if (Objects.nonNull(resultHandle)) {
				resultHandle.accept(result);
			}
			return result;
		} catch (Throwable e) {
			final T value = getValue(module, key);
			if (Objects.nonNull(value)) {
				return value;
			}
			throw e;
		}
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue, boolean priorityCache) {
		return getValueAndCache(module ,key, defaultValue, priorityCache, v -> setValue(module, key, v));
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValueAndCache(CacheKeyEnum module, String key, T defaultValue) {
		final Supplier<? extends T> supplier = () -> defaultValue;
		return getValueAndCache(module ,key, supplier);
	}

	/**
	 * 设置参数
	 *
	 * @param module	缓存模块
	 * @param key   	缓存Key
	 * @param value 	缓存Value
	 * @param <T>   	缓存Value类型
	 * @return Value
	 */
	public <T extends Serializable> T setValue(CacheKeyEnum module ,String key, T value) {
		final Cache<String, Serializable> cache = getCache(module);
		if (Objects.nonNull(value)) {
			cache.put(key, SerializeUtil.serialize(value));
		}
		return value;
	}

	/**
	 * 获取缓存
	 * @param module	模块
	 * @return			模块缓存
	 */
	private Cache<String, Serializable> getCache(CacheKeyEnum module) {
		return ehCacheCacheManager.getCache(module.key(), String.class, Serializable.class);
	}

	/**
	 * 删除对象
	 * @param module 模块
	 * @param key	key
	 * @return	是否成功
	 */
	public Boolean delete(CacheKeyEnum module, String key) {
		final Cache<String, Serializable> cache = getCache(module);
		if (Objects.nonNull(cache)) {
			cache.remove(key);
		}
		return true;
	}

	/**
	 * 删除对象
	 * @param module 模块
	 * @return	是否成功
	 */
	public Boolean delete(CacheKeyEnum module) {
		final Cache<String, Serializable> cache = getCache(module);
		if (Objects.nonNull(cache)) {
			cache.clear();
		}
		return true;
	}

	/**
	 * 设置参数
	 *
	 * @param module	缓存模块
	 * @param key   	缓存Key
	 * @param value 	缓存Value
	 * @param <T>   	缓存Value类型
	 * @return Value
	 */
	public <T extends Serializable> T setValueBase(CacheKeyEnum module ,String key, T value) {
		final Cache<String, Serializable> cache = getCache(module);
		if (Objects.nonNull(value)) {
			cache.put(key, value);
		}
		return value;
	}

	/**
	 * 获取数据
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T extends Serializable> T getValueBase(CacheKeyEnum module, String key) {
		final Cache<String, Serializable> cache = getCache(module);
		final Serializable t = cache.get(key);
		if (Objects.nonNull(t)) {
			return (T)t;
		};
		return null;
	}

}
