package com.ywwl.customer.center.common.config;

import com.ywwl.customer.center.common.interceptor.TimeoutInterceptor;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @date 2019-04-09
 */
@Configuration
public class OkHttpConfiguration {

	@Value("${ok.http.connect-timeout}")
	private Integer connectTimeout;

	@Value("${ok.http.read-timeout}")
	private Integer readTimeout;

	@Value("${ok.http.write-timeout}")
	private Integer writeTimeout;

	@Value("${ok.http.max-idle-connections}")
	private Integer maxIdleConnections;

	@Value("${ok.http.keep-alive-duration}")
	private Long keepAliveDuration;

	@Bean
	public OkHttpClient okHttpClient() {
		return new OkHttpClient.Builder()
				.sslSocketFactory(getSSLSocketFactory(), getTrustManager())
				.connectionPool(pool())
				.connectTimeout(connectTimeout, TimeUnit.SECONDS)
				.readTimeout(readTimeout, TimeUnit.SECONDS)
				.writeTimeout(writeTimeout,TimeUnit.SECONDS)
				.hostnameVerifier((hostname, session) -> true)
				// 添加超时拦截器
				.addInterceptor(new TimeoutInterceptor())
				.build();
	}

	/**
	 * 获取这个SSLSocketFactory
	 **/
	public static SSLSocketFactory getSSLSocketFactory() {
		try {
			SSLContext sslContext = SSLContext.getInstance("SSL");
			sslContext.init(null, new TrustManager[]{getTrustManager()}, new SecureRandom());
			return sslContext.getSocketFactory();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取TrustManager
	 */
	private static X509TrustManager getTrustManager() {
		return new X509TrustManager() {
			@Override
			public void checkClientTrusted(X509Certificate[] chain, String authType) {
			}
			@Override
			public void checkServerTrusted(X509Certificate[] chain, String authType) {
			}
			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return new X509Certificate[]{};
			}
		};
	}

	@Bean
	public ConnectionPool pool() {
		return new ConnectionPool(maxIdleConnections, keepAliveDuration, TimeUnit.SECONDS);
	}
}
