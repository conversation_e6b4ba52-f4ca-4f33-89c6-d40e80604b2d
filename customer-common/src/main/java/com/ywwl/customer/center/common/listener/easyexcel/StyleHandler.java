package com.ywwl.customer.center.common.listener.easyexcel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;

import java.util.List;

/**
 * //TODO 样式
 *
 * <AUTHOR>
 * @date 2023/2/13
 */
public class StyleHandler implements CellWriteHandler {

    /**
     * //TODO 操作列
     *
     * @date 2023/2/16
     */
    private List<Integer> columnIndexS;
    /**
     * //TODO 颜色
     *
     * @date 2023/2/16
     */
    private Short colorIndex;
    private Boolean aBoolean = false;

    public StyleHandler(List<Integer> columnIndexS, Short colorIndex, Boolean aBoolean) {
        this.columnIndexS = columnIndexS;
        this.colorIndex = colorIndex;
        this.aBoolean = aBoolean;
    }

    @Override
    public int order() {
        return 50003;
    }

    public StyleHandler() {
    }

    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (aBoolean) {
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
            CellStyle style = workbook.createCellStyle();

            // 设置背景颜色为灰色
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 设置边框样式
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderTop(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);

            // 设置文字居中对齐
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setWrapText(true);

            cell.setCellStyle(style);

            Font font = workbook.createFont();
            font.setBold(true);
            font.setFontHeightInPoints((short) 12);
            if (isHead) {
                if (CollectionUtils.isNotEmpty(columnIndexS) && colorIndex != null && columnIndexS.contains(cell.getColumnIndex())) {
                    // 只对标题行进行处理
                    font.setColor(colorIndex);
                }
            }
            style.setFont(font);
            cell.setCellStyle(style);
        }

    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {

    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {

    }


    /***
     * //TODO  内容水平居中
     * <AUTHOR>
     * @date 2023/2/16 9:53

     * @return com.alibaba.excel.write.style.HorizontalCellStyleStrategy
     */
    public HorizontalCellStyleStrategy getStyleStrategy() {
        //内容样式策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //垂直居中,水平居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        // 字体策略
        WriteFont contentWriteFont = new WriteFont();
        // 字体大小
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        //头策略使用默认
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
