// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 15:08
 * @ModifyDate 2023/3/14 15:08
 * @Version 1.0
 */
public enum ReturnCRMEnum {
    SEND_BACK("4","0"),
    ASK_FOR("6","1"),
    PAID_BY_SHIPPER("20","2"),
    FREIGHT_AT_DESTINATION ( "19","3");

    private String type;
    private String desc;


    ReturnCRMEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String type) {
        for (ReturnCRMEnum returnTypeEnum : ReturnCRMEnum.values()) {
            if (type.equals(returnTypeEnum.getType())) {
                return returnTypeEnum.getDesc();
            }
        }
        return null;
    }
}
