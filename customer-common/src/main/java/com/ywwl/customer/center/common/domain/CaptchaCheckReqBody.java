package com.ywwl.customer.center.common.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/7/6
 */
@Data
@Builder
public class CaptchaCheckReqBody {
    /**
     * 操作编码
     */
    private String opcode;
    /**
     * 平台类型
     */
    @JsonProperty(value = "platformtype")
    private String platformType;

    @JsonProperty(value = "loginname")
    private String loginName;

    private String email;
    /**
     * 基础id
     */
    @JsonProperty(value = "baseid")
    private String baseId;

    @JsonProperty(value = "checkcode")
    private String checkCode;
}
