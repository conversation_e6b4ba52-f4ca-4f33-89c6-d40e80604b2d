package com.ywwl.customer.center.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据签名工具类
 */
@Component
@Slf4j
public class SignUtils {


    public static void main(String[] args) {
        String token = "CPS18039501855MANAGERTEST";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = String.valueOf(RandomUtils.nextInt(1000, 10000));
        ;

        Map<String, Object> values = new HashMap<>();
        values.put("title", "22222");
        values.put("content", "中华人民共和国2");
        values.put("area", 1);
        values.put("category", 2);
        values.put("categorySecond", 3);
        values.put("createUserId", 12);
        values.put("updateUserId", 14);
        values.put("top", false);
        values.put("timestamp",timestamp);
        values.put("nonce",nonce);

        String sign = getSignForManager(token, values);

        System.out.println(sign+"---"+timestamp+"---"+nonce);
    }


    /**
     * 进行加签
     *
     *
     * @param valueMap 需要签名的集合，未处理前的
     * @return 处理后，返回的签名值
     */
    public static String getSign(String token,Map<String, String>  valueMap)
    {
        //对参数按key进行字典升序排列
        String soreValueMap = SortUtils.formatUrlParam(valueMap, "utf-8", true);
        //将key拼接在请求参数的前面
        String signVlue = soreValueMap + "&token=" + token;
        //形成MD5加密后的签名
        String md5SignVlues = MD5Util.md5(signVlue);
        return md5SignVlues;
    }

    /**
     * 进行验签操作
     *
     * @param valueMap 请求参数
     *
     * @param sign 接口调用方传过来的sign
     *
     * @return 验签成功返回true  否则返回false
     */
    public static boolean verifySign(String token, Map<String, String> valueMap, String sign) {

        System.out.println("服务器接收签名为:"+sign);
        //对参数按key进行字典升序排列
        String soreValueMap = SortUtils.formatUrlParam(valueMap, "utf-8", true);
        //将key拼接在请求参数的前面
        String signVlue = soreValueMap + "&token=" + token;
        //形成MD5加密后的签名
        String md5SignVlues = MD5Util.md5(signVlue);
        System.out.println("服务端处理得到签名为:"+md5SignVlues);
        if (md5SignVlues.equals(sign)) {
            return true;
        }

        return false;
    }


    /**
     * 将Key对应value转为通用Object
     *
     * @param token
     * @param valueMap
     * @return
     */
    public static String getSignForManager(String token, Map<String, Object> valueMap) {
        //对参数按key进行字典升序排列
        String soreValueMap = SortUtils.formatUrlParamForManager(valueMap, "utf-8", true);
        //将key拼接在请求参数的前面
        String signVlue = soreValueMap + "&token=" + token;
        //形成MD5加密后的签名
        String md5SignVlues = MD5Util.md5(signVlue);
        return md5SignVlues;
    }


    /**
     * 进行验签操作
     *
     * @param valueMap 请求参数
     * @param sign     接口调用方传过来的sign
     * @return 验签成功返回true  否则返回false
     */
    public static boolean verifySignForManager(String token, Map<String, Object> valueMap, String sign) {

        System.out.println("服务器接收签名为:" + sign);
        //对参数按key进行字典升序排列
        String soreValueMap = SortUtils.formatUrlParamForManager(valueMap, "utf-8", true);
        //将key拼接在请求参数的前面
        String signVlue = soreValueMap + "&token=" + token;
        //形成MD5加密后的签名
        String md5SignVlues = MD5Util.md5(signVlue);
        System.out.println("服务端处理得到签名为:" + md5SignVlues);
        if (md5SignVlues.equals(sign)) {
            return true;
        }

        return false;
    }



}
