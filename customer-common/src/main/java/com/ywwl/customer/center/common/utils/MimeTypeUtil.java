package com.ywwl.customer.center.common.utils;

import okhttp3.MediaType;

import java.util.HashMap;
import java.util.Map;

/**
 * 响应类型工具类
 *
 * <AUTHOR>
 * @since 2023/12/28 09:48
 **/
public class MimeTypeUtil {

    /**
     * 类型Map
     */
    public final static Map<String, String> mimeTypeMap;

    static {
        mimeTypeMap = new HashMap<>();
        mimeTypeMap.put("video/3gpp2", "3g2");
        mimeTypeMap.put("video/3gp", "3gp");
        mimeTypeMap.put("video/3gpp", "3gp");
        mimeTypeMap.put("application/x-compressed", "7zip");
        mimeTypeMap.put("audio/x-acc", "aac");
        mimeTypeMap.put("audio/ac3", "ac3");
        mimeTypeMap.put("application/postscript", "ai");
        mimeTypeMap.put("audio/x-aiff", "aif");
        mimeTypeMap.put("audio/aiff", "aif");
        mimeTypeMap.put("audio/x-au", "au");
        mimeTypeMap.put("video/x-msvideo", "avi");
        mimeTypeMap.put("video/msvideo", "avi");
        mimeTypeMap.put("video/avi", "avi");
        mimeTypeMap.put("application/x-troff-msvideo", "avi");
        mimeTypeMap.put("application/macbinary", "bin");
        mimeTypeMap.put("application/mac-binary", "bin");
        mimeTypeMap.put("application/x-binary", "bin");
        mimeTypeMap.put("application/x-macbinary", "bin");
        mimeTypeMap.put("image/bmp", "bmp");
        mimeTypeMap.put("image/x-bmp", "bmp");
        mimeTypeMap.put("image/x-bitmap", "bmp");
        mimeTypeMap.put("image/x-xbitmap", "bmp");
        mimeTypeMap.put("image/x-win-bitmap", "bmp");
        mimeTypeMap.put("image/x-windows-bmp", "bmp");
        mimeTypeMap.put("image/ms-bmp", "bmp");
        mimeTypeMap.put("image/x-ms-bmp", "bmp");
        mimeTypeMap.put("application/bmp", "bmp");
        mimeTypeMap.put("application/x-bmp", "bmp");
        mimeTypeMap.put("application/x-win-bitmap", "bmp");
        mimeTypeMap.put("application/cdr", "cdr");
        mimeTypeMap.put("application/coreldraw", "cdr");
        mimeTypeMap.put("application/x-cdr", "cdr");
        mimeTypeMap.put("application/x-coreldraw", "cdr");
        mimeTypeMap.put("image/cdr", "cdr");
        mimeTypeMap.put("image/x-cdr", "cdr");
        mimeTypeMap.put("zz-application/zz-winassoc-cdr", "cdr");
        mimeTypeMap.put("application/mac-compactpro", "cpt");
        mimeTypeMap.put("application/pkix-crl", "crl");
        mimeTypeMap.put("application/pkcs-crl", "crl");
        mimeTypeMap.put("application/x-x509-ca-cert", "crt");
        mimeTypeMap.put("application/pkix-cert", "crt");
        mimeTypeMap.put("text/css", "css");
        mimeTypeMap.put("text/x-comma-separated-values", "csv");
        mimeTypeMap.put("text/comma-separated-values", "csv");
        mimeTypeMap.put("application/vnd.msexcel", "csv");
        mimeTypeMap.put("application/x-director", "dcr");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        mimeTypeMap.put("application/x-dvi", "dvi");
        mimeTypeMap.put("message/rfc822", "eml");
        mimeTypeMap.put("application/x-msdownload", "exe");
        mimeTypeMap.put("video/x-f4v", "f4v");
        mimeTypeMap.put("audio/x-flac", "flac");
        mimeTypeMap.put("video/x-flv", "flv");
        mimeTypeMap.put("image/gif", "gif");
        mimeTypeMap.put("application/gpg-keys", "gpg");
        mimeTypeMap.put("application/x-gtar", "gtar");
        mimeTypeMap.put("application/x-gzip", "gzip");
        mimeTypeMap.put("application/mac-binhex40", "hqx");
        mimeTypeMap.put("application/mac-binhex", "hqx");
        mimeTypeMap.put("application/x-binhex40", "hqx");
        mimeTypeMap.put("application/x-mac-binhex40", "hqx");
        mimeTypeMap.put("text/html", "html");
        mimeTypeMap.put("image/x-icon", "ico");
        mimeTypeMap.put("image/x-ico", "ico");
        mimeTypeMap.put("image/vnd.microsoft.icon", "ico");
        mimeTypeMap.put("text/calendar", "ics");
        mimeTypeMap.put("application/java-archive", "jar");
        mimeTypeMap.put("application/x-java-application", "jar");
        mimeTypeMap.put("application/x-jar", "jar");
        mimeTypeMap.put("image/jp2", "jp2");
        mimeTypeMap.put("video/mj2", "jp2");
        mimeTypeMap.put("image/jpx", "jp2");
        mimeTypeMap.put("image/jpm", "jp2");
        mimeTypeMap.put("image/jpeg", "jpeg");
        mimeTypeMap.put("image/pjpeg", "jpeg");
        mimeTypeMap.put("application/x-javascript", "js");
        mimeTypeMap.put("application/json", "json");
        mimeTypeMap.put("text/json", "json");
        mimeTypeMap.put("application/vnd.google-earth.kml+xml", "kml");
        mimeTypeMap.put("application/vnd.google-earth.kmz", "kmz");
        mimeTypeMap.put("text/x-log", "log");
        mimeTypeMap.put("audio/x-m4a", "m4a");
        mimeTypeMap.put("audio/mp4", "m4a");
        mimeTypeMap.put("application/vnd.mpegurl", "m4u");
        mimeTypeMap.put("audio/midi", "mid");
        mimeTypeMap.put("application/vnd.mif", "mif");
        mimeTypeMap.put("video/quicktime", "mov");
        mimeTypeMap.put("video/x-sgi-movie", "movie");
        mimeTypeMap.put("audio/mpeg", "mp3");
        mimeTypeMap.put("audio/mpg", "mp3");
        mimeTypeMap.put("audio/mpeg3", "mp3");
        mimeTypeMap.put("audio/mp3", "mp3");
        mimeTypeMap.put("video/mp4", "mp4");
        mimeTypeMap.put("video/mpeg", "mpeg");
        mimeTypeMap.put("application/oda", "oda");
        mimeTypeMap.put("audio/ogg", "ogg");
        mimeTypeMap.put("video/ogg", "ogg");
        mimeTypeMap.put("application/ogg", "ogg");
        mimeTypeMap.put("font/otf", "otf");
        mimeTypeMap.put("application/x-pkcs10", "p10");
        mimeTypeMap.put("application/pkcs10", "p10");
        mimeTypeMap.put("application/x-pkcs12", "p12");
        mimeTypeMap.put("application/x-pkcs7-signature", "p7a");
        mimeTypeMap.put("application/pkcs7-mime", "p7c");
        mimeTypeMap.put("application/x-pkcs7-mime", "p7c");
        mimeTypeMap.put("application/x-pkcs7-certreqresp", "p7r");
        mimeTypeMap.put("application/pkcs7-signature", "p7s");
        mimeTypeMap.put("application/pdf", "pdf");
        mimeTypeMap.put("application/octet-stream", "pdf");
        mimeTypeMap.put("application/x-x509-user-cert", "pem");
        mimeTypeMap.put("application/x-pem-file", "pem");
        mimeTypeMap.put("application/pgp", "pgp");
        mimeTypeMap.put("application/x-httpd-php", "php");
        mimeTypeMap.put("application/php", "php");
        mimeTypeMap.put("application/x-php", "php");
        mimeTypeMap.put("text/php", "php");
        mimeTypeMap.put("text/x-php", "php");
        mimeTypeMap.put("application/x-httpd-php-source", "php");
        mimeTypeMap.put("image/png", "png");
        mimeTypeMap.put("image/x-png", "png");
        mimeTypeMap.put("application/powerpoint", "ppt");
        mimeTypeMap.put("application/vnd.ms-powerpoint", "ppt");
        mimeTypeMap.put("application/vnd.ms-office", "ppt");
        mimeTypeMap.put("application/msword", "doc");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", "pptx");
        mimeTypeMap.put("application/x-photoshop", "psd");
        mimeTypeMap.put("image/vnd.adobe.photoshop", "psd");
        mimeTypeMap.put("audio/x-realaudio", "ra");
        mimeTypeMap.put("audio/x-pn-realaudio", "ram");
        mimeTypeMap.put("application/x-rar", "rar");
        mimeTypeMap.put("application/rar", "rar");
        mimeTypeMap.put("application/x-rar-compressed", "rar");
        mimeTypeMap.put("audio/x-pn-realaudio-plugin", "rpm");
        mimeTypeMap.put("application/x-pkcs7", "rsa");
        mimeTypeMap.put("text/rtf", "rtf");
        mimeTypeMap.put("text/richtext", "rtx");
        mimeTypeMap.put("video/vnd.rn-realvideo", "rv");
        mimeTypeMap.put("application/x-stuffit", "sit");
        mimeTypeMap.put("application/smil", "smil");
        mimeTypeMap.put("text/srt", "srt");
        mimeTypeMap.put("image/svg+xml", "svg");
        mimeTypeMap.put("application/x-shockwave-flash", "swf");
        mimeTypeMap.put("application/x-tar", "tar");
        mimeTypeMap.put("application/x-gzip-compressed", "tgz");
        mimeTypeMap.put("image/tiff", "tiff");
        mimeTypeMap.put("font/ttf", "ttf");
        mimeTypeMap.put("text/plain", "txt");
        mimeTypeMap.put("text/x-vcard", "vcf");
        mimeTypeMap.put("application/videolan", "vlc");
        mimeTypeMap.put("text/vtt", "vtt");
        mimeTypeMap.put("audio/x-wav", "wav");
        mimeTypeMap.put("audio/wave", "wav");
        mimeTypeMap.put("audio/wav", "wav");
        mimeTypeMap.put("application/wbxml", "wbxml");
        mimeTypeMap.put("video/webm", "webm");
        mimeTypeMap.put("image/webp", "webp");
        mimeTypeMap.put("audio/x-ms-wma", "wma");
        mimeTypeMap.put("application/wmlc", "wmlc");
        mimeTypeMap.put("video/x-ms-wmv", "wmv");
        mimeTypeMap.put("video/x-ms-asf", "wmv");
        mimeTypeMap.put("font/woff", "woff");
        mimeTypeMap.put("font/woff2", "woff2");
        mimeTypeMap.put("application/xhtml+xml", "xhtml");
        mimeTypeMap.put("application/excel", "xl");
        mimeTypeMap.put("application/msexcel", "xls");
        mimeTypeMap.put("application/x-msexcel", "xls");
        mimeTypeMap.put("application/x-ms-excel", "xls");
        mimeTypeMap.put("application/x-excel", "xls");
        mimeTypeMap.put("application/x-dos_ms_excel", "xls");
        mimeTypeMap.put("application/xls", "xls");
        mimeTypeMap.put("application/x-xls", "xls");
        mimeTypeMap.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        mimeTypeMap.put("application/vnd.ms-excel", "xlsx");
        mimeTypeMap.put("application/xml", "xml");
        mimeTypeMap.put("text/xml", "xml");
        mimeTypeMap.put("text/xsl", "xsl");
        mimeTypeMap.put("application/xspf+xml", "xspf");
        mimeTypeMap.put("application/x-compress", "z");
        mimeTypeMap.put("application/x-zip", "zip");
        mimeTypeMap.put("application/zip", "zip");
        mimeTypeMap.put("application/x-zip-compressed", "zip");
        mimeTypeMap.put("application/s-compressed", "zip");
        mimeTypeMap.put("multipart/x-zip", "zip");
        mimeTypeMap.put("text/x-scriptzsh", "zsh");
    }

    /**
     * 获取文件后缀
     *
     * @param mimeType 类型
     * @return 后缀
     */
    public static String getSuffix(String mimeType) {
        return mimeTypeMap.get(mimeType);
    }

    /**
     * 获取文件后缀
     *
     * @param mimeType 类型
     * @return 后缀
     */
    public static String getSuffix(MediaType mimeType) {
        return getSuffix(mimeType.toString());
    }


}
