// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 15:08
 * @ModifyDate 2023/3/14 15:08
 * @Version 1.0
 */
public enum ReturnNameEnum {
    SEND_BACK("退回-司机","4"),
    ASK_FOR("退回-快递（寄付）","20"),
    PAID_BY_SHIPPER("退回-快递（到付）","19"),
    FREIGHT_AT_DESTINATION ( "退回-自取","6");

    private String type;
    private String desc;


    ReturnNameEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(String type) {
        for (ReturnNameEnum returnTypeEnum : ReturnNameEnum.values()) {
            if (type.equals(returnTypeEnum.getType())) {
                return returnTypeEnum.getDesc();
            }
        }
        return null;
    }
}
