package com.ywwl.customer.center.common.enums;

import lombok.Getter;

@Getter
public enum BusinessApplyStatusEnum {
    NOT_SUPPORT(-2, "不支持开通业务线"),
    WAIT_DEVELOP(-1,"敬请期待"),
    NOT_OPEN(0,"未开通业务"),
    NOT_AUTH(1,"未认证"),
    AUTHED(2,"已认证"),
    WAIT_AUDIT(3,"待审核"),
    AUDIT_FAIL(4,"审核失败"),
    WAIT_SIGN(5,"待签约"),
    SIGNED(6,"完成"),
    UPDATE_COMMISSION(9, "补齐委托书"),
    CONFIRM_INFORMATION(10, "确认付款信息"),
    COMPLETE_CARD_ATTACH(11, "补齐证件照"),
    ;

    private Integer value;
    private String desc;

    BusinessApplyStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static BusinessApplyStatusEnum getStatus(Integer value){
        for (BusinessApplyStatusEnum businessApplyStatusEnum : BusinessApplyStatusEnum.values()) {
            if(businessApplyStatusEnum.getValue().equals(value)){
                return businessApplyStatusEnum;
            }
        }
        return null;
    }
}
