package com.ywwl.customer.center.common.enums;

/**
 * 短信验证码类型
 *
 * <AUTHOR>
 * @date 2018/6/7
 */
public enum SMSTypeEnum {
    REGISTER("10", "注册验证码"),
    LOGIN("20", "登陆验证码"),
    REST_PASSWORD("30", "找回密码"),
    CHANGE_PASSWORD("60", "密码太简单修改密码"),
    CONFIRM_AGREEMENT("31", "确认协议发送短信"),
    UPDATE_MERCHANT_NAME("34", "修改商户名称"),
    UPDATE_USER_INFO("35", "修改用户信息"),
    UN_FREEZE_MERCHANT("36", "解冻商户申请");

    private String value;
    private String desc;

    SMSTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String value() {
        return value;
    }

    public String desc() {
        return desc;
    }
}
