package com.ywwl.customer.center.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2020/7/5 11:05
 */
public class PrivacyDimmer {
    private static final String OVERLAY = "****";
    private static final int THREE = 3;
    private static final int SEVEN = 7;
    private static final int FOUR = 4;
    private static final int TWO=2;

    /**
     * 139****0504
     *
     * @param content
     * @return
     */
    public static String maskMobile(String content) {
        if (StringUtils.isEmpty(content)) {
            return "";
        }
        return StringUtils.overlay(content, OVERLAY, THREE, SEVEN);
    }

    /**
     * 过滤邮箱账号
     * 132***************
     *
     * @param email
     * @return
     */
    public static String maskEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return "";
        }
        String at = "@";
        if (!email.contains(at)) {
            return email;
        }
        /**
         * 这里主要逻辑是需要保留邮箱的注册商 比如@qq.com
         */
        int length = StringUtils.indexOf(email, at);
        String content = StringUtils.substring(email, 0, length);
        String mask = StringUtils.overlay(content, OVERLAY, THREE, SEVEN);
        return mask + StringUtils.substring(email, length);
    }

    /**
     * 身份证打码操作
     * 132***************
     *
     * @param idCard
     * @return
     */
    public static String maskIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return "";
        }
        return StringUtils.overlay(idCard, OVERLAY, THREE, idCard.length() - FOUR);
    }

    /**
     * @Description 银行卡
     * <AUTHOR>
     * @Date 2020/7/5 11:31
     */
    public static String maskBankCard(String bankCard) {
        if (StringUtils.isBlank(bankCard)) {
            return "";
        }
        return StringUtils.overlay(bankCard, OVERLAY, FOUR, bankCard.length() - FOUR);
    }

    /**
     * @Description 支付宝账号
     * <AUTHOR>
     * @Date 2020/7/5 15:19
     */
    public static String maskApliPay(String account) {
        if (StringUtils.isBlank(account)) {
            return "";
        }
        String at = "@";
        if (account.contains(at)) {
            return maskEmail(account);
        } else {
            return maskMobile(account);
        }
    }

    /**
     * @Description 只剩余最后四位
     * <AUTHOR>
     * @Date 2020/7/6 16:59
     */
    public static String maskPhone(String content) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        return StringUtils.overlay(content, "*******", 0, SEVEN);
    }

    /**
     * <AUTHOR>
     * @description 加密名字
     * @date 2023/6/29 10:42
     **/
    public static String maskName(String content){
        if (StringUtils.isBlank(content)) {
            return "";
        }
        return StringUtils.overlay(content, "**", 0, content.length()-1);
    }

    /**
     * @author: dinghy 
     * @createTime: 2024/5/11 15:20
     * @description: //
     */
    public static String maskTaxCode(String content){
        if (StringUtils.isBlank(content)) {
            return "";
        }
        return StringUtils.overlay(content, OVERLAY, TWO, content.length() - TWO);
    }

    public static void main(String[] args) {
        System.out.println(PrivacyDimmer.maskMobile("***********"));
        System.out.println(PrivacyDimmer.maskEmail("<EMAIL>"));
        System.out.println(PrivacyDimmer.maskEmail("<EMAIL>"));
        System.out.println(PrivacyDimmer.maskIdCard("1321281***********"));
        System.out.println(PrivacyDimmer.maskBankCard("6227002434611488232"));
        System.out.println(maskPhone("***********"));
        System.out.println(maskName("张三"));
        System.out.println(maskTaxCode("111222"));
    }

}
