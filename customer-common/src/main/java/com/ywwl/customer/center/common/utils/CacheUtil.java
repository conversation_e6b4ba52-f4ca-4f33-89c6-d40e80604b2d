package com.ywwl.customer.center.common.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 缓存工具类
 *
 * <AUTHOR>
 * @date 2023/03/24 15:29
 **/
@Component
public class CacheUtil {

	/**
	 * RedisTemplate
	 */
	private static RedisTemplate redisTemplate;
	/**
	 * EhCacheTemplate
	 */
	private static EhCacheTemplate ehCacheTemplate;

	@Autowired
	public CacheUtil(RedisTemplate redisTemplate, EhCacheTemplate ehCacheTemplate) {
		CacheUtil.redisTemplate = redisTemplate;
		CacheUtil.ehCacheTemplate = ehCacheTemplate;
	}

	/**
	 * 获取redis缓存类
	 * @return	redis缓存类
	 */
	public static RedisTemplate redis() {
		return redisTemplate;
	}

	/**
	 * 获取ehcache缓存类
	 * @return	ehcache缓存类
	 */
	public static EhCacheTemplate ehCache() {
		return ehCacheTemplate;
	}

}
