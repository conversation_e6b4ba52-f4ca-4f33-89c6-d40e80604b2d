package com.ywwl.customer.center.common.enums;

import lombok.Getter;

@Getter
public enum BusinessTypeEnum {
    STRAIGHT(0, "小包专线"),
    FBA(1, "FBA专线"),
    OVERSEA(2, "海外派"),
    CHINA_WAREHOUSE(3, "中国仓业务"),
    YWE_WAREHOUSE(4, "YWE海外仓业务"),

    ;
    private Integer value;
    private String desc;

    BusinessTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public static final BusinessTypeEnum getBusinessTypeEnum(Integer value) {
        for (BusinessTypeEnum businessTypeEnum : BusinessTypeEnum.values()) {
            if (businessTypeEnum.getValue().equals(value)) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
