package com.ywwl.customer.center.common.utils;

import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;

public class PdfUtil {
    public static final String COMPANY_PROOF = "/data/pdf/企业修修改手机号证明.pdf";
    public static final String ALI_TYPE_FONT = "/data/pdf/ARIALUNI.TTF";

    public static void generateCompanyUpdatePhoneProof(Map<String, Object> map, HttpServletResponse response) {

        PdfStamper stamper;
        try {
            StringBuilder fileName = new StringBuilder("变更手机号企业证明模版").append(".pdf");
            response.setContentType("application/force-download");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName.toString(), "UTF-8"));
            BaseFont bf = BaseFont.createFont(ALI_TYPE_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            PdfReader pdfReader = new PdfReader(COMPANY_PROOF);
            stamper = new PdfStamper(pdfReader, response.getOutputStream());
            AcroFields form = stamper.getAcroFields();
            // 默认12号字体
            form.addSubstitutionFont(bf);
            for (String key : map.keySet()) {
                String value = map.get(key).toString();
                form.setField(key, value);
            }
            stamper.setFormFlattening(true);
            stamper.close();
            pdfReader.close();
        } catch (Exception e) {
            e.printStackTrace();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getOutputStream().write(e.getMessage().getBytes());
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }


}

