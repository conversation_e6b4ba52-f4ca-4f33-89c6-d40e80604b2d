package com.ywwl.customer.center.common.domain;


import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO 响应码说明
 *
 * <AUTHOR>
 * @date 2023/02/16 15:30
 **/
public enum ResponseCode {
    /**
     * 服务器成功返回请求的数据
     *
     * @date 2023/2/28
     */
    PORTAL_200("200", "操作成功", "服务器成功返回请求的数据"),
    /**
     * 服务器成功返回请求的数据
     *
     * @date 2023/2/28
     */
    PORTAL_201("201", "操作成功", "服务器成功返回请求的数据"),
    /**
     * 一个请求已经进入后台排队（异步任务）
     *
     * @date 2023/2/28
     */
    PORTAL_202("202", "操作成功", "一个请求已经进入后台排队（异步任务）"),
    /**
     * 删除数据成功
     *
     * @date 2023/2/28
     */
    PORTAL_204("204", "操作成功", "删除数据成功"),


    PORTAL_3001("3001", "公共crm不可用", "调用公共crm服务异常"),
    PORTAL_3002("3002", "主数据不可用", "调用主数据服务异常"),
    PORTAL_3003("3003", "售前不可用", "调用售前服务异常"),
    PORTAL_3004("3004", "微信管理不可用", "调用微信管理后台异常"),
    PORTAL_3005("3005", "账务中心不可用", "调用账务中心服务异常"),
    PORTAL_3006("3006", "账务中心运单明细服务不可用", "调用账务中心运单明细服务异常"),
    PORTAL_3007("3007", "FBA服务不可用", "调用fba服务异常"),
    PORTAL_3008("3008", "工单服务不可用", "调用工单服务异常"),


    /**
     * 发出的请求有错误,服务器没有进行新建或修改数据的操作
     *
     * @date 2023/2/28
     */
    PORTAL_400("400", "无操作", "发出的请求有错误,服务器没有进行新建或修改数据的操作"),
    /**
     * 用户没有权限（令牌、用户名、密码错误）
     *
     * @date 2023/2/28
     */
    PORTAL_401("401", "用户未登录", "用户没有权限（令牌、用户名、密码错误）"),

    PORTAL_402("402", "您暂未完成实名认证，请先完成实名认证", "客户未实名认证跳转到实名认证流程"),
    /**
     * 用户得到授权,但是访问是被禁止的
     *
     * @date 2023/2/28
     */
    PORTAL_403("403", "无权限", "用户得到授权,但是访问是被禁止的"),
    /**
     * 发出的请求针对的是不存在的记录,服务器没有进行操作
     *
     * @date 2023/2/28
     */
    PORTAL_404("404", "资源不存在", "发出的请求针对的是不存在的记录,服务器没有进行操作"),
    /**
     * 请求的格式不可得
     *
     * @date 2023/2/28
     */
    PORTAL_406("406", "格式错误", "请求的格式不可得"),
    /**
     * 请求的资源被永久删除,且不会再得到的
     *
     * @date 2023/2/28
     */
    PORTAL_410("410", "资源不存在", "请求的资源被永久删除,且不会再得到的"),
    /**
     * 当创建一个对象时,发生一个验证错误
     *
     * @date 2023/2/28
     */
    PORTAL_422("422", "参数验证失败", "当创建一个对象时,发生一个验证错误"),

    PORTAL_413("413", "请先完成小包专线业务开通", "小包专线状态未开通"),
    PORTAL_414("414", "请先开通业务线", "未开通业务线"),
    PORTAL_415("415", "您的信息可能存在异常,请联系客服/销售处理", "数据异常"),

    /**
     * 服务器发生错误,请检查服务器
     *
     * @date 2023/2/28
     */
    PORTAL_500("500", "服务器异常", "服务器发生错误,请检查服务器"),
    /**
     * 网关错误
     *
     * @date 2023/2/28
     */
    PORTAL_502("502", "网关异常", "网关错误"),
    /**
     * 服务不可用,服务器暂时过载或维护
     *
     * @date 2023/2/28
     */
    PORTAL_503("503", "服务器异常", "服务不可用,服务器暂时过载或维护"),
    /**
     * 访问超时
     *
     * @date 2023/2/28
     */
    PORTAL_504("504", "访问超时", "网关超时"),
    /**
     * 在2秒内同样的请求提交了两次
     *
     * @date 2023/2/28
     */
    PORTAL_505("505", "请勿重复操作", "在2秒内同样的请求提交了两次"),
    /**
     * 请重新验证验证码或重新获取验证码后再验证
     *
     * @date 2023/2/28
     */
    PORTAL_5001("5001", "验证失败,请重试", "请重新验证验证码或重新获取验证码后再验证"),

    /**
     * 重新填写进行修改密码或调整密码复杂度
     *
     * @date 2023/2/28
     */
    PORTAL_5002("5002", "密码过于简单,请修改密码", "请重新填写进行修改密码或调整密码复杂度"),

    /**
     * 请重新获取图片验证码后再验证
     *
     * @date 2023/2/28
     */
    PORTAL_5003("5003", "图片验证码已失效,请刷新重试", "请重新获取图片验证码后再验证"),

    /**
     * 请检查两次密码输入是否一致
     *
     * @date 2023/2/28
     */
    PORTAL_5004("5004", "两次密码输入不一致,请检查后重新输入", "请检查两次密码输入是否一致"),

    /**
     * 请刷新当前页面再次使用
     *
     * @date 2023/2/28
     */
    PORTAL_5005("5005", "窗口失效,请刷新重试", "请刷新当前页面再进行操作"),

    /**
     * 当前发送请求失败,请刷新后重试
     *
     * @date 2023/2/28
     */
    PORTAL_5006("5006", "发送失败,请刷新重试", "当前发送请求失败,请刷新后重试"),
    /**
     * 当前短信验证码失效,请重新点击发送
     *
     * @date 2023/2/28
     */
    PORTAL_5007("5007", "短信验证码失效,请刷新重试", "当前短信验证码失效,请重新点击发送"),
    /**
     * 认证服务不可用,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5008("5008", "当前认证服务不可用", "认证服务不可用,请联系技术人员"),
    /**
     * 当前信息异常,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5009("5009", "登录失败", "当前信息异常,请联系技术人员"),
    /**
     * 当前请求参数缺失
     *
     * @date 2023/2/28
     */
    PORTAL_5010("5010", "参数异常", "当前请求参数缺失"),
    /**
     * 当前服务异常,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5011("5010", "当前服务异常", "当前服务异常,请联系技术人员"),
    /**
     * 当前服务异常,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5012("5012", "登录信息有误，请检查后重新输入", "用户名错误或者密码错误或者用户不存在"),
    /**
     * // 用户信息缺失,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5013("5013", "用户信息缺失,请联系客服", "用户信息缺失,请联系技术人员"),
    /**
     * // 请刷新当前页面或者重新进行操作
     *
     * @date 2023/2/28
     */
    PORTAL_5014("5014", "重设密码的链接有错误或已过期", "请刷新当前页面或者重新进行操作"),
    /**
     * // CRM服务暂时不可用
     *
     * @date 2023/2/28
     */
    PORTAL_5015("5015", "直发CRM服务暂时不可用", "请求CRM服务端出现异常,请联系技术人员"),
    PORTAL_5016("5016", "新邮箱不能与现在邮箱相同", "新邮箱不能与现在邮箱相同,请重新输入新的邮箱"),
    PORTAL_5017("5017", "当前请求非法", "当前请求非法,非正常请求"),
    /**
     * 异常件服务不可用,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5018("5018", "当前服务不可用", "异常件服务不可用,请联系技术人员"),
    /**
     * 工单服务不可用,请联系技术人员
     *
     * @date 2023/2/28
     */
    PORTAL_5019("5019", "当前服务不可用", "工单服务不可用,请联系技术人员"),
    /**
     * 发货账号为空
     *
     * @date 2023/2/28
     */
    PORTAL_5020("5020", "请选择制单账号", "发货账号为空"),
    /**
     * 获取商户密钥失败,请进行秘钥重置后再进行此操作
     *
     * @date 2023/2/28
     */
    PORTAL_5021("5021", "获取商户密钥失败,请进行秘钥重置后再进行此操作", "获取商户密钥失败,请进行秘钥重置后再进行此操作"),
    /**
     * 请重新获取验证码后再验证
     *
     * @date 2023/2/28
     */
    PORTAL_5022("5022", "验证超时,请重试", "请重新获取验证码后再验证"),
    /**
     * KYC上传为空
     *
     * @date 2023/2/28
     */
    PORTAL_5023("5023", "提交上传为空", "KYC上传为空"),
    /**
     * 请勿操作其他商户的数据
     *
     * @date 2023/2/28
     */
    PORTAL_5024("5024", "当前请求非法", "请勿操作其他商户的数据"),
    /**
     * 在销售CRM日均票件量获取为空
     *
     * @date 2023/2/28
     */
    PORTAL_5025("5025", "日均票件量获取为空", "在销售CRM日均票件量获取为空"),
    /**
     * 在销售CRM日均票件量获取为失败
     *
     * @date 2023/2/28
     */
    PORTAL_5026("5026", "日均票件量获取失败", "在销售CRM日均票件量获取为失败"),
    /**
     * <AUTHOR>
     * @description 子用用户不允许操作
     * @date 2023/3/21 16:58
     **/
    PORTAL_5027("5027", "只允许管理员操作", "操作仅支持管理员"),
    /**
     * <AUTHOR>
     * @description 文件下载失败
     * @date 2023/3/21 16:58
     **/
    PORTAL_5028("5028", "文件下载失败", "文件流下载流获取失败"),
    /**
     * // 未知异常错误
     *
     * @date 2023/3/23
     */
    PORTAL_5029("5029", "网络异常,请稍后再试", "当前功能异常,请联系技术人员"),

    /**
     * PLM获取产品调用失败
     *
     * @date 2023/3/23
     */
    PORTAL_5030("5030", "PLM无法获取到产品", "PLM获取产品调用失败"),
    /**
     * PLM获取国家调用失败
     *
     * @date 2023/3/23
     */
    PORTAL_5031("5031", "PLM无法获取到国家", "PLM获取国家调用失败"),
    /**
     * 获取商户密钥失败,请进行秘钥重置后再进行此操作
     *
     * @date 2023/3/26
     */
    PORTAL_5032("5032", "获取商户密钥失败,请进行秘钥重置后再进行此操作", "获取商户密钥失败,请进行秘钥重置后再进行此操作"),
    /**
     * 获取发货账号为空
     *
     * @date 2023/3/26
     */
    PORTAL_5033("5033", "获取制单账号为空", "获取制单账号为空"),
    PORTAL_5034("5034", "新手机号不能与现在手机号相同", "新手机号不能与现在手机号相同,请重新输入新的手机号"),
    PORTAL_5035("5035", "校验推荐销售手机号不可用", "售前管理系统服务不可用,请联系技术人员"),
    PORTAL_5036("5036", "新建线索服务不可用", "通过销售新建线索服务不可用,请联系技术人员"),
    PORTAL_5037("5037", "请填写销售信息", "销售信息缺失,请填写销售信息"),
    PORTAL_5038("5038", "无推荐销售销售时城市必填", "无推荐销售销售时城市必填,请填写城市信息"),
    PORTAL_5039("5039", "所在城市必填", "单独选择fba业务类型时所在城市可空其余必填"),
    PORTAL_5040("5040", "日均票件量必填", "FBA业务类型可不选,其余业务类型日均票件量必填"),
    PORTAL_5041("5041", "获取国家为空", "获取国家为空"),
    PORTAL_5042("5042", "签收证明时申请原因必填", "签收证明时申请原因必填"),
    PORTAL_5043("5043", "新用户名不能与现在用户名相同", "新用户名不能与现在用户名相同,请重新输入新的用户名"),
    PORTAL_5044("5044", "业务类型异常", "业务类型参数有问题"),
    PORTAL_5045("5045", "当前以下运单修改信息失败", "当前以下运单修改信息失败,请重新进行处理"),
    PORTAL_5046("5046", "CRM工单服务不可用", "当前CRM工单服务不可用,请联系技术人员"),
    PORTAL_5047("5047", "未查询到相应数据", "未查询到相应数据"),
    PORTAL_5048("5048", "MES服务不可用", "MES服务不可用,请联系技术人员"),
    PORTAL_5049("5049", "预估重派费试算失败", "预估重派费试算失败,请联系技术人员"),
    PORTAL_5050("5050", "对不起,您没有权限", "初始化密码,当前操作无权限"),
    PORTAL_5051("5051", "身份证号不能为空", "身份证号不能为空,当前商户信息身份证为空"),
    PORTAL_5052("5052", "纳税人识别号不能为空", "纳税人识别号不能为空,当前纳税人识别号为空"),
    PORTAL_5053("5053", "选择纸质发票时地址不得为空", "创建发票申请选择纸质发票时地址不得为空"),
    PORTAL_5054("5054", "获取微信扫描二维码异常", "当前功能异常,请联系技术人员"),
    PORTAL_5055("5055", "微信二维码已过期", "微信扫码超时"),
    PORTAL_5056("5056", "操作超时,请重试", "微信绑定超时"),
    PORTAL_5057("5057", "制单账号不得为空", "制单账号不得为空,请选择制单账号"),
    PORTAL_5058("5058", "当前数据部分处理失败", "当前数据部分女理失败请重试"),
    PORTAL_5059("5059", "您尚未开通任何业务线，请开通成功后请再进行此操作", "您尚未开通任何业务线，请开通成功后请再进行此操作"),
    PORTAL_5060("5060", "无权限或不合法", "当前接口不合法,未经过授权"),
    PORTAL_5061("5061", "该文件格式不支持预览", "文件格式不支持"),
    PORTAL_5062("5062", "当前调度服务不可用", "当前调度服务不可用,请联系技术人员"),
    PORTAL_5063("5063", "操作失败", "当前操作失败,请联系技术人员"),
    PORTAL_5064("5064", "未查到围栏信息", "未查到围栏信息"),
    PORTAL_5065("5065", "打印标签失败", "打印标签失败"),
    PORTAL_5067("5067", "付款账号查询失败", "付款账号查询异常,请联系技术人员"),
    PORTAL_5068("5068", "推荐销售不存在，请核实后填写", "校验销售"),
    PORTAL_5069("5069", "根据办公地址未匹配到销售所对应城市", "根据办公地址未匹配到销售所对应城市"),
    PORTAL_5070("5070", "处理方式为重派时必须上传附件", "处理方式为重派时必须上传附件"),
    PORTAL_5071("5071", "当前账户名与商户名一致不允许上传委托书！", "当前账户名与商户名一致不允许上传委托书！"),
    PORTAL_5072("5072", "当前账号[为空/无状态]请重试!", "当前账号[为空/无状态]请重试!"),
    PORTAL_5073("5073", "重量预报模板模板下载异常!", "重量预报模板模板下载异常!"),
    PORTAL_5074("5074", "您没有账期可开!", "操作失败:未查询到该商户可申请开票的账期"),
    PORTAL_5075("5075", "处理方式不得为空!", "异常处理方式不得为空!"),
    PORTAL_5076("5076", "当前数据退件地址不全", "退件地址信息不全!"),
    PORTAL_5077("5077", "请您完善银行卡相关信息", "请您完善银行卡相关信息!"),
    PORTAL_5078("5078", "您尚未实名认证，请联系销售/客服操作", "变更客户名未进行实名认证!"),
    /**
     * 6*** 小包相关
     * 61** 快件截留相关
     * 62** 待领取运单相关
     */
    PORTAL_6101("6101", "查询数量超出限制", "减少查询数量至提示数量以下"),
    PORTAL_6102("6102", "快件截留参数异常", "检查是否有选择截留数据"),
    PORTAL_6103("6103", "小包业务账号获取异常", "小包业务账号获取异常"),
    PORTAL_6104("6104", "新单号不存在", "请检查新单号真实性"),
    PORTAL_6105("6105", "新单号必须与老单号是同一个制单账号", "请检查新单号"),
    PORTAL_6106("6106", "新单号不能和老单号一致", "请检查新单号，不允许和老单号相同"),
    PORTAL_6201("6201", "当前查询非法", "请检查制单账号是否为当前客户下"),
    PORTAL_6202("6202", "获取待领取运单数据错误！", "请联系追踪系统处理！"),
    PORTAL_6203("6203", "查询参数丢失！", "请联系Portal系统技术处理！"),
    PORTAL_6204("6204", "追踪接口调用异常！", "请联系追踪系统处理！"),
    PORTAL_6205("6205", "请选择运单号！", "请检查是否选择运单号！"),
    PORTAL_6206("6206", "未查到轨迹信息！", "请检查运单号是否正确！"),
    PORTAL_6207("6207", "请先开通小包业务", "未查询到小包业务信息"),
    PORTAL_6208("6208", "小包业务账号未开通", "业务账号为空"),
    PORTAL_6209("6209", "查询账务运单明细失败", "查询账务运单明细失败"),
    PORTAL_6210("6210", "未查询到需要签署计费服务确认书,请刷新页面", "未查询到待签署服务确认书数据"),
    PORTAL_6211("6211", "账户冻结状态，不支持操作，请联系客服解冻后再处理", "账户冻结状态，不支持回复，请联系客服解冻后再处理"),
    PORTAL_6212("6211", "您没有账期可开!", "您没有账期可开!"),
    PORTAL_6213("6213","查询小包信息异常","查询小包信息异常"),
    PORTAL_6214("6214","资料完整度不为100%,请先完善资料","资料完整度不足"),
    PORTAL_6215("6215","您的账号存在未清款项，请联系客服核实","客户存在坏账,需要联系客服处理"),
    PORTAL_6216("6216","此运单未查询到末端POD（签收证明）！","此运单未查询到末端POD（签收证明）！"),
    /**
     * <AUTHOR>
     * @description 认证相关
     * @date 2023/3/30 16:43
     **/
    PORTAL_7001("7001 ", "是否认证状态异常,请联系客服/销售", "认证状态authStatus为空"),
    PORTAL_7002("7002", "申请状态异常,请联系客服/销售", "申请类型applyType为空"),
    PORTAL_7003("7003", "客户企业类型异常,请联系客服/销售", "客户企业/个人类型customerType为空"),
    PORTAL_7004("7004", "审核状态异常,请联系客服/销售", "审核状态auditStatus为空"),
    PORTAL_7005("7005", "客户所属区域异常,请联系客服/销售", "客户所属区域customerArea为空"),
    PORTAL_7006("7006", "客户企业认证异常,请联系客服/销售", "企业认证authStatus为空"),
    PORTAL_7007("7007", "人工审核认证客户只能进行纸质签约,请联系客服/销售", "人工审核认证客户"),
    PORTAL_7008("7008", "未查询到客户信息", "未查询到客户信息"),
    PORTAL_7009("7009", "商户证件资料审核中，请联系对应销售/客服！", "商户证件资料审核中，请联系对应销售/客服！"),
    PORTAL_7010("7010", "请完成实名认证流程后再签署委托书（实名认证提交入口：用户管理-实名认证）", "applyType不是完成状态"),
    PORTAL_7101("7101", "账单下载失败", "账单下载失败"),
    /**
     * //
     *
     * @date 省市区
     */
    PORTAL_8001("8001", "未找到对应省", "未找到对应省,当前对应省未找到"),
    PORTAL_8002("8002", "未找到对应市", "未找到对应市,当前对应市未找到"),
    PORTAL_8003("8003", "未找到对应区", "未找到对应区,当前对应曲未找到"),
    /***
     * //  9610
     * <AUTHOR>
     * @date 2023/4/4 10:44

     */
    PORTAL_9001("9001", "中文品名不允许重复", "当前中文品名不允许重复,请重新填写"),
    PORTAL_9002("9002", "导入文件为空,请填写后重新上传", "导入文件为空,请填写后重新上传!"),
    PORTAL_4001("4001", "当前付款账号正在处理中，请勿重复提交，如有疑问请联系客服", "当前付款账号正在处理中，请勿重复提交，如有疑问请联系客服!"),
    PORTAL_4002("4002", "请完善已有流程中的支付宝账号，如有问题请联系客服协助", "请完善已有流程中的支付宝账号，如有问题请联系客服协助!"),
    PORTAL_4003("4003", "您已添加过该平台的账号，请勿重复添加", "您已添加过该平台的账号，请勿重复添加!"),
    PORTAL_4004("4004", "当前付款账号在我司已备案，请勿重复备案", "当前付款账号在我司已备案，请勿重复备案"),
    PORTAL_4005("4005", "备案失败", "当前付款账号在我司已备案，请勿重复备案"),
    PORTAL_4006("4006", "银行卡信息有误,请检查修改后重新提交", "银行卡信息比对失败"),
    PORTAL_4007("4007", "身份信息有误,请检查修改后重新提交", "个人信息比对信息失败"),
    PORTAL_4008("4008", "企业信息有误,请检查修改后重新提交", "企业工商信息校验失败"),
    PORTAL_4009("4009", "未查询到银行名称,请联系销售/客服", "签署合同银行卡备案"),
    PORTAL_4010("4010", "银行卡备案失败,请联系销售/客服", "签署合同个人银行卡备案失败"),
    PORTAL_4011("4011", "未查询到企业认证银行卡信息,请联系销售/客服", "签署合同企业银行卡备案"),
    PORTAL_4012("4012", "企业银行卡备案失败,请联系销售/客服", "签署合同企业银行卡备案失败"),
    PORTAL_4013("4013", "未完成实名认证,请联系销售/客服", "签署合同企业银行卡备案失败"),
    PORTAL_4014("4014", "打印敦煌标签失败", "打印敦煌标签失败"),

    /***
     * 运价试算
     * <AUTHOR>
     * @date 2023/5/18 10:44
     */
    PORTAL_9201("9201", "您当前所发产品超出了重量范围，请更换产品重新制单", "您当前所发产品超出了重量范围，请更换产品重新制单"),
    PORTAL_9202("9202", "您当前所填写信息不支持发运，请确认实重重量/体积重量/尺寸是否在该产品的发运条件内，如有疑问请联系客服/销售！", "您当前所填写信息不支持发运，请确认实重重量/体积重量/尺寸是否在该产品的发运条件内，如有疑问请联系客服/销售！"),
    PORTAL_9203("9203", "重量不能为空", "重量不能为空"),
    PORTAL_9204("9204", "长不能为空", "长不能为空"),
    PORTAL_9205("9205", "宽不能为空", "宽不能为空"),
    PORTAL_9206("9206", "高不能为空", "高不能为空"),
    PORTAL_9207("9207", "地址类型不能为空", "地址类型不能为空"),
    PORTAL_9208("9208", "邮编不能为空", "邮编不能为空"),
    PORTAL_9209("9209", "交税模式不能为空", "交税模式不能为空"),
    PORTAL_9210("9210", "至少输入一条箱单信息或箱数不能为空", "至少输入一条箱单信息或箱数不能为空"),
    PORTAL_9211("9211", "商户号不能为空", "商户号不能为空"),
    PORTAL_9212("9212", "您没有可供运价试算的制单账号", "您没有可供运价试算的制单账号"),
    PORTAL_9213("9213", "提交报价单申请网络异常", "提交报价单申请网络异常"),

    /***
     * FBA
     * <AUTHOR>
     * @date 2023/5/18 10:44
     */
    PORTAL_9101("9101", "FBA状态错误", "FBA状态错误"),
    PORTAL_9102("9102", "获取fba业务账号异常", "获取fba业务账号异常"),
    PORTAL_9103("9103", "获取fba详细信息异常", "获取fba详细信息异常"),
    PORTAL_9104("9104", "获取fba令牌异常", "获取fba令牌异常"),


    /***
     * 注册
     * <AUTHOR>
     * @date 2023/5/18 10:44
     */
    PORTAL_9301("9301", "当前注册链接失效，请填写销售人员手机号", "URL链接target参数解密失败"),
    /***
     * EJF
     * <AUTHOR>
     * @date 2023/9/4 10:44
     */
    PORTAL_9401("9401", "EJF身份验证失败", "EJF的身份验证和ApiToken不匹配"),
    PORTAL_9402("9402", "无运单数据", "时效报表无运单数据"),
    PORTAL_9403("9403", "{}月份数据不存在，请跳过该月份", "时效报表表数据不存在"),
    PORTAL_9404("9404", "查询时间范围不能超过31天", "查询时间范围不能超过31天"),


    /***
     * 商业快递
     *
     * <AUTHOR>
     * @date 2023/5/18 10:44
     */
    PORTAL_9501("9501", StrUtil.format("status字段状态值错误"), "商业快递查询状态字段错误，无匹配值"),
    PORTAL_9502("9502", StrUtil.format("打印标签失败"), "打印标签失败"),
    PORTAL_9503("9503", StrUtil.format("模板无法识别"), "模板无法识别"),

    /**
     * @author: dinghy
     * @createTime: 2025/4/14 14:44
     * @description: 海外派
     */
    PORTAL_9601("9601", "海外派crm服务不可用", "海外派crm服务不可用"),
    PORTAL_9602("9602", "校验地址服务不可用", "海外派crm服务不可用"),
    PORTAL_9603("9604", "退件地址校验失败", "海外派crm服务不可用"),
    ;
    /**
     * 错误码
     */
    private final String code;
    /**
     * 提示语
     */
    private final String message;
    /**
     * 错误描述
     */
    private final String description;
    /**
     * 固定异常
     */
    private final ResponseException error;

    ResponseCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
        this.error = new ResponseException(this);
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }

    public ResponseException getError() {
        return error;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ResponseException extends RuntimeException {
        /**
         * 异常
         */
        ResponseCode responseCode;

        /**
         * 构造
         *
         * @param responseCode 响应码
         */
        public ResponseException(ResponseCode responseCode) {
            super(responseCode.getMessage());
            this.responseCode = responseCode;
        }
    }

}
