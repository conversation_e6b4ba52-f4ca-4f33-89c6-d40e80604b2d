package com.ywwl.customer.center.common.utils;

import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Supplier;

import static com.ywwl.customer.center.common.enums.CacheKeyEnum.IDEMPOTENT_ASPECT;

/**
 * redis
 *
 * <AUTHOR>
 * @date 2023/03/23 15:23
 **/
public class RedisTemplate extends org.springframework.data.redis.core.RedisTemplate<String, Object> {

	/**
	 * 各种类型的自定义
	 *
	 * @param <V> Value type
	 * @return 各种类型的自定义操作
	 */
	public <V> ValueOperations<String, V> opsForValueType() {
		return (ValueOperations<String, V>) super.opsForValue();
	}

	public <V> ListOperations<String, V> opsForListType() {
		return (ListOperations<String, V>) super.opsForList();
	}

	public <V> SetOperations<String, V> opsForSetType() {
		return (SetOperations<String, V>) super.opsForSet();
	}

	public <V> ZSetOperations<String, V> opsForZSetType() {
		return (ZSetOperations<String, V>) super.opsForZSet();
	}

	/**
	 * 设置KeyValue
	 * @param module	模块
	 * @param key		Key
	 * @param value		Value
	 * @param timeout	超时时间
	 * @return	是否设置成功
	 */
	public Boolean setIfAbsent(CacheKeyEnum module, String key, String value,Duration timeout) {
		// 获取Key
		final String sourceKey = StringUtils.join(module.key(), ":", key);
		return super.opsForValue().setIfAbsent(sourceKey, value, timeout);
	}

	/**
	 * 设置KeyValue
	 * @param key		Key
	 * @param value		Value
	 * @param timeout	超时时间
	 * @return	是否设置成功
	 */
	public Boolean setIfAbsent(String key, String value,Duration timeout) {
		return setIfAbsent(IDEMPOTENT_ASPECT, key, value, timeout);
	}

	/**
	 * 设置Key
	 * @param key		Key
	 * @param timeout	超时时间
	 * @return	是否设置成功
	 */
	public Boolean setIfAbsent(String key ,Duration timeout) {
		return setIfAbsent(IDEMPOTENT_ASPECT, key, "1", timeout);
	}

	/**
	 * 是否存在
	 * @param module	模块
	 * @param key		Key
	 * @return			是否存在
	 */
	public Boolean hasKey(CacheKeyEnum module ,String key) {
		// 获取Key
		final String sourceKey = StringUtils.join(module.key(), ":", key);
		return hasKey(sourceKey);
	}

	/**
	 * 获取数据
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValue(CacheKeyEnum module, String key) {
		return getValue(module, key, null);
	}

	/**
	 * 获取数据
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValue(CacheKeyEnum module, String key, T defaultValue) {
		// 获取Key
		final String sourceKey = StringUtils.join(module.key(), ":", key);
		if (Boolean.TRUE.equals(hasKey(sourceKey))) {
			return (T) opsForValue().get(sourceKey);
		}
		return defaultValue;
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue) {
		return getValueAndCache(module, key, defaultValue, true);
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param resultHandle	结果处理方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue, boolean priorityCache, Consumer<T> resultHandle) {
		// 缓存优先
		if (priorityCache) {
			final T value = getValue(module, key);
			if (Objects.nonNull(value)) {
				return value;
			}
			T result = defaultValue.get();
			if (Objects.nonNull(resultHandle)) {
				resultHandle.accept(result);
			}
			return result;
		}
		// 值优先
		try {
			T result = defaultValue.get();
			if (Objects.nonNull(resultHandle)) {
				// 更新
				resultHandle.accept(result);
			}
			return result;
		} catch (Throwable e) {
			final T value = getValue(module, key);
			if (Objects.nonNull(value)) {
				return value;
			}
			// 从缓存也无法获取到
			throw e;
		}
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue, boolean priorityCache) {
		return getValueAndCache(module, key, defaultValue, priorityCache, module.getTimeout());
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue, boolean priorityCache, long timeout) {
		return getValueAndCache(module, key, defaultValue, priorityCache, Duration.ofSeconds(timeout));
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param priorityCache	是否缓存优先
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, Supplier<? extends T> defaultValue, boolean priorityCache, Duration duration) {
		return getValueAndCache(module, key, defaultValue, priorityCache, v -> setValue(module, key, v, duration));
	}

	/**
	 * 获取数据并对数据进行操作
	 * @param module		缓存模块
	 * @param key			缓存Key
	 * @param defaultValue	值获取方法
	 * @param <T>			结果类型
	 * @return				结果
	 */
	public <T> T getValueAndCache(CacheKeyEnum module, String key, T defaultValue) {
		return getValueAndCache(module, key, () -> defaultValue);
	}


	/**
	 * 设置参数
	 *
	 * @param module	缓存模块
	 * @param key   	缓存Key
	 * @param value 	缓存Value
	 * @param <T>   	缓存Value类型
	 * @return Value
	 */
	public <T> T setValue(CacheKeyEnum module, String key, T value) {
		return setValue(module, key, value, module.getTimeout());
	}

	/**
	 * 设置参数
	 *
	 * @param module	缓存模块
	 * @param key   	缓存Key
	 * @param value 	缓存Value
	 * @param timeout 	超时时间
	 * @param <T>   	缓存Value类型
	 * @return Value
	 */
	public <T> T setValue(CacheKeyEnum module, String key, T value, long timeout) {
		return setValue(module, key, value, Duration.ofSeconds(timeout));
	}

	/**
	 * 设置参数
	 *
	 * @param module	缓存模块
	 * @param key   	缓存Key
	 * @param value 	缓存Value
	 * @param duration 	超时时间
	 * @param <T>   	缓存Value类型
	 * @return Value
	 */
	public <T> T setValue(CacheKeyEnum module, String key, T value, Duration duration) {
		final String sourceKey = StringUtils.join(module.key(), ":", key);
		if (Objects.nonNull(duration)) {
			opsForValue().set(sourceKey, value, duration);
		}else {
			opsForValue().set(sourceKey, value);
		}
		return value;
	}


	/**
	 * 删除对象
	 *
	 * @param module 模块
	 * @param key    key
	 * @return 是否成功
	 */
	public Boolean delete(CacheKeyEnum module, String key) {
		final String sourceKey = StringUtils.join(module.key(), ":", key);
		return super.delete(sourceKey);
	}

	/**
	 * 删除对象
	 *
	 * @param module 模块
	 * @return 是否成功
	 */
	public Boolean delete(CacheKeyEnum module) {
		final Set<String> keys = keys(module.key() + "*");
		if (!CollectionUtils.isEmpty(keys)) {
			super.delete(keys);
		}
		return true;
	}

}
