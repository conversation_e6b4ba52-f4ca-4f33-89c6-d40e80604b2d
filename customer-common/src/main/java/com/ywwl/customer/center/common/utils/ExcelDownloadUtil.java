package com.ywwl.customer.center.common.utils;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public class ExcelDownloadUtil {
    /**
     * 生成带下拉框的Excel并直接下载
     * @param response      HttpServletResponse
     * @param dropdownOptions 下拉框选项（按单元格顺序，例如：第1个下拉框对应索引0）
     */
    public static void downloadWithDropdowns(
            HttpServletResponse response,
            InputStream is,
            List<String[]> dropdownOptions) throws Exception {
            // 1. 从classpath读取模板
            // 2. 使用POI处理模板，添加下拉框
            Workbook workbook = WorkbookFactory.create(is);
            Sheet sheet = workbook.getSheetAt(0);


            // 示例：假设在下拉框需要设置在A1和B1单元格

            String[] channelOptions = dropdownOptions.get(0);
            // 在D1，设置产品
            addDropdown(sheet, 0, 3, channelOptions);
            // 3. 将处理后的模板暂存到临时文件（或内存）
            // 注意：此处可将workbook直接写入HttpServletResponse输出流，但需要解决EasyExcel与POI的混合写问题
            // 简化方案：先保存带下拉框的模板，再用EasyExcel填充
            try (OutputStream os = response.getOutputStream()) {
                workbook.write(os); // 临时写入（实际需优化，见下方说明）
            }


        // 4. 使用EasyExcel填充数据（需优化步骤3的代码，以下为逻辑示意）
        // 实际应用中，需确保下拉框和数据写入不冲突，可能需要分开处理
        // 例如：先用POI添加下拉框生成中间模板，再用EasyExcel填充数据到中间模板
    }


    private static void addDropdown(Sheet sheet, int rowIndex, int columnIndex, String[] options) {
        DataValidationHelper validationHelper = new XSSFDataValidationHelper((XSSFSheet) sheet);
        DataValidationConstraint constraint = validationHelper.createExplicitListConstraint(options);
        CellRangeAddressList addressList = new CellRangeAddressList(rowIndex, rowIndex, columnIndex, columnIndex);
        DataValidation validation = validationHelper.createValidation(constraint, addressList);
        validation.setShowErrorBox(true);
        sheet.addValidationData(validation);
    }
}
