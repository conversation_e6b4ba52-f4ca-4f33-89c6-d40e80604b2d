// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 15:08
 * @ModifyDate 2023/3/14 15:08
 * @Version 1.0
 */
public enum AccountTypeEnum{

    COMMON(0, "直发业务"),
    FBA(1, "FBA专线"),
    OVERSEA(2, "海外仓"),
    CHINA_WAREHOUSE(3, "中国仓业务");

    private Integer type;
    private String desc;


    AccountTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
