package com.ywwl.customer.center.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2018/7/24
 */
@Configuration
@ConfigurationProperties(prefix = "attachment-file-cloud")
@Data
public class AttachmentFileCloudConfig {
    private String uploadUrl;
    private String downUrl;
    private String token;
    /**
     * 与ext约定值
     */
    private String tenantId;
}