package com.ywwl.customer.center.common.domain;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 发货账号
 * </p>
 *
 * <AUTHOR>
 * @since 2018-06-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Shipper implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 提交申请时与商户绑定使用
     */
    private Long merchantId;

    private Long userId;
    /**
     * 客户号(旧系统)
     */
    private String customerCode;
    /**
     * 商户号
     */
    private String merchantCode;
    /**
     * 揽收仓
     */
    private String pickWarehouseId;

    private String pickWarehouseName;
    /**
     * 收件方式
     * 0: 上门取件 1: 客户自寄
     */
    private Integer shipType;
    /**
     * 揽件城市
     */
    private Integer pickCity;
    /**
     * 揽件地址省
     */
    private String receiveProvince;
    /**
     * 揽件地址市
     */
    private String receiveCity;
    /**
     * 揽件地址区
     */
    private String receiveArea;
    /**
     * 揽件地址
     */
    private String receiveAddress;
    /**
     * 是否使用取件地址作为退件地址
     */
    private Boolean pickAddressAsReturnAddress;
    /**
     * 自送网点
     */
    private Integer serviceStation;
    /**
     * 退件地址省
     */
    private String returnProvince;
    /**
     * 退件地址市
     */
    private String returnCity;
    /**
     * 退件地址区
     */
    private String returnArea;
    /**
     * 退货地址
     */
    private String returnAddress;
    /**
     * 销售员工id
     */
    private String salesManagerId;
    /**
     * 销售员工生效时间
     */
    private LocalDateTime salesManagerEffectTime;
    /**
     * 流程状态
     * 0:待审核 1:审核未通过 2:创建成功
     */
    private Integer processStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime modifyTime;

    /**
     * 0-不活动  1-活动 2 -冻结
     * SLEEP(0, "不活动"), ACTIVE(1, "活动"), FROZEN(2, "冻结");
     */
    private String ejfStatus;

    private String uploadTime;

    /**
     * zhg-2020-11/17
     * 退件方式
     * 【 0 司机带回】 【1 客户自提】 【2 快递退回 】
     */
    private Integer returnType;

    private Integer accountType;

    /**
     * 发货人到货地址
     *
     * @date 2022/3/14 15:35
     */
    private String contactAddress;
    /**
     * 到货仓
     *
     * @date 2022/3/14 15:35
     */
    private String arrivalWarehouse;
    /**
     * 发货人手机号
     *
     * @date 2022/3/21 9:31
     */
    private String contactPhone;
    /**
     * 联系人姓名
     *
     * @date 2022/3/21 9:32
     */
    private String contactName;
    /**
     * 联系人邮箱
     *
     * @date 2022/3/30 14:48
     */
    private String contactEmail;
    /**
     * 联系人qq
     *
     * @date 2022/3/30 14:49
     */
    private String contactQq;

    private String businessName;

    private String businessPhone;

    private String businessEmail;

    /**
     * 是否直发
     */
    public boolean whetherToShipDirectly() {
        return 0 == getAccountType().intValue();
    }

}
