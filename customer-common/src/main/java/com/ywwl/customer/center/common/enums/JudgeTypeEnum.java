// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/22 16:26
 * @ModifyDate 2023/3/22 16:26
 * @Version 1.0
 */
public enum JudgeTypeEnum {
    FAKE(0,"假"),
    REAL(1,"真");


    private Integer type;
    private String desc;

    JudgeTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }


    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
