package com.ywwl.customer.center.common.utils;


import java.util.HashMap;
import java.util.Map;

/**
* //
*<AUTHOR>
*@date 2023/4/19
*/
public class AreaShipperUtils {

    static Map wareArea;

    static {
        wareArea = new HashMap();
        wareArea.put("01", "0");
        wareArea.put("15", "0");
        wareArea.put("18", "0");
        wareArea.put("36", "0");
        wareArea.put("24", "0");
        wareArea.put("37", "0");
        wareArea.put("19", "0");
        wareArea.put("20", "0");

        wareArea.put("14", "1");
        wareArea.put("09", "1");
        wareArea.put("02", "1");
        wareArea.put("12", "1");
        wareArea.put("23", "1");
        wareArea.put("25", "1");
        wareArea.put("05", "1");
        wareArea.put("26", "1");
        wareArea.put("06", "1");
        wareArea.put("07", "1");

        wareArea.put("03", "2");
        wareArea.put("10", "2");
        wareArea.put("04", "2");
        wareArea.put("38", "2");
        wareArea.put("17", "2");
        wareArea.put("29", "2");
        wareArea.put("28", "2");
        wareArea.put("45", "2");
        wareArea.put("21", "2");
        wareArea.put("30", "2");
        wareArea.put("27", "2");
        wareArea.put("16", "2");

        wareArea.put("08", "7");
    }

    public static Map<String, String> selectWareArea() {
        return wareArea;
    }
}
