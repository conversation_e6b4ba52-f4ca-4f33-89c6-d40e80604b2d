package com.ywwl.customer.center.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/4/4 9:17
 */
@Slf4j
public class StringHandleUtils {
    /**
     * 去除空格
     *
     * @param obj 去掉前后空格
     * @return
     */
    public static void stripTrim(Object obj) {
        try {
            String name = String.class.getName();
            if (Objects.nonNull(obj)) {
                Class<?> aClass = obj.getClass();
                Field[] fields = aClass.getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    Class<?> type = field.getType();
                    int modifiers = field.getModifiers();
                    if (Modifier.isFinal(modifiers) || Modifier.isStatic(modifiers)) {
                        continue;
                    }
                    if (name.equals(type.getName())) {
                        // 拿到原值
                        Object targetProperty = field.get(obj);
                        if (Objects.nonNull(targetProperty)) {
                            String value = (String) targetProperty;
                            // 目标值去空格
                            String trim = StringUtils.trim(value);
                            field.set(obj, trim);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("去除空格异常,原因:{}", e);
        }
    }



}
