// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.common.enums;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/22 16:26
 * @ModifyDate 2023/3/22 16:26
 * @Version 1.0
 */
public enum SceneTypeEnum {
    INSERT_ORDER(1,"制单"),
    QUERY_ORDER(2,"查单");


    private Integer type;
    private String desc;

    SceneTypeEnum(Integer type, String desc){
        this.type = type;
        this.desc = desc;
    }


    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
