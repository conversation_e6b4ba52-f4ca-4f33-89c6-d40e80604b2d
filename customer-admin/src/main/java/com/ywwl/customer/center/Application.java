package com.ywwl.customer.center;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Indexed;

/**
 * 主启动类
 *
 * <AUTHOR>
 * @date 2023/02/16 16:48
 **/
@MapperScan(basePackages = {"com.ywwl.customer.center.modules.international.dao"
							,"com.ywwl.customer.center.modules.upload.mapper"
							,"com.ywwl.customer.center.modules.general.plm.mapper"
							,"com.ywwl.customer.center.modules.common.provider.dao"
							,"com.ywwl.customer.center.modules.common.account.mapper"
							,"com.ywwl.customer.center.modules.timebase.mapper"
							,"com.ywwl.customer.center.framework.log.dao"})
@SpringBootApplication
@EnableAsync
@Indexed
@EnableScheduling
public class Application {

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

}
