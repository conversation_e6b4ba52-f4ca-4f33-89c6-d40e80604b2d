package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.dto.*;
import com.ywwl.customer.center.modules.ejf.entity.Draft;
import com.ywwl.customer.center.modules.ejf.entity.Express;
import com.ywwl.customer.center.modules.ejf.entity.ProductDetails;
import com.ywwl.customer.center.modules.ejf.entity.SenderInfo;
import com.ywwl.customer.center.modules.ejf.enums.SearchStatus;
import com.ywwl.customer.center.modules.ejf.enums.WaybillStatusEnum;
import com.ywwl.customer.center.modules.ejf.service.DraftService;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.ejf.service.ProcessResult;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.ejf.util.ExcelImportUtil;
import com.ywwl.customer.center.modules.ejf.vo.DraftSimpleListVO;
import com.ywwl.customer.center.modules.ejf.vo.OrderSimpleListVO;
import com.ywwl.customer.center.modules.ejf.vo.OrderVO;
import com.ywwl.customer.center.modules.ejf.vo.ParcelInfoVO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.ywwl.customer.center.api.ejf.OrderController.FILE_NAME_EXCEL;

/**
 * 通用控制器
 *
 * <AUTHOR>
 * @date 2022/08/15 10:15
 **/
@RequestMapping("/ejf/current")
@Validated
@RestController
public class CurrentController extends BaseController implements ProcessResult {

    @Resource
    private OrderService orderService;
    @Resource
    private DraftService draftService;
    @Resource
    private AccountService accountService;
    @Resource
    private CrmService crmService;

    @RequestMapping("/printInfo")
    @Logger(module = Module.EJF, name = "导出运单")
    public void printInfo(@RequestBody MedleyDTO key, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) throws IOException {
        // 验证权限
        accountService.existAccountThrow(key.getUserId());
        // 设置下载HTTP头
        EJFUtil.setDownloadResponse(response,
                String.format(FILE_NAME_EXCEL,
                        key.getUserId(),
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))));
        key.setPortalUserId(currentUser.getUserId());
        List<OrderVO> orders = new LinkedList<>();
        // 获取运单数据
        if (CollectionUtil.isNotEmpty(key.getWaybillNumbers())) {
            // 设置换单号
            final WaybillKeysDTO waybillKeys = key.getWaybillKeys();
            waybillKeys.setNeedExchange(false);
            List<OrderVO> orderList = orderService.getOrderVOById(waybillKeys);
            final List<String> ways = orderList.stream()
                    .map(OrderVO::getWaybillNumber)
                    .collect(Collectors.toList());
            final List<AbnormalExchangeDTO.DataDTO> exchange = orderService.getAbnormalExchange(ways);
            if (CollectionUtil.isNotEmpty(orderList) && CollectionUtil.isNotEmpty(exchange)) {
                orderList.forEach(x -> {
                    // 设置换运单号
                    final String waybillNumber = x.getWaybillNumber();
                    exchange.forEach(ex -> {
                        if (Objects.equals(waybillNumber, ex.getNewWaybillNumber()) || Objects.equals(waybillNumber, ex.getOldWaybillNumber())) {
                            x.setOldExpressCode(ex.getOldWaybillNumber());
                            x.setNewExpressCode(ex.getNewWaybillNumber());
                        }
                    });
                });
            }
            orders.addAll(orderList);
        }

        // 获取订单数据
        if (CollectionUtil.isNotEmpty(key.getIds())) {
            List<OrderKey> orderKeyList = key.getOrderKeys().getOrderKeyList();
            List<OrderVO> orderList = orderKeyList.parallelStream()
                    .map(k -> draftService.getOrder(k))
                    .map(EJFUtil::getOrderVO)
                    .collect(Collectors.toList());
            Collections.reverse(orderList);
            orders.addAll(orderList);
        }
        // 进行数据输出
        if (CollectionUtil.isNotEmpty(orders)) {
            for (OrderVO orderVO : orders) {
                final SenderInfo senderInfo = orderVO.getSenderInfo();
                // 进行脱敏
                if (Objects.nonNull(senderInfo) && StringUtils.isNotBlank(senderInfo.getTaxNumber())) {
                    senderInfo.setTaxNumber(PrivacyDimmer.maskTaxCode(senderInfo.getTaxNumber()));
                }
                final ParcelInfoVO parcelInfo = orderVO.getParcelInfo();
                // 进行脱敏
                if (Objects.nonNull(parcelInfo) && StringUtils.isNotBlank(parcelInfo.getIoss())) {
                    parcelInfo.setIoss(PrivacyDimmer.maskTaxCode(parcelInfo.getIoss()));
                }
                if (Objects.equals(orderVO.getStatus(), WaybillStatusEnum.INTERCEPTED.getCode())) {
                    // 设置截留运单信息
                    orderService.interceptionInformation(orderVO.getWaybillNumber(), null, null, orderVO::setNewExpressCode);
                    orderVO.setOldExpressCode(orderVO.getWaybillNumber());
                }
            }
            ExcelImportUtil.writeExcel(orders, response.getOutputStream());
        } else {
            throw BusinessException.msg("您未选择运单或无法获取到运单信息");
        }
    }

    /**
     * 订单是否可修改
     *
     * @param param 参数
     * @return 基础信息    产品详情
     */
    @RequestMapping("/isEdit")
    @Valid
    @Logger(module = Module.EJF, name = "订单是否可修改")
    public JsonResult<?> isEdit(@RequestBody ProductDetailsDTO param) {
        ProductDetails.DataDTO productDetails = orderService.productDetails(param.getProductNumber());
        if (orderService.isEdit(productDetails)) {
            return JsonResult.success("该产品的订单允许修改", null);
        }
        return JsonResult.error("该产品的订单不允许修改", null);
    }

    /**
     * 订单是否支持保险
     *
     * @param param 参数
     * @return 基础信息    产品详情
     */
    @RequestMapping("/isInsurance")
    @Valid
    @Logger(module = Module.EJF, name = "订单是否支持保险")
    public JsonResult<?> isInsurance(@RequestBody ProductDetailsDTO param) {
        ProductDetails.DataDTO productDetails = orderService.productDetails(param.getProductNumber());
        if (orderService.isInsurance(productDetails)) {
            return JsonResult.success("该产品的订单支持保险", null);
        }
        return JsonResult.error("该产品的订单不支持保险", null);
    }


    /**
     * 获取运单状态统计
     *
     * @param currentUser 当前用户
     * @param month       最近多少越
     * @return 运单状态数量
     */
    @RequestMapping("/getExpresses")
    @Logger(module = Module.EJF, name = "获取小包首页数量统计")
    public JsonResult<?> getExpresses(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam @NotNull(message = "month不能为空") Integer month) {
        // 获取所有制单账号
        final List<AccountGetResVO> shippingAccount = orderService.getShippingAccount(currentUser.getUserCode(), 2);
        // 构建制单账号请求对象
        final Map<String, Integer> expressesSimple = new HashMap<>();
        for (AccountGetResVO accountGetResVO : shippingAccount) {
            String code = accountGetResVO.getAccountCode();
            DraftPageDTO p = DraftPageDTO.builder()
                    .userId(code)
                    // 开始日期往前30天
                    .startTime(StrUtil.format("{} 00:00:00", DateUtil.offsetMonth(DateUtil.date(), -month).toDateStr()))
                    // 结束日期为当天
                    .endTime(StrUtil.format("{} 23:59:59", DateUtil.today()))
                    .build();
            Collection<Express> expresses = orderService.getExpresses(p, false, true, false);
            for (Express express : expresses) {
                expressesSimple.merge(express.getStatus(), express.getQuantity(), Integer::sum);
            }
        }
        return JsonResult.success(expressesSimple);
    }


    /**
     * 获取所有订单
     *
     * @param pageParam 页面参数
     * @return 基础信息 返回结果集
     */
    @RequestMapping("/getOrder")
    @Valid
    @Logger(module = Module.EJF, name = "查询运单和订单")
    public JsonResult<?> getOrder(@RequestBody DraftPageDTO pageParam) {
        // 验证权限
        accountService.existAccountThrow(pageParam.getUserId());
        if (StringUtils.isBlank(pageParam.getStartTime())) {
            pageParam.setStartTime("2018-01-01 00:00:00");
        }
        if (StringUtils.isBlank(pageParam.getEndTime())) {
            pageParam.setEndTime(DateUtil.today() + " 23:59:59");
        }
        SearchStatus status = SearchStatus.getSearchStatus(pageParam.getStatus());
        final Collection<Express> expresses = orderService.getExpresses(pageParam);
        switch (status) {
            case ALL_DRAFT:
            case UNDOCUMENTED:
            case ORDER_FAILED:
            case BILLED_DRAFT:
                // 设置需要查询的状态
                pageParam.setListStatus(status.getOrderEnumKey());
                // 调用接口查询
                DraftSimpleListVO draftOrder = getDraftOrder(pageParam);
                draftOrder.setExpresses(expresses);
                return JsonResult.success(draftOrder);
            case ALL_ORDER:
            case BILLED:
            case SHIPMENT_CONFIRMED:
            case RECEIVED:
            case SHIPPED:
            case HAS_BEEN_PLACED:
            case CANCELLED:
            case RETAINED:
            case RETURN_EXCEPTION:
            case END_OF_TRACE:
            case OLD_EJF:
                // 设置需要查询的状态
                pageParam.setListStatus(status.getWaybillEnumKey());
                // 调用接口查询
                OrderSimpleListVO officialOrder = getOfficialOrder(pageParam);
                officialOrder.setExpresses(expresses);
                return JsonResult.success(officialOrder);
        }
        return JsonResult.error("status状态错误，" + Arrays.toString(SearchStatus.values()));
    }

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /**
     * 订单生成运单号
     *
     * @param pageParam 条件参数
     * @return 基础信息
     */
    @RequestMapping("/generateExpressCodeAll")
    @Valid
    @Logger(module = Module.EJF, name = "订单生成运单号(所有)")
    public JsonResult<?> generateExpressCodeAll(@RequestBody DraftPageDTO pageParam) {
        final UserAgent user = getUser();
        // 验证权限
        accountService.existAccountThrow(pageParam.getUserId());
        // 获取商户信息
        final PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(getUserCode());
        final Integer ejfStatus = packetApplyInfo.getEjfStatus();
        if (!Objects.equals(ejfStatus, 1)) {
            return JsonResult.error("制单账号处于金额冻结状态，请充值运费活动账号再制单，如有疑问请联系销售！");
        }
        // 验证商户状态
        if (!crmService.checkMerchantStatus(user.getMerchantNo(), user.getUserCode())) {
            return JsonResult.error("您的账号存在未清款项，请联系客服核实！");
        }
        SearchStatus status = SearchStatus.getSearchStatus(pageParam.getStatus());
        switch (status) {
            case UNDOCUMENTED:
            case ORDER_FAILED:
            case ALL_DRAFT:
                // 设置为最大页码
                pageParam.setPageNum(1);
                pageParam.setPageSize(Integer.MAX_VALUE);
                pageParam.setStatus(null);
                pageParam.setListStatus(status.getOrderEnumKey());
                DraftSimpleListVO draftSimpleListVO = getDraftOrder(pageParam);
                List<String> ids = draftSimpleListVO.getRecords().stream().map(Draft::getId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(ids)) {
                    return JsonResult.error("订单列表为空");
                }
                OrderKeysDTO key = new OrderKeysDTO();
                key.setUserId(pageParam.getUserId());
                key.setIds(ids);
                final String token = draftService.getToken(key.getUserId());
                draftService.generateExpressCode(key, token);
                int time = (int) Math.ceil(ids.size() / 1000d * 90);
                return JsonResult.success("制单任务已提交，总订单数量 " + ids.size() + " 条，预计处理时间 " + time + " 秒，请稍后查询", null);
            case BILLED_DRAFT:
            case ALL_ORDER:
            case BILLED:
            case SHIPMENT_CONFIRMED:
            case RECEIVED:
            case SHIPPED:
            case HAS_BEEN_PLACED:
            case CANCELLED:
            case RETAINED:
            case RETURN_EXCEPTION:
            case OLD_EJF:
        }
        return JsonResult.error("仅支持[未制单]/[制单失败]状态的订单进行该操作");
    }

    private DraftSimpleListVO getDraftOrder(@RequestBody DraftPageDTO pageParam) {
        return draftService.listOrder(pageParam);
    }

    private OrderSimpleListVO getOfficialOrder(@RequestBody DraftPageDTO pageParam) {
        return orderService.getOrderListVO(pageParam);
    }


    /**
     * 检测是否已存在订单
     *
     * @param pageParam 订单转运单参数
     */
    @PostMapping("/whetherTheOrderNumberExistsList")
    public JsonResult<?> whetherTheOrderNumberExistsList(@RequestBody DraftPageDTO pageParam) throws IOException {
        // 设置为最大页码
        pageParam.setPageNum(1);
        pageParam.setPageSize(Integer.MAX_VALUE);
        SearchStatus status = SearchStatus.getSearchStatus(pageParam.getStatus());
        pageParam.setStatus(null);
        pageParam.setListStatus(status.getOrderEnumKey());
        DraftSimpleListVO draftSimpleListVO = getDraftOrder(pageParam);
        List<String> ids = draftSimpleListVO.getRecords().stream().map(Draft::getOrderNumber).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ids)) {
            return JsonResult.error("订单为空", null, "501");
        }
        return whetherTheOrderNumberExists(WhetherTheOrderNumberExistsParam.builder().orderNumber(ids).userId(pageParam.getUserId()).build());
    }

    /**
     * 检测是否已存在订单
     *
     * @param param 参数
     */
    @PostMapping("/whetherTheOrderNumberExists")
    public JsonResult<?> whetherTheOrderNumberExists(@RequestBody WhetherTheOrderNumberExistsParam param) throws IOException {
        // 请求是否有重复订单
        OrderNumberDetectionResponseDTO.DataDTO dataDTO = orderService.whetherTheOrderNumberExists(param.getOrderNumber(), param.getUserId());
        if (Objects.isNull(dataDTO) || CollectionUtils.isEmpty(dataDTO.getListOrderNumber())) {
            return JsonResult.success();
        }
        int size = dataDTO.getListOrderNumber().size();
        if (size <= 10) {
            // 如果是10个文件，则不生成文件
            return JsonResult.error(
                    StrUtil.format("当前有 {} 个订单号近90天重复制单，订单号为 {}，请确认是否继续制单！", size, dataDTO.getListOrderNumber()),
                    ImmutableMap.of("file", false));
        } else {
            // 已存在订单号ExcelData
            final List<List<String>> data = dataDTO.getListOrderNumber()
                    .stream()
                    .map(Collections::singletonList)
                    .collect(Collectors.toList());
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                // 构建Base64
                EasyExcel.write(outputStream)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("已存在订单号")
                        .head(Collections.singletonList(Collections.singletonList("已存在订单号")))
                        .doWrite(data);
                String base64 = EJFUtil.byteToString(outputStream);
                // 构建结果集
                ImmutableMap<String, ? extends Serializable> response = ImmutableMap.of(
                        "base64", base64,
                        "fileName", StrUtil.format("导入订单_已存在订单号_{}.xlsx", DateUtil.today()),
                        "file", true);
                return JsonResult.error(StrUtil.format("当前有 {} 个订单号近90天重复制单，请确认是否继续制单！", size)
                        , response);
            }
        }
    }


    /**
     * 获取所有订单
     *
     * @param pageParam 页面参数
     * @return 基础信息 返回结果集
     */
    @RequestMapping("/getCompleteOrder")
    @Valid
    @Logger(module = Module.EJF, name = "多状态查询运单和订单")
    public JsonResult<?> getCompleteOrder(@RequestBody DraftPageDTO pageParam) {
        // 验证权限
        accountService.existAccountThrow(pageParam.getUserId());
        if (StringUtils.isBlank(pageParam.getStartTime())) {
            pageParam.setStartTime("2018-01-01 00:00:00");
        }
        if (StringUtils.isBlank(pageParam.getEndTime())) {
            pageParam.setEndTime(DateUtil.today() + " 23:59:59");
        }
        // 设置需要查询的状态
        // 调用接口查询
        OrderSimpleListVO officialOrder = getOfficialOrder(pageParam);
        return JsonResult.success(officialOrder);
    }

}
