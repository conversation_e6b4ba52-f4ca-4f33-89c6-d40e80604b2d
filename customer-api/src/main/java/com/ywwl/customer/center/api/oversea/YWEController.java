package com.ywwl.customer.center.api.oversea;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.general.cmcc.enums.WarehouseClassifyTypeEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.modules.international.dto.AddressDTO;
import com.ywwl.customer.center.modules.international.dto.AddressQueryDTO;
import com.ywwl.customer.center.modules.international.dto.DeleteAddressDTO;
import com.ywwl.customer.center.modules.international.service.AddressService;
import com.ywwl.customer.center.modules.international.vo.AddressListVo;
import com.ywwl.customer.center.modules.overseas.dto.YWEBusinessApplyDTO;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.InjectWarehouseVO;
import com.ywwl.customer.center.modules.overseas.vo.YWEApplyInfoVo;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RequestMapping("oversea")
@RestController
public class YWEController extends BaseController {
    @Resource
    private YWEOverseaService ywEOverseaService;
    @Resource
    private CrmService crmService;
    @Resource
    private AddressService addressService;
    @Resource
    private CmccService cmccService;

    @RequestMapping("getOverseaApplyInfo")
    public JsonResult getOverseaApplyInfo() {
        YWEApplyInfoVo yweApplyAllInfoVo = ywEOverseaService.getYWEApplyAllInfoVo(getUserCode());
        if (Objects.isNull(yweApplyAllInfoVo)) {
            yweApplyAllInfoVo = new YWEApplyInfoVo();
            yweApplyAllInfoVo.setBillMail(getUser().getEmail());
        }
        return JsonResult.success(yweApplyAllInfoVo);
    }


    @Logger(module = Module.YWE_OVERSEA, name = "海外派业务线开通提交")
    @PostMapping("saveOverseaApply")
    public JsonResult saveOverseaApply(@RequestBody YWEBusinessApplyDTO yweBusinessApplyDTO) {
        UserAgent currentUser = getUser();
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .provinceName(yweBusinessApplyDTO.getOfficeProvinceName())
                        .cityName(yweBusinessApplyDTO.getOfficeCityName())
                        .areaName(yweBusinessApplyDTO.getOfficeAreaName())
                        .address(yweBusinessApplyDTO.getOfficeAddress())
                        .desc("新开通YWE业务线办公地址")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        yweBusinessApplyDTO.setUserCode(currentUser.getUserCode());
        yweBusinessApplyDTO.setNo(currentUser.getMerchantNo());
        yweBusinessApplyDTO.setAdminPhone(currentUser.getPhone());
        yweBusinessApplyDTO.setOperator(currentUser.getLoginName());
        ywEOverseaService.saveOverseaApply(yweBusinessApplyDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/15 16:03
     * @description: 查询注入仓
     */
    @RequestMapping("getInjectWarehouseList")
    public JsonResult getInjectWarehouseList(){
        List<InjectWarehouseVO> injectWarehouseVOS= ywEOverseaService.getInjectWarehouseList();
        return JsonResult.success(injectWarehouseVOS);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/16 15:29
     * @description: 查询退件地址
     */
    @RequestMapping("getAddressList")
    public JsonResult getAddressList(){
        UserAgent currentUser = getUser();
        YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(currentUser.getUserCode());
        if(yweApplyInfoVo==null){
            return JsonResult.success(new ArrayList<>());
        }
        AddressQueryDTO build = AddressQueryDTO.builder().userCode(currentUser.getUserCode()).merchantCode(yweApplyInfoVo.getMerchantCode()).accountType(BusinessTypeEnum.YWE_WAREHOUSE.getValue()).build();
        AddressListVo addressListVo = addressService.listYWEAddress(build);
        if(addressListVo.getData()!=null&&addressListVo.getData().size()>0){
            List<WareHouseVo> warehouseByType = cmccService.getWarehouseByType(WarehouseClassifyTypeEnum.OVERSEA_WAREHOUSE.getType());
            Map<String, String> stringMap = warehouseByType.stream()
                    .filter(Objects::nonNull)
                    .filter(vo -> vo.getCode() != null)
                    .collect(Collectors.toMap(
                            WareHouseVo::getCode,
                            vo -> vo.getName() != null ? vo.getName() : "",
                            (oldVal, newVal) -> newVal    // 重复时，保留新值（也可以拼接）
                    ));
            addressListVo.getData().forEach(addressDTO -> {
                addressDTO.setWarehouseName(stringMap.get(addressDTO.getWarehouse()));
            });
        }
        return JsonResult.success(addressListVo);
    }

    @Logger(module = Module.YWE_OVERSEA, name = "地址库新增")
    @PostMapping("addAddress")
    public JsonResult saveAddress(@RequestBody JSONObject jsonObject){
        UserAgent currentUser = getUser();
        AddressDTO addressDTO = jsonObject.to(AddressDTO.class);
        addressDTO.setMethod("A");
        addressDTO.setAccountType(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(currentUser.getUserCode());
        addressDTO.setMerchantCode(yweApplyInfoVo.getMerchantCode());
        JsonResult jsonResult = ywEOverseaService.validateAddress(addressDTO.getAddress(), addressDTO.getCityName(), addressDTO.getProvinceName(), addressDTO.getContactZipCode());
        if(!jsonResult.getSuccess()){
            return jsonResult;
        }
        addressService.saveAndUpdateAddress(addressDTO);
        return JsonResult.success();
    }

    @Logger(module = Module.YWE_OVERSEA, name = "地址库修改")
    @PostMapping("updateAddress")
    public JsonResult updateAddress(@RequestBody JSONObject jsonObject){
        UserAgent currentUser = getUser();
        AddressDTO addressDTO = jsonObject.to(AddressDTO.class);
        addressDTO.setMethod("U");
        addressDTO.setAccountType(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        JsonResult jsonResult = ywEOverseaService.validateAddress(addressDTO.getAddress(), addressDTO.getCityName(), addressDTO.getProvinceName(), addressDTO.getContactZipCode());
        if(!jsonResult.getSuccess()){
            return jsonResult;
        }
        addressService.saveAndUpdateAddress(addressDTO);
        return JsonResult.success();
    }

    @Logger(module = Module.YWE_OVERSEA, name = "地址库删除")
    @PostMapping("deleteAddress")
   public JsonResult deleteAddress(@Valid @RequestBody DeleteAddressDTO addressDTO){
        UserAgent currentUser = getUser();
        addressDTO.setMethod("D");
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        addressService.deleteAddress(addressDTO);
        return JsonResult.success();
  }

}
