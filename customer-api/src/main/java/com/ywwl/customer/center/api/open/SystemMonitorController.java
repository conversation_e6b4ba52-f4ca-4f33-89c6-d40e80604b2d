package com.ywwl.customer.center.api.open;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.modules.common.provider.domain.SysUserOnline;
import com.ywwl.customer.center.modules.common.provider.service.SysUserOnlineService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2019/10/17 10:07
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
public class SystemMonitorController {
    @Resource
    private SysUserOnlineService sysUserOnlineService;
    @Resource
    private SessionDAO sessionDAO;

    /**
     * 获取当前的session数量
     *
     * @return
     */
    @RequestMapping("sessionMonitor")
    public JsonResult getOnLineSessionNumber() {
        JsonResult<Object> jsonResult = new JsonResult<>();
        jsonResult.setSuccess(true);
        try {
            // 直接从redis查询shiroSession的key数量
            Set<String> keys = new HashSet<>();
            ScanOptions scanOptions = ScanOptions.scanOptions().match("shiro:session:*").count(5000).build();
            CacheUtil.redis().execute((RedisCallback<Set<String>>) connection -> {
                try (Cursor<byte[]> cursor = connection.scan(scanOptions)) {
                    cursor.forEachRemaining(bytes -> keys.add(new String(bytes, StandardCharsets.UTF_8)));
                }
                return keys;
            });
            HashMap<String, Object> resultMap = new HashMap<>(4);
            resultMap.put("hasCurrentUser", keys.size());
            jsonResult.setData(resultMap);
            jsonResult.setMessage("操作成功");
        } catch (Exception e) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
        }
        return jsonResult;
    }

    /**
     * 根据条件查询在线人数列表
     *
     * @return
     */
    @RequestMapping("onlineSessionList")
    public JsonResult getOnLineSessionNumbers(@RequestBody JSONObject jsonObject) {
        JsonResult jsonResult = new JsonResult<>();
        if (jsonObject == null) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("参数为空");
            return jsonResult;
        }
        try {
            String startTime = jsonObject.getString("startTime");
            String endTime = jsonObject.getString("endTime");
            if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("参数缺失");
                return jsonResult;
            }
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.between("create_time", startTime, endTime);
            List<SysUserOnline> userOnlines = sysUserOnlineService.list(queryWrapper);
            jsonResult.setSuccess(true);
            jsonResult.setData(userOnlines);
            jsonResult.setMessage("操作成功");
        } catch (Exception e) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
        }
        return jsonResult;
    }

}
