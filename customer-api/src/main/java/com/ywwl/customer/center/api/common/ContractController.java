package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.auth.dto.QueryContractDTO;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.vo.ContractVo;
import com.ywwl.customer.center.modules.general.crm.vo.SignContractVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @date: 2023/3/16 16:48
 * <p>合同相关</p>
 */
@RequestMapping("contract")
@RestController
public class ContractController extends BaseController {
    @Resource
    private CommonCrmService commonCrmService;

    /**
     * <AUTHOR>
     * @description 合同签署接口
     * @date 2023/3/17 13:38
     **/
    @Logger(module = Module.CONTRACT, name = "获取客户合同签约地址")
    @PostMapping("getSignUrl")
    public JsonResult<Object> getSignUrl(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryContractDTO queryContractDTO) {
        if (!currentUser.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        Map<String,String> resMap=new HashMap<>();
        resMap.put("signUrl", signContractVO.getUrl());
        resMap.put("contractName", signContractVO.getContractName());
        return JsonResult.success(resMap);
    }


    /**
     * <AUTHOR>
     * @description 查询合同列表
     * @date 2023/3/24 9:35
     **/
    @GetMapping("getContractList")
    public JsonResult<List> getContractList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        List<ContractVo> contractList = commonCrmService.getContractList(currentUser.getUserCode());
        return JsonResult.success(contractList);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/27 14:10
     * @description: 查询指定类型的合同
     */
    @PostMapping("getContractByType")
    public JsonResult getContractByType(@JsonParam Integer businessType, @JsonParam Integer contractType) {
        if(businessType==null||contractType==null){
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        List<ContractVo> contractList = commonCrmService.getContractList(getUserCode());
        if (contractList != null) {
            List<ContractVo> contractVoList = contractList.stream().filter(x -> businessType.equals(x.getAccountType()) && contractType.equals(x.getContractType()))
                    .collect(Collectors.toList());
            if (!contractVoList.isEmpty()) {
                return JsonResult.success(contractVoList);
            }
        }
        return JsonResult.success();
    }

}
