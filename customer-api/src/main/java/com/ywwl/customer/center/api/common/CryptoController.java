package com.ywwl.customer.center.api.common;

import cn.hutool.core.date.TemporalAccessorUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 加密控制器
 *
 * <AUTHOR>
 * @date 2022/08/29 16:45
 **/
@RestController
@RequestMapping("/crypto")
public class CryptoController extends BaseController {


    @Resource
    PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    FbaApplyService fbaApplyService;

    /**
     * 默认加密密钥
     */
    private static final String KEY_STR = "cjv&^*%V7%E6(78$V*%^";
    /**
     * 默认加密密钥
     */
    private static final byte[] KEY = KEY_STR.concat("&8c#d&^c^cvs").getBytes(StandardCharsets.UTF_8);
    /**
     * 默认密码对象类
     */
    public static final SymmetricCrypto AES_I = new SymmetricCrypto(SymmetricAlgorithm.AES, KEY);
    public static final SymmetricCrypto DES_I = new SymmetricCrypto(SymmetricAlgorithm.DES, KEY);

    /**
     * 加密
     *
     * @param param 参数
     * @return
     */
    @PostMapping("/encode")
    public JsonResult<Object> encode(@RequestBody Param param) {
        SymmetricCrypto aes = Optional.ofNullable(param.getKey())
                .map(key -> key.getBytes(StandardCharsets.UTF_8))
                .map(bytes -> new SymmetricCrypto(SymmetricAlgorithm.AES, bytes))
                .orElse(AES_I);
        //加密为16进制表示
        Object encryptHex = aes.encryptHex(param.getSource());
        return JsonResult.success(encryptHex);
    }

    /**
     * 商户号加密
     *
     * @return 加密后的商户号
     */
    @PostMapping("/encodeMerchantCode")
    public JsonResult<Object> encodeMerchantCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        final String time = TemporalAccessorUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        final AES aes = SecureUtil.aes(StringUtils.join(KEY_STR, time).getBytes(StandardCharsets.UTF_8));

        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        //加密为16进制表示
        Object encryptHex = aes.encryptHex(merchantCode);
        return JsonResult.success(ImmutableMap.of("target", encryptHex, "time", time));
    }

    /**
     * FBA业务账号加密
     *
     * @return 加密后的业务账号
     */
    @PostMapping("/encodeFbaMerchantCode")
    public JsonResult<Object> encodeFbaMerchantCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        final String time = TemporalAccessorUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        final AES aes = SecureUtil.aes(StringUtils.join(KEY_STR, time).getBytes(StandardCharsets.UTF_8));
        final String fbaBusinessCode = fbaApplyService.getFbaBusinessCode(currentUser.getUserCode(), false);
        //加密为16进制表示
        Object encryptHex = aes.encryptHex(fbaBusinessCode);
        return JsonResult.success(ImmutableMap.of("target", encryptHex, "time", time));
    }

    /**
     * @author: dinghy
     * @createTime: 2024/1/2 14:08
     * @description: 首页跳转轨迹, 如果有小包默认小包, 没有查fba
     */
    @PostMapping("/getEncodeMerchant")
    public JsonResult<Object> getEncodeMerchant(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        final String time = TemporalAccessorUtil.format(LocalDateTime.now(), "yyyyMMddHHmm");
        final AES aes = SecureUtil.aes(StringUtils.join(KEY_STR, time).getBytes(StandardCharsets.UTF_8));
        String merchantCode = null;
        try {
            merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        } catch (Exception e) {
            try {
                merchantCode = fbaApplyService.getFbaBusinessCode(currentUser.getUserCode(), false);
            } catch (Exception ex) {

            }
        }
        if (merchantCode != null) {
            Object encryptHex = aes.encryptHex(merchantCode);
            return JsonResult.success(ImmutableMap.of("target", encryptHex, "time", time));
        }
        //加密为16进制表示
        return JsonResult.error("无查询权限");
    }

    /**
     * 解密
     *
     * @param param 参数
     * @return
     */
    @PostMapping("/decode")
    public JsonResult<Object> decode(@RequestBody Param param) {
        SymmetricCrypto aes = Optional.ofNullable(param.getKey())
                .map(key -> key.getBytes(StandardCharsets.UTF_8))
                .map(bytes -> new SymmetricCrypto(SymmetricAlgorithm.AES, bytes))
                .orElse(AES_I);
        //加密为16进制表示
        Object encryptHex = aes.decryptStr(param.getSource(), StandardCharsets.UTF_8);
        return JsonResult.success(encryptHex);
    }

    @Data
    private static class Param {
        private String source;
        private String key;
    }
}
