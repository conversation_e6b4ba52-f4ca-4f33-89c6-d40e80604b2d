package com.ywwl.customer.center.api.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.timebase.dto.*;
import com.ywwl.customer.center.modules.timebase.entity.DeliveryDetail;
import com.ywwl.customer.center.modules.timebase.handler.TimeBaseRowWriteHandler;
import com.ywwl.customer.center.modules.timebase.service.DeliveryDetailService;
import com.ywwl.customer.center.modules.timebase.service.DeliveryReportService;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 时效报表控制器
 *
 * <AUTHOR>
 * @since 2023/09/08 10:10
 **/
@RestController
@RequestMapping("/timebase")
public class TimebaseController {

    /**
     * 时效报表模板
     */
    public static final String TEMPLATE = "时效报表_{}.xlsx";
    /**
     * 账号服务
     */
    @Resource
    private AccountService accountService;
    /**
     * 时效报表明细服务
     */
    @Resource
    private DeliveryDetailService deliveryDetailService;
    /**
     * 时效报表服务
     */
    @Resource
    private DeliveryReportService deliveryReportService;

    /**
     * 表头格式
     */
    private final HorizontalCellStyleStrategy styleStrategy;

    /**
     * 构造函数初始化表头格式
     */
    public TimebaseController() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("Calibri");
        headWriteFont.setBold(true);
        headWriteFont.setFontHeightInPoints((short) 11);
        headWriteCellStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIGHT_GREEN.getIndex());
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteFont.setFontName("Calibri");
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        // 策略
        styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 获取时效报表明细
     *
     * @param dto 参数
     * @return 时效报表明细
     */
    @PostMapping("/getReport")
    @Validated
    public JsonResult<?> getReport(@RequestBody ReportParamDTO dto) {
        // 处理请求参数
        processingParameter(dto);
        return JsonResult.success(deliveryReportService.getReport(dto));
    }

    /**
     * 导出明细信息
     *
     * @param dto 参数
     * @return Base64
     */
    @PostMapping("/getDetail")
    @Validated
    public JsonResult<?> getDetail(@RequestBody ReportParamDTO dto) throws IOException {
        // 处理请求参数
        processingParameter(dto);
        // 获取界面报表数据
        final ReportVO report = deliveryReportService.getReport(dto);
        final List<DeliveryDetail> detail = deliveryDetailService.getDetail(dto);
        final List<DeliveryGatherDTO> reports = deliveryReportService
                .getDeliveryReports(dto)
                .stream()
                .map(DeliveryGatherDTO::new)
                .collect(Collectors.toList());
        // 空数据判断
        if (CollectionUtil.isEmpty(detail) || CollectionUtil.isEmpty(reports)) {
            throw ResponseCode.PORTAL_9402.getError();
        }
        // 标题、颜色处理
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter writer = EasyExcel.write(outputStream)
                     .registerWriteHandler(new TimeBaseRowWriteHandler())
                     .registerWriteHandler(new TimeBaseRowWriteHandler.ColumnWidthHandler())
                     .build()) {
            // 设置妥投标题
            final String allTitle = StrUtil.format("总妥投情况({}~{})",
                    dto.getDate().get(0).toDateStr(),
                    dto.getDate().get(1).toDateStr());
            // 基础汇总
            basicSummary(report, writer);
            // 产品汇总
            productSummary(dto, reports, writer, allTitle);
            // 产品国家汇总
            productCountrySummary(reports, writer, allTitle);
            // 详细信息
            detail(dto, detail, writer);
            writer.finish();
            // 生成pdf
            String result = EJFUtil.byteToString(outputStream);
            final String fileName = StrUtil.format(TEMPLATE, DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT));
            return JsonResult.success(ImmutableMap.of("fileName", fileName, "base64", result));
        } finally {
            // 清除线程变量
            TimeBaseRowWriteHandler.queueThreadLocal.remove();
        }
    }

    /**
     * 详细信息
     *
     * @param dto    参数
     * @param detail 详细信息
     * @param writer 写入器
     */
    private static void detail(ReportParamDTO dto, List<DeliveryDetail> detail, ExcelWriter writer) {
        // 时间排序
        detail.sort(Comparator.comparing(DeliveryDetail::getDate));
        final Map<Integer, Collection<DeliveryDetail>> weekGroup = detail.stream().collect(
                Collectors.toMap(
                        k -> LocalDateTimeUtil.weekOfYear(k.getDate()),
                        ListUtil::toList,
                        CollectionUtil::addAll
                )
        );
        // 处理sheet
        weekGroup.entrySet().stream()
                // 时间排序
                .sorted(Map.Entry.comparingByKey())
                .forEach(entity -> {
                    final Integer week = entity.getKey();
                    final Collection<DeliveryDetail> deliveryDetails = entity.getValue();
                    if (CollectionUtil.isNotEmpty(deliveryDetails)) {
                        deliveryDetails
                                .stream()
                                .findFirst()
                                .ifPresent(deliveryDetail -> {
                                    final DateTime dateTemp = new DateTime(deliveryDetail.getDate().toInstant(ZoneOffset.ofHours(8)));
                                    DateTime minTemp = DateUtil.beginOfWeek(dateTemp);
                                    if (dto.getDate().get(0).after(minTemp)) {
                                        minTemp = dto.getDate().get(0);
                                    }
                                    DateTime maxTemp = DateUtil.endOfWeek(dateTemp);
                                    if (dto.getDate().get(1).before(maxTemp)) {
                                        maxTemp = dto.getDate().get(1);
                                    }
                                    // 格式化sheet名称
                                    final String sheetName = StrUtil.format("{}年{}周-{}~{}",
                                            DateUtil.format(minTemp, DatePattern.NORM_YEAR_PATTERN),
                                            week,
                                            DateUtil.format(minTemp, DatePattern.NORM_DATE_PATTERN),
                                            DateUtil.format(maxTemp, DatePattern.NORM_DATE_PATTERN));
                                    WriteSheet sheet = EasyExcel.writerSheet(sheetName).head(DeliveryDetail.class).build();
                                    writer.write(deliveryDetails, sheet);
                                });
                    }
                });
    }

    /**
     * 产品国家汇总
     *
     * @param reports  报表数据
     * @param writer   写入器
     * @param allTitle 总标题
     */
    private static void productCountrySummary(List<DeliveryGatherDTO> reports, ExcelWriter writer, String allTitle) {
        // 按照产品国家分组
        final Map<String, List<DeliveryGatherDTO>> reportGroupByProduct =
                reports.stream().collect(Collectors.groupingBy(DeliveryGatherDTO::getProductId));
        final List<DeliveryGatherDTO> productCountrySummaryList = new LinkedList<>();
        reportGroupByProduct.forEach((k, v) -> {
            // 按照制单账号-国家-产品分组进行聚合
            final Map<String, List<DeliveryGatherDTO>> productCountryGroup = v.stream()
                    .collect(Collectors.groupingBy(DeliveryGatherDTO::accountCountryProduct));
            productCountryGroup.values().forEach(value -> {
                if (CollectionUtil.isNotEmpty(value)) {
                    final String account = value.get(0).getAccount();
                    final String productId = value.get(0).getProductId();
                    final String countryId = value.get(0).getCountryId();
                    productCountrySummaryList.add(getAggregation(account, productId, countryId, value));
                }
            });
            // 插入合计列
            productCountrySummaryList.add(getAggregation(null, "合计", null, v));
        });
        // 设置妥投标题
        TimeBaseRowWriteHandler.queueThreadLocal.set(allTitle);
        // 产品国家汇总sheet
        WriteSheet productCountrySummary = EasyExcel.writerSheet("产品国家汇总").head(DeliveryGatherDTO.class).build();
        writer.write(productCountrySummaryList, productCountrySummary);
    }

    /**
     * 产品汇总
     *
     * @param dto      参数
     * @param reports  报表数据
     * @param writer   写入器
     * @param allTitle 总标题
     */
    private static void productSummary(ReportParamDTO dto, List<DeliveryGatherDTO> reports, ExcelWriter writer, String allTitle) {
        // 先排序
        reports.sort(Comparator.comparing(DeliveryGatherDTO::getDate));
        // 按照产品分组
        final List<DeliveryGatherDTO> productSummaryList = new LinkedList<>();
        final Map<String, List<DeliveryGatherDTO>> reportGroupByAccountProduct =
                reports.stream().collect(Collectors.groupingBy(DeliveryGatherDTO::accountProduct));
        reportGroupByAccountProduct.values().forEach(v -> {
            if (CollectionUtil.isNotEmpty(v)) {
                final String account = v.get(0).getAccount();
                final String productId = v.get(0).getProductId();
                productSummaryList.add(getAggregation(account, productId, null, v));
            }
        });
        productSummaryList.add(getAggregation(null, "合计", null, productSummaryList));
        // 插入空行
        productSummaryList.add(DeliveryGatherDTO.builder().build());
        TimeBaseRowWriteHandler.queueThreadLocal.set(allTitle);
        WriteSheet productSummary = EasyExcel.writerSheet("产品汇总").needHead(false).build();
        // 写入产品总汇总
        final WriteTable writeTable = EasyExcel.writerTable(0)
                .needHead(Boolean.TRUE)
                .head(DeliveryGatherDTO.class)
                .build();
        writer.write(productSummaryList, productSummary, writeTable);
        productSummaryList.clear();
        AtomicInteger count = new AtomicInteger(0);
        // 分周汇总
        reports.stream()
                .collect(Collectors.groupingBy(k -> LocalDateTimeUtil.weekOfYear(k.getDate())))
                .entrySet()
                .stream()
                // 按照周排序
                .sorted(Map.Entry.comparingByKey())
                .forEach(v -> {
                    final Integer week = v.getKey();
                    final List<DeliveryGatherDTO> value = v.getValue();
                    value.stream().collect(Collectors.groupingBy(DeliveryGatherDTO::accountProduct))
                            .values()
                            .forEach(v1 -> {
                                if (CollectionUtil.isNotEmpty(v1)) {
                                    final String account = v1.get(0).getAccount();
                                    final String productId = v1.get(0).getProductId();
                                    productSummaryList.add(getAggregation(account, productId, null, v1));
                                }
                            });
                    // 插入合计
                    productSummaryList.add(getAggregation(null, "合计", null, productSummaryList));
                    // 插入空行
                    productSummaryList.add(DeliveryGatherDTO.builder().build());
                    // 设置妥投标题时间
                    final Date dateTemp = Date.from(value.get(0).getDate().toInstant(ZoneOffset.ofHours(8)));
                    DateTime minTemp = DateUtil.beginOfWeek(dateTemp);
                    if (dto.getDate().get(0).after(minTemp)) {
                        minTemp = dto.getDate().get(0);
                    }
                    DateTime maxTemp = DateUtil.endOfWeek(dateTemp);
                    if (dto.getDate().get(1).before(maxTemp)) {
                        maxTemp = dto.getDate().get(1);
                    }
                    // 设置妥投标题
                    TimeBaseRowWriteHandler.queueThreadLocal.set(StrUtil.format("{}年{}周-妥投情况({}~{})",
                            minTemp.toString("yyyy"),
                            week,
                            minTemp.toDateStr(),
                            maxTemp.toDateStr())
                    );
                    // 写入产品总汇总
                    WriteTable writeTableTemp = EasyExcel.writerTable(count.incrementAndGet())
                            .needHead(Boolean.TRUE)
                            .head(DeliveryGatherDTO.class)
                            .build();
                    writer.write(productSummaryList, productSummary, writeTableTemp);
                    productSummaryList.clear();
                });
    }

    /**
     * 基础汇总
     *
     * @param report 报表数据
     * @param writer 写入器
     */
    private void basicSummary(ReportVO report, ExcelWriter writer) {
        // 写入基础汇总
        final SuccessfulDeliveredAnalyseVO deliveredAnalyse = report.getSuccessfulDeliveredAnalyse();
        WriteSheet baseSummary = EasyExcel.writerSheet("基础汇总").needHead(false).build();
        // 基础信息
        WriteTable baseTable = EasyExcel
                .writerTable(0)
                .needHead(true)
                .registerWriteHandler(styleStrategy)
                .head(ListUtil.of(
                        ListUtil.of("基础数据"),
                        ListUtil.of("基础数据")
                ))
                .build();
        writer.write(ListUtil.of(
                ListUtil.of("发出件数", report.getQuantity()),
                ListUtil.of("妥投件数", report.getSuccessQuantity()),
                ListUtil.of("未妥投件数", report.getUnSuccessQuantity()),
                ListUtil.of("当前妥投率", report.getDeliveredProbability() + "%"),
                ListUtil.of("平均妥投天数", report.getAvgDeliveredDays()),
                ListUtil.of()
        ), baseSummary, baseTable);
        // 妥投成功情况分析
        baseTable = EasyExcel
                .writerTable(1)
                .needHead(true)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .head(ListUtil.of(
                        ListUtil.of("妥投成功情况分析", "妥投天数"),
                        ListUtil.of("妥投成功情况分析", "妥投件数"),
                        ListUtil.of("妥投成功情况分析", "妥投占比"),
                        ListUtil.of("妥投成功情况分析", "累计妥投件数"),
                        ListUtil.of("妥投成功情况分析", "累计妥投率")
                ))
                .build();
        BigInteger theCumulativeNumberOfSuccessfulSubmissions = BigInteger.ZERO;
        final BigDecimal multiplicand = new BigDecimal(100);
        writer.write(ListUtil.of(
                ListUtil.of("≤5(天)", deliveredAnalyse.getLte5(), deliveredAnalyse.getLte5Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getLte5()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("=6(天)", deliveredAnalyse.getEq6(), deliveredAnalyse.getEq6Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getEq6()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("=7(天)", deliveredAnalyse.getEq7(), deliveredAnalyse.getEq7Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getEq7()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("=8(天)", deliveredAnalyse.getEq8(), deliveredAnalyse.getEq8Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getEq8()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("=9(天)", deliveredAnalyse.getEq9(), deliveredAnalyse.getEq9Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getEq9()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("=10(天)", deliveredAnalyse.getEq10(), deliveredAnalyse.getEq10Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getEq10()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("11~15(天)", deliveredAnalyse.getGt10Lte15(), deliveredAnalyse.getGt10Lte15Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getGt10Lte15()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of("16~20(天)", deliveredAnalyse.getGt15Lte20(), deliveredAnalyse.getGt15Lte20Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getGt15Lte20()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of(">20(天)", deliveredAnalyse.getGt20(), deliveredAnalyse.getGt20Rate() + "%",
                        theCumulativeNumberOfSuccessfulSubmissions = theCumulativeNumberOfSuccessfulSubmissions.add(deliveredAnalyse.getGt20()),
                        (NumberUtil.div(theCumulativeNumberOfSuccessfulSubmissions, report.getQuantity()).multiply(multiplicand).setScale(2, RoundingMode.HALF_UP)) + "%"),
                ListUtil.of()
        ), baseSummary, baseTable);
        // 产品妥投情况分析
        TableDataDTO<String, List<Object>> data =
                baseData(report.getResourceProductModuleDeliveredProbability(), "产品维度妥投率", "产品");
        baseTable = EasyExcel
                .writerTable(2)
                .needHead(true)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .head(data.getHead())
                .build();
        writer.write(data.getData(), baseSummary, baseTable);
        // 国家妥投情况分析
        data = baseData(report.getResourceCountryModuleDeliveredProbability(), "国家妥投率分析", "国家");
        baseTable = EasyExcel
                .writerTable(3)
                .needHead(true)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .head(data.getHead())
                .build();
        writer.write(data.getData(), baseSummary, baseTable);
        // 发货日期妥投情况分析
        data = baseData(report.getSendDateDeliveredProbability(), "发货时间妥投率分析", "录入日期");
        baseTable = EasyExcel
                .writerTable(4)
                .needHead(true)
                .registerWriteHandler(styleStrategy)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .head(data.getHead())
                .build();
        writer.write(data.getData(), baseSummary, baseTable);
    }

    /**
     * 表数据
     *
     * @param moduleDelivered 妥投率数据
     * @return 产品头
     */
    private TableDataDTO<String, List<Object>> baseData(List<ModuleDeliveredProbabilityVO> moduleDelivered, String tableName, String columnName) {
        // 妥投率数据
        final List<ModuleDeliveredProbabilityVO> probabilityVOS =
                moduleDelivered.stream().filter(v -> !v.getType().equals("未妥投率")).collect(Collectors.toList());
        List<List<String>> head = ListUtil.of(
                ListUtil.of(tableName, columnName),
                ListUtil.of(tableName, "发出件数"),
                ListUtil.of(tableName, "妥投件数"),
                ListUtil.of(tableName, "未妥投件数"),
                ListUtil.of(tableName, "妥投率")
        );
        List<List<Object>> data = new ArrayList<>();
        probabilityVOS.forEach(module ->
                data.add(ListUtil.of(
                        module.getName(),
                        // 发出件数
                        module.getTotalQuantity(),
                        // 妥投件数
                        module.getQuantity(),
                        // 未妥投件数
                        module.getTotalQuantity().subtract(module.getQuantity()),
                        // 妥投率
                        module.getRate() + "%")));
        data.add(ListUtil.of());
        return TableDataDTO.<String, List<Object>>builder()
                .data(data)
                .head(head)
                .build();
    }

    /**
     * 独立聚合函数
     *
     * @param productId 产品id
     * @param countryId 国家id
     * @param v         数据
     * @return 聚合后数据
     */
    private static DeliveryGatherDTO getAggregation(String account, String productId, String countryId, List<DeliveryGatherDTO> v) {
        final DeliveryGatherDTO deliveryGather = DeliveryGatherDTO
                .builder()
                .account(account)
                .productId(productId)
                .countryId(countryId)
                .quantity(getSum(v, DeliveryGatherDTO::getQuantity))
                .successQuantity(getSum(v, DeliveryGatherDTO::getSuccessQuantity))
                .unSuccessQuantity(getSum(v, DeliveryGatherDTO::getUnSuccessQuantity))
                .average(getAvg(v, x -> NumberUtil.mul(x.getAverage(), NumberUtil.toBigDecimal(x.getSuccessQuantity())), DeliveryGatherDTO::getSuccessQuantity))
                .least(v.stream().map(DeliveryGatherDTO::getLeast)
                        .filter(Objects::nonNull)
                        .filter(num -> !Objects.equals(num, BigInteger.ZERO))
                        .min(BigInteger::compareTo)
                        .orElse(BigInteger.ZERO))
                .lte5(getSum(v, DeliveryGatherDTO::getLte5))
                .eq6(getSum(v, DeliveryGatherDTO::getEq6))
                .eq7(getSum(v, DeliveryGatherDTO::getEq7))
                .eq8(getSum(v, DeliveryGatherDTO::getEq8))
                .eq9(getSum(v, DeliveryGatherDTO::getEq9))
                .eq10(getSum(v, DeliveryGatherDTO::getEq10))
                .gt10Lte15(getSum(v, DeliveryGatherDTO::getGt10Lte15))
                .gt15Lte20(getSum(v, DeliveryGatherDTO::getGt15Lte20))
                .gt20(getSum(v, DeliveryGatherDTO::getGt20))
                .build();
        // 设置妥投率
        deliveryGather.setDeliveredProbability(getDivide(deliveryGather.getSuccessQuantity(), deliveryGather.getQuantity()));
        deliveryGather.setLte5Rate(getDivide(deliveryGather.getLte5(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setEq6Rate(getDivide(deliveryGather.getEq6(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setEq7Rate(getDivide(deliveryGather.getEq7(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setEq8Rate(getDivide(deliveryGather.getEq8(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setEq9Rate(getDivide(deliveryGather.getEq9(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setEq10Rate(getDivide(deliveryGather.getEq10(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setGt10Lte15Rate(getDivide(deliveryGather.getGt10Lte15(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setGt15Lte20Rate(getDivide(deliveryGather.getGt15Lte20(), deliveryGather.getSuccessQuantity()));
        deliveryGather.setGt20Rate(getDivide(deliveryGather.getGt20(), deliveryGather.getSuccessQuantity()));
        return deliveryGather;
    }

    /**
     * 取平均值，除0以外
     *
     * @param v     数据
     * @param avg   平均值函数
     * @param size  总数量
     * @return 平均值
     */
    private static BigDecimal getAvg(List<DeliveryGatherDTO> v,
                                     Function<DeliveryGatherDTO, BigDecimal> avg,
                                     Function<DeliveryGatherDTO, BigInteger> size) {
        // 过滤0
        final BigDecimal allDay = v.stream()
                .map(avg)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        final BigInteger sum = v.stream().map(size).reduce(BigInteger.ZERO, BigInteger::add);
        if (Objects.equals(sum, BigInteger.ZERO)) {
            return BigDecimal.ZERO;
        }
        // 取平均数
        return NumberUtil.div(allDay, NumberUtil.toBigDecimal(sum), 1, RoundingMode.HALF_UP);
    }

    /**
     * 除法
     *
     * @param divisor  被除数
     * @param dividend 除数
     * @return 商
     */
    public static BigDecimal getDivide(BigInteger divisor, BigInteger dividend) {
        return getDivide(new BigDecimal(divisor), new BigDecimal(dividend));
    }

    /**
     * 除法
     *
     * @param divisor  被除数
     * @param dividend 除数
     * @return 商
     */
    public static BigDecimal getDivide(BigDecimal divisor, BigDecimal dividend) {
        return getDivide(divisor, dividend, 4);
    }

    /**
     * 除法
     *
     * @param divisor  被除数
     * @param dividend 除数
     * @param scale    保留小数位
     * @return 商
     */
    public static BigDecimal getDivide(BigDecimal divisor, BigDecimal dividend, Integer scale) {
        if (Objects.nonNull(divisor) &&
                Objects.nonNull(dividend) &&
                BigDecimal.ZERO.compareTo(dividend) != 0) {
            return divisor.divide(dividend, scale, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 求和
     *
     * @param v    数据
     * @param rate 求和函数
     * @return 和
     */
    private static BigInteger getSum(List<DeliveryGatherDTO> v, Function<DeliveryGatherDTO, BigInteger> rate) {
        return v.stream().map(rate).filter(Objects::nonNull)
                .reduce(BigInteger.ZERO, BigInteger::add);
    }

    /**
     * 处理请求参数
     *
     * @param dto 查询参数
     */
    private void processingParameter(ReportParamDTO dto) {
        DateTime startTime = dto.getDate().get(0);
        DateTime endTime = dto.getDate().get(1);
        final long between = startTime.between(endTime, DateUnit.of(ChronoUnit.DAYS));
        if (between > 31) {
            throw ResponseCode.PORTAL_9404.getError();
        }
        if (Objects.equals(dto.getDateType(), 0)) {
            // 如果是周，则获取周的开始和结束时间
            startTime = DateUtil.beginOfWeek(startTime, true);
            endTime = DateUtil.endOfWeek(endTime, true);
            // 覆盖时间
            dto.setDate(ListUtil.of(startTime, endTime));
        }
        // 如果是所有
        List<String> accountList = ListUtil.of(dto.getAccountCode());
        if (Objects.equals(dto.getAccountCode(), "-1")) {
            // 获取所有账号
            final List<AccountGetResVO> accounts = accountService.getAccounts(
                    AccountGetReqDTO.builder()
                            .accountType(0)
                            .scene(2).build());
            if (CollectionUtil.isNotEmpty(accounts)) {
                accountList = accounts.stream()
                        .map(AccountGetResVO::getAccountCode)
                        .collect(Collectors.toList());
            }
        }
        dto.setAccountCodeList(accountList);
    }

}
