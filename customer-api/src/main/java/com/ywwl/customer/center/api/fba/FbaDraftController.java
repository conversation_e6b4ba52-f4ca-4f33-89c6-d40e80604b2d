package com.ywwl.customer.center.api.fba;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.fba.dto.FbaDraftListDTO;
import com.ywwl.customer.center.modules.fba.dto.FbaDraftResultDTO;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.service.FbaDraftService;
import com.ywwl.customer.center.modules.fba.vo.FbaOrderVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * FBA草稿控制器
 *
 * <AUTHOR>
 * @date 2022/08/24 15:51
 **/
@Slf4j
@RestController
@RequestMapping("/fba/draft")
public class FbaDraftController extends BaseController {

    @Resource
    private FbaDraftService fbaDraftService;
    @Resource
    private FbaApplyService fbaApplyService;

    /**
     * 新建草稿订单信息
     *
     * @return 结果
     */
    @PostMapping("/draftExpress")
    @Logger(module = Module.FBA, name = "新建草稿订单信息")
    public JsonResult<?> draftExpress(@RequestBody FbaOrderVO fbaOrderVo) {
        // 设置发货账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        fbaOrderVo.setCustomerCode(businessCode);
        FbaDraftResultDTO<?> result = fbaDraftService.draftExpress(fbaOrderVo);
        if (result.getSuccess()) {
            return JsonResult.success(result.getMessage(), result.getData());
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 修改草稿订单信息
     *
     * @return 结果
     */
    @PostMapping("/updateDraftExpress")
    @Logger(module = Module.FBA, name = "修改草稿订单信息")
    public JsonResult<?> updateDraftExpress(@RequestBody FbaOrderVO fbaOrderVo) {
        // 设置发货账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        fbaOrderVo.setCustomerCode(businessCode);
        FbaDraftResultDTO<?> result = fbaDraftService.updateDraftExpress(fbaOrderVo);
        if (result.getSuccess()) {
            return JsonResult.success(result.getMessage(), result.getData());
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 草稿订单信息详细查询
     *
     * @return 结果
     */
    @PostMapping("/getDraftExpress")
    @Logger(module = Module.FBA, name = "草稿订单信息详细查询")
    public JsonResult<FbaOrderVO> getDraftExpress(@RequestBody Param param) {
        // 设置发货账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        param.setCustomerCode(businessCode);
        FbaDraftResultDTO<FbaOrderVO> result = fbaDraftService.getDraftExpress(param.getId(), param.getCustomerCode());
        if (result.getSuccess()) {
            return JsonResult.success(result.getData());
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 草稿订单信息列表查询
     *
     * @return 结果
     */
    @PostMapping("/listDraftExpress")
    @Logger(module = Module.FBA, name = "草稿订单信息列表查询")
    public JsonResult<?> listDraftExpress(@RequestBody FbaDraftListDTO param) {
        // 设置发货账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        param.setCustomerCode(businessCode);
        FbaDraftResultDTO<?> result = fbaDraftService.listDraftExpress(param);
        if (result.getSuccess()) {
            return JsonResult.success(result.getData());
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 草稿订单删除
     *
     * @return 结果
     */
    @PostMapping("/deleteDraftExpress")
    @Logger(module = Module.FBA, name = "草稿订单删除")
    public JsonResult<?> deleteDraftExpress(@RequestBody Param param) {
        // 设置发货账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        param.setCustomerCode(businessCode);
        FbaDraftResultDTO<?> result = fbaDraftService.deleteDraftExpress(param.getIds(), param.getCustomerCode());
        if (result.getSuccess()) {
            return JsonResult.success(result.getMessage(), result.getData());
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 草稿订单生成运单
     *
     * @return 结果
     */
    @PostMapping("/generateExpressCode")
    public JsonResult<?> generateExpressCode(@RequestBody FbaOrderVO fbaOrderVo) {
        try {
            // 设置发货账号
            final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
            fbaOrderVo.setCustomerCode(businessCode);
            // 检测申报总价值是否超过限度
            fbaDraftService.declareValueVerification(fbaOrderVo);
            // 校验参数
            fbaDraftService.verifyFbaOrder(fbaOrderVo);
            // 校验是否可以下单
            fbaDraftService.detectionSupportCollection(fbaOrderVo);
            // 订单转运单
            FbaDraftResultDTO<?> result = fbaDraftService.generateExpressCode(fbaOrderVo.getId(), fbaOrderVo.getCustomerCode());
            if (result.getSuccess()) {
                return JsonResult.success(result.getMessage(), result.getData());
            }
            return JsonResult.error(result.getMessage());
        } catch (BusinessException e) {
            log.error("提交FBA订单信息异常,原因:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        }
    }

    @Data
    private static class Param {
        Integer id;
        Integer[] ids;
        String customerCode;
    }

}
