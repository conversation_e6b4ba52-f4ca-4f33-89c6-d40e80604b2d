package com.ywwl.customer.center.api.common;

import com.alibaba.excel.util.StringUtils;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.modules.common.provider.domain.ClientReadRecord;
import com.ywwl.customer.center.modules.common.provider.enums.ClientReadTypeEnum;
import com.ywwl.customer.center.modules.common.provider.service.ClientReadRecordService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

@RequestMapping("cusRead")
@RestController
public class ClientReadRecordController extends BaseController {
    @Resource
    private ClientReadRecordService clientReadRecordService;

    /**
     * @author: dinghy
     * @createTime: 2024/4/26 17:35
     * @description: 根据类型查看是否已经查看内容，true是已查看，false是未查看
     */
    @RequestMapping("ifReadByType")
    public JsonResult ifReadByType(@JsonParam @NotBlank(message = "type不能为空") String type){
       Boolean flag=  clientReadRecordService.checkIfReadByType(type,getUser());
       return JsonResult.success(flag);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/26 17:37
     * @description: 点击已读操作
     */
    @RequestMapping("readAction")
    public JsonResult readAction(@JsonParam @NotBlank(message = "type不能为空") String type,@JsonParam String typeId){
        UserAgent user = getUser();
        String clientTypeDesc = ClientReadTypeEnum.getClientTypeDesc(type);
        if(StringUtils.isNotBlank(clientTypeDesc)){
            ClientReadRecord clientReadRecord = ClientReadRecord.builder().userId(user.getUserId()).typeId(StringUtils.isBlank(typeId)?"0":typeId).type(clientTypeDesc).build();
            clientReadRecordService.save(clientReadRecord);
        }
        return JsonResult.success();
    }

    @RequestMapping("getReadActionInfo")
    public JsonResult getReadActionInfo(@JsonParam @NotBlank(message = "type不能为空") String type){
        ClientReadRecord record = clientReadRecordService.getRecord(type, getUser());
        return JsonResult.success(record);
    }
}
