package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.upload.entity.FileUploadRecord;
import com.ywwl.customer.center.modules.upload.enums.FileUploadRecordTypeEnum;
import com.ywwl.customer.center.modules.upload.service.FileUploadRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Controller
@RequestMapping("fileTemplate")
public class FileTemplateController {
    @Resource
    private FileUploadRecordService fileUploadRecordService;

    /**
     * @author: dinghy
     * @createTime: 2024/6/28 17:24
     * @description: 下载模版
     */
    @GetMapping("download")
    public void downloadTemplate(@NotBlank(message = "参数缺失")Integer type, HttpServletResponse response){
        FileUploadRecordTypeEnum fileUploadRecordEnum = FileUploadRecordTypeEnum.toFileUploadRecordEnum(type);
        List<FileUploadRecord> activeFileUploadRecord = fileUploadRecordService.getActiveFileUploadRecord(fileUploadRecordEnum);
        if(activeFileUploadRecord.isEmpty()){
            throw new BusinessException("模版异常");
        }
        FileUploadRecord fileUploadRecord = activeFileUploadRecord.get(0);
        String url = fileUploadRecord.getUrl();
        if(StringUtils.isBlank(url)){
            throw new BusinessException("模版异常");
        }
        HttpUtil.download(url, response, fileUploadRecord.getOriginalName(), true);
    }
}
