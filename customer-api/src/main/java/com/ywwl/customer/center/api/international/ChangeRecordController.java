// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.international;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.ejf.dto.ExtractDTO;
import com.ywwl.customer.center.modules.ejf.entity.Channel;
import com.ywwl.customer.center.modules.ejf.service.BaseInfoService;
import com.ywwl.customer.center.modules.international.service.ChangeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/29 17:45
 * @ModifyDate 2023/3/29 17:45
 * @Version 1.0
 */
@RequestMapping("/changeRecord")
@RestController
public class ChangeRecordController extends BaseController{

    @Resource
    private ChangeRecordService changeRecordService;
    @Resource
    BaseInfoService baseInfoService;


    @PostMapping("/list")
    public JsonResult<?> changeRecordList(@RequestBody JSONObject jsonObject){
        JsonResult result = changeRecordService.changeRecordList(jsonObject);
        return result;
    }

    @PostMapping("/extract")
    public JsonResult<?> extract(@RequestBody @Validated ExtractDTO param){
        param.setIsExtract(1);
        changeRecordService.extract(param);
        return JsonResult.success();
    }


    /**
     * 获取所有产品
     *
     * @return
     */
    @GetMapping("getChannel")
    public JsonResult<?> getChannel() {
        JsonResult jsonResult = new JsonResult<>();
        List<Channel> channels = baseInfoService.getChannel();
        jsonResult.setSuccess(true);
        jsonResult.setData(channels);

        return jsonResult;
    }
}
