package com.ywwl.customer.center.web.Interceptor;

import com.alibaba.fastjson2.JSON;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessApplyStatusEnum;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/3/31 14:48
 */
public class PacketAndFbaInterceptor implements HandlerInterceptor {
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private FbaApplyService fbaApplyService;
    @Resource
    private YWEOverseaService yWEOverseaService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            UserAgent userAgent = JsonUtils.parse((String) principal, UserAgent.class);
            if(Objects.nonNull(userAgent)){
                Integer packetStatus = packetBusinessApplyService.getNotLimitState(userAgent.getUserCode());
                Integer fbaApplyStatus = fbaApplyService.getFbaApplyStatus(userAgent.getUserCode());
                Integer applyStatus = yWEOverseaService.getApplyStatus(userAgent.getUserCode());
                if (!(BusinessApplyStatusEnum.SIGNED.getValue().equals(packetStatus) || BusinessApplyStatusEnum.SIGNED.getValue().equals(fbaApplyStatus)|| BusinessApplyStatusEnum.SIGNED.getValue().equals(applyStatus))) {
                    response.setContentType("application/json");
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().write(JSON.toJSONString(JsonResult.error(ResponseCode.PORTAL_414)));
                    return false;
                }

            }
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
