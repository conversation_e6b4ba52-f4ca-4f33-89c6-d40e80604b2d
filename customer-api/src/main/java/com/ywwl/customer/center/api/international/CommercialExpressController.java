package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.dto.CommercialExpressDetailDTO;
import com.ywwl.customer.center.modules.common.provider.dto.CommercialExpressDisposeDTO;
import com.ywwl.customer.center.modules.common.provider.dto.CommercialExpressListDTO;
import com.ywwl.customer.center.modules.common.provider.vo.CommercialExpressDetailVo;
import com.ywwl.customer.center.modules.international.service.CommercialExpressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/commercialExpress")
public class CommercialExpressController extends BaseController {
    @Resource
    private CommercialExpressService commercialExpressService;

    /***
     * //  商业快递确认列表
     * <AUTHOR>
     * @date 2024/3/12 13:44
     * @param currentUser 登录信息
     * @param commercialExpressListDTO 列表入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/list")
    public JsonResult<Object> list(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody CommercialExpressListDTO commercialExpressListDTO) {
        try {
            return commercialExpressService.list(commercialExpressListDTO);
        } catch (BusinessException e) {
            log.error("商业快递确认列表查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }catch (Exception e) {
            log.error("商业快递确认列表查询异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查询尺寸明细以及待确认费用
     * <AUTHOR>
     * @date 2024/3/12 14:30
     * @param commercialExpressDetailDTO 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */

    @Validated
    @PostMapping("/detail")
    public JsonResult<Object> getDetail(@RequestBody CommercialExpressDetailDTO commercialExpressDetailDTO) {
        try {
            CommercialExpressDetailVo detail = commercialExpressService.getDetail(commercialExpressDetailDTO);
            return JsonResult.success(detail);
        } catch (BusinessException e) {
            log.error("商业快递确认明细查询异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("商业快递确认明细查询异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  处理运单
     * <AUTHOR>
     * @date 2024/3/12 14:45
     * @param currentUser 登录信息
     * @param commercialExpressDisposeDTO 处理运单号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "商业快递费用确认处理运单")
    @Validated
    @PostMapping("/dispose")
    public JsonResult<Object> dispose(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody CommercialExpressDisposeDTO commercialExpressDisposeDTO) {
        try {
            commercialExpressDisposeDTO.setOperatorName(currentUser.getLoginName());
            return commercialExpressService.dispose(commercialExpressDisposeDTO);
        } catch (BusinessException e) {
            log.error("商业快递处理运单异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("商业快递处理运单异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }
}
