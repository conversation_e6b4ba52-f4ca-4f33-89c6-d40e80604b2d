package com.ywwl.customer.center.api.open;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.config.CpsManagerConfig;
import com.ywwl.customer.center.modules.common.provider.domain.SignReqWrapper;
import com.ywwl.customer.center.modules.common.provider.domain.SysContent;
import com.ywwl.customer.center.modules.common.provider.domain.SysContentUser;
import com.ywwl.customer.center.modules.common.provider.dto.NoticeSearchDTO;
import com.ywwl.customer.center.modules.common.provider.service.SysContentService;
import com.ywwl.customer.center.modules.common.provider.service.SysContentUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Slf4j
@RestController
@RequestMapping("/api/sysContent")
public class SysContentApi {


    @Resource
    private CpsManagerConfig cpsManagerConfig;

    @Resource
    private SysContentService sysContentService;

    @Resource
    private SysContentUserService sysContentUserService;

    /**
     * @author: dinghy
     * @createTime: 2024/5/22 10:03
     * @description: 微信小程序查询消息通知
     */
    @PostMapping("queryContentList")
    public JsonResult queryContentList(@RequestBody NoticeSearchDTO noticeSearchDTO){
        return sysContentService.getNoticeSearchList(noticeSearchDTO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/5/30 10:45
     * @description: 微信小程序查询通知公告详情
     */
    @PostMapping("noticeRecord")
    public JsonResult noticeRecord(@JsonParam Long contentId,@JsonParam Long userId){
        SysContent sysContent = sysContentService.getById(contentId);
        if (ObjectUtils.isNotEmpty(sysContent)) {
            SysContentUser sysContentUser = new SysContentUser();
            sysContentUser.setContentId(contentId);
            sysContentUser.setUserId(userId);
            QueryWrapper<SysContentUser> wrapper = new QueryWrapper<>(sysContentUser);
            SysContentUser contentUser = sysContentUserService.getOne(wrapper);
            if (contentUser == null) {
                Integer viewNum = sysContent.getViewNum();
                if (viewNum == null) {
                    sysContent.setViewNum(1);
                } else {
                    sysContent.setViewNum(viewNum + 1);
                }
                sysContentService.updateById(sysContent);
                sysContentUserService.save(sysContentUser);
            } else {
                sysContentUserService.updateById(sysContentUser);
            }

            return JsonResult.success(sysContent);
        } else {
            return JsonResult.error(ResponseCode.PORTAL_5047);
        }
    }


    /***
     * //  新增系统内容
     * <AUTHOR>
     * @date 2023/4/20 16:27
     * @param param 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/save")
    public JsonResult<Object> insertContent(@RequestBody @Valid SignReqWrapper param) {
        Boolean verify = verify(param.getRealParam().getSign());
        if (!verify) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        try {
            return sysContentService.saveContent(param);
        } catch (Exception e) {
            log.info("新增系统内容异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    /**
     * 更新系统内容
     *
     * @param param
     * @return
     */
    @PostMapping("/update")
    public JsonResult<Object> updateContent(@RequestBody SignReqWrapper param) {
        Boolean verify = verify(param.getRealParam().getSign());
        if (!verify) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        try {
            return sysContentService.contentUpdate(param);
        } catch (Exception e) {
            log.info("修改系统内容异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    /**
     * 分页查询系统内容
     *
     * @param reqBody
     * @return
     */
    @PostMapping("/list")
    public JsonResult<Object> loadSysContent(@RequestBody JSONObject reqBody) {
        String sign = reqBody.getString("sign");
        if (StringUtils.isBlank(sign)) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        Boolean verify = verify(sign);
        if (!verify) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        return sysContentService.contentList(reqBody);
    }

    /**
     * 批量逻辑删除
     *
     * @param param
     * @return
     */
    @PostMapping("/batch/delete")
    public JsonResult<Object> deleteByIds(@RequestBody SignReqWrapper param) {
        Boolean verify = verify(param.getRealParam().getSign());
        if (!verify) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        try {
            return sysContentService.contentDelete(param);
        } catch (Exception e) {
            log.info("批量系统内容异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  校验签名;是否请求合法
     * <AUTHOR>
     * @date 2022/2/28 10:29
     * @return java.lang.Boolean
     */
    public Boolean verify(String signature) {
        if (cpsManagerConfig.getToken().equals(signature)) {
            return true;
        } else {
            return false;
        }
    }
}
