package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.system.entity.SmsType;
import com.ywwl.customer.center.system.service.SmsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * //短信发送
 *
 * <AUTHOR>
 * @date 2023/3/2
 */
@Validated
@RestController
@RequestMapping("/sms")
public class SmsController extends BaseController {
    @Resource
    private SmsService smsService;

    /***
     * //  针对每个用户根据类型发送短信
     * <AUTHOR>
     * @date 2023/3/2 14:03
     * @param currentUser 登录信息
     * @param smsType  短信类型
     * @param request  响应
     * @return JsonResult
     */

    @Logger(module = Module.COMMON, name = "针对每个用户根据类型发送短信")
    @PostMapping("/sendSmsCode")
    public JsonResult<Object> sendSmsCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody SmsType smsType, HttpServletRequest request) {
        try {
            smsService.sendSmsCode(request.getSession().getId(), currentUser.getPhone(), smsType.getType());

        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        return JsonResult.success();
    }

    /***
     * // 校验短信
     * <AUTHOR>
     * @date 2023/3/28 9:45
     * @param currentUser 登录信息
     * @param smsCode 验证码
     * @param request  响应
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "短信校验")
    @RequestMapping("/checkSmsCode")
    public JsonResult<Object> checkSmsCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @NotBlank(message = "验证码不得为空") @JsonParam String smsCode, HttpServletRequest request) {
        try {
            smsService.checkSmsCode(request.getSession().getId(), currentUser.getPhone(), smsCode);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        return JsonResult.success();
    }
}
