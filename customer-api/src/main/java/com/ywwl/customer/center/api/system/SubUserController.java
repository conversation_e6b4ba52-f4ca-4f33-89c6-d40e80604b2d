package com.ywwl.customer.center.api.system;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.api.common.IndexController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.OPCodeEnum;
import com.ywwl.customer.center.common.enums.VerifyStatusEnum;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.system.constant.SysConstants;
import com.ywwl.customer.center.system.dto.*;
import com.ywwl.customer.center.system.response.SubUserInfoResBody;
import com.ywwl.customer.center.system.service.PermissionService;
import com.ywwl.customer.center.system.service.UserService;
import com.ywwl.customer.center.system.service.impl.SubUserService;
import com.ywwl.customer.center.system.vo.SubUserListVo;
import com.ywwl.customer.center.system.vo.SubUserRoleDetailVo;
import com.ywwl.customer.center.system.vo.SubUserRoleListVo;
import com.ywwl.customer.center.system.vo.UserMenusVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/3/31 16:01
 * <p>子用户</p>
 */
@Slf4j
@RestController
@RequestMapping("/subUser")
public class SubUserController extends BaseController {
    @Resource
    private UserService userService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private SubUserService subUserService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private AccountService accountService;
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private CrmService crmService;

    /***
     * //  查询子用户角色-列表
     * <AUTHOR>
     * @date 2023/3/13 15:36
     * @param subUserDTO 入参m
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.system.vo.SubUserListVo>
     */
    @ResponseBody
    @PostMapping("/getSubUserRole/list")
    public JsonResult<SubUserRoleListVo> subUserList(@Valid @RequestBody SubUserRoleDTO subUserDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent) {
        subUserDTO.setUserId(userAgent.getUserId());
        return userService.subUserRoleList(subUserDTO);
    }

    /***
     * //  新增子用户角色
     * <AUTHOR>
     * @date 2023/3/13 17:07
     * @param userAgent  登录信息
     * @param addSubUserRoleDTO 新增角色信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "新增子用户角色")
    @ResponseBody
    @PostMapping("/add/subUserRole")
    public JsonResult<Object> addSubUserRole(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody AddSubUserRoleDTO addSubUserRoleDTO) {
        getMerChantStatus(userAgent.getUserCode());
        addSubUserRoleDTO.setUserId(userAgent.getUserId());
        accountService.checkAllAccountCode(addSubUserRoleDTO.getPortal().getAccount(), userAgent.getUserCode());
        return userService.addSubUserRole(addSubUserRoleDTO);
    }

    /***
     * //  修改子用户角色
     * <AUTHOR>
     * @date 2023/3/14 10:38
     * @param userAgent 登录信息
     * @param updateSubUserRoleDTO 修改参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "修改子用户角色")
    @ResponseBody
    @PostMapping("/update/subUserRole")
    public JsonResult<Object> updateSubUserRole(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody UpdateSubUserRoleDTO updateSubUserRoleDTO) {
        updateSubUserRoleDTO.setUserId(userAgent.getUserId());
        accountService.checkAllAccountCode(updateSubUserRoleDTO.getPortal().getAccount(), userAgent.getUserCode());
        return userService.updateSubUserRole(updateSubUserRoleDTO);
    }

    /***
     * //删除子用户角色信息
     * <AUTHOR>
     * @date 2023/3/14 10:53
     * @param userAgent 登录信息
     * @param deleteSubUserRoleDTO  删除信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "删除子用户角色")
    @ResponseBody
    @PostMapping("/delete/subUserRole")
    public JsonResult<Object> deleteSubUserRole(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody DeleteSubUserRoleDTO deleteSubUserRoleDTO) {
        deleteSubUserRoleDTO.setUserId(userAgent.getUserId());
        return userService.deleteSubUserRole(deleteSubUserRoleDTO);
    }

    /***
     * //  子用户角色详情
     * <AUTHOR>
     * @date 2023/3/14 14:11
     * @param userAgent 登录信息
     * @param subUserRoleDetailDTO  查询入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.system.vo.SubUserRoleDetailVo>
     */
    @Logger(module = Module.USER, name = "子用户角色详情")
    @ResponseBody
    @PostMapping("/getSubUserRole/detail")
    public JsonResult<SubUserRoleDetailVo> getSubUserRoleDetail(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody SubUserRoleDetailDTO subUserRoleDetailDTO) {
        subUserRoleDetailDTO.setUserId(userAgent.getUserId());
        return userService.getSubUserRoleDetail(subUserRoleDetailDTO);
    }

    /***
     * // 子用户列表
     * <AUTHOR>
     * @date 2023/3/14 14:33
     * @param userAgent 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.system.vo.SubUserListVo>
     */
    @Logger(module = Module.USER, name = "子用户列表")
    @ResponseBody
    @PostMapping("/getSubUser/list")
    public JsonResult<SubUserListVo> subUserList(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @RequestBody SubUserListDTO subUserListDTO) {
        subUserListDTO.setSource("subscription");
        subUserListDTO.setOpcode(OPCodeEnum.User_List.value());
        subUserListDTO.setMerchantNo(userAgent.getMerchantNo());
        try {
            subUserListDTO.setMerchantName(customerAuthService.getCustomerName(subUserListDTO.getMerchantNo()));
        } catch (Exception e) {
            subUserListDTO.setMerchantName(SysConstants.NAME);
        }
        return userService.subUserList(subUserListDTO);
    }

    /***
     * //  新增子用户
     * <AUTHOR>
     * @date 2023/3/14 14:53
     * @param userAgent 登录信息
     * @param addSubUserDTO 新增信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "新增子用户")
    @ResponseBody
    @PostMapping("/add/subUser")
    public JsonResult<Object> addSubUser(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody AddSubUserDTO addSubUserDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(userAgent.getUserCode())
                        .mail(addSubUserDTO.getEmail())
                        .phone(addSubUserDTO.getMobile())
                        .desc("添加子用户")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        getMerChantStatus(userAgent.getUserCode());
        addSubUserDTO.setMerchantNo(userAgent.getMerchantNo());
        addSubUserDTO.setPlatformType(0);
        addSubUserDTO.setOpcode(OPCodeEnum.ADD_USER.value());
        addSubUserDTO.setUserCode(userAgent.getUserCode());
        return userService.addSubUser(addSubUserDTO);
    }

    /***
     * //  修改子用户
     * <AUTHOR>
     * @date 2023/3/14 15:05
     * @param userAgent  登录信息
     * @param updateSubUserDTO 更新参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "修改子用户")
    @ResponseBody
    @PostMapping("/update/subUser")
    public JsonResult<Object> updateSubUser(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody UpdateSubUserDTO updateSubUserDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(userAgent.getUserCode())
                        .mail(updateSubUserDTO.getEmail())
                        .phone(updateSubUserDTO.getMobile())
                        .desc("修改子用户")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        updateSubUserDTO.setMerchantNo(userAgent.getMerchantNo());
        updateSubUserDTO.setOpcode(OPCodeEnum.UPDATE_USER.value());
        return userService.updateSubUser(updateSubUserDTO);
    }

    /***
     * //  删除子用户
     * <AUTHOR>
     * @date 2023/3/14 15:12
     * @param deleteSubUserDTO 删除
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "删除子用户")
    @ResponseBody
    @PostMapping("/delete/subUser")
    public JsonResult<Object> deleteSubUser(@Valid @RequestBody DeleteSubUserDTO deleteSubUserDTO) {
        deleteSubUserDTO.setOpcode(OPCodeEnum.DELETE_USER.value());
        return userService.deleteSubUser(deleteSubUserDTO);
    }

    /***
     * //  子用户详情
     * <AUTHOR>
     * @date 2023/3/15 11:42
     * @param userAgent  登录信息
     * @param userId 用户id
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.system.response.SubUserInfoResBody>
     */
    @Logger(module = Module.USER, name = "子用户详情")
    @ResponseBody
    @PostMapping("/getSubUser/detail")
    public JsonResult<SubUserInfoResBody> getSubUserDetail(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @NotNull(message = "用户ID不得为空") @JsonParam Long userId) {
        SubUserInfoResBody subUserInfoResBody = subUserService.requestSubUserInfo(userId, userAgent.getAccessToken());
        return JsonResult.success(subUserInfoResBody);
    }

    /***
     * //  用户菜单权限
     * <AUTHOR>
     * @date 2023/3/15 11:42
     * @param userAgent  登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.system.response.SubUserInfoResBody>
     */
    @ResponseBody
    @GetMapping("/getUserMenus/detail")
    public JsonResult<UserMenusVo> getUserMenus(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent) {
        return permissionService.getUserMenus(userAgent.getUserId());
    }

    @Resource
    private FbaApplyService fbaApplyService;

    public void getMerChantStatus(String userCode) {
        Integer packetStatus = packetBusinessApplyService.getPacketStatus(userCode);
        Integer desc = VerifyStatusEnum.getValue(packetStatus);
        boolean fbaOpen = false;
        try {
            // 是否开通FBA
            final Integer applyStatus = fbaApplyService.getFbaApplyStatus(userCode);
            fbaOpen = !IndexController.STATUS_ENUMS.contains(applyStatus);
        } catch (Throwable ignored) {
        }
        if (Objects.isNull(desc) && !fbaOpen) {
            throw new BusinessException(ResponseCode.PORTAL_5059);
        }
    }
}
