// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.common.account.dto.AccountEditReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountInsertReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 14:49
 * @ModifyDate 2023/3/14 14:49
 * @Version 1.0
 */
@Slf4j
@RequestMapping("/account")
@RestController
public class AccountController extends BaseController {

    @Resource
    private AccountService accountService;


    /**
     * 新增制单账号
     * @param currentUser
     * @param accountInsertReqDTO
     * @return
     */
    @PostMapping("/saveAccount")
    public JsonResult<String> saveAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid AccountInsertReqDTO accountInsertReqDTO){
        accountInsertReqDTO.setUserCode(currentUser.getUserCode());
        accountInsertReqDTO.setSourceType("Portal");
        JsonResult result = accountService.saveAccount(accountInsertReqDTO);
        return result;
    }

    /**
     * 查询制单账号
     * @param accountGetReqDTO
     * @return
     */
    @PostMapping("/loadAccounts")
    public JsonResult<List<AccountGetResVO>> getAccounts(@RequestBody AccountGetReqDTO accountGetReqDTO){
        List<AccountGetResVO> accounts = accountService.getAccounts(accountGetReqDTO);
        accounts.stream().forEach(item->{
            if(item.getEnableStatus()!=0||item.getSearchStatus()!=0){
                item.setApiToken(null);
            }
        });
        return JsonResult.success(accounts);
    }

    /**
     * 编辑制单账号权限
     * @param currentUser
     * @param accountEditReqDTO
     * @return
     */
    @PostMapping("/editAccount")
    public JsonResult editAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody AccountEditReqDTO accountEditReqDTO){
        accountEditReqDTO.setOperator(currentUser.getLoginName());
        accountService.existAccountThrow(accountEditReqDTO.getAccountCode());
        JsonResult result = accountService.editAccount(accountEditReqDTO);
        return result;
    }


    @PostMapping("/getAccountForAll")
    public JsonResult<Map<Integer,List<AccountGetResVO>>> getAccountForAll(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody AccountGetReqDTO accountGetReqDTO){
        Map<Integer,List<AccountGetResVO>> result = accountService.getAccountForAll(accountGetReqDTO.getBusiness(),currentUser,null);
        return JsonResult.success(result);
    }

}
