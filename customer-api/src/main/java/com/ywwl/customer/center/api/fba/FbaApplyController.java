package com.ywwl.customer.center.api.fba;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.provider.service.CrmOldService;
import com.ywwl.customer.center.modules.common.provider.vo.EnterpriseWeChatVo;
import com.ywwl.customer.center.modules.fba.dto.FbaApplyDTO;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.service.FbaService;
import com.ywwl.customer.center.modules.fba.vo.FbaApplyInfoVO;
import com.ywwl.customer.center.modules.fba.vo.FbaIndexVO;
import com.ywwl.customer.center.modules.fba.vo.FbaInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/5/13 13:51
 */
@RequestMapping("/fba/apply")
@RestController
@Slf4j
public class FbaApplyController extends BaseController {
    @Resource
    private FbaApplyService fbaApplyService;
    @Resource
    private FbaService fbaService;
    @Resource
    private CrmOldService crmOldService;
    /**
     * <AUTHOR>
     * @description fba申请
     * @date 2023/5/13 13:53
     **/
    @Logger(module = Module.FBA,name="提交FBA业务申请")
    @PostMapping("fbaApply")
    public JsonResult<Object> fbaApply(@Valid @RequestBody FbaApplyDTO fbaApplyDTO) {
        UserAgent currentUser = getUser();
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        fbaApplyDTO.setUserCode(currentUser.getUserCode());
        fbaApplyDTO.setNo(currentUser.getMerchantNo());
        fbaApplyDTO.setAdminPhone(currentUser.getPhone());
        fbaApplyService.saveApply(fbaApplyDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 获取fba业务线申请信息
     * @date 2023/5/17 10:08
     **/
    @GetMapping("getApplyInfo")
    public JsonResult<Object> getFbaApplyInfo(){
        UserAgent currentUser = getUser();
        FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(currentUser.getUserCode());
        if(Objects.isNull(fbaApplyInfo)){
            fbaApplyInfo=new FbaApplyInfoVO();
            fbaApplyInfo.setBillMail(currentUser.getEmail());
        }
        return JsonResult.success(fbaApplyInfo);
    }

    /**
     * <AUTHOR>
     * @description 查询fba页面展示信息
     * @date 2023/5/25 15:13
     **/
    @GetMapping("info")
    public JsonResult<Object> getFbaInfo(){
        FbaIndexVO fbaIndexInfo=fbaApplyService.getFbaIndexInfo(getUserCode());
        try {
            FbaInfoVO fbaInfo = fbaService.getFbaInfo(fbaIndexInfo.getAccountCode());
            fbaIndexInfo.setSaleName(fbaInfo.getSaleName());
            fbaIndexInfo.setSalePhone(fbaInfo.getSalePhone());
            fbaIndexInfo.setServiceName(fbaInfo.getReceiverName());
            fbaIndexInfo.setFrozenStatus(fbaInfo.getMerchantStatus());
            fbaIndexInfo.setFrozenStatusName(fbaInfo.getMerchantStatusName());
            EnterpriseWeChatVo enterpriseWeChat = crmOldService.searchEnterpriseWeChat(fbaInfo.getIntroducerSale());
            fbaIndexInfo.setEnterpriseWeChat(enterpriseWeChat.getQrCode());
            EnterpriseWeChatVo receiverEnterpriseWeChat = crmOldService.searchEnterpriseWeChat(fbaInfo.getReceiver());
            if(receiverEnterpriseWeChat!=null){
                fbaIndexInfo.setReceiverEnterpriseWeChat(receiverEnterpriseWeChat.getQrCode());
                fbaIndexInfo.setReceiverFixedLinePhone(receiverEnterpriseWeChat.getLandlinePhone());
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return JsonResult.success(fbaIndexInfo);
    }
}
