package com.ywwl.customer.center.api.common;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.provider.domain.SysContent;
import com.ywwl.customer.center.modules.common.provider.domain.SysContentUser;
import com.ywwl.customer.center.modules.common.provider.dto.NoticeSearchDTO;
import com.ywwl.customer.center.modules.common.provider.dto.NotificationDTO;
import com.ywwl.customer.center.modules.common.provider.service.SysContentService;
import com.ywwl.customer.center.modules.common.provider.service.SysContentUserService;
import com.ywwl.customer.center.modules.common.provider.vo.NoticeSearchVo;
import com.ywwl.customer.center.modules.common.provider.vo.TableVo;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * // 系统内容控制器
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Slf4j
@RestController()
@RequestMapping("/sys/content")
public class SysContentController extends BaseController {

    @Resource
    private SysContentService sysContentService;
    @Resource
    private SysContentUserService sysContentUserService;

    /***
     * //  首页通知通告
     * <AUTHOR>
     * @date 2023/4/20 10:33
     * @param currentUser 登录信息
     * @param noticeSearchDTO 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */

    @Logger(module = Module.COMMON, name = "首页通知通告")
    @PostMapping("/getHomePageNoticeList")
    public JsonResult<Object> getHomePageNoticeList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody NoticeSearchDTO noticeSearchDTO) {
        noticeSearchDTO.setUserId(currentUser.getUserId());
        noticeSearchDTO.setUserCode(currentUser.getUserCode());
        try {
            return sysContentService.getHomePageNoticeList(noticeSearchDTO);
        } catch (Exception e) {
            log.error("首页通知通告异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }

    /**
     * 获取未读公共数量
     *
     * <AUTHOR>
     * @date 2021/3/17 16:40
     */
    @Logger(module = Module.COMMON, name = "未读通告数量")
    @RequestMapping("/getUnReadNoticeCount")
    public JsonResult<Object> getUnReadNoticeCount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody NoticeSearchDTO noticeSearchDTO) {
        try {
            noticeSearchDTO.setUserId(currentUser.getUserId());
            JSONObject reqBody = new JSONObject();
            reqBody.put("userId", noticeSearchDTO.getUserId());
            reqBody.put("category", 0);
            reqBody.put("keyword", noticeSearchDTO.getKeyword());
            reqBody.put("deleteFlag", false);
            reqBody.put("userCode", currentUser.getUserCode());
            Map<String, Object> unReadNoticeCount = sysContentService.getUnReadNoticeCount(reqBody);

            return JsonResult.success(unReadNoticeCount);
        } catch (Exception e) {
            log.error("未读通告数量查询异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }

    /***
     * // 查看公告并记录
     * <AUTHOR>
     * @date 2023/4/20 14:32
     * @param id 公告id
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "记录公告查看记录")
    @GetMapping("/notification/record")
    public JsonResult<Object> content(@RequestParam int id, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        SysContent sysContent = sysContentService.getById(id);
        if (ObjectUtils.isNotEmpty(sysContent)) {
            SysContentUser sysContentUser = new SysContentUser();
            sysContentUser.setContentId((long) id);
            sysContentUser.setUserId(currentUser.getUserId());
            QueryWrapper<SysContentUser> wrapper = new QueryWrapper<>(sysContentUser);
            SysContentUser contentUser = sysContentUserService.getOne(wrapper);
            if (contentUser == null) {
                Integer viewNum = sysContent.getViewNum();
                if (viewNum == null) {
                    sysContent.setViewNum(1);
                } else {
                    sysContent.setViewNum(viewNum + 1);
                }
                sysContentService.updateById(sysContent);
                sysContentUserService.save(sysContentUser);
            } else {
                sysContentUserService.updateById(sysContentUser);
            }

            return JsonResult.success(sysContent);
        } else {
            return JsonResult.error(ResponseCode.PORTAL_5047);
        }
    }

    /***
     * //  重要通知
     * <AUTHOR>
     * @date 2023/4/21 14:05
     * @param currentUser 登录信息
     * @param noticeSearchDTO 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "重要通知查询")
    @RequestMapping("/getNoticeSearchList")
    public JsonResult<Object> getNoticeSearchList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody NoticeSearchDTO noticeSearchDTO) {
        noticeSearchDTO.setUserId(currentUser.getUserId());
        try {
            noticeSearchDTO.setUserCode(currentUser.getUserCode());
            return sysContentService.getNoticeSearchList(noticeSearchDTO);
        } catch (Exception e) {
            log.error("重要通知查询查询异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }


    @GetMapping("/table")
    @ResponseBody
    public JsonResult<TableVo<NotificationDTO>> noticeTable(String context, @RequestParam String categoryId,
                                                            @RequestParam(defaultValue = "10") Integer limit,
                                                            @RequestParam(defaultValue = "1") Integer currentPage,
                                                            String top, @RequestParam String type) {
        String siteId = "1";
        Integer topInt = null;
        if (!StringUtils.isBlank(top) && !AbnormalConstant.NULL.equals(top)) {
            topInt = Integer.valueOf(top);
        }
        NoticeSearchVo searchVo = new NoticeSearchVo(categoryId, siteId, context, limit, currentPage, topInt, type);
        try {
            searchVo.setUserCode(getUserCode());
            TableVo<NotificationDTO> table = sysContentService.table(searchVo);
            return JsonResult.success(table);
        } catch (Exception e) {
            log.error("通知通告列表查询异常：{}", e.getMessage());
            return JsonResult.success();
        }

    }

    /***
     * //最新通知
     * <AUTHOR>
     * @date 2023/7/3 14:14
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "最新公告")
    @GetMapping("/getNotification")
    public JsonResult<Object> getNotification(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            NotificationDTO popoutNotification = sysContentService.inquirePopoutNotification(currentUser.getUserId());
            return JsonResult.success(popoutNotification);
        } catch (Exception e) {
            log.error("获取最新公告异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }
}
