package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.provider.dto.*;
import com.ywwl.customer.center.modules.common.provider.service.MessageSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * //消息订阅
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
@Validated
@Slf4j
@RequestMapping("/messageSubscription")
@RestController
public class MessageSubscriptionController extends BankController {
    @Resource
    private MessageSubscriptionService messageSubscriptionService;

    /***
     * //  消息订阅列表
     * <AUTHOR>
     * @date 2023/8/16 9:58
     * @param currentUser 登录参数
     * @param subscriptionListDTO 列表参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/list")
    @Logger(module = Module.COMMON, name = "消息订阅列表")
    public JsonResult<Object> list(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody SubscriptionListDTO subscriptionListDTO) {
        try {
            subscriptionListDTO.setNo(currentUser.getMerchantNo());
            subscriptionListDTO.setUserCode(currentUser.getUserCode());
            return messageSubscriptionService.list(subscriptionListDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("消息订阅列表异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("消息订阅列表异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  订阅类型列表
     * <AUTHOR>
     * @date 2023/8/16 10:38
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "订阅类型列表")
    @GetMapping("/typeList")
    public JsonResult<Object> subscriptionTypeList() {
        try {
            return messageSubscriptionService.subscriptionTypeList();
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("订阅类型列表异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("订阅类型列表异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    /***
     * //  新增订阅
     * <AUTHOR>
     * @date 2023/8/16 11:02
     * @param currentUser 登录信息
     * @param subscriptionListDTO  订阅参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/save")
    @Logger(module = Module.COMMON, name = "新增订阅")
    public JsonResult<Object> saveSubscription(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody SubscriptionSaveDTO subscriptionListDTO) {
        try {
            subscriptionListDTO.setCreateId(currentUser.getLoginName());
            return messageSubscriptionService.saveSubscription(subscriptionListDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("新增订阅异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增订阅异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  订阅编辑
     * <AUTHOR>
     * @date 2023/8/16 11:10
     * @param currentUser 登录参数
     * @param subscriptionUpdateDTO 编辑参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/update")
    @Logger(module = Module.COMMON, name = "订阅编辑")
    public JsonResult<Object> updateSubscription(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody SubscriptionUpdateDTO subscriptionUpdateDTO) {
        try {
            subscriptionUpdateDTO.setUpdateId(currentUser.getLoginName());
            return messageSubscriptionService.updateSubscription(subscriptionUpdateDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("订阅编辑异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("订阅编辑异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  取消订阅
     * <AUTHOR>
     * @date 2023/8/16 16:57
     * @param currentUser 登录参数
     * @param subUserId 取消人ID
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/cancel")
    @Logger(module = Module.COMMON, name = "取消订阅")
    public JsonResult<Object> subscriptionCancel(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam @NotNull(message = "用户不得为空") String subUserId) {
        try {
            return messageSubscriptionService.subscriptionCancel(subUserId, currentUser.getLoginName());
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("取消异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("取消异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /**
     * //订阅详情
     *
     * @param currentUser                登录信息
     * @param subscriptionParticularsDTO 订阅参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2023/8/17 9:52
     */
    @Validated
    @PostMapping("/particulars")
    @Logger(module = Module.COMMON, name = "订阅详情")
    public JsonResult<Object> subscriptionCancel(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody SubscriptionParticularsDTO subscriptionParticularsDTO) {
        try {
            return messageSubscriptionService.subscriptionParticulars(subscriptionParticularsDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("订阅详情异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("订阅详情异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //
     * <AUTHOR>
     * @date 2023/8/18 14:47
     * @param currentUser 登录信息
     * @param subscriptionLogDTO  日志参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/logs")
    @Logger(module = Module.COMMON, name = "订阅日志")
    public JsonResult<Object> logs(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody SubscriptionLogDTO subscriptionLogDTO) {
        try {
            return messageSubscriptionService.logs(subscriptionLogDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("订阅日志异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("订阅日志异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  消息通知
     * <AUTHOR>
     * @date 2023/8/18 15:13
     * @param currentUser  登录信息
     * @param pushRecordDTO 推送
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/pushList")
    @Logger(module = Module.COMMON, name = "消息通知列表")
    public JsonResult<Object> pushList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody PushRecordDTO pushRecordDTO) {
        try {
            pushRecordDTO.setSubUserId(String.valueOf(currentUser.getUserId()));
            return messageSubscriptionService.pushList(pushRecordDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("消息通知异常:{}", e.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("消息通知异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  标记已读
     * <AUTHOR>
     * @date 2023/8/18 15:42
     * @param list  列id
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/read")
    @Logger(module = Module.COMMON, name = "标记已读订阅信息")
    public JsonResult<Object> read(@RequestBody @NotEmpty(message = "数据不得为空") List<String> list) {
        try {
            return messageSubscriptionService.read(list);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("标记已读异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("标记已读异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  删除
     * <AUTHOR>
     * @date 2023/8/18 15:42
     * @param list  列id
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/delete")
    @Logger(module = Module.COMMON, name = "删除订阅通知信息")
    public JsonResult<Object> delete(@RequestBody @NotEmpty(message = "数据不得为空") List<String> list) {
        try {
            return messageSubscriptionService.delete(list);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("删除异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("删除异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  消息订阅产品数据
     * <AUTHOR>
     * @date 2024/1/17 15:33
     * @param messageProductDTO 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/productList")
    @Logger(module = Module.COMMON, name = "消息订阅产品数据")
    public JsonResult<Object> getProductList(@RequestBody MessageProductDTO messageProductDTO) {
        try {
            return messageSubscriptionService.product(messageProductDTO);
        } catch (IllegalArgumentException | ResponseCode.ResponseException e) {
            log.error("消息订阅产品数据:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("消息订阅产品数据", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }
}
