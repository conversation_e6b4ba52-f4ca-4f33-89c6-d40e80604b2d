package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.listener.easyexcel.EasyExcelCompliance;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import com.ywwl.customer.center.modules.international.domain.ForecastTemplate;
import com.ywwl.customer.center.modules.international.dto.ForecastPageDTO;
import com.ywwl.customer.center.modules.international.dto.ForecastSaveDTO;
import com.ywwl.customer.center.modules.international.service.ForecastService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * //TODO 重量预报
 *
 * <AUTHOR>
 * @date 2023/2/7
 */
@Slf4j
@RestController
@RequestMapping("/forecast")
public class WeightForecastController extends BaseController {
    @Resource
    private ForecastService forecastService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /***
     * //TODO
     * <AUTHOR>
     * @param currentUser 登录信息
     * @param forecastPageVo  页面查询参数
     * @return com.cmhb.module.base.JsonResult<Object>
     */
    @Validated
    @PostMapping("/list")
    public JsonResult<Object> list(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody ForecastPageDTO forecastPageVo) {
        try {
            ForecastPageDTO.ConditionDTO condition = forecastPageVo.getCondition();
            if (Objects.isNull(condition)) {
                throw ResponseCode.PORTAL_5010.getError();
            }
            condition.setCustomerCode(getPacketApplyInfo(currentUser.getUserCode()));
            return forecastService.forecastList(forecastPageVo);
        } catch (Exception e) {
            log.error("重量预报列表查询异常：{}", e.getMessage() + currentUser.getMerchantNo());
            return JsonResult.error(ResponseCode.PORTAL_5063);
        }
    }

    /***
     * //TODO 模板下载
     * <AUTHOR>
     * @date 2023/2/8 11:05
     * @param response 响应信息
     * @return com.alibaba.fastjson2.JSONObject
     */
    @GetMapping("/downloadTemplate")
    public JsonResult<Object> downloadTemplate(HttpServletResponse response) {
        try {
            return forecastService.downloadTemplate(response);
        } catch (Exception e) {
            log.error("重量预报模板下载异常:{}", e.getMessage());
            return JsonResult.error();
        }
    }


    /***
     * //TODO  导入文件
     * <AUTHOR>
     * @date 2023/2/8 14:25
     * @param file 文件
     * @param currentUser  登录信息
     * @return com.cmhb.module.base.JsonResult<Object>
     */
    @PostMapping("/importTemplate")
    public JsonResult<Object> importData(MultipartFile file, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response, String type) {
        EasyExcelCompliance<ForecastTemplate> list = new EasyExcelCompliance<>();
        try {
            EasyExcel.read(file.getInputStream(), ForecastTemplate.class, list).sheet().headRowNumber(2).doRead();
            List<ForecastTemplate> dataS = list.getDatas();
            boolean numValidated = dataS.size() > AbnormalConstant.NUM;
            if (numValidated) {
                return JsonResult.error(AbnormalConstant.ERROR_IMPORT);
            }
            if (StringUtils.isBlank(type)) {
                return JsonResult.error(AbnormalConstant.UNIT_OF_WEIGHT);
            }
            return forecastService.forecastImportData(dataS, currentUser, response, type);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("导入预报模板异常:{}", e.getMessage() + currentUser.getMerchantNo());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("导入预报模板异常:{}", e.getMessage() + currentUser.getMerchantNo());
            return JsonResult.error(ResponseCode.PORTAL_5063);
        }
    }

    /***
     * //TODO  导出列表数据
     * <AUTHOR>
     * @param currentUser  登录信息
     * @param forecastPageVo 查询参数
     * @param response  浏览器
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Validated
    @PostMapping("/downloadList")
    public JsonResult<Object> downloadList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody ForecastPageDTO forecastPageVo, HttpServletResponse response) {
        try {
            ForecastPageDTO.ConditionDTO condition = forecastPageVo.getCondition();
            if (Objects.isNull(condition)) {
                throw ResponseCode.PORTAL_5010.getError();
            }
            condition.setCustomerCode(getPacketApplyInfo(currentUser.getUserCode()));
            return forecastService.downloadList(forecastPageVo, response);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("导出列表数据异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("重量预报列表数据下载异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5063);
        }
    }

    public String getPacketApplyInfo(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo.getMerchantCode();
    }

    /***
     * //新增重量预报
     * <AUTHOR>
     * @date 2023/9/19 9:44
     * @param currentUser  登录信息
     * @param forecastSaveDTO  新增信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/save")
    public JsonResult<Object> saveForecast(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody ForecastSaveDTO forecastSaveDTO) {
        try {

            return forecastService.saveForecast(currentUser, forecastSaveDTO);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("新增重量预报异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增重量预报异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5063);
        }
    }
}
