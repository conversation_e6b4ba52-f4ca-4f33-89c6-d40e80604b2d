package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.modules.international.dto.*;
import com.ywwl.customer.center.modules.international.service.AddressService;
import com.ywwl.customer.center.modules.international.service.DispatchService;
import com.ywwl.customer.center.modules.international.vo.AddressListVo;
import com.ywwl.customer.center.modules.international.vo.CollectVo;
import com.ywwl.customer.center.modules.international.vo.ReturnAddressVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * // 地址
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/multiAddress")
public class AddressController extends BaseController {
    @Resource
    private DispatchService dispatchService;
    @Resource
    private AddressService addressService;
    @Resource
    private CrmService crmService;

    /***
     * //新增/编辑/删除 多地址
     * <AUTHOR>
     * @date 2023/3/22 10:04
     * @param currentUser 登录信息
     * @param addressDTO 操作参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "地址库新增")
    @PostMapping("/new")
    public JsonResult<Object> saveAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody AddressDTO addressDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .provinceName(addressDTO.getProvinceName())
                        .cityName(addressDTO.getCityName())
                        .areaName(addressDTO.getAreaName())
                        .address(addressDTO.getAddress())
                        .desc("新增退件地址")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        addressDTO.setMethod("A");
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        return addressService.saveAndUpdateAddress(addressDTO);
    }

    @Logger(module = Module.SMALL_PACKAGE, name = "地址库更新")
    @PostMapping("/update")
    public JsonResult<Object> updateAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody AddressDTO addressDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .provinceName(addressDTO.getProvinceName())
                        .cityName(addressDTO.getCityName())
                        .areaName(addressDTO.getAreaName())
                        .address(addressDTO.getAddress())
                        .desc("修改退件地址")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        addressDTO.setMethod("U");
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        return addressService.saveAndUpdateAddress(addressDTO);
    }

    @Logger(module = Module.SMALL_PACKAGE, name = "地址库删除")
    @PostMapping("/delete")
    public JsonResult<Object> deleteAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody DeleteAddressDTO addressDTO) {
        addressDTO.setMethod("D");
        addressDTO.setUserCode(currentUser.getUserCode());
        addressDTO.setOperator(currentUser.getLoginName());
        return addressService.deleteAddress(addressDTO);
    }

    /***
     * // 查询多地址信息
     * <AUTHOR>
     * @date 2023/3/22 10:15
     * @param currentUser 登录参数
     * @param addressQueryDTO  查询条件
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.AddressListVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "地址库列表")
    @PostMapping("/list")
    public JsonResult<AddressListVo> listAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody AddressQueryDTO addressQueryDTO) {
        addressQueryDTO.setUserCode(currentUser.getUserCode());
        return addressService.listAddress(addressQueryDTO);
    }

    /***
     * //  批量删除多地址信息
     * <AUTHOR>
     * @date 2023/3/22 10:25
     * @param currentUser 登录信息
     * @param addressDeleteDTO 删除参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "地址库批量删除")
    @PostMapping("/batchDelete")
    public JsonResult<Object> batchDeleteAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody AddressDeleteDTO addressDeleteDTO) {
        addressDeleteDTO.setOperator(currentUser.getLoginName());
        return addressService.batchDeleteAddress(addressDeleteDTO);
    }

    /***
     * //TODO 校验围栏地址
     * <AUTHOR>
     * @date 2022/9/1 13:33
     * @param
     * fenceVo
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "校验围栏地址")
    @PostMapping("/verifyFence")
    public JsonResult<Object> verifyFence(@RequestBody @Valid FenceDTO fenceVo) {
        try {
            return dispatchService.verifyFence(fenceVo);
        } catch (Exception e) {
            log.error("校验围栏地址异常:{}", fenceVo);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  校验省市区名字
     * <AUTHOR>
     * @date 2022/9/2 11:10
     * @param siteVo 参数
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "校验省市区")
    @PostMapping("/verifySite")
    public JsonResult<Object> verifySite(@RequestBody @Valid SiteDTO siteVo) {
        try {
            return dispatchService.verifySite(siteVo);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("校验省市区名字异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }
    /***
     * //根据仓查询揽收点
     * <AUTHOR>
     * @date 2024/1/2 13:37
     * @param code 仓代码
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "查询所有揽收点")
    @PostMapping("/dropOffPoint")
    @Validated
    public JsonResult<Object> getDropOffPoint(@JsonParam @NotNull(message = "揽收仓不得为空") String code) {
        try {
            List<CollectVo> warehouseList = dispatchService.getDropOffPointList(code);
            return JsonResult.success(warehouseList);
        } catch (Exception e) {
            log.error("当前查询调度揽收点异常，异常原因", e);
            return JsonResult.success();
        }
    }

    // 获取退件地址信息
    @Logger(module = Module.SMALL_PACKAGE, name = "根据客户号和退件方式查询退件地址")
    @RequestMapping("getReturnAddressByType")
    public JsonResult getReturnAddressByType(@RequestBody QueryReturnAddressDTO queryReturnAddressDTO){
        queryReturnAddressDTO.setUserCode(queryReturnAddressDTO.getUserCode());
        ReturnAddressVo returnAddressVo=addressService.getReturnAddressByType(queryReturnAddressDTO);
        return JsonResult.success(returnAddressVo);
    }
}
