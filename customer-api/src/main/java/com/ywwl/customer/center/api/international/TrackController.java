// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.international;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.ejf.dto.TrackInfoDTO;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.international.dto.TailStrokeDTO;
import com.ywwl.customer.center.modules.international.dto.TrackStatisticsReqDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.TrackService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.international.vo.TailStrokeVo;
import com.ywwl.customer.center.modules.international.vo.TrackStatisticsRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 16:01
 * @ModifyDate 2023/3/14 16:01
 * @Version 1.0
 */
@Slf4j
@RequestMapping("/track")
@RestController
public class TrackController extends BaseController {

    @Resource
    private TrackService trackService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private AccountService accountService;

    @Resource
    OrderService orderService;

    /**
     * 时效统计
     *
     * @param currentUser
     * @param trackStatisticsReqDTO
     * @return
     */
    @PostMapping("/trackStatistics")
    public JsonResult trackStatistics(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid TrackStatisticsReqDTO trackStatisticsReqDTO) {
        trackStatisticsReqDTO.setUserCode(currentUser.getUserCode());
        accountService.existAccountThrow(trackStatisticsReqDTO.getCustomerCodeList());
        JsonResult<TrackStatisticsRespVO> result = trackService.getDeliveredExpressData(trackStatisticsReqDTO);
        return result;
    }


    /**
     * 待领取运单
     *
     * @param currentUser
     * @param tailStrokeDTO
     * @return
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "待领取运单列表")
    @PostMapping("/notTake")
    public JsonResult<Object> getPageList(@Validated @RequestBody TailStrokeDTO tailStrokeDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        log.info("分页查询追踪数据前端入参:{}", tailStrokeDTO);
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start("分页查询追踪数据-列表");
            List<String> accountList = tailStrokeDTO.getAccountList();
            if (!accountService.existAccount(accountList, currentUser, 0)) {
                return JsonResult.error(ResponseCode.PORTAL_5060);
            }
            tailStrokeDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
            TailStrokeVo tailStroke = trackService.getTailStroke(tailStrokeDTO);
            stopWatch.stop();
            log.info("分页查询追踪数据总耗时为:{}", stopWatch.getTotalTimeMillis());
            return JsonResult.success(tailStroke.getData());
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            return JsonResult.error(ResponseCode.PORTAL_5063);
        }

    }


    /***
     * //TODO  记录已读运单
     * <AUTHOR>
     * @date 2022/5/7 17:32
     * @param list
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "记录已读运单")
    @PostMapping("/recordTheWaybill")
    @ResponseBody
    public JsonResult<Object> recordTheWaybill(@RequestBody List<String> list) {
        log.info("记录已读运单前端入参:{}", list);
        return trackService.recordTheWaybill(list);

    }

    /**
     * 导出待领取运单
     *
     * @param tailStrokeDTO 入参
     * @param currentUser   登录信息
     * @param response      响应信息
     * @return com.alibaba.fastjson2.JSONObject
     * <AUTHOR>
     * @date 2022/8/23 15:57
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "导出运单")
    @PostMapping("/export/toTakeDetail")
    @ResponseBody
    public void export(@Validated @RequestBody TailStrokeDTO tailStrokeDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) {
        try {
            tailStrokeDTO.setCurrent(1);
            List<String> accountList = tailStrokeDTO.getAccountList();
            if (!accountService.existAccount(accountList, currentUser, 0)) {
                throw ResponseCode.PORTAL_5060.getError();
            }
            tailStrokeDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
             trackService.export(tailStrokeDTO, currentUser, response);
        } catch (BusinessException e) {
            log.error("导出待领取运单异常:{}", e.getMessage());
            throw new BusinessException(e.getMessage());
        } catch (Exception e) {
            log.error("导出待领取运单异常:{}", e.getMessage());
            throw ResponseCode.PORTAL_5029.getError();
        }
    }

    /***
     * //  通过运单号查询
     * <AUTHOR>
     * @date 2023/4/14 9:35
     * @param waybillNumber 运单号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @RequestMapping("checkPoint/{waybillNumber}")
    @ResponseBody
    public JsonResult<Object> getTracking(@PathVariable(value = "waybillNumber") @Validated @NotBlank(message = "运单号不得为空") String waybillNumber) {
        return trackService.getTracking(waybillNumber);
    }

    public String getMerchantCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo.getMerchantCode();
    }

    /**
     * 获取路线结果集
     *
     * @param userId        userId
     * @param waybillNumber 运单号
     * @return              结果
     */
    @PostMapping("/getCheckPoint")
    public JsonResult<?> getCheckPoint(@JsonParam @NotBlank(message = "运单号不得为空") String userId,
                                       @JsonParam @NotBlank(message = "运单号不得为空") String waybillNumber) {
        List<TrackInfoDTO.ResultDTO.CheckpointsDTO> checkPoint = orderService.getCheckPoint(userId, waybillNumber);
        if (CollectionUtil.isEmpty(checkPoint)) {
            checkPoint = orderService.getCheckPoint(trackAuthorization, waybillNumber);
        }
        return JsonResult.success(checkPoint);
    }

    /**
     * 追踪验证
     */
    @Value("${track.Authorization}")
    String trackAuthorization;

}
