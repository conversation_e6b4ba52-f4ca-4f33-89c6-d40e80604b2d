package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.general.cmcc.dto.CurrencyRateDTO;
import com.ywwl.customer.center.modules.general.cmcc.dto.QueryAddressDTO;
import com.ywwl.customer.center.modules.general.cmcc.dto.QueryCityPostCodeDTO;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @date: 2023/3/13 11:27
 */
@RequestMapping("common")
@RestController
public class CmccController {
    @Resource
    private CmccService cmccService;

    /**
     * @author: dinghy
     * @createTime: 2024/12/5 11:17
     * @description: 查询城市邮编列表
     */
    @PostMapping("getCityPostCodeList")
    public JsonResult getCityPostCodeList(@Valid @RequestBody QueryCityPostCodeDTO queryCityPostCodeDTO) {
        if (StringUtils.isBlank(queryCityPostCodeDTO.getPostCode()) && StringUtils.isBlank(queryCityPostCodeDTO.getCityName())) {
            return JsonResult.error("邮编和城市至少输入一项");
        }
        List<CityPostCodeVo> cityPostCodeList = cmccService.getCityPostCodeList(queryCityPostCodeDTO);
        String dtoCountryName = queryCityPostCodeDTO.getCountryName();
        String dtoCityName = queryCityPostCodeDTO.getCityName();
        String dtoPostCode = queryCityPostCodeDTO.getPostCode();
        List<CityPostCodeVo> list=cityPostCodeList.stream()
                .filter(x -> {
                    if(!x.getIsEnable().equals(1)){
                        return false;
                    }
                    String countryName = x.getCountryName();
                    String cityName = x.getCityName();
                    String postalName = x.getPostalName();
                    if (!dtoCountryName.equals(countryName)) {
                        return false;
                    }
                    if(StringUtils.isNotBlank(dtoCityName)&&StringUtils.isNotBlank(dtoPostCode)){
                        return dtoCityName.equals(cityName) && dtoPostCode.equals(postalName);
                    }
                    if (StringUtils.isNotBlank(dtoPostCode) && dtoPostCode.equals(postalName)) {
                        return true;
                    }
                    if (StringUtils.isNotBlank(dtoCityName) && dtoCityName.equals(cityName)) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

        return JsonResult.success(list);
    }

    /**
     * <AUTHOR>
     * @description 根据类型查询仓库
     * @date 2023/3/13 11:30
     **/
    @GetMapping("getWarehouseByType/{type}")
    public JsonResult<Object> getWarehouseByType(@PathVariable Integer type) {
        List<WareHouseVo> warehouses = cmccService.getWarehouseByType(type);
        return JsonResult.success(warehouses);
    }

    /**
     * <AUTHOR>
     * @description 查询省市区
     * @date 2023/3/13 13:34
     **/
    @PostMapping("getCountryCodeList")
    public JsonResult<Object> getCountryCodeList(@RequestBody QueryAddressDTO queryAddressDTO) {
        Collection<AddressVo> addressList = cmccService.getAddressList(queryAddressDTO);
        return JsonResult.success(addressList);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/2 13:39
     * @description: 查询汇率
     */
    @PostMapping("getCurrencyRateList")
    public JsonResult getCurrencyRateList(@Valid @RequestBody CurrencyRateDTO currencyRateDTO){
        List<CurrencyRateVO> cmccCurrencyRateList = cmccService.getWeeklyCurrencyRateList(currencyRateDTO);
        return JsonResult.success(cmccCurrencyRateList);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/6/9 17:48
     * @description: 查询异常件二级分类
     */
    @GetMapping("getAbnormalTypeList")
    public JsonResult getAbnormalTypeList(){
        List<AbnormalTypeVO> list=cmccService.getAbnormalTypeCache();
        return JsonResult.success(list);
    }
}
