package com.ywwl.customer.center.web.Interceptor;

import com.alibaba.fastjson2.JSON;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAuthStateEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerAuthStateVo;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/3/24 15:49
 */
public class CustomerAuthInterceptor implements HandlerInterceptor {
    @Resource
    private CustomerAuthService customerAuthService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            UserAgent userAgent = JsonUtils.parse((String) principal, UserAgent.class);
           if(Objects.nonNull(userAgent)){
               CustomerAuthStateVo authState = customerAuthService.getAuthState(userAgent.getUserCode());
               if (!CustomerAuthStateEnum.SUCCESS.getValue().equals(authState.getAuthState())&&!CustomerAuthStateEnum.COMPLETE_UN_LIMIT.getValue().equals(authState.getAuthState())) {
                   response.setContentType("application/json");
                   response.setCharacterEncoding("utf-8");
                   response.getWriter().write(JSON.toJSONString(JsonResult.error(ResponseCode.PORTAL_402)));
                   return false;
               }
           }
        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }
}
