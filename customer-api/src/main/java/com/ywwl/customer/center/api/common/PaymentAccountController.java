package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.domain.PaymentAccount;
import com.ywwl.customer.center.modules.common.provider.dto.AlipayCheckDTO;
import com.ywwl.customer.center.modules.common.provider.dto.EnterpriseVerifyDTO;
import com.ywwl.customer.center.modules.common.provider.dto.RecordSignDTO;
import com.ywwl.customer.center.modules.common.provider.service.PaymentAccountService;
import com.ywwl.customer.center.modules.common.provider.vo.PaymentAccountNum;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.vo.FbaApplyInfoVO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.YWEApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Objects;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/5/4
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/payment")
public class PaymentAccountController extends BaseController {
    @Resource
    private PaymentAccountService paymentAccountService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private FbaApplyService fbaApplyService;
    @Resource
    private YWEOverseaService ywEOverseaService;

    /***
     * //列表查询
     * <AUTHOR>
     * @date 2023/5/4 17:00
     * @param currentUser 登录信息
     * @param status 查询状态
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.util.List < com.ywwl.customer.center.modules.common.provider.domain.PaymentAccount>>
     */
    @PostMapping("list")
    @Logger(module = Module.COMMON, name = "付款账号列表")
    public JsonResult<PaymentAccountNum> paymentAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam String status, @Validated @NotBlank(message = "业务类型不得为空") @JsonParam String businessType) {
        try {
            String merchantCode = getMerchantCode(currentUser.getUserCode(), Integer.valueOf(businessType));
            return paymentAccountService.paymentList(merchantCode, status, businessType);
        } catch (BusinessException e) {
            log.error("付款账号列表查询异常:{}", e.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("付款账号列表查询异常:{}", e.getMessage());
            return JsonResult.error();
        }
    }

    /***
     * //新增付款账号
     * <AUTHOR>
     * @date 2023/5/8 13:49
     * @param currentUser 登录信息
     * @param paymentAccount  账号信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/create")
    @Validated
    @Idempotent
    @Logger(module = Module.COMMON, name = "新增付款账号")
    public JsonResult<Object> createPaymentAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody PaymentAccount paymentAccount) {
        try {
            paymentAccount.setNo(currentUser.getMerchantNo());
            return paymentAccountService.createPaymentAccount(paymentAccount, currentUser.getUserCode());
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("新增付款账号异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("新增付款账号异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    public String getMerchantCode(String userCode, Integer businessType) {
        String merchantCode = null;
        if (BusinessTypeEnum.STRAIGHT.getValue().equals(businessType)) {
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
            if (Objects.isNull(packetApplyInfo) || StringUtils.isEmpty(packetApplyInfo.getMerchantCode())) {
                throw new BusinessException(ResponseCode.PORTAL_6103);
            }
            merchantCode = packetApplyInfo.getMerchantCode();
        } else if (BusinessTypeEnum.FBA.getValue().equals(businessType)) {
            FbaApplyInfoVO fbaApplySimpleInfo = fbaApplyService.getFbaApplySimpleInfo(userCode);
            if (Objects.isNull(fbaApplySimpleInfo) || StringUtils.isEmpty(fbaApplySimpleInfo.getAccountCode())) {
                throw new BusinessException(ResponseCode.PORTAL_6103);
            }
            merchantCode = fbaApplySimpleInfo.getAccountCode();
        }else if (BusinessTypeEnum.OVERSEA.getValue().equals(businessType)||BusinessTypeEnum.YWE_WAREHOUSE.getValue().equals(businessType)) {
            YWEApplyInfoVo yweApplyAllInfoVo = ywEOverseaService.getYWEApplyAllInfoVo(userCode);
            if (Objects.isNull(yweApplyAllInfoVo) || StringUtils.isEmpty(yweApplyAllInfoVo.getMerchantCode())){
                throw new BusinessException(ResponseCode.PORTAL_6103);
            }
            merchantCode = yweApplyAllInfoVo.getMerchantCode();
        }
        return merchantCode;
    }

    /***
     * //客户补齐委托书
     * <AUTHOR>
     * @date 2023/5/9 10:14
     * @param currentUser 登录信息
     * @param paymentAccount 付款信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Idempotent
    @PostMapping("/customerUpdate")
    @Validated
    @Logger(module = Module.COMMON, name = "客户补齐委托书")
    public JsonResult<Object> clientPolishing(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody PaymentAccount paymentAccount) {
        try {
            paymentAccount.setNo(currentUser.getMerchantNo());
            paymentAccount.setBusinessTypes(Collections.singletonList(paymentAccount.getBusinessType()));
            return paymentAccountService.clientPolishing(paymentAccount, currentUser.getUserCode());
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("客户补齐委托书异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("客户补齐委托书异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //确认付款账号展示
     * <AUTHOR>
     * @date 2023/5/9 14:13
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/confirmList")
    @Logger(module = Module.COMMON, name = "确认付款列表")
    public JsonResult<Object> confirmPaymentList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return paymentAccountService.confirmPaymentList(currentUser.getUserCode());
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("确认付款列表异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("确认付款列表异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //客服发起补齐账号展示
     * <AUTHOR>
     * @date 2023/5/9 14:13
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/serviceList")
    @Logger(module = Module.COMMON, name = "客服发起付款列表")
    public JsonResult<Object> servicePaymentList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return paymentAccountService.servicePaymentList(currentUser.getUserCode());
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("客服发起付款列表异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("客服发起付款列表异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  确认付款账号提交
     * <AUTHOR>
     * @date 2023/5/9 15:50
     * @param currentUser 登录信息
     * @param paymentAccount 提交信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Idempotent
    @PostMapping("/confirmSubmit")
    @Logger(module = Module.COMMON, name = "确认付款账号提交")
    public JsonResult<Object> confirmSubmit(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody PaymentAccount paymentAccount) {
        try {
            paymentAccount.setNo(currentUser.getMerchantNo());
            return paymentAccountService.confirmSubmit(paymentAccount, currentUser.getUserCode());
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("确认付款账号提交异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("确认付款账号提交异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * // 无效账号
     * <AUTHOR>
     * @date 2023/5/9 16:32
     * @param paymentAccount  信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/deleteAccount")
    @Logger(module = Module.COMMON, name = "无效账号")
    public JsonResult<Object> deleteAccount(@RequestBody PaymentAccount paymentAccount) {
        return paymentAccountService.deleteAccount(paymentAccount);
    }

    /***
     * //  客服补齐账号提交
     * <AUTHOR>
     * @date 2023/5/9 15:50
     * @param currentUser 登录信息
     * @param paymentAccount 提交信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Idempotent
    @PostMapping("/serviceSubmit")
    @Logger(module = Module.COMMON, name = "客服补齐账号提交")
    public JsonResult<Object> serviceSubmit(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody PaymentAccount paymentAccount) {
        try {
            return paymentAccountService.serviceSubmit(paymentAccount, currentUser);
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("客服补齐账号提交提交异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("客服补齐账号提交提交异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * // 逆向脱敏
     * <AUTHOR>
     * @date 2023/5/10 11:05
     * @param paymentAccount 查询参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/reverseDesensitization")
    @Logger(module = Module.COMMON, name = "逆向脱敏")
    public JsonResult<Object> reverseDesensitization(@RequestBody PaymentAccount paymentAccount, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        return paymentAccountService.reverseDesensitization(paymentAccount, currentUser.getUserCode());
    }

    /***
     * <AUTHOR>
     * @description 获取二维码
     * @date 2023/5/10 13:51
     **/
    @Logger(module = Module.COMMON, name = "获取二维码")
    @PostMapping("getQrCode")
    public JsonResult<Object> getQrCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotNull(message = "参数不能为空") @JsonParam Integer id) {
        return paymentAccountService.getQrCode(id, currentUser.getUserCode());
    }

    /**
     * <AUTHOR>
     * @description 获取付款委托书签署地址
     * @date 2023/5/10 13:53
     **/
    @Logger(module = Module.CONTRACT, name = "获取付款委托书签署地址")
    @RequestMapping("getSignUrl")
    public JsonResult<Object> getSignPaymentBookUrl(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotNull(message = "参数不能为空") @JsonParam Integer id, @JsonParam Boolean viewContract) {
        Assert.isTrue(getUser().isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        return paymentAccountService.getSignPaymentBookUrl(id, currentUser,viewContract);
    }

    /**
     * <AUTHOR>
     * @description 查询支付宝订单状态
     * @date 2023/5/10 15:23
     **/
    @PostMapping("getAlipayOrderStatus")
    @ResponseBody
    public JsonResult getAlipayOrderStatus(@NotNull(message = "参数不能为空") @JsonParam String id) {
        return paymentAccountService.getAlipayOrderStatus(id);
    }

    /***
     * //备案失败以及第三方打回重新提交
     * <AUTHOR>
     * @date 2023/5/10 15:56
     * @param paymentAccount 信息
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Idempotent
    @RequestMapping("/editAgain")
    @Logger(module = Module.COMMON, name = "客服补齐账号提交")
    public JsonResult<Object> editAgain(@RequestBody PaymentAccount paymentAccount, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        paymentAccount.setNo(currentUser.getMerchantNo());
        paymentAccount.setBusinessTypes(Collections.singletonList(paymentAccount.getBusinessType()));
        return paymentAccountService.editAgain(paymentAccount, currentUser.getUserCode());
    }

    /***
     * //随机编码
     * <AUTHOR>
     * @date 2023/5/15 16:21
     * @param paymentAccount 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.String>
     */
    @Logger(module = Module.COMMON, name = "随机编码")
    @PostMapping("/code")
    public JsonResult<String> code(@RequestBody PaymentAccount paymentAccount, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = getMerchantCode(currentUser.getUserCode(), paymentAccount.getBusinessType());
        paymentAccount.setMerchantCode(merchantCode);
        String code = paymentAccountService.soleCode(paymentAccount);
        return JsonResult.success(code);
    }

    /***
     * //  企业三要素信息
     * <AUTHOR>
     * @date 2023/8/24 10:32
     * @param enterpriseVerify 三要素
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/businessVerification")
    public JsonResult<Object> businessVerification(@RequestBody EnterpriseVerifyDTO enterpriseVerify) {
        try {
            return paymentAccountService.businessVerification(enterpriseVerify);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("企业工商验证异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("企业工商验证异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    /***
     * // 查询支行信息
     * <AUTHOR>
     * @date 2023/8/24 13:40
     * @param flowId  流程id
     * @param keyword  关键字
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/branchBankList")
    public JsonResult<Object> getBranchBankList(@NotNull(message = "流程id不得为空") @JsonParam String flowId, @NotNull(message = "银行不得为空") @JsonParam String keyword) {
        try {
            return paymentAccountService.getBranchBankList(flowId, keyword);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("查询支行信息异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询支行信息异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查询企业银行卡认证信息
     * <AUTHOR>
     * @date 2023/8/24 13:46
     * @param flowId 流程id
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/companyBankAuthInfo")
    public JsonResult<Object> getCompanyBankAuthInfo(@NotNull(message = "流程id不得为空") @JsonParam String flowId, @NotNull(message = "业务类型不得为空") @JsonParam Integer businessType) {
        try {
            return paymentAccountService.getCompanyBankAuthInfo(flowId, businessType);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("查询企业银行卡认证信息异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询企业银行卡认证信息异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  校验金额
     * <AUTHOR>
     * @date 2023/8/24 13:53
     * @param flowId  流程id
     * @param amount  金额
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Idempotent(timeout = 3)
    @Validated
    @PostMapping("/verifyTransferAmount")
    public JsonResult<Object> verifyTransferAmount(@NotNull(message = "流程id不得为空") @JsonParam String flowId, @NotNull(message = "金额不得为空") @JsonParam String amount) {
        try {
            return paymentAccountService.verifyTransferAmount(flowId, amount);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("校验金额异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("校验金额异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * // 支付宝账号校验重复账号
     * <AUTHOR>
     * @date 2023/9/18 10:20
     * @param alipayCheckDTO 卡号
     * @param currentUser 登录信息
     * @return com.cmhb.common.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/alipayCheck")
    public JsonResult<Object> alipayCheck(@RequestBody AlipayCheckDTO alipayCheckDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return paymentAccountService.alipayCheck(alipayCheckDTO.getBankCard(), currentUser, alipayCheckDTO.getBusinessType(), alipayCheckDTO.getId());
        } catch (BusinessException e) {
            log.error("支付宝账号校验重复账号异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("支付宝账号校验重复账号异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //
     * <AUTHOR>
     * @date 2023/10/7 14:17
     * @param paymentAccount 账号信息
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/revalidateBank")
    public JsonResult<Object> revalidateBank(@RequestBody PaymentAccount paymentAccount, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return paymentAccountService.revalidateBank(paymentAccount, currentUser);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("银行打款重新验证异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("银行打款重新验证异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /**
     * @author: dinghy 
     * @createTime: 2024/2/26 11:31
     * @description: 查询备案银行卡，获取合同地址
     */
    @Idempotent
    @PostMapping("contractAndRecord")
    public JsonResult contractAndRecord(@NotNull(message = "业务类型不能为空") @JsonParam Integer businessType,@JsonParam Boolean viewContract){
        Assert.isTrue(getUser().isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        return paymentAccountService.contractAndRecord(getUser(),businessType,viewContract);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/2/26 15:42
     * @description: 个人银行卡备案，签约
     */
    @Idempotent
    @PostMapping("getPersonRecordSign")
    public JsonResult getPersonRecordSign(@Valid @RequestBody RecordSignDTO recordSignDTO){
        Assert.isTrue(getUser().isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        return paymentAccountService.getRecordSign(getUser(),recordSignDTO);
    }

}
