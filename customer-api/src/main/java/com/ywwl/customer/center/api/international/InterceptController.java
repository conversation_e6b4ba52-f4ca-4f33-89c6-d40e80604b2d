// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.international;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.international.dto.*;
import com.ywwl.customer.center.modules.international.service.WaybillInterceptService;
import com.ywwl.customer.center.modules.international.vo.InterceptExpressListVo;
import com.ywwl.customer.center.modules.international.vo.InterceptExpressVo;
import com.ywwl.customer.center.modules.international.vo.TmsInterceptStatusVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ywwl.customer.center.framework.enums.Module.COMMON;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/29 17:45
 * @ModifyDate 2023/3/29 17:45
 * @Version 1.0
 */
@RequestMapping("/intercept")
@RestController
public class InterceptController extends BaseController{

    public final static Integer MAX_COUNTS = 50;
    /**
     * 申请截留字段
     */
    private static final Integer PORTAL = 0;

    @Resource
    private WaybillInterceptService waybillInterceptService;

    /**
     * 根据运单号获取运单信息
     * @param interceptCodeDTO
     * @return
     */
    @PostMapping("/getExpressByNumbers")
    public JsonResult<List<InterceptExpressVo>> getExpressByNumbers(@RequestBody InterceptCodeDTO interceptCodeDTO){
        if(Objects.isNull(interceptCodeDTO)){
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }
        // 去掉空字段
        interceptCodeDTO.setEpCodes(interceptCodeDTO.getEpCodes().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(interceptCodeDTO.getEpCodes())) {
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        if(CollectionUtils.size(interceptCodeDTO.getEpCodes()) > MAX_COUNTS){
            return JsonResult.error(ResponseCode.PORTAL_6101.getMessage().concat(String.valueOf(MAX_COUNTS)));
        }

        InterceptExpressListVo interceptExpressListVo = waybillInterceptService.loadDatasByEJF(interceptCodeDTO.getEpCodes());
        return JsonResult.success(interceptExpressListVo.getExpresses());
    }


    /**
     * 创建截留
     * @param interceptOrders   EJF包装字段
     * @return  截留响应
     */
    @PostMapping(value = "/createInterceptByWayBillNo")
    @Logger(module = Module.EJF,name = "申请截留", type = Type.WAYBILL_NUMBER, req = "wayBillNos")
    public JsonResult<?> createIntercept(@RequestBody InterceptOrdersDTO interceptOrders) {
        List<InterceptOrderDTO> interceptOrder = interceptOrders.getInterceptOrder();
        InterceptCreateDTO interceptResDto = new InterceptCreateDTO();
        interceptResDto.setSourceType(PORTAL);
        interceptResDto.setDetail(interceptOrder);
        JsonResult<?> intercept = createIntercept(interceptResDto);
        if (intercept.getSuccess()) {
            return JsonResult.success(intercept.getMessage());
        }
        return JsonResult.error(intercept.getMessage());
    }

    @PostMapping(value = "/cancelInterceptEJF")
    @Logger(module = Module.EJF,name = "取消截留", type = Type.WAYBILL_NUMBER, req = "wayBillNo")
    public JsonResult<?> cancelInterceptEJF(@RequestBody InterceptCancelDTO interceptCancelDTO) {
        JsonResult<?> jsonResult = cancelIntercept(interceptCancelDTO);
        if (jsonResult.getSuccess()) {
            return JsonResult.success("取消截留成功，请稍后再查询运单", null);
        }
        return JsonResult.error(jsonResult.getMessage());
    }

    /**
     * 创建截留
     * @param interceptCreateDTO 截留请求
     * @return 截留响应,截留页面截留
     */
    @PostMapping("/createIntercept")
    @Logger(module = Module.EJF,name = "申请截留", type = Type.WAYBILL_NUMBER, req = "detail.wayBillNo")
    public JsonResult<?> createIntercept( @RequestBody InterceptCreateDTO interceptCreateDTO){
        if(Objects.isNull(interceptCreateDTO) || CollectionUtils.isEmpty(interceptCreateDTO.getDetail())){
            return JsonResult.error(ResponseCode.PORTAL_6102.getMessage());
        }

        JSONObject result;
        try {
            result = waybillInterceptService.createIntercept(interceptCreateDTO);
        }catch (BusinessException be){
            return JsonResult.error(be.getMessage());
        }

        if(Objects.isNull(result)){
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        if(!result.getBoolean("result")){
            return JsonResult.error(result.getString("message"));
        }

        return JsonResult.success(result.getString("message"));
    }


    /**
     * 取消截留
     * @param interceptCancelDTO
     * @return
     */
    @PostMapping("/cancelIntercept")
    @Logger(module = COMMON,name = "取消截留",type = Type.WAYBILL_NUMBER, req = "wayBillNo")
    public JsonResult<?> cancelIntercept( @RequestBody InterceptCancelDTO interceptCancelDTO){
        if(Objects.isNull(interceptCancelDTO) || CollectionUtils.isEmpty(interceptCancelDTO.getWayBillNo())){
            return JsonResult.error(ResponseCode.PORTAL_6102.getMessage());
        }

        JSONObject result;
        try {
            result = waybillInterceptService.cancellIntercept(interceptCancelDTO);
        }catch (BusinessException be){
            return JsonResult.error(be.getMessage());
        }

        if(Objects.isNull(result)){
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        if(!result.getBoolean("result")){
            return JsonResult.error(result.getString("message"));
        }

        return JsonResult.success(result.getString("message"));
    }


    /**
     * 查询当前单号下的截留日志
     * @param epCode
     * @return
     */
    @GetMapping(value = "/interceptLog/{epCode}")
    public JsonResult interceptLog(@PathVariable String epCode) {
        if (StringUtils.isBlank(epCode)) {
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        JSONObject result = waybillInterceptService.interceptLog(epCode);

        if (Objects.isNull(result) || !result.getBoolean("result")) {
            return JsonResult.error(result.getString("message"));
        }

        return JsonResult.success(result.getString("message"),result.getJSONArray("data"));
    }


    /**
     * 根据运单号获取tms最新申请状态信息
     * @param interceptCodeDTO
     * @return
     */
    @PostMapping(value = "/getTmsStatusByNumbers")
    public JsonResult<TmsInterceptStatusVo> getTmsStatusByNumbers(@RequestBody InterceptCodeDTO interceptCodeDTO) {
        if (Objects.isNull(interceptCodeDTO) || CollectionUtils.isEmpty(interceptCodeDTO.getEpCodes())) {
            return JsonResult.error(ResponseCode.PORTAL_6102.getMessage());
        }

        if(CollectionUtils.size(interceptCodeDTO.getEpCodes()) > MAX_COUNTS){
            return JsonResult.error(ResponseCode.PORTAL_6101.getMessage().concat(String.valueOf(MAX_COUNTS)));
        }

        TmsInterceptStatusVo vo = waybillInterceptService.loadExpressTmsStatus(interceptCodeDTO.getEpCodes());

        return JsonResult.success(vo);
    }

}
