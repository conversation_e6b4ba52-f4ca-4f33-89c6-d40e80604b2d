// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.common;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/15 11:18
 * @ModifyDate 2023/3/15 11:18
 * @Version 1.0
 */
public class TestDemo{
    public static void main(String[] args) {
        Map<String,Object> map = new HashMap<String,Object>();

        Map<String,Object> map2 = null;

        map.put("name1","jack");
        map.put("name2",null);

        String abc = (String) Optional.ofNullable(map2).map(value-> value.get("name1")).orElse("message");
        System.out.println(abc);
    }
}
