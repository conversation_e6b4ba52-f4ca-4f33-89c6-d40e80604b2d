package com.ywwl.customer.center.api.ejf;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.ejf.entity.*;
import com.ywwl.customer.center.modules.ejf.service.RestrictedInformationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *
 * <AUTHOR>
 * @date 2022/12/23 10:15
 **/
@RequestMapping("/ejf/limit")
@RestController
public class RestrictedInformationController {

	@Resource
	RestrictedInformationService serviceInf;


	@RequestMapping("/nameRestriction")
	@Logger(module = Module.EJF,name = "限制信息查询")
	@Validated
	public JsonResult<?> nameRestriction(@RequestBody GoodsNameRequestType param) {
		GetGoodsNameLimitResponseType result = serviceInf.nameRestriction(param);
		if (result.getSuccess()) {
			return JsonResult.success(result.getData());
		}
		return JsonResult.error(result.getMessage());
	}

	@RequestMapping("/germanPostcodeValidAddressQuery")
	@Logger(module = Module.EJF,name = "德邮邮编查询")
	@Validated
	public JsonResult<?> germanPostcodeValidAddressQuery(@RequestBody GetDhlRootingRequestType param) {
		GetDhlRootingResponseType result = serviceInf.germanPostcodeValidAddressQuery(param);
		if (result.getSuccess()) {
			return JsonResult.success(result.getData());
		}
		return JsonResult.error(result.getMessage());
	}

	@RequestMapping("/frenchPostcodeValidForCityEnquiries")
	@Logger(module = Module.EJF,name = "法邮邮编查询")
	@Validated
	public JsonResult<?> frenchPostcodeValidForCityEnquiries(@RequestBody GetFrenchRootingRequestType param) {
		GetFrenchRootingResponseType result = serviceInf.frenchPostcodeValidForCityEnquiries(param);
		if (result.getSuccess()) {
			return JsonResult.success(result.getData());
		}
		return JsonResult.error(result.getMessage());
	}


}
