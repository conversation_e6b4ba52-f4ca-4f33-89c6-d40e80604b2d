package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.dto.AbnormalReturnListDTO;
import com.ywwl.customer.center.modules.international.dto.AbnormalReturnVerifyDTO;
import com.ywwl.customer.center.modules.international.service.AbnormalReturnService;
import com.ywwl.customer.center.modules.international.vo.AbnormalReturnListVo;
import com.ywwl.customer.center.modules.international.vo.AbnormalReturnNumVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;


/**
 * // 退件签收
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/abnormalReturn")
public class AbnormalReturnController extends BaseController {
    @Resource
    private AbnormalReturnService abnormalReturnService;
    @Resource
    private AccountService accountService;

    /***
     * //列表
     * <AUTHOR>
     * @date 2023/3/23 16:16
     * @param abnormalReturnListDTO 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.AbnormalReturnListVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "退件签收列表")
    @PostMapping("/list")
    public JsonResult<AbnormalReturnListVo> list(@RequestBody @Valid AbnormalReturnListDTO abnormalReturnListDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            List<String> shippingAccounts = abnormalReturnListDTO.getShippingAccount();
            if (null != shippingAccounts && !shippingAccounts.isEmpty()) {
                if (!accountService.existAccount(shippingAccounts, currentUser, 0)) {
                    return JsonResult.error(ResponseCode.PORTAL_5060);
                }
            }
            return abnormalReturnService.exceptionList(abnormalReturnListDTO);
        } catch (Exception e) {
            log.error("列表查询异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }

    }

    /***
     * //确认签收
     * <AUTHOR>
     * @date 2023/3/23 16:47
     * @param abnormalReturnVerifyDTO 处理参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "退件确认签收", type = Type.WAYBILL_NUMBER, req = "v.waybillNumber")
    @PostMapping("/batchConfirmation")
    public JsonResult<Object> batchConfirmation(@RequestBody @Valid List<AbnormalReturnVerifyDTO> abnormalReturnVerifyDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            List<String> collect = abnormalReturnVerifyDTO.parallelStream().map(AbnormalReturnVerifyDTO::getOperator).collect(Collectors.toList());
            if (!accountService.existAccount(collect, currentUser, 0)) {
                return JsonResult.error(ResponseCode.PORTAL_5060);
            }
            return abnormalReturnService.batchConfirmation(abnormalReturnVerifyDTO);
        } catch (Exception e) {
            log.error("确认签收异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  列表数量
     * <AUTHOR>
     * @date 2023/3/23 16:49
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.AbnormalReturnNumVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "退件列表数量")
    @PostMapping("/numbers")
    public JsonResult<AbnormalReturnNumVo> getAbnormalNum(@RequestBody @Valid AbnormalReturnListDTO abnormalReturnListDTO) {
        try {
            return abnormalReturnService.numbers(abnormalReturnListDTO);
        } catch (Exception e) {
            log.error("列表数量查询异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  导出文件
     * <AUTHOR>
     * @date 2023/3/23 17:07
     * @param response 响应
     * @param abnormalReturnListDTO  导出参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/excelDownload")
    public JsonResult<Object> download(HttpServletResponse response, @Valid @RequestBody AbnormalReturnListDTO abnormalReturnListDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {

        try {
            List<String> shippingAccounts = abnormalReturnListDTO.getShippingAccount();
            if (null != shippingAccounts && !shippingAccounts.isEmpty()) {
                if (!accountService.existAccount(shippingAccounts, currentUser, 0)) {
                    return JsonResult.error(ResponseCode.PORTAL_5060);
                }
            }
            return abnormalReturnService.downloadList(response, abnormalReturnListDTO);
        } catch (Exception e) {
            log.error("导出文件异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }
}