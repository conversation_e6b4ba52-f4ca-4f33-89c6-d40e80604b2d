package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.system.dto.UploadFileDTO;
import com.ywwl.customer.center.system.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;

/**
 * @author: dinghy
 * @date: 2023/3/7 14:06
 */
@Controller
@RequestMapping("/file")
@Slf4j
public class FileController extends BaseController {
    public static final String YW = "yanwentech";
    @Resource
    private FileService fileService;

    /**
     * <AUTHOR>
     * @description 上传文件
     * @date 2023/3/7 16:50
     **/
    @Logger(module= Module.COMMON,name = "上传文件")
    @PostMapping("/upload/attachment")
    @ResponseBody
    public JsonResult<String> uploadFile(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, UploadFileDTO uploadFileDTO) {
        uploadFileDTO.setUserId(String.valueOf(currentUser.getUserId()));
        if (StringUtils.isBlank(uploadFileDTO.getObjId())) {
            uploadFileDTO.setObjId(currentUser.getMerchantNo());
        }
        String url = fileService.uploadFile(uploadFileDTO);
        return JsonResult.success(url);
    }

    /**
     * <AUTHOR>
     * @description 下载文件, 参数传入下载地址
     * @date 2023/3/22 14:10
     **/
    @Logger(module= Module.COMMON,name = "下载文件")
    @GetMapping("download")
    public void downloadFile(@NotBlank(message = "[下载地址]不正确") String path, HttpServletResponse response) {
        boolean append = false;
        if (path.contains(YW)) {
            append = true;
        }
        HttpUtil.download(path, response, append);
    }

    /**
     * <AUTHOR>
     * @description 查看文件
     * @date 2023/3/29 17:11
     **/
    @Logger(module= Module.COMMON,name = "预览文件")
    @GetMapping("view/{fileName}")
    public void viewFile(@NotBlank(message = "[下载地址]不正确") String path, HttpServletResponse response, @PathVariable String fileName) {
        boolean append = false;
        if (path.contains(YW)) {
            append = true;
        }
        HttpUtil.view(path, response, append);
    }
}
