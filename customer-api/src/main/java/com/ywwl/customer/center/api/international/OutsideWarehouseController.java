package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.dto.CalculateCostDTO;
import com.ywwl.customer.center.modules.international.dto.CustomerHandleDTO;
import com.ywwl.customer.center.modules.international.dto.OutsideWarehouseDTO;
import com.ywwl.customer.center.modules.international.dto.OutsideWarehouseDataDTO;
import com.ywwl.customer.center.modules.international.service.OutsideWarehouseService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.OverseasRePickExportVo;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.*;


/***
 * //TODO  仓外异常件
 * <AUTHOR>
 * @date 2023/1/6 15:30
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/outsideWarehouse")
public class OutsideWarehouseController extends BaseController {
    @Resource
    private OutsideWarehouseService outsideWarehouseService;
    @Resource
    private AccountService accountService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /***
     * //TODO  仓外异常件列表查询
     * <AUTHOR>
     * @date 2022/4/2 14:02
     * @param currentUser 用户登录信息
     * @param outsideWarehouseDTO 列表查询
     * @return com.cmhb.common.JsonResult<Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "仓外异常列表")
    @RequestMapping("/selectPage")
    public JsonResult<Object> selectPage(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody OutsideWarehouseDTO outsideWarehouseDTO) {
        try {
            return outsideWarehouseService.selectPage(outsideWarehouseDTO, currentUser);
        } catch (ResponseCode.ResponseException | BusinessException e) {
            log.error("仓外异常件查询异常:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("仓外异常件查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  仓外异常件单个处理
     * <AUTHOR>
     * @date 2022/4/2 14:02
     * @param currentUser 用户登录信息
     * @param customerHandleDTO 单个处理参数
     * @return com.cmhb.common.JsonResult<Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "仓外异常单个处理")
    @RequestMapping("/customerHandle")
    public JsonResult<Object> customerHandle(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody CustomerHandleDTO customerHandleDTO) {
        customerHandleDTO.setOperator(currentUser.getLoginName());
        try {
            if(customerHandleDTO.getBusinessType()==null||customerHandleDTO.getBusinessType()==0){
                PacketApplyInfoVo packetApplyInfo = getPacketApplyInfo(currentUser.getUserCode());
                packetBusinessApplyService.packetFreezeStatus(packetApplyInfo);
            }
            return outsideWarehouseService.customerHandle(customerHandleDTO);
        } catch (BusinessException e) {
            log.error("仓外异常件单个处理异常：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("仓外异常件单个处理异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/26 16:17
     * @description: 批量处理海外重派数据
     */
    @PostMapping("batchHandleOversea")
    public JsonResult batchHandleOversea(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody List<CustomerHandleDTO> customerHandleDTOList){
        try {
            List<String> errorMessages = Collections.synchronizedList(new ArrayList<>());
            customerHandleDTOList.parallelStream().forEach(
                    customerHandleDTO -> {
                        customerHandleDTO.setOperator(currentUser.getLoginName());
                        JsonResult<Object> objectJsonResult = outsideWarehouseService.customerHandle(customerHandleDTO);
                        if(!objectJsonResult.getSuccess()){
                            errorMessages.add("运单号："+customerHandleDTO.getWaybillNumber()+"-->"+objectJsonResult.getMessage());
                        }
                    }
            );
            if(errorMessages.isEmpty()){
                return JsonResult.success();
            }
            return JsonResult.error("处理失败数据："+errorMessages);
        } catch (BusinessException e) {
            log.error("批量处理海外重派数据异常：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("仓外异常件单个处理异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  批量处理仓外异常件
     * <AUTHOR>
     * @date 2022/4/2 14:11
     * @param currentUser 用户登录信息
     * @param customerHandleDTOList 批量处理仓外参数
     * @return com.cmhb.common.JsonResult<Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "仓外异常批量处理")
    @RequestMapping("/customerBatchHandle")
    public JsonResult<Object> customerBatchHandle(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody List<CustomerHandleDTO> customerHandleDTOList) {
        //operator处理人;data多个处理数据
        Map<String, Object> map = new HashMap<>(4);
        map.put("operator", currentUser.getLoginName());
        map.put("datas", customerHandleDTOList);
        try {
            PacketApplyInfoVo packetApplyInfo = getPacketApplyInfo(currentUser.getUserCode());
            packetBusinessApplyService.packetFreezeStatus(packetApplyInfo);
            return outsideWarehouseService.customerBatchHandle(map);
        } catch (BusinessException e) {
            log.error("仓外异常件多个处理异常：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("仓外异常件多个处理异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  获取所有异常原因
     * <AUTHOR>
     * @date 2022/4/2 14:41
     * @return com.cmhb.common.JsonResult<Object>
     */
    @RequestMapping("/getAbnormalType")
    public JsonResult<Object> getAbnormalType() {
        try {
            return outsideWarehouseService.getAbnormalType();
        } catch (Exception e) {
            log.error("仓外异常原因获取异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //TODO  导出仓外数据
     * <AUTHOR>
     * @date 2022/7/29 11:29
     * @param currentUser 用户登录信息
     * @param outsideWarehouseDTO 导出参数
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "仓外导出数据")
    @PostMapping("/download")
    public JsonResult<Object> download(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody OutsideWarehouseDTO outsideWarehouseDTO, HttpServletResponse response) {
        try {
            log.info("导出全部数据参数:{}", outsideWarehouseDTO);
            return outsideWarehouseService.download(currentUser, outsideWarehouseDTO, response);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            return JsonResult.error(e.getMessage());
        } catch (IOException e) {
            log.error("导出全部数据异常:{},{}", e.getMessage(), currentUser.getMerchantNo());
            return JsonResult.error(ResponseCode.PORTAL_5028);
        }
    }

    /***
     * //TODO  导出选择的数据进行下载
     * <AUTHOR>
     * @date 2022/8/1 9:52
     * @param list 集合参数
     * @param response 相应信息
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "仓外导出选择数据")
    @RequestMapping("/portionDownload")
    public JsonResult<Object> portionDownload(@RequestBody List<OutsideWarehouseDataDTO> list, HttpServletResponse response) {
        try {
            log.info("导出选择的数据参数:{}", list);
            return outsideWarehouseService.portionDownload(list, response);
        } catch (BusinessException | AssertionError e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("导出全部数据异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5028);
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/6 10:46
     * @description: 海外派海外重派导出
     */
    @RequestMapping("/exportOverseaReSend")
    public JsonResult exportOverseaReSend(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody OutsideWarehouseDTO outsideWarehouseDTO, HttpServletResponse response){
        return outsideWarehouseService.exportOverseaReSend(currentUser,outsideWarehouseDTO,response);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/16 15:17
     * @description: 海外重派导入处理
     */
    @RequestMapping("/importOverseaBatch")
    public JsonResult importOverseaBatch(MultipartFile file) {
        try {
            List<CustomerHandleDTO> list = new LinkedList<>();
            String loginName = getUser().getLoginName();
            EasyExcel.read(file.getInputStream(), OverseasRePickExportVo.class, new AnalysisEventListener<OverseasRePickExportVo>() {
                @Override
                public void invoke(OverseasRePickExportVo data, AnalysisContext context) {
                    String abnormalType = data.getHandleOptions();
                    String regroupType = data.getRegroupType();
                    String combineTrajectory = data.getCombineTrajectory();
                    if(!"待处理".equals(data.getStatus())){
                        throw new BusinessException("只能处理待处理工单");
                    }
                    if(StringUtils.isBlank(data.getOrderId())){
                        throw new BusinessException("工单编号不能为空");
                    }
                    if(StringUtils.isBlank(data.getWaybillNumber())){
                        throw new BusinessException("运单号不能为空");
                    }
                    if(StringUtils.isBlank(abnormalType)){
                        throw new BusinessException("可选处理方式不能为空");
                    }
                    if(StringUtils.isBlank(data.getReceiverName())){
                        throw new BusinessException("收件人姓名不能为空");
                    }
                    if(StringUtils.isBlank(data.getReceiverZipCode())){
                        throw new BusinessException("收件人邮编不能为空");
                    }
                    if(StringUtils.isBlank(data.getReceiverCity())){
                        throw new BusinessException("收件人城市不能为空");
                    }
                    if(StringUtils.isBlank(data.getReceiverAddress1())){
                        throw new BusinessException("收件人地址1不能为空");
                    }
                    CustomerHandleDTO  customerHandleDTO = new CustomerHandleDTO();
                    customerHandleDTO.setOrderId(data.getOrderId());
                    if(abnormalType.equals("海外重派")){
                        if(StringUtils.isBlank(regroupType)){
                            throw new BusinessException("是否按照原地址重派不能为空");
                        }
                        if(StringUtils.isBlank(combineTrajectory)){
                            throw new BusinessException("是否合并展示原单号轨迹不能为空");
                        }
                        customerHandleDTO.setHandleType(6);
                        if(data.getCombineTrajectory().equals("是")){
                            customerHandleDTO.setCombineTrajectory(1);
                        }else if (data.getCombineTrajectory() .equals("否")){
                            customerHandleDTO.setCombineTrajectory(0);
                        }else {
                            throw new BusinessException("未知是否合并展示原单号轨迹处理方式");
                        }
                        if(data.getRegroupType().equals("是")){
                            customerHandleDTO.setRegroupType(1);
                        }else if (data.getRegroupType() .equals("否")){
                            customerHandleDTO.setRegroupType(2);
                        }else {
                            throw new BusinessException("未知是否按照原地址重派处理方式");
                        }
                    }else if (abnormalType.equals("销毁")){
                        customerHandleDTO.setHandleType(7);
                    }else{
                        throw new BusinessException("未知处理方式");
                    }
                    customerHandleDTO.setWaybillNumber(data.getWaybillNumber());
                    customerHandleDTO.setReceiverName(data.getReceiverName());
                    customerHandleDTO.setReceiverPhone(data.getReceiverPhone());
                    customerHandleDTO.setReceiverAddress1(data.getReceiverAddress1());
                    customerHandleDTO.setReceiverAddress2(data.getReceiverAddress2());
                    customerHandleDTO.setReceiverCity(data.getReceiverCity());
                    customerHandleDTO.setReceiverState(data.getReceiverState());
                    customerHandleDTO.setReceiverZipCode(data.getReceiverZipCode());
                    customerHandleDTO.setEmail(data.getEmail());
                    customerHandleDTO.setReceiverCompany(data.getReceiverCompany());
                    customerHandleDTO.setReceiverAreaId(data.getReceiverAreaId());
                    customerHandleDTO.setOverseas("1");
                    list.add(customerHandleDTO);
                }
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {

                }
            }).sheet().headRowNumber(2).doRead();
            List<String> errorMessages = Collections.synchronizedList(new ArrayList<>());
            list.parallelStream().forEach(
                    customerHandleDTO -> {
                        customerHandleDTO.setOperator(loginName);
                        JsonResult<Object> objectJsonResult = outsideWarehouseService.customerHandle(customerHandleDTO);
                        if(!objectJsonResult.getSuccess()){
                            errorMessages.add("运单号："+customerHandleDTO.getWaybillNumber()+"-->"+objectJsonResult.getMessage());
                        }
                    }
            );
            if(errorMessages.isEmpty()){
                return JsonResult.success();
            }
            return JsonResult.error("处理失败数据："+errorMessages);
        } catch (IOException e) {
           return JsonResult.error("文件解析失败");
        }
    }
    /***
     * //TODO  计算预估重派费用
     * <AUTHOR>
     * @date 2022/9/7 15:48
     * @param calculateCostDTO 计算费用参数
     * @param currentUser 用户登录信息
     * @return com.cmhb.common.JsonResult<Object>
     */
    @RequestMapping("/calculate")
    public JsonResult<Object> calculate(@RequestBody @Valid CalculateCostDTO calculateCostDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            String customerCode = calculateCostDTO.getCustomerCode();
            List<String> stringList = Collections.singletonList(customerCode);
            if(calculateCostDTO.getBusinessType()==null||calculateCostDTO.getBusinessType()==0){
                if (!accountService.existAccount(stringList, currentUser, 0)) {
                    return JsonResult.error(ResponseCode.PORTAL_5060);
                }
                calculateCostDTO.setProductCode("1139");
            }else{
                calculateCostDTO.setProductCode("1576");
            }
            return outsideWarehouseService.calculateCost(calculateCostDTO, currentUser);
        } catch (BusinessException | AssertionError e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("计算预估重派费用异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    public PacketApplyInfoVo getPacketApplyInfo(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo;
    }
}
