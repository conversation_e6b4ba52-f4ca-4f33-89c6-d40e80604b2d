package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.amazon.result.*;
import com.ywwl.customer.center.modules.amazon.service.AmazonOrderService;
import com.ywwl.customer.center.modules.amazon.vo.AmazonOrderDetailVO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.international.service.StraightCrmService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.ywwl.customer.center.common.domain.ResponseCode.PORTAL_5065;

/**
 * 亚马逊订单
 *
 * <AUTHOR>
 * @date 2022/09/05 17:23
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/order/amazon")
public class AmazonController extends BaseController {
    /**
     * 交接单模板名称
     */
    public static final String TEMPLATE = "交接单_{}.xlsx";

    private static final String labelName = "打印标签_%s.pdf";
    @Resource
    private AmazonOrderService service;
    @Resource
    AccountService accountService;

    @Resource
    StraightCrmService straightCrmService;

    /**
     * 交接单模板路径
     */
    private static final ClassPathResource TEMPLATE_PATH = new ClassPathResource("data/交接单模板-Amazon.xlsx");

    /**
     * 获取平台账号
     *
     * @param param 参数
     */
    @PostMapping("/queryPlatformAccount")
    public JsonResult<?> queryPlatformAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryPlatformAccountParam param) throws IOException {
        // 验证权限
        accountService.existAccountThrow(param.getAccountCode());
        return JsonResult.success(service.queryPlatformAccount(param));
    }

    @Data
    static class Label {
        /**
         * 运单号
         */
        String waybillNumber;
        /**
         * 订单号
         */
        String orderNumber;
    }

    /**
     * 打印标签
     */
    @PostMapping("/printLabels")
    public JsonResult<?> printLabels(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser,
                                     @JsonParam String type,
                                     @JsonParam List<Label> nums,
                                     @JsonParam String customerCode) {
        if (CollectionUtil.isEmpty(nums)) {
            return JsonResult.error("运单号不能为空");
        }
        Map<String, String> successList = new LinkedHashMap<>();
        List<String> errorMsg = new ArrayList<>();
        if (StrUtil.equals(type, "AMAZON")) {
            List<String> trackNumbers = nums.stream().map(Label::getWaybillNumber).collect(Collectors.toList());
            // 使用有序map
            for (String trackNumber : trackNumbers) {
                try {
                    successList.put(trackNumber, service.getLabelBase64String(customerCode, trackNumber));
                } catch (Throwable e) {
                    final String msg = StrUtil.format("{} 打印失败 {}", trackNumber, e.getMessage());
                    log.error(msg, e);
                    errorMsg.add(msg);
                }
            }
        } else {
            for (Label num : nums) {
                try {
                    final String dhLabelVase64String = service.getDhLabelVase64String(customerCode, num.getWaybillNumber(), num.getOrderNumber());
                    successList.put(num.getWaybillNumber(), dhLabelVase64String);
                } catch (Throwable e){
                    final String msg = StrUtil.format("{} 打印失败 {}", num.getWaybillNumber(), e.getMessage());
                    log.error(msg, e);
                    errorMsg.add(msg);
                }
            }
        }
        final List<List<String>> successWsListSplit = CollectionUtil.split(successList.keySet(), 50);
        for (List<String> ws : successWsListSplit) {
            final UpdatePrintStatusResultDTO dto = service.updatePrintStatus(ws);
            if (dto.getHasError()) {
                ws.forEach(successList::remove);
                final String msg = StrUtil.format("{} 打印失败 {}", ws, dto.getData());
                log.error(msg);
                errorMsg.add(msg);
            }
        }
        if (CollectionUtil.isNotEmpty(successList)) {
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                generatePdf(ListUtil.toList(successList.values()), outputStream);
                String result = EJFUtil.byteToString(outputStream);
                Map<String, String> map = new HashMap<>(2);
                String fileName = String.format(labelName, LocalDate.now());
                map.put("fileName", fileName);
                map.put("file", result);
                if (CollectionUtil.isEmpty(errorMsg)) {
                    return JsonResult.success(map);
                } else {
                    return JsonResult.error("打印部分成功，其他部分失败错误原因 \n" + CollectionUtil.join(errorMsg, "\n"), map);
                }
            } catch (DocumentException e) {
                log.error(e.getMessage(), e);
                return JsonResult.error(PORTAL_5065);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        if (CollectionUtil.isNotEmpty(errorMsg)) {
            return JsonResult.error(CollectionUtil.join(errorMsg, "\n"));
        }
        return JsonResult.error(PORTAL_5065);
    }

    public void generatePdf(List<String> data, OutputStream outputStream) throws DocumentException {
        Document document = new Document();
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            PdfCopy pdfCopy = new PdfCopy(document, outputStream);
            document.open();
            for (int i = 0; i < data.size(); i++) {
                byte[] buffer = decoder.decode(data.get(i));
                PdfReader reader = new PdfReader(buffer);
                for (int j = 0; j < reader.getNumberOfPages(); j++) {
                    document.newPage();
                    PdfImportedPage page = pdfCopy.getImportedPage(reader, j + 1);
                    pdfCopy.addPage(page);
                }
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            document.close();
        }
    }

    /**
     * <AUTHOR>
     * @description 查询亚马逊订单
     * @date 2022/9/6 10:38
     **/
    @PostMapping("queryOrders")
    public JsonResult<?> queryOrders(@RequestBody QueryOrderParam queryOrderParam) throws IOException {
        return JsonResult.success(service.queryOrders(queryOrderParam));
    }

    /**
     * <AUTHOR>
     * @description 查询订单详情
     * @date 2022/9/6 14:24
     **/
    @GetMapping("queryOrderDetail/{trackNumber}")
    public JsonResult<?> queryOrderDetail(@PathVariable String trackNumber) {
        final AmazonOrderDetail detail = service.queryOrderDetail(trackNumber);
        return JsonResult.success(new AmazonOrderDetailVO(detail));
    }

    /**
     * 生成交接单
     *
     * @param queryParam 运单号列表
     */
    @RequestMapping("/generateTransferOrder")
    @Logger(module = Module.AMAZON, name = "生成交接单", recordRsp = false)
    public JsonResult<?> generateTransferOrder(@RequestBody QueryOrderParam queryParam) throws IOException {
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 获取商户信息
        final PacketApplyInfoVo packetApplyInfo = straightCrmService.getPacketApplyInfo(getUserCode());
        // 获取有权限的账号
        final List<String> accounts = getPrivilegedAccount(queryParam.getUserId());
        final QueryOrderResultDTO result = service.getAmazonList(queryParam);
        if (result.getSuccess()) {
        // 获取数据
        final List<AmazonOrder> data = Optional
                .ofNullable(result.getData())
                .orElse(Collections.emptyList())
                .stream()
                // 过滤权限
                .filter(item -> accounts.contains(item.getPlatfromCustomCode()))
            .map(service::amazonInterfaceToLocal)
            .collect(Collectors.toList());
        IntStream.rangeClosed(1, data.size()).forEach(i -> data.get(i - 1).setId(i));
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             ExcelWriter write = EasyExcel.write(outputStream).withTemplate(TEMPLATE_PATH.getInputStream()).build()) {
            ImmutableMap<?, ?> param =
                    ImmutableMap.of(
                            "date", date,
                            "customerCode", queryParam.getUserId(),
                            "customerName", packetApplyInfo.getMerchantName(),
                            "size", data.size());
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig config = FillConfig.builder().forceNewRow(true).build();
            write.fill(data, config, writeSheet);
            write.fill(param, writeSheet);
            write.finish();
            final String base64 = EJFUtil.byteToString(outputStream);
            // 返回交接单
            return JsonResult.success(ImmutableMap.of(
                    "base64", base64,
                    "fileName", StrUtil.format(queryParam.getPlatformName()+TEMPLATE, DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT)))
            );
            }
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 获取有权限的账号
     *
     * @param userId 用户id
     * @return 有权限的账号
     * @throws IOException 异常
     */
    private List<String> getPrivilegedAccount(String userId) throws IOException {
        // 获取绑定的发货账号信息
        final QueryPlatformAccountParam accountParam = new QueryPlatformAccountParam();
        accountParam.setAccountCode(userId);
        return service
                .queryPlatformAccount(accountParam).stream()
                .map(QueryPlatformAccountResult.DataDTO::getPlamCode)
                .collect(Collectors.toList());
    }


}
