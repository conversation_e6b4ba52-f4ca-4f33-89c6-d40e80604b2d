package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.DocumentException;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.listener.easyexcel.FreezePaneHandler;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.aspect.ParameterValidationAspect;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.modules.business.dto.*;
import com.ywwl.customer.center.modules.business.enums.BusinessFixedEnum;
import com.ywwl.customer.center.modules.business.enums.ProductTypeEnum;
import com.ywwl.customer.center.modules.business.enums.WaybillStatusEnum;
import com.ywwl.customer.center.modules.business.service.BusinessService;
import com.ywwl.customer.center.modules.business.util.BusinessUtil;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.annotation.Import;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import com.ywwl.customer.center.modules.ejf.component.excel.CommentWriteHandler;
import com.ywwl.customer.center.modules.ejf.dto.ExpressDeliveryInfoDTO;
import com.ywwl.customer.center.modules.ejf.entity.Currency;
import com.ywwl.customer.center.modules.ejf.entity.*;
import com.ywwl.customer.center.modules.ejf.enums.SearchStatus;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.dto.BusinessOrderEnumDTO.DataDTO.ValueDTO;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import com.ywwl.customer.center.modules.general.plm.dto.ProductResultDTO;
import com.ywwl.customer.center.modules.general.plm.enums.PLMPlatformEnum;
import com.ywwl.customer.center.modules.international.dto.CalcPriceParamDTO;
import com.ywwl.customer.center.modules.international.dto.PLMCalcPriceParamDTO;
import com.ywwl.customer.center.modules.international.dto.PLMCalcPriceResultDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.system.dto.UploadFileDTO;
import com.ywwl.customer.center.system.enums.UploadObjNameEnum;
import com.ywwl.customer.center.system.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.ywwl.customer.center.framework.enums.Module.BUSINESS;
import static com.ywwl.customer.center.modules.business.util.BusinessUtil.entry;

/**
 * 商业快递控制器
 *
 * <AUTHOR>
 * @since 2023/10/10 15:29
 **/
@RestController
@Slf4j
@RequestMapping("/business")
@Validated
public class BusinessController extends BaseController {

    /**
     * 商业快递服务类
     */
    @Resource
    private BusinessService businessService;

    /**
     * 主数据服务类
     */
    @Resource
    private CmccService cmccService;

    /**
     * 账号服务类
     */
    @Resource
    private AccountService accountService;

    /**
     * 订单服务类
     */
    @Resource
    private OrderService orderService;

    /**
     * 文件服务类
     */
    @Resource
    private FileService fileService;

    /**
     * 一次最大导入条数
     */
    @Value("${business.import-max-size}")
    private Integer size;

    /**
     * 模板名称
     */
    public static String TRANSFER_ORDER = "商业快递_交接单_%s.xlsx";

    /**
     * 获取产品详情
     */
    @Value("${calculate-engine.productDetail}")
    private String productDetailUrl;

    /**
     * portal运价试算接口
     */
    private static final String TAIL_PRODUCT = "/api/product/tailProduct";

    /**
     * PLM接口
     */
    @Value("${plm.url}")
    private String plmUrl;

    /**
     * 需要设置退件信息
     */
    public static final List<String> EXPRESS =
            ListUtil.of(
                    WaybillStatusEnum.WAREHOUSE_RETURN.getCode(),
                    WaybillStatusEnum.FOREIGN_RETURN.getCode(),
                    WaybillStatusEnum.SIGN_FOR_RETURN.getCode());

    /**
     * 获取商业快递枚举类型
     *
     * @return 商业快递枚举类型
     */
    @PostMapping("/getBusinessOrderTypeEnum")
    public JsonResult<?> getBusinessOrderTypeEnum() {
        return JsonResult.success(
                CacheUtil.ehCache().getValueAndCache(
                        CacheKeyEnum.BUSINESS,
                        "all",
                        this::getBusinessOrderTypeEnumV,
                        true)
        );
    }

    /**
     * 获取商业快递枚举类型
     *
     * @return 商业快递枚举类型
     */
    private HashMap<String, List<ValueDTO>> getBusinessOrderTypeEnumV() {
        // 获取电池类型
        final List<ValueDTO> batteryType =
                cmccService.getBusinessOrderTypeEnum(CmccItemEnum.BATTERY_TYPE);
        // 获取商品类型
        final List<ValueDTO> commodityType =
                cmccService.getBusinessOrderTypeEnum(CmccItemEnum.COMMODITY_TYPE);
        // 获取包裹类型
        final List<ValueDTO> packType =
                cmccService.getBusinessOrderTypeEnum(CmccItemEnum.PACK_TYPE);
        // 获取税号类型
        final List<ValueDTO> taxType =
                cmccService.getBusinessOrderTypeEnum(CmccItemEnum.TAX_TYPE);
        return (HashMap<String, List<ValueDTO>>) MapUtil.ofEntries(
                entry("batteryType", batteryType),
                entry("commodityType", commodityType),
                entry("packType", packType),
                entry("taxType", taxType)
        );
    }

    /**
     * 商业快递列表查询
     *
     * @param param 参数
     * @return 商业快递列表
     */
    @Logger(module = BUSINESS, name = "商业快递列表查询", recordRsp = false)
    @PostMapping("/getCurrentBusinesses")
    public JsonResult<?> getCurrentBusinesses(@Valid @RequestBody SearchParamDTO param) {
        accountService.existAccountThrow(param.getUserId());
        final Map<String, Integer> express = getExpress(param)
                .stream()
                .collect(Collectors.toMap(
                        Express::getStatus,
                        Express::getQuantity));
        switch (param.getStatus()) {
            case ALL_DRAFT:
            case UNDOCUMENTED:
            case ORDER_FAILED:
            case BILLED_DRAFT:
                // 草稿查询
                param.setOrderNumbers(param.getListNumber());
                param.setListStatus(param.getStatus().getOrderEnumKey());
                final InnerBeDraftOrderGetListFilterDTO businessDraft =
                        businessService.getBusinessDraft(param);
                if (!businessDraft.getSuccess()) {
                    return JsonResult.error(businessDraft.getMessage());
                }
                // 设置userId
                Optional.ofNullable(businessDraft.getData())
                        .map(InnerBeDraftOrderGetListFilterDTO.DataDTO::getRecords)
                        .ifPresent(v -> v.forEach(k -> k.setUserId(param.getUserId())));
                return JsonResult.success(MapUtil.ofEntries(
                        entry("waybill", businessDraft.getData()),
                        entry("express", express))
                );
            case ALL_ORDER:
            case BILLED:
            case SHIPMENT_CONFIRMED:
            case RECEIVED:
            case SHIPPED:
            case HAS_BEEN_PLACED:
            case CANCELLED:
            case RETAINED:
            case END_OF_TRACE:
            case RETURN_EXCEPTION:
                // 运单查询
                param.setListStatus(param.getStatus().getWaybillEnumKey());
                final InnerBeOrderPortalGetListFilterDTO business = businessService.getBusiness(param);
                if (!business.getSuccess()) {
                    return JsonResult.error(business.getMessage());
                }
                // 额外信息设置
                extraneousInformation(param, business);
                return JsonResult.success(MapUtil.ofEntries(
                        entry("waybill", business.getData()),
                        entry("express", express))
                );
            default:
                throw ResponseCode.PORTAL_9501.getError();
        }
    }

    /**
     * 额外信息设置
     *
     * @param param    参数
     * @param business 运单信息
     */
    private void extraneousInformation(SearchParamDTO param, InnerBeOrderPortalGetListFilterDTO business) {
        Optional.ofNullable(business.getData())
                .map(InnerBeOrderPortalGetListFilterDTO.DataDTO::getRecords)
                .ifPresent(records -> {
                    final Map<String, List<BusinessSimpleOrderDTO>> recordByStatus =
                            records.stream().collect(Collectors.groupingBy(BusinessSimpleOrderDTO::getStatus));
                    try {
                        // 处理截留
                        final List<BusinessSimpleOrderDTO> intercepted = recordByStatus.get(WaybillStatusEnum.INTERCEPTED.getCode());
                        if (CollectionUtil.isNotEmpty(intercepted)) {
                            for (BusinessSimpleOrderDTO order : intercepted) {
                                orderService.interceptionInformation(
                                        order.getWaybillNumber(),
                                        order::setHowToHandle,
                                        order::setTrappedState,
                                        order::setNewExpressCode);
                            }
                        }
                    } catch (Exception e) {
                        log.error("商业快递设置截留信息异常 {}", param, e);
                    }

                    try {
                        // 处理退件
                        final List<BusinessSimpleOrderDTO> warehouseReturn = records.stream().filter(x -> EXPRESS.contains(x.getStatus())).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(warehouseReturn)) {
                            for (BusinessSimpleOrderDTO order : warehouseReturn) {
                                final ExpressDeliveryInfoDTO.DataDTO dto = orderService.getExpressDeliveryInfo(order.getWaybillNumber());
                                if (Objects.nonNull(dto)) {
                                    // 设置快递公司和快递单号
                                    order.setExpressCompanyName(dto.getExpressCompanyName());
                                    order.setTrackingNumber(dto.getTrackingNumber());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("商业快递设置退件信息异常 {}", param, e);
                    }

                    try {
                        // 处理取消原因
                        final List<BusinessSimpleOrderDTO> cancelOrders = recordByStatus.get(WaybillStatusEnum.CANCEL.getCode());
                        if (CollectionUtil.isNotEmpty(cancelOrders)) {
                            final List<String> waybillNumbers = cancelOrders.stream().map(BusinessSimpleOrderDTO::getWaybillNumber).collect(Collectors.toList());
                            // 获取取消原因
                            final InnerBeOrderCancelRecordGetListDTO cancelBusinessOrderInfo =
                                    businessService.cancelBusinessOrderInfo(param.getUserId(), waybillNumbers);
                            if (cancelBusinessOrderInfo.getSuccess()) {
                                // 取消原因后转map进行匹配
                                final Map<String, InnerBeOrderCancelRecordGetListDTO.DataDTO> cancelNoteBean =
                                        cancelBusinessOrderInfo.getData().stream().collect(Collectors.toMap(InnerBeOrderCancelRecordGetListDTO.DataDTO::getWaybillNumber, Function.identity(), (v1, v2) -> v2));
                                cancelOrders.forEach(order -> {
                                    final InnerBeOrderCancelRecordGetListDTO.DataDTO cancel = cancelNoteBean.get(order.getWaybillNumber());
                                    if (Objects.nonNull(cancel)) {
                                        order.setCancelNote(cancel.getNote());
                                        order.setCancelTime(cancel.getCreateTime());
                                        order.setCancelType(cancel.getType());
                                    }
                                });
                            } else {
                                log.error("获取取消原因失败 {} {}", param, cancelBusinessOrderInfo.getMessage());
                            }
                        }
                    } catch (Exception e) {
                        log.error("商业快递设置取消原因信息异常 {}", param, e);
                    }
                });
    }

    /**
     * 获取商业快递统计
     *
     * @param param 参数
     * @return 商业快递统计
     */
    private Collection<Express> getExpress(SearchParamDTO param) {
        // 获取草稿统计
        final InnerBeDraftOrderStatusGetListDTO draftStatisticsResult =
                businessService.getBusinessDraftStatistics(param);
        List<ExpressDTO> draftStatistics = new ArrayList<>();
        if (draftStatisticsResult.getSuccess()) {
            draftStatistics = draftStatisticsResult.getData().getList();
        } else {
            log.error("获取草稿统计失败 {} {}", param, draftStatisticsResult.getMessage());
        }
        // 获取运单统计s
        final InnerBeOrderStatusGetListFilterDTO orderStatisticsResult =
                businessService.getBusinessOrderStatistics(param);
        List<ExpressDTO> orderStatistics = new ArrayList<>();
        if (orderStatisticsResult.getSuccess()) {
            orderStatistics = orderStatisticsResult.getData().getExpress();
        } else {
            log.error("获取运单统计失败 {} {}", param, orderStatisticsResult.getMessage());
        }
        return getExpress(draftStatistics, orderStatistics);
    }

    /**
     * 获取数量统计对象
     *
     * @param draftStatistics 草稿统计
     * @param orderStatistics 运单统计
     */
    private static Collection<Express> getExpress(List<ExpressDTO> draftStatistics, List<ExpressDTO> orderStatistics) {
        final Map<SearchStatus, Express> baseExpress = Stream.of(SearchStatus.values())
                .filter(x -> !Objects.equals(x.getStatus(), "-1"))
                .filter(x -> !Objects.equals(x.getStatus(), "-2"))
                .collect(Collectors.toMap(
                        k -> k,
                        v -> Express.builder()
                                .status(v.getStatus())
                                .description(v.getMsg())
                                .quantity(0).build(),
                        (v1, v2) -> v1)
                );
        for (SearchStatus searchStatus : baseExpress.keySet()) {
            // 获取汇总数量类
            Express gather = baseExpress.get(searchStatus);
            Set<String> orderEnumKey = searchStatus.getOrderEnumKey();
            Set<String> waybillEnumKey = searchStatus.getWaybillEnumKey();
            if (!CollectionUtils.isEmpty(orderStatistics)) {
                // 循环进行汇总运单数量
                for (ExpressDTO express : orderStatistics) {
                    if (waybillEnumKey.contains(express.getStatus())) {
                        gather.setQuantity(gather.getQuantity() + express.getQuantity());
                    }
                }
            }
            // 循环进行汇总订单数量
            if (!CollectionUtils.isEmpty(draftStatistics)) {
                // 循环进行汇总订单数量
                for (ExpressDTO express : draftStatistics) {
                    if (orderEnumKey.contains(express.getStatus())) {
                        gather.setQuantity(gather.getQuantity() + express.getQuantity());
                    }
                }
            }
        }
        return baseExpress.values();
    }

    /**
     * 获取产品列表
     *
     * @param param 参数
     * @return 产品列表
     */
    @PostMapping("/getBaseChannel")
    @Validated
    public JsonResult<?> getBaseChannel(@RequestBody InnerBeChannelGetListParamDTO param) {
        if (StringUtils.isBlank(param.getWarehouseCode())) {
            // 获取账号信息
            final AccountGetResVO account = accountService.getAccount(param.getUserId());
            param.setWarehouseCode(account.getWarehouseCode());
        }
        return vo(businessService.getBaseChannel(param));
    }

    /**
     * 获取产品列表
     *
     * @param userId            仓库号
     * @param countryId         国家id
     * @param warehouseCode     仓库id
     * @param primaryClassify   主类id
     * @param secondaryClassify 次类id
     * @return 产品列表
     */
    @PostMapping("/getChannel")
    public JsonResult<?> getChannel(@JsonParam @NotBlank(message = "制单账号不能为空") String userId,
                                    @JsonParam String countryId,
                                    @JsonParam String warehouseCode,
                                    @JsonParam String primaryClassify,
                                    @JsonParam String secondaryClassify) {
        if (StringUtils.isBlank(warehouseCode)) {
            // 获取账号信息
            final AccountGetResVO account = accountService.getAccount(userId);
            warehouseCode = account.getWarehouseCode();
        }
        final BusinessChannelDTO channel = businessService.getBaseChannel(userId, warehouseCode, countryId);
        if (channel.getSuccess()) {
            final List<BusinessChannelDTO.DataDTO> channelData = channel.getData();
            if (CollectionUtil.isEmpty(channelData)) {
                return JsonResult.success();
            }
            // 所有产品
            Map<ProductTypeEnum, List<BusinessChannelDTO.DataDTO>> channelMap = channelData.stream()
                    .collect(Collectors.groupingBy(entry ->
                            EnumUtil.getBy(
                                    ProductTypeEnum::getCode,
                                    entry.getPrimaryClassify(),
                                    ProductTypeEnum.OTHER))
                    );
            // 如果限制了一级分类
            if (StringUtils.isNotBlank(primaryClassify)) {
                return JsonResult.success(classifiedProduct(primaryClassify, secondaryClassify, channelMap));
            } else {
                // 推荐产品
                final List<BusinessChannelDTO.DataDTO> recommendChannel =
                        channelData.stream().filter(BusinessChannelDTO.DataDTO::getHasRecommend)
                                .collect(Collectors.toList());
                channelMap.put(ProductTypeEnum.RECOMMEND_PRODUCTS, recommendChannel);
                return JsonResult.success(allProducts(channelMap));
            }
        }
        return JsonResult.error(channel.getMessage());
    }

    /**
     * 全部产品
     *
     * @param channelMap 产品Map
     * @return 响应
     */
    private static List<ImmutableMap<String, Object>> allProducts(Map<ProductTypeEnum, List<BusinessChannelDTO.DataDTO>> channelMap) {
        // 商业快递需要过滤二级分类
        final List<BusinessChannelDTO.DataDTO> dto =
                channelMap.get(ProductTypeEnum.COMMERCIAL_EXPRESS_DELIVERY);
        if (CollectionUtil.isNotEmpty(dto)) {
            dto.forEach(d -> {
                if (!Objects.equals(d.getSecondaryClassify(), "59")) {
                    d.setPrimaryClassify(ProductTypeEnum.OTHER.getCode());
                }
            });
        }
        // 仅小包产品
        channelMap = MapUtil.getAny(
                channelMap,
                ProductTypeEnum.RECOMMEND_PRODUCTS,
                ProductTypeEnum.YAN_WEN_ECONOMY,
                ProductTypeEnum.YAN_WEN_EXTERNAL_WAYBILL_NUMBER,
                ProductTypeEnum.YAN_WEN_PROFESSIONAL_TRANSPORT_LINES,
                ProductTypeEnum.REGISTERED_CHINESE_MAIL,
                ProductTypeEnum.CHINA_POST_ECONOMICS,
                ProductTypeEnum.COMMERCIAL_EXPRESS_DELIVERY,
                ProductTypeEnum.OUTWARD_MAIL_PRODUCTS
        );
        // 按照产品类型排序
        channelMap = MapUtil.sort(channelMap, Comparator.comparingInt(ProductTypeEnum::getSort));
        for (List<BusinessChannelDTO.DataDTO> value : channelMap.values()) {
            value.sort(Comparator.comparing(BusinessChannelDTO.DataDTO::getDisplayOrder));
        }
        return channelMap.entrySet()
                .stream()
                .map(entry -> ImmutableMap.of("key", entry.getKey(), "value", entry.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 分类产品
     *
     * @param primaryClassify   主类
     * @param secondaryClassify 次类
     * @param channelMap        产品Map
     * @return 响应
     */
    private static List<BusinessChannelDTO.DataDTO> classifiedProduct(String primaryClassify, String secondaryClassify, Map<ProductTypeEnum, List<BusinessChannelDTO.DataDTO>> channelMap) {
        final ProductTypeEnum primaryClass = EnumUtil.getBy(
                ProductTypeEnum::getCode,
                primaryClassify,
                ProductTypeEnum.OTHER);
        List<BusinessChannelDTO.DataDTO> dtoList = channelMap.get(primaryClass);
        if (CollectionUtil.isNotEmpty(dtoList) &&
                StringUtils.isNotBlank(secondaryClassify)) {
            // 过滤二级分类
            dtoList = dtoList.stream()
                    .filter(x -> Objects.equals(x.getSecondaryClassify(), secondaryClassify))
                    .collect(Collectors.toList());
        }
        return dtoList;
    }

    /**
     * 创建运单
     *
     * @param param 参数
     * @return 运单号
     */
    @Validated({Input.class, Default.class})
    @PostMapping("/createBusinessOrder")
    @Logger(module = BUSINESS, name = "创建运单", type = Type.WAYBILL_NUMBER, rsp = "data.waybillNumber")
    public JsonResult<?> createBusinessOrder(@RequestBody BusinessOrderDTO param) {
        accountService.existAccountThrow(param.getUserId());
        return vo(businessService.createBusinessOrder(param));
    }

    /**
     * 取消运单
     *
     * @param param 参数
     * @return 取消运单结果
     */
    @Validated
    @PostMapping("/cancelBusinessOrder")
    @Logger(module = BUSINESS, name = "取消运单", type = Type.WAYBILL_NUMBER, req = "waybillNumbers")
    public JsonResult<?> cancelBusinessOrder(@RequestBody BeOrderCancelParamsDTO param) {
        accountService.existAccountThrow(param.getUserId());
        // 设置平台
        param.setPlatform(EJFUrl.SOURCE);
        final String template = "取消失败 {} {}";
        final List<BeOrderCancelParamDTO> params = param.getBeOrderCancelParamDTO();
        // 获取取消失败的
        final List<String> errorList = params.parallelStream()
                .map(cancelParam -> MapUtil.entry(cancelParam, businessService.cancelBusinessOrder(cancelParam)))
                // 仅获取失败的
                .filter(v -> !v.getValue().getSuccess())
                .map(x -> StrUtil.format(template, x.getKey(), x.getValue().getMessage()))
                .collect(Collectors.toList());
        // 有错误则返回错误
        if (CollectionUtil.isNotEmpty(errorList)) {
            return JsonResult.error(String.join(StringUtils.LF, errorList));
        }
        return JsonResult.success();
    }

    /**
     * 运单详细
     *
     * @param waybillNumber 运单号
     * @return 运单详细结果
     */
    @Validated
    @PostMapping("/getBusinessOrderDetail")
    @Logger(module = BUSINESS, name = "运单详细")
    public JsonResult<?> getBusinessOrderDetail(
            @JsonParam @NotBlank(message = "制单账号不能为空") String userId,
            @JsonParam @NotBlank(message = "运单号不能为空") String waybillNumber) {
        accountService.existAccountThrow(userId);
        return vo(businessService.getBusinessOrderDetail(userId, waybillNumber));
    }

    /**
     * 运单导出
     *
     * @param userId         制单账号
     * @param waybillNumbers 运单号
     * @return 运单详细结果
     */
    @Validated
    @PostMapping("/exportBusinessOrder")
    @Logger(module = BUSINESS, name = "运单导出", recordRsp = false)
    public JsonResult<?> exportBusinessOrder(
            @JsonParam @NotBlank(message = "制单账号不能为空") String userId,
            @JsonParam @Size(min = 1, message = "运单号不能为空") List<String> waybillNumbers,
            @JsonParam @Size(min = 1, message = "运单号不能为空") List<String> ids) throws IOException {
        accountService.existAccountThrow(userId);
        List<BusinessOrderDTO> finalDetails = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(waybillNumbers)) {
            final List<List<String>> waybillNumberSplit = CollectionUtil.split(waybillNumbers, 50);
            final List<InnerBeExpressOrderGetListParamDTO> paramList =
                    waybillNumberSplit.stream().map(x ->
                                    InnerBeExpressOrderGetListParamDTO.builder()
                                            .listNumber(x)
                                            .userId(userId).build())
                            .collect(Collectors.toList());
            for (InnerBeExpressOrderGetListParamDTO param : paramList) {
                // 获取运单列表
                final BeOrderGetListDTO details = businessService.getBusinessOrderDetailBatch(param);
                // 判断是否成功
                if (!details.getSuccess()) {
                    return JsonResult.error(details.getMessage());
                } else {
                    finalDetails.addAll(CollectionUtil.reverse(details.getData()));
                }

            }
            // 运单去掉id
            CollectionUtil.emptyIfNull(finalDetails).forEach(x -> x.setId(null));
        }
        if (CollectionUtil.isNotEmpty(ids)) {
            // 草稿列表
            ids.stream().map(id -> businessService.getBusinessDraftDetail(userId, id))
                    .filter(BeOrderGetDTO::getSuccess)
                    .map(BeOrderGetDTO::getData)
                    .forEach(finalDetails::add);
        }
        if (CollectionUtil.isEmpty(finalDetails)) {
            return JsonResult.error("未查询到运单数据");
        }
        // 商业快递设置退件信息设置
        finalDetails.forEach(detail -> {
            if (EXPRESS.contains(detail.getStatus())) {
                final String waybillNumber = detail.getWaybillNumber();
                try {
                    final ExpressDeliveryInfoDTO.DataDTO dto = orderService.getExpressDeliveryInfo(waybillNumber);
                    if (Objects.nonNull(dto)) {
                        // 设置快递公司和快递单号
                        detail.setReturnExpressCompanyName(dto.getExpressCompanyName());
                        detail.setReturnTrackingNumber(dto.getTrackingNumber());
                    }
                } catch (Throwable e) {
                    log.error("商业快递设置退件信息异常 {}", waybillNumber, e);
                }
            }
        });
        final List<BusinessExcelPropertyDTO> property = finalDetails.stream()
                .map(BusinessUtil::to)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out)
                    .sheet("运单信息")
                    .head(BusinessExcelPropertyDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezePaneHandler(0, 2))
                    .doWrite(property);
            final String base64 = EJFUtil.byteToString(out);
            return JsonResult.success(ImmutableMap.of(
                    "base64", base64,
                    "fileName", StrUtil.format("商业快递_运单导出_{}.xlsx",
                            DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT))
            ));
        }
    }


    /**
     * 运单修改
     *
     * @param param 参数
     * @return 运单修改结果
     */
    @Validated({Input.class, Default.class})
    @PostMapping("/editBusinessOrder")
    @Logger(module = BUSINESS, name = "运单修改", type = Type.WAYBILL_NUMBER, req = "waybillNumber")
    public JsonResult<?> editBusinessOrder(@RequestBody BusinessOrderDTO param) {
        accountService.existAccountThrow(param.getUserId());
        return vo(businessService.editBusinessOrder(param));
    }

    /**
     * 打印标签
     *
     * @param param 参数
     * @return 批量结果
     */
    @Validated
    @PostMapping("/businessOrderPrintLabelBatch")
    @Logger(module = BUSINESS, name = "打印标签", type = Type.WAYBILL_NUMBER, req = "waybillNumbers")
    public JsonResult<?> businessOrderPrintLabelBatch(@RequestBody InnerBeOrderLabelGetListParamDTO param) {
        accountService.existAccountThrow(param.getUserId());
        // 获取打印标签
        final Map<Boolean, List<Map.Entry<InnerBeOrderLabelGetListParamDTO, InnerBeOrderLabelGetListDTO>>> label = param
                .toList()
                .parallelStream()
                .map(v -> MapUtil.entry(v, businessService.businessOrderPrintLabelBatch(v)))
                .collect(Collectors.groupingBy(v -> {
                    final InnerBeOrderLabelGetListDTO value = v.getValue();
                    return Boolean.TRUE.equals(value.getSuccess()) &&
                            Objects.nonNull(value.getData()) &&
                            Boolean.TRUE.equals(value.getData().getIsSuccess());
                }));
        // 获取成功的pdf
        final List<String> successPdf = label.get(Boolean.TRUE).stream()
                .map(v -> v.getValue().getData().getBase64String())
                .collect(Collectors.toList());
        // 获取失败的pdf
        final List<Map.Entry<InnerBeOrderLabelGetListParamDTO, InnerBeOrderLabelGetListDTO>> failPdf = label.get(Boolean.FALSE);
        // 合并base64并返回
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            final JsonResult<Object> result = JsonResult.success();
            // 合并pdf
            if (CollectionUtil.isNotEmpty(successPdf)) {
                EJFUtil.generatePdf(successPdf, out);
                String base64 = EJFUtil.byteToString(out);
                result.setData(ImmutableMap.of(
                        "base64", base64,
                        "fileName", StrUtil.format("商业快递_打印标签_{}.pdf",
                                DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT))
                ));
            }
            // 失败处理
            if (CollectionUtil.isNotEmpty(failPdf)) {
                final String error = failPdf.stream()
                        .map(fail -> {
                            final InnerBeOrderLabelGetListDTO value = fail.getValue();
                            String errorMsg = value.getMessage();
                            if (Objects.nonNull(value.getData())) {
                                errorMsg = value.getData().getErrorMsg();
                            }
                            return StrUtil.format("{}打印失败{}",
                                    fail.getKey().getWaybillNumber(),
                                    errorMsg);
                        })
                        .collect(Collectors.joining(StringUtils.LF));
                result.setSuccess(false);
                result.setCode("500");
                result.setMessage(error);
            }
            return result;
        } catch (IOException | DocumentException e) {
            log.error("打印标签-批量失败 {}", param.getWaybillNumbers(), e);
            throw ResponseCode.PORTAL_9502.getError();
        }
    }

    /**
     * 创建订单
     *
     * @param param 参数
     * @return 运单号
     */
    @PostMapping("/createBusinessDraft")
    @Logger(module = BUSINESS, name = "创建订单")
    public JsonResult<?> createBusinessDraft(@RequestBody BusinessOrderDTO param) {
        accountService.existAccountThrow(param.getUserId());
        // 创建订单
        final InnerBeDraftOrderImportDTO dto = businessService.importBusinessOrder(param.getUserId(), ListUtil.of(param));
        if (!dto.getSuccess()) {
            return vo(dto);
        }
        // 判断是否创建成功
        final List<InnerBeDraftOrderImportDTO.DataDTO.RecordsDTO> records = dto.getData().getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            final InnerBeDraftOrderImportDTO.DataDTO.RecordsDTO record = records.get(0);
            if (record.getSuccess()) {
                return JsonResult.success(record);
            }
            return JsonResult.error(record.getErrorMessage());
        }
        return JsonResult.error();
    }

    /**
     * 文件上传
     *
     * @param files  文件
     * @param userId 制单账号
     * @return 上传结果
     */
    @PostMapping("/fileUpload/{userId}")
    @Logger(module = BUSINESS, name = "文件上传", recordReq = false)
    public JsonResult<?> fileUpload(@RequestParam(value = "files") MultipartFile[] files, @PathVariable("userId") String userId) {
        // 文件上传结果
        final List<UploadFileDTO> businessFileResult = Arrays.stream(files)
                .filter(Objects::nonNull)
                .map(file -> UploadFileDTO.builder()
                        .appid(UploadObjNameEnum.PORTAL_BUSINESS.getValue())
                        .objName(UploadObjNameEnum.PORTAL_BUSINESS.getValue())
                        .objId("PORTAL_BUSINESS_" + userId + "_" + IdUtil.fastSimpleUUID())
                        .attach(file)
                        .userId(userId)
                        .singleFile(true)
                        .build())
                .collect(Collectors.toList());
        List<Map<String, String>> successUrl = new ArrayList<>();
        List<String> errorUpload = new ArrayList<>();
        // 上传文件
        for (UploadFileDTO uploadFileDTO : businessFileResult) {
            try {
                final String file = fileService.uploadFile(uploadFileDTO);
                successUrl.add(
                        MapUtil.ofEntries(
                                entry("fileName", uploadFileDTO.getAttach().getOriginalFilename()),
                                entry("fileUrl", file))
                );
            } catch (Throwable e) {
                errorUpload.add(uploadFileDTO.getAttach().getOriginalFilename() + " 上传失败 " + e.getMessage());
            }
        }
        // 有错误项
        if (CollectionUtil.isNotEmpty(errorUpload)) {
            return JsonResult.error(String.join(StringUtils.LF, errorUpload), successUrl);
        }
        return JsonResult.success(successUrl);
    }

    /**
     * 导入订单
     *
     * @param files  文件
     * @param userId 参数
     * @return 导入订单结果
     */
    @PostMapping("/importBusinessOrder/{userId}")
    @Logger(module = BUSINESS, name = "导入订单", recordReq = false)
    public JsonResult<?> importBusinessOrder(@RequestParam(value = "files") MultipartFile files, @PathVariable("userId") String userId) throws IOException {
        accountService.existAccountThrow(userId);
        // 源数据
        List<Map<Integer, String>> sourceData = new LinkedList<>();
        // 单号
        Set<String> orderNumberSet = new HashSet<>();
        boolean[] isItMoreThan = {false};
        EasyExcel.read(files.getInputStream(), new ReadListener<Map<Integer, String>>() {
            @Override
            public void onException(Exception exception, AnalysisContext context) {
            }

            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                sourceData.add(data);
                // 设置单号
                orderNumberSet.add(data.get(3));
            }

            @Override
            public void extra(CellExtra extra, AnalysisContext context) {
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
            }

            @Override
            public boolean hasNext(AnalysisContext context) {
                if (orderNumberSet.size() > size) {
                    isItMoreThan[0] = true;
                    return false;
                }
                return true;
            }

            @Override
            public void invokeHead(Map headMap, AnalysisContext context) {
            }
        }).sheet(0).headRowNumber(0).doRead();
        if (isItMoreThan[0]) {
            return JsonResult.error("一次导入最多" + size + "条数据,请修改后重新上传！");
        }
        // 设置数据和头部
        List<Map<Integer, String>> sourceMap = Collections.emptyList();
        List<List<String>> head = Collections.emptyList();
        for (int i = 0; i < sourceData.size(); i++) {
            Map<Integer, String> map = sourceData.get(i);
            if (StrUtil.contains(StrUtil.trim(map.get(0)), "交货仓")) {
                sourceMap = sourceData.stream().skip(i + 1).collect(Collectors.toList());
                head = sourceData.get(i).
                        values()
                        .stream()
                        .map(Arrays::asList)
                        .collect(Collectors.toList());
                break;
            }
        }
        // 模板无法识别
        if (CollectionUtil.isEmpty(head)) {
            throw ResponseCode.PORTAL_9503.getError();
        }
        List<BusinessOrderDTO> orderList = new ArrayList<>();
        List<BusinessOrderDTO> polymerizationList = new ArrayList<>();
        String currentOrderNumber = null;
        for (int i = 0; i < sourceMap.size(); i++) {
            Map<Integer, String> orderMap = sourceMap.get(i);
            final BusinessOrderDTO order = mapToBusinessOrder(orderMap);
            // 设置行号
            order.setSequenceNo(i);
            if (StringUtils.isBlank(order.getOrderNumber())) {
                // 订单号为空
                polymerizationList.add(order);
            } else if (StringUtils.isNotBlank(order.getOrderNumber()) && StringUtils.isBlank(currentOrderNumber)) {
                polymerizationList.add(order);
                currentOrderNumber = order.getOrderNumber();
            } else if (Objects.equals(currentOrderNumber, order.getOrderNumber())) {
                polymerizationList.add(order);
            } else {
                orderList.add(polymerization(polymerizationList));
                polymerizationList.clear();
                polymerizationList.add(order);
                currentOrderNumber = order.getOrderNumber();
            }
        }
        // 最后一次聚合
        if (CollectionUtil.isNotEmpty(polymerizationList)) {
            orderList.add(polymerization(polymerizationList));
        }
        // 校验订单
        List<BusinessOrderDTO> successList = new ArrayList<>();
        final List<ImportMessage> errorList = orderList.stream().map(orderDTO -> {
            orderDTO.setUserId(userId);
            final List<String> violation = ParameterValidationAspect.getConstraintViolation(orderDTO, Default.class, Import.class);
            if (CollectionUtil.isNotEmpty(violation)) {
                return ImportMessage.builder().status("导入失败")
                        .message(String.join(StrUtil.COMMA, violation))
                        .row(orderDTO.getSequenceNo())
                        .orderNumber(orderDTO.getOrderNumber())
                        .source(orderDTO.getSource())
                        .build();
            }
            successList.add(orderDTO);
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        // 导入订单
        if (CollectionUtil.isNotEmpty(successList)) {
            // 转换枚举
            successList.forEach(this::valueToKey);
            // 通过sequenceNo转map
            final Map<Integer, BusinessOrderDTO> orderMap =
                    IterUtil.toMap(successList, BusinessOrderDTO::getSequenceNo, Function.identity());
            final InnerBeDraftOrderImportDTO importResult =
                    businessService.importBusinessOrder(userId, successList);
            if (!importResult.getSuccess()) {
                // 如果都失败了
                successList.stream()
                        .map(BusinessOrderDTO::getSequenceNo)
                        .map(error -> ImportMessage.builder()
                                .row(error)
                                .message(importResult.getMessage())
                                .status("导入失败")
                                .source(orderMap.get(error).getSource())
                                .build())
                        .forEach(errorList::add);
            } else {
                // 处理失败的
                final InnerBeDraftOrderImportDTO.DataDTO data = importResult.getData();
                data.getRecords().stream()
                        .filter(v -> !v.getSuccess())
                        .map(v -> ImportMessage.builder()
                                .row(v.getSequenceNo())
                                .message(v.getErrorMessage())
                                .orderNumber(v.getOrderNumber()).status("导入失败")
                                .source(orderMap.get(v.getSequenceNo()).getSource())
                                .build())
                        .forEach(errorList::add);
            }
        }
        if (CollectionUtil.isNotEmpty(errorList)) {
            // 根据行号排序
            errorList.sort(Comparator.comparing(ImportMessage::getRow));
            // 合并错误数据
            List<Map<Integer, String>> errorData =
                    errorList.stream().map(ImportMessage::getSource)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                 ExcelWriter writer = EasyExcel.write(outputStream).build()) {
                final String errorDatasheetName = "错误运单数据";
                // 添加批注
                final List<CommentModel> comments = errorList.stream()
                        .map(v ->
                                CommentModel.createCommentModel(
                                        errorDatasheetName,
                                        v.getRow(),
                                        0,
                                        v.getMessage()))
                        .collect(Collectors.toList());
                WriteSheet dataSheet = EasyExcel.writerSheet()
                        .sheetName(errorDatasheetName)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .registerWriteHandler(new CommentWriteHandler(comments))
                        .head(head)
                        .build();
                WriteSheet errorSheet = EasyExcel.writerSheet()
                        .sheetName("错误信息")
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .head(ImportMessage.class)
                        .build();
                writer.write(errorData, dataSheet);
                // 标识准确行号
                errorList.forEach(v -> v.setRow(v.getRow() + 2));
                writer.write(errorList, errorSheet);
                writer.finish();
                String base64 = EJFUtil.byteToString(outputStream);
                final JsonResult<?> error = JsonResult.error("导入失败");
                final ImmutableMap<String, ? extends Serializable> result = ImmutableMap.of(
                        "failuresCount", errorList.size(),
                        "successCount", orderList.size() - errorList.size(),
                        "totalCount", orderList.size(),
                        "errorMsg", base64,
                        "fileName", StrUtil.format("商业快递_导入错误数据_{}.xlsx",
                                DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT)
                        ));
                error.putAll(result);
                error.put(JsonResult.CODE_TAG, 502);
                return error;
            }
        }
        return JsonResult.success("全部导入成功", null);
    }

    /**
     * value转key
     *
     * @param businessOrderDTO 订单
     */
    private void valueToKey(BusinessOrderDTO businessOrderDTO) {
        if (StringUtils.isNotBlank(businessOrderDTO.getDutyType())) {
            // 设置关税类型
            Optional.ofNullable(BusinessFixedEnum.DUTY_TYPE.get(StringUtils.strip(businessOrderDTO.getDutyType())))
                    .ifPresent(businessOrderDTO::setDutyType);
        }
        if (StringUtils.isNotBlank(businessOrderDTO.getShippingMethod())) {
            // 设置发货方式
            Optional.ofNullable(BusinessFixedEnum.SHIPPING_METHOD.get(StringUtils.strip(businessOrderDTO.getShippingMethod())))
                    .ifPresent(businessOrderDTO::setShippingMethod);
        }
        if (StringUtils.isNotBlank(businessOrderDTO.getReceiverInfo().getTaxType())) {
            // 获取税号类型
            final List<ValueDTO> taxType = cmccService.getBusinessOrderTypeEnum(
                    CmccItemEnum.TAX_TYPE,
                    StringUtils.strip(businessOrderDTO.getReceiverInfo().getTaxType()),
                    ValueDTO::getCode,
                    ValueDTO::getValue);
            taxType.stream().findFirst().ifPresent(x -> businessOrderDTO.getReceiverInfo().setTaxType(x.getCode()));
        }
        if (StringUtils.isNotBlank(businessOrderDTO.getPackageType())) {
            // 获取包裹类型
            final List<ValueDTO> packType = cmccService.getBusinessOrderTypeEnum(
                    CmccItemEnum.PACK_TYPE,
                    StringUtils.strip(businessOrderDTO.getPackageType()),
                    ValueDTO::getCode,
                    ValueDTO::getValue);
            packType.stream().findFirst().ifPresent(x -> businessOrderDTO.setPackageType(x.getCode()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getBatteryType())) {
            // 获取电池类型
            final List<ValueDTO> batteryType = cmccService.getBusinessOrderTypeEnum(
                    CmccItemEnum.BATTERY_TYPE,
                    StringUtils.strip(businessOrderDTO.getBatteryType()),
                    ValueDTO::getCode,
                    ValueDTO::getValue);
            batteryType.stream().findFirst().ifPresent(x -> businessOrderDTO.setBatteryType(x.getCode()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getGoodsType())) {
            // 获取商品类型
            final List<ValueDTO> commodityType = cmccService.getBusinessOrderTypeEnum(
                    CmccItemEnum.COMMODITY_TYPE,
                    StringUtils.strip(businessOrderDTO.getGoodsType()),
                    ValueDTO::getCode,
                    ValueDTO::getValue);
            commodityType.stream().findFirst().ifPresent(x -> businessOrderDTO.setGoodsType(x.getCode()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getCustomsInfo().getCurrency())) {
            // 获取币种
            final List<Currency> currency = EJFUtil.baseInfoService.getCurrencyByEverything(StringUtils.strip(businessOrderDTO.getCustomsInfo().getCurrency()));
            currency.stream().findFirst().ifPresent(x -> businessOrderDTO.getCustomsInfo().setCurrency(x.getCode()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getReceiverInfo().getCountryId())) {
            // 获取国家
            final List<CountryResultDTO.RegionListDTO> country = EJFUtil.plmService.getCountry(StringUtils.strip(businessOrderDTO.getReceiverInfo().getCountryId()));
            country.stream().findFirst().ifPresent(x -> businessOrderDTO.getReceiverInfo().setCountryId(x.getId()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getChannelId())) {
            // 获取产品
            final List<ProductResultDTO.DataDTO> products = EJFUtil.plmService.getProducts(
                    PLMPlatformEnum.EJF,
                    StringUtils.strip(businessOrderDTO.getChannelId()),
                    ProductResultDTO.DataDTO::getProductNumber,
                    ProductResultDTO.DataDTO::getProductCnName);
            products.stream().findFirst().ifPresent(x -> businessOrderDTO.setChannelId(x.getProductNumber()));
        }

        if (StringUtils.isNotBlank(businessOrderDTO.getCompanyCode())) {
            // 获取仓库
            final List<WareHouseVo> warehouses = cmccService.getWarehouses(StringUtils.strip(businessOrderDTO.getCompanyCode()), WareHouseVo::getCode, WareHouseVo::getName);
            warehouses.stream().findFirst().ifPresent(x -> businessOrderDTO.setCompanyCode(x.getCode()));
        }

    }

    /**
     * 订单聚合
     *
     * @param orderList 订单列表
     * @return 聚合后的订单
     */
    private BusinessOrderDTO polymerization(List<BusinessOrderDTO> orderList) {
        if (CollectionUtil.isNotEmpty(orderList)) {
            // 获取包裹信息
            final List<BusinessOrderDTO.ParcelInfoDTO> parcelInfos =
                    orderList.stream().map(BusinessOrderDTO::getParcelInfo)
                            .flatMap(Collection::stream)
                            .filter(v -> StringUtils.isNotBlank(v.getHeight()))
                            .collect(Collectors.toList());
            // 获取商品信息
            final List<BusinessOrderDTO.ProductInfoDTO> productInfos =
                    orderList.stream().map(BusinessOrderDTO::getProductInfo)
                            .flatMap(Collection::stream)
                            .filter(v -> StringUtils.isNotBlank(v.getGoodsNameEn()))
                            .collect(Collectors.toList());
            // 获取商品信息
            final List<Map<Integer, String>> sources =
                    orderList.stream().map(BusinessOrderDTO::getSource)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
            // 获取第一个订单
            final BusinessOrderDTO order = orderList.get(0);
            // 开始行号
            final Integer sequenceNo =
                    orderList.stream().map(BusinessOrderDTO::getSequenceNo).min(Integer::compareTo).orElse(order.getSequenceNo());
            // 设置外箱数量
            order.getCustomsInfo().setTotalQuantity(CollectionUtil.size(parcelInfos));
            // 返回聚合后的订单
            return BusinessOrderDTO.builder()
                    .sequenceNo(sequenceNo)
                    .companyCode(order.getCompanyCode())
                    .channelId(order.getChannelId())
                    .orderNumber(order.getOrderNumber())
                    .remark(order.getRemark())
                    .packageType(order.getPackageType())
                    .goodsType(order.getGoodsType())
                    .batteryType(order.getBatteryType())
                    .dutyType(order.getDutyType())
                    .shippingMethod(order.getShippingMethod())
                    .domesticLogisticsCompany(order.getDomesticLogisticsCompany())
                    .domesticTrackingNo(order.getDomesticTrackingNo())
                    .receiverInfo(order.getReceiverInfo())
                    .senderInfo(order.getSenderInfo())
                    .customsInfo(order.getCustomsInfo())
                    .parcelInfo(parcelInfos)
                    .productInfo(productInfos)
                    .source(sources)
                    .build();
        }
        return null;
    }

    /**
     * map转订单
     *
     * @param map map
     * @return 订单
     */
    private static BusinessOrderDTO mapToBusinessOrder(Map<Integer, String> map) {
        // 基本信息
        // final String sequenceNo = map.get(-1);
        final String companyCode = map.get(0);
        final String countryId = map.get(1);
        final String channelId = map.get(2);
        final String currency = map.get(3);
        final String orderNumber = map.get(4);
        final String packageType = map.get(5);
        final String goodsType = map.get(6);
        final String batteryType = map.get(7);
        final String dutyType = map.get(8);
        final String shippingMethod = map.get(9);
        final String domesticLogisticsCompany = map.get(10);
        final String domesticTrackingNo = map.get(11);

       

        // 包裹信息
        final String length = map.get(12);
        final String width = map.get(13);
        final String height = map.get(14);
        final String weight = map.get(15);

        // 寄运商品信息
        final String goodsNameEn = map.get(16);
        final String goodsNameCh = map.get(17);
        final String priceSimple = map.get(18);
        final String quantity = map.get(19);
        final String hscode = map.get(20);
        final String material = map.get(21);
        final String use = map.get(22);
        final String brand = map.get(23);
        final String model = map.get(24);

         // 申报信息
        final String ioss = map.get(25);
        final String eori = map.get(26);
        final String packFee = map.get(27);
        final String insurance = map.get(28);
        final String otherFee = map.get(29);
        // final String price = map.get(17); // 申报总价

        // 发件人信息
        final String fState = map.get(30);
        final String fCity = map.get(31);
        final String fDistrict = map.get(32);
        final String fAddress = map.get(33);
        final String fZipCode = map.get(34);
        final String fCompany = map.get(35);
        final String fTaxNumber = map.get(36);
        final String fName = map.get(37);
        final String fPhone = map.get(38);
        final String fEmail = map.get(39);

        // 收件人信息
        final String rState = map.get(40);
        final String rCity = map.get(41);
        final String rOutskirts = map.get(42);
        final String rCompany = map.get(43);
        final String rName = map.get(44);
        final String rZipCode = map.get(45);
        final String rMobile = map.get(46);
        final String rPhone = map.get(47);
        final String rEmail = map.get(48);
        final String rTaxType = map.get(49);
        final String rTaxNumber = map.get(50);
        final String rAddress1 = map.get(51);
        final String rAddress2 = map.get(52);
        final String rAddress3 = map.get(53);

        // 其他信息
        final String isSignature = Objects.equals(map.get(54), "是") ? "1" : "0";
        final String isInsurance = Objects.equals(map.get(55), "是") ? "1" : "0";
        final String isCustomsService = Objects.equals(map.get(56), "是") ? "1" : "0";
        final String remark = map.get(57);


        final BusinessOrderDTO.ProductInfoDTO productInfo = BusinessOrderDTO.ProductInfoDTO.builder()
                .goodsNameCh(goodsNameCh)
                .goodsNameEn(goodsNameEn)
                .price(priceSimple)
                .quantity(quantity)
                .hscode(hscode)
                .material(material)
                .brand(brand)
                .model(model)
                .use(use)
                // 商品销售链接
                //.url()
                .build();

        final BusinessOrderDTO.ParcelInfoDTO parcelInfo = BusinessOrderDTO.ParcelInfoDTO.builder()
                .height(height)
                .width(width)
                .length(length)
                .weight(weight)
                .build();

        final BusinessOrderDTO.CustomInfosDTO customsInfo = BusinessOrderDTO.CustomInfosDTO.builder()
                .isCustomsService(isCustomsService)
                .isSignature(isSignature)
                .isInsurance(isInsurance)
                .otherFee(otherFee)
                .currency(currency)
                .insurance(insurance)
                .packFee(packFee)
                .ioss(ioss)
                .eori(eori)
                // 申报总价
                // .price(price)
                // 外箱数量
                //.totalQuantity()
                .build();


        final BusinessOrderDTO.SenderInfoDTO senderInfo = BusinessOrderDTO.SenderInfoDTO.builder()
                .name(fName)
                .phone(fPhone)
                .email(fEmail)
                .company(fCompany)
                // 发件人国家
                //.countryId()
                .state(fState)
                .city(fCity)
                .district(fDistrict)
                .zipCode(fZipCode)
                .taxNumber(fTaxNumber)
                .address(fAddress)
                .build();


        final BusinessOrderDTO.ReceiverInfoDTO receiverInfo = BusinessOrderDTO.ReceiverInfoDTO.builder()
                .name(rName)
                .phone(rPhone)
                .mobile(rMobile)
                .email(rEmail)
                .company(rCompany)
                .countryId(countryId)
                .state(rState)
                .outskirts(rOutskirts)
                .city(rCity)
                .zipCode(rZipCode)
                .address1(rAddress1)
                .address2(rAddress2)
                .address3(rAddress3)
                // 门牌号
                //.houseNumber()
                .taxType(rTaxType)
                .taxNumber(rTaxNumber)
                .build();

        return BusinessOrderDTO.builder()
                .companyCode(companyCode)
                .channelId(channelId)
                .orderNumber(orderNumber)
                .remark(remark)
                .packageType(packageType)
                .goodsType(goodsType)
                .batteryType(batteryType)
                .dutyType(dutyType)
                .shippingMethod(shippingMethod)
                .domesticLogisticsCompany(domesticLogisticsCompany)
                .domesticTrackingNo(domesticTrackingNo)
                .receiverInfo(receiverInfo)
                .senderInfo(senderInfo)
                .customsInfo(customsInfo)
                .parcelInfo(ListUtil.toList(parcelInfo))
                .productInfo(ListUtil.toList(productInfo))
                .source(ListUtil.toList(map))
                .build();
    }

    /**
     * 订单修改
     *
     * @param param 参数
     * @return 草稿订单修改结果
     */
    @PostMapping("/editBusinessDraft")
    @Logger(module = BUSINESS, name = "订单修改")
    public JsonResult<?> editBusinessDraft(@RequestBody BusinessOrderDTO param) {
        accountService.existAccountThrow(param.getUserId());
        return vo(businessService.editBusinessDraft(param));
    }

    /**
     * 订单生成运单
     *
     * @param ids 参数
     * @return 订单生成运单结果
     */
    @Validated
    @PostMapping("/businessDraftToOrder")
    @Logger(module = BUSINESS, name = "订单生成运单", type = Type.WAYBILL_NUMBER, rsp = "data.waybillNumber")
    public JsonResult<?> businessDraftToOrder(
            @JsonParam @NotBlank(message = "制单账号不能为空") String userId,
            @JsonParam @Size(min = 1, message = "ids不能为空") List<String> ids) {
        accountService.existAccountThrow(userId);
        final Map<Boolean, List<Map.Entry<String, InnerBeDraftOrderGenerateDTO>>> response = ids.parallelStream()
                // 去重
                .distinct()
                .map(id -> MapUtil.entry(id, businessService.businessDraftToOrder(userId, id)))
                .collect(Collectors.groupingBy(v -> v.getValue().getSuccess()));
        // 生成成功的
        final List<Map.Entry<String, InnerBeDraftOrderGenerateDTO>> success = response.get(Boolean.TRUE);
        // 生成失败的
        final List<Map.Entry<String, InnerBeDraftOrderGenerateDTO>> fail = response.get(Boolean.FALSE);
        final JsonResult<Object> result = JsonResult.success();
        if (CollectionUtil.isNotEmpty(success)) {
            // 设置结果
            result.setData(success.stream().map(Map.Entry::getValue).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(fail)) {
            // 构建错误信息
            final String error = fail.stream()
                    .map(x -> StrUtil.format("订单ID {} 生成运单失败 {}", x.getKey(), x.getValue().getMessage()))
                    .collect(Collectors.joining(StringUtils.LF));
            result.setSuccess(false);
            result.setCode("500");
            result.setMessage(error);
        }
        return result;
    }

    /**
     * 订单生成运单-所有
     *
     * @param param 参数
     * @return 订单生成运单结果
     */
    @PostMapping("/businessDraftToOrderAll")
    @Logger(module = BUSINESS, name = "订单生成运单-所有")
    public JsonResult<?> businessDraftToOrder(@RequestBody SearchParamDTO param) {
        accountService.existAccountThrow(param.getUserId());
        switch (param.getStatus()) {
            case ALL_DRAFT:
            case UNDOCUMENTED:
            case ORDER_FAILED:
            case BILLED_DRAFT:
                // 草稿查询
                param.setOrderNumbers(param.getListNumber());
                param.setListStatus(param.getStatus().getOrderEnumKey());
                param.setPageNum(1);
                param.setPageSize(Integer.MAX_VALUE);
                final InnerBeDraftOrderGetListFilterDTO businessDraft =
                        businessService.getBusinessDraft(param);
                if (!businessDraft.getSuccess()) {
                    return JsonResult.error(businessDraft.getMessage());
                }
                final InnerBeDraftOrderGetListFilterDTO.DataDTO draftList = businessDraft.getData();
                if (CollectionUtil.isNotEmpty(draftList.getRecords())) {
                    final List<String> ids = draftList.getRecords().stream()
                            .map(BusinessSimpleDraftDTO::getId).collect(Collectors.toList());
                    // 批量订单转运单
                    return businessDraftToOrder(param.getUserId(), ids);
                }
            case ALL_ORDER:
            case BILLED:
            case SHIPMENT_CONFIRMED:
            case RECEIVED:
            case SHIPPED:
            case HAS_BEEN_PLACED:
            case CANCELLED:
            case RETAINED:
            case END_OF_TRACE:
            case RETURN_EXCEPTION:
            default:
                throw ResponseCode.PORTAL_9501.getError();
        }
    }

    /**
     * 删除订单
     *
     * @param userId 参数
     * @param ids    参数
     * @return 删除订单结果
     */
    @Validated
    @PostMapping("/deleteBusinessDraft")
    @Logger(module = BUSINESS, name = "删除订单")
    public JsonResult<?> deleteBusinessDraft(
            @JsonParam @NotBlank(message = "制单账号不能为空") String userId,
            @JsonParam @Size(min = 1, message = "ids不能为空") List<String> ids) {
        accountService.existAccountThrow(userId);
        return vo(businessService.deleteBusinessDraft(userId, ids));
    }

    /**
     * 订单详细
     *
     * @param userId 参数
     * @param id     参数
     * @return 订单详细结果
     */
    @Validated
    @PostMapping("/getBusinessDraftDetail")
    @Logger(module = BUSINESS, name = "订单详细")
    public JsonResult<?> getBusinessDraftDetail(
            @JsonParam @NotBlank(message = "制单账号不能为空") String userId,
            @JsonParam @NotBlank(message = "id不能为空") String id) {
        accountService.existAccountThrow(userId);
        return vo(businessService.getBusinessDraftDetail(userId, id));
    }

    /**
     * 已存在订单号查询
     *
     * @param param 参数
     * @return 已存在订单号查询结果
     */
    @Validated
    @PostMapping("/getExistBusinessDraftNumber")
    @Logger(module = BUSINESS, name = "已存在订单号查询")
    public JsonResult<?> getExistBusinessDraftNumber(@RequestBody InnerBeOrderNumberGetListParamDTO param) throws IOException {
        accountService.existAccountThrow(param.getUserId());
        // 查询最近三个月的
        param.setStartTime(DateUtil.offsetMonth(DateUtil.date(), -3).toDateStr());
        param.setEndTime(DateUtil.date().toDateStr());
        final InnerBeOrderNumberGetListDTO result = businessService.getExistBusinessDraftNumber(param);
        if (result.getSuccess()) {
            final InnerBeOrderNumberGetListDTO.DataDTO resultData = result.getData();
            if (Objects.isNull(resultData)) {
                return JsonResult.success();
            }
            final List<String> number = resultData.getListOrderNumber();
            if (CollectionUtil.isEmpty(number)) {
                return JsonResult.success();
            }
            int size = CollectionUtil.size(number);
            if (size <= 10) {
                // 如果是10个文件，则不生成文件
                return JsonResult.error(
                        StrUtil.format("当前有 {} 个订单号近90天重复制单，订单号为 {}，请确认是否继续制单！", size, number),
                        ImmutableMap.of("file", false));
            } else {
                // 已存在订单号ExcelData
                final List<List<String>> data = number.stream()
                        .map(Collections::singletonList)
                        .collect(Collectors.toList());
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    // 构建Base64
                    EasyExcel.write(outputStream)
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .sheet("已存在订单号")
                            .head(Collections.singletonList(Collections.singletonList("已存在订单号")))
                            .doWrite(data);
                    String base64 = EJFUtil.byteToString(outputStream);
                    // 构建结果集
                    ImmutableMap<String, ? extends Serializable> response = ImmutableMap.of(
                            "base64", base64,
                            "fileName", StrUtil.format("商业快递_已存在订单号_{}.xlsx", DateUtil.today()),
                            "file", true);
                    return JsonResult.error(StrUtil.format("当前有 {} 个订单号近90天重复制单，请确认是否继续制单！", size)
                            , response);
                }
            }
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 已存在订单号查询
     *
     * @param param 参数
     * @return 已存在订单号查询结果
     */
    @PostMapping("/getExistBusinessDraftNumberAll")
    @Logger(module = BUSINESS, name = "已存在订单号查询-所有")
    public JsonResult<?> getExistBusinessDraftNumberAll(@RequestBody SearchParamDTO param) throws IOException {
        accountService.existAccountThrow(param.getUserId());
        switch (param.getStatus()) {
            case ALL_DRAFT:
            case UNDOCUMENTED:
            case ORDER_FAILED:
            case BILLED_DRAFT:
                // 草稿查询
                param.setOrderNumbers(param.getListNumber());
                param.setListStatus(param.getStatus().getOrderEnumKey());
                param.setPageNum(1);
                param.setPageSize(Integer.MAX_VALUE);
                final InnerBeDraftOrderGetListFilterDTO businessDraft =
                        businessService.getBusinessDraft(param);
                if (!businessDraft.getSuccess()) {
                    return JsonResult.error(businessDraft.getMessage());
                }
                final InnerBeDraftOrderGetListFilterDTO.DataDTO draftList = businessDraft.getData();
                if (CollectionUtil.isNotEmpty(draftList.getRecords())) {
                    final List<String> orderNumbers = draftList.getRecords().stream()
                            .map(BusinessSimpleDraftDTO::getOrderNumber).collect(Collectors.toList());
                    final InnerBeOrderNumberGetListParamDTO checkParam = InnerBeOrderNumberGetListParamDTO.builder()
                            .listOrderNumber(orderNumbers)
                            .userId(param.getUserId())
                            .build();
                    // 批量订单转运单
                    return getExistBusinessDraftNumber(checkParam);
                }
            case ALL_ORDER:
            case BILLED:
            case SHIPMENT_CONFIRMED:
            case RECEIVED:
            case SHIPPED:
            case HAS_BEEN_PLACED:
            case CANCELLED:
            case RETAINED:
            case END_OF_TRACE:
            case RETURN_EXCEPTION:
            default:
                throw ResponseCode.PORTAL_9501.getError();
        }
    }

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /**
     * 多产品运价试算
     *
     * @param param 参数
     * @return 运价试算结果
     */
    @Validated
    @PostMapping("/manyPieces")
    public JsonResult<?> manyPieces(@RequestBody ManyPiecesParamDTO param) {
        final BigDecimal totalWeight = param.getItems().stream()
                .map(ManyPiecesParamDTO.ItemsDTO::getWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        final CalcPriceParamDTO paramDTO = new CalcPriceParamDTO();
        paramDTO.setCountryId(param.getCountryId());
        paramDTO.setCityId(param.getCompanyCode());
        paramDTO.setPostCode(param.getPostCode());
        paramDTO.setProductAttributes(param.getProductAttributes());
        paramDTO.setProductTypes("17");
        paramDTO.setWeight(String.valueOf(totalWeight));

        // 开启失败重试
        PLMCalcPriceResultDTO productResult = HttpUtil.doPost(
                plmUrl.concat(TAIL_PRODUCT),
                new PLMCalcPriceParamDTO(paramDTO),
                PLMCalcPriceResultDTO.class,
                true);
        // 使用断言判断是否获取成功
        if (!productResult.getSuccess()) {
            return JsonResult.error("获取产品失败" + productResult.getMessage());
        }
        List<PLMCalcPriceResultDTO.DataDTO> products = productResult.getData();
        if (CollectionUtil.isEmpty(products)) {
            return JsonResult.error("您所输入的查询信息未找到适配产品，请更新查询信息重试或咨询销售！");
        }
        final Map<String, PLMCalcPriceResultDTO.DataDTO> productMap = products.stream()
                .collect(Collectors.toMap(PLMCalcPriceResultDTO.DataDTO::getProductNumber, Function.identity(), (a, b) -> a));


        final PacketApplyInfoVo info = packetBusinessApplyService.getPacketApplyInfo(getUserCode());
        final String merchantCode = info.getMerchantCode();
        if (CollectionUtil.isNotEmpty(products)) {
            final List<ManyPieceDTO> pieces = products.parallelStream().map(v -> {
                final ManyPieceParamDTO dto = new ManyPieceParamDTO();
                dto.setProductCode(v.getProductNumber());
                dto.setCompanyCodeFrom(param.getCompanyCode());
                dto.setCustomerCode(merchantCode);
                dto.setCountryId(param.getCountryId());
                dto.setPostCode(param.getPostCode());
                dto.setChargingTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
                final List<ManyPieceParamDTO.ItemsDTO> items = new ArrayList<>();
                param.getItems().stream().map(item -> {
                    final Integer count = item.getCount();
                    return IntStream.range(0, count).mapToObj(i -> {
                        final ManyPieceParamDTO.ItemsDTO itemsDTO =
                                new ManyPieceParamDTO.ItemsDTO();
                        itemsDTO.setLength(item.getLength());
                        itemsDTO.setWidth(item.getWidth());
                        itemsDTO.setHigh(item.getHeight());
                        itemsDTO.setWeight(item.getWeight());
                        itemsDTO.setUnit(item.getUnit());
                        return itemsDTO;
                    }).collect(Collectors.toList());
                }).flatMap(Collection::stream).forEach(items::add);
                dto.setItems(items);
                final ManyPieceDTO result = businessService.manyPiece(dto);
                final ManyPieceDTO.DataDTO dataDTO = result.getData();
                if (Objects.nonNull(dataDTO)) {
                    // 设置产品信息
                    dataDTO.setProductCode(v.getProductNumber());
                    dataDTO.setProductName(v.getProductName());
                    dataDTO.setProductDetail(productMap.get(v.getProductNumber()));
                    final JSONObject productDetail = productDetail(v.getProductNumber(), param.getCountryId());
                    // 获取成功才设置
                    if (productDetail != null && productDetail.getBoolean("result") && productDetail.getJSONObject("data") != null) {
                        dataDTO.setRemark(productDetail.getJSONObject("data").getJSONObject("priceDirectoryRemark"));
                        JSONArray topcRemarks = productDetail.getJSONObject("data").getJSONArray("priceDirectoryRemarkTopc");
                        // 设置天数
                        final String referAging = topcRemarks.stream().map(t -> (JSONObject) t)
                                .filter(t -> Objects.equals(t.getString("countryId"), param.getCountryId()))
                                .findFirst()
                                .map(t -> t.getString("referAging"))
                                .orElse(null);
                        dataDTO.setReferAging(referAging);
                        dataDTO.setTopcRemarks(topcRemarks);
                    }
                }
                return result;
            }).collect(Collectors.toList());
            return JsonResult.success(pieces);
        }
        return JsonResult.error("无产品");
    }

    /**
     * 获取产品详情
     *
     * @param productCode  产品ID
     * @param countryId    国家ID
     * @return  产品详情
     */
    public JSONObject productDetail(String productCode, String countryId) {
        //------依次去拿去产品说明------//
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("productCode", productCode);
        jsonObject.put("countryIds", ListUtil.toList(countryId));
        return HttpUtil.doPost(productDetailUrl, jsonObject);
    }

    /**
     * 运价试算
     *
     * @param param 参数
     * @return 运价试算结果
     */
    @Validated
    @PostMapping("/manyPiece")
    @Logger(module = BUSINESS, name = "商业快递运价试算")
    public JsonResult<?> manyPiece(@RequestBody ManyPieceParamDTO param) {
        // 设置揽收仓
        if (StringUtils.isBlank(param.getCompanyCodeFrom())) {
            final AccountGetResVO account = accountService.getAccount(param.getCustomerCode());
            final String warehouseCode = account.getWarehouseCode();
            param.setCompanyCodeFrom(warehouseCode);
        }
        // 设置计费单位
        CollectionUtil.emptyIfNull(param.getItems()).forEach(v -> v.setUnit("kg"));
        // 设置计费时间
        param.setChargingTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        final ManyPieceDTO dto = businessService.manyPiece(param);
        if (dto.getResult()) {
            final ManyPieceDTO.DataDTO data = dto.getData();
            final List<ManyPieceDTO.DataDTO.ExpenseItemsDTO> expenseItems = data.getExpenseItems();
            // 计费项
            if (CollectionUtil.isNotEmpty(expenseItems)) {
                final BigDecimal totalMoney = expenseItems.stream()
                        .map(ManyPieceDTO.DataDTO.ExpenseItemsDTO::getCnyMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return JsonResult.success(ImmutableMap.of("totalMoney", totalMoney, "item", expenseItems));
            }
            return JsonResult.error("无计费项，试算失败");
        }
        return JsonResult.error(dto.getMessage());
    }

    /**
     * 确认发货
     *
     * @param userId         制单账号
     * @param waybillNumbers 运单号
     * @return 确认发货
     */
    @Validated
    @PostMapping("/confirmDelivery")
    @Logger(module = BUSINESS, name = "确认发货")
    public JsonResult<?> confirmDelivery(@JsonParam @NotBlank(message = "制单账号不能为空") String userId,
                                         @JsonParam @Size(min = 1, message = "运单号不能为空") List<String> waybillNumbers) {
        accountService.existAccountThrow(userId);
        final String template = "确认发货失败 {} {}";
        // 获取取消失败的
        final List<String> errorList = waybillNumbers.parallelStream()
                .map(param -> MapUtil.entry(param, businessService.businessOrderConfirmDelivery(userId, param)))
                // 仅获取失败的
                .filter(v -> !v.getValue().getSuccess())
                .map(x -> StrUtil.format(template, x.getKey(), x.getValue().getMessage()))
                .collect(Collectors.toList());
        // 有错误则返回错误
        if (CollectionUtil.isNotEmpty(errorList)) {
            return JsonResult.error(String.join(StringUtils.LF, errorList));
        }
        return JsonResult.success();
    }

    /**
     * 取消确认发货
     *
     * @param userId         制单账号
     * @param waybillNumbers 运单号
     * @return 取消确认发货
     */
    @Validated
    @PostMapping("/cancelDelivery")
    @Logger(module = BUSINESS, name = "取消确认发货")
    public JsonResult<?> cancelDelivery(@JsonParam @NotBlank(message = "制单账号不能为空") String userId,
                                        @JsonParam @Size(min = 1, message = "运单号不能为空") List<String> waybillNumbers) {
        accountService.existAccountThrow(userId);
        final String template = "取消确认发货失败 {} {}";
        // 获取取消失败的
        final List<String> errorList = waybillNumbers.parallelStream().map(w ->
                        InnerBeOrderAcknowledgeCancelParamDTO.builder()
                                .userId(userId)
                                .waybillNumber(w)
                                .platform(EJFUrl.SOURCE)
                                .build())
                .map(param -> MapUtil.entry(param, businessService.businessOrderCancelDelivery(param)))
                // 仅获取失败的
                .filter(v -> !v.getValue().getSuccess())
                .map(x -> StrUtil.format(template, x.getKey().getWaybillNumber(), x.getValue().getMessage()))
                .collect(Collectors.toList());
        // 有错误则返回错误
        if (CollectionUtil.isNotEmpty(errorList)) {
            return JsonResult.error(String.join(StringUtils.LF, errorList));
        }
        return JsonResult.success();
    }

    /**
     * 生成交接单
     *
     * @param param 参数
     * @return JsonResult
     */
    @RequestMapping("/generateTransferOrder")
    @Valid
    @Logger(module = BUSINESS, name = "生成交接单", type = Type.WAYBILL_NUMBER, req = "listNumber", recordRsp = false)
    public JsonResult<?> generateTransferOrder(@RequestBody InnerBeExpressOrderGetListParamDTO param) throws IOException {
        accountService.existAccountThrow(param.getUserId());
        final BeOrderGetListDTO detail = businessService.getBusinessOrderDetailBatch(param);
        if (detail.getSuccess()) {
            final List<BusinessOrderDTO> detailData = detail.getData();
            if (CollectionUtil.isNotEmpty(detailData)) {
                try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                    // 转换为简单订单对象
                    final List<SimpleOrder> orders = IntStream.range(0, detailData.size())
                            .mapToObj(i -> new SimpleOrder(i + 1, detailData.get(i)))
                            .collect(Collectors.toList());
                    // 生成交接单
                    orderService.generateTransferOrder(getUserCode(), out, orders);
                    final String fileName = String.format(TRANSFER_ORDER, DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT));
                    final String base64 = EJFUtil.byteToString(out);
                    return JsonResult.success(ImmutableMap.of("fileName", fileName, "base64", base64));
                }
            } else {
                return JsonResult.error("获取订单为空");
            }
        }
        return JsonResult.error(detail.getMessage());
    }


    /**
     * 获取json结果
     *
     * @return json结果
     */
    public JsonResult<?> vo(GeneralDTO dto) {
        if (dto.getSuccess()) {
            return JsonResult.success(dto.getData());
        }
        return JsonResult.error(dto.getMessage());
    }

    // 根据新的plm,通过产品查询国家
    @RequestMapping("getCountryListByProductCode")
    public JsonResult getCountryListByProductCode(@JsonParam String channelId){
       businessService.getCountryByProductCode(channelId);
       return JsonResult.success(businessService.getCountryByProductCode(channelId));
    }

}
