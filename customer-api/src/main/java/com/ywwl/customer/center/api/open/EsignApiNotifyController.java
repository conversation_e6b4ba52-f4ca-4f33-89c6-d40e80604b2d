package com.ywwl.customer.center.api.open;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * @author: dinghy
 * @date: 2023/3/7 17:45
 */
@Slf4j
@Controller
@RequestMapping("/api/esign")
public class EsignApiNotifyController {
    @Resource
    private CustomerAuthService commonCrmService;

    /**
     * @description 处理人脸识别回调
     * <AUTHOR>
     * @date 2021/11/5 17:22
     */
    @RequestMapping("faceAuthNotify")
    @ResponseBody
    public ResponseEntity faceAuthCallback(@RequestBody JSONObject jsonObject) {
        try {
            log.info("人脸识认证回调参数:{}", jsonObject);
            commonCrmService.pushFaceAuthNotifyMessage(jsonObject);
            return new ResponseEntity(HttpStatus.OK);
        } catch (Exception e) {
            log.error("人脸识认证回调异常,原因:{}", e.getMessage());
            return new ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * <AUTHOR>
     * @description
     * @date 2023/3/8 9:28
     **/
    @RequestMapping("sendBankMoneyNotify")
    @ResponseBody
    public ResponseEntity sendCompanyBankMoneyCallback(@RequestBody JSONObject jsonObject){
        try {
            log.info("企业打款通知回调参数:{}", jsonObject);
            commonCrmService.pushCompanyBankMoneyNotify(jsonObject);
            return new ResponseEntity(HttpStatus.OK);
        } catch (Exception e) {
            log.error("企业打款通知回调异常,原因:{}", e.getMessage());
            return new ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * <AUTHOR>
     * @description
     * @date 2023/3/17 14:11
     **/
    @RequestMapping("contractNotify")
    @ResponseBody
    public ResponseEntity contractNotify(@RequestBody JSONObject jsonObject){
        try {
            log.info("合同签署通知回调参数:{}", jsonObject);
            commonCrmService.pushContractNotify(jsonObject);
            return new ResponseEntity(HttpStatus.OK);
        } catch (Exception e) {
            log.error("合同签署通知回调异常,原因:{}", e.getMessage());
            DingTalkClient.sendMessage(e.getMessage()+"--"+jsonObject.toString());
            return new ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
