package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.provider.service.IndexService;
import com.ywwl.customer.center.modules.international.dto.ByOrderDTO;
import com.ywwl.customer.center.modules.international.dto.MaterialApplyDTO;
import com.ywwl.customer.center.modules.international.service.CrmWorkOrderService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.CollectAddressVo;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/4/12
 */
@Slf4j
@RestController
@RequestMapping("/workOrder")
public class WorkOrderController extends BaseController {
    @Resource
    private CrmWorkOrderService crmWorkOrderService;

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private AccountService accountService;
    @Resource
    private IndexService indexService;

    /**
     * 根据工单类型查询工单编号
     *
     * @param map
     * @return
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "根据工单类型查询工单编号")
    @PostMapping("/generateId")
    public JsonResult<Object> generateId(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.generateId(map);
    }

    /***
     * //  获取工单编号
     * <AUTHOR>
     * @date 2023/4/12 16:47
     * @param byOrderDTO 参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "获取工单编号")
    @PostMapping("/getByOrderId")
    public JsonResult<Object> getByOrderId(@RequestBody ByOrderDTO byOrderDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        List<String> shippingAccounts = byOrderDTO.getCustomerCodes();
        if (null != shippingAccounts && !shippingAccounts.isEmpty()) {
            if (!accountService.existAccount(shippingAccounts, currentUser, 0)) {
                return JsonResult.error(ResponseCode.PORTAL_5060);
            }
        }
        return crmWorkOrderService.getByOrderId(byOrderDTO);
    }

    /***
     * //  创建工单
     * <AUTHOR>
     * @date 2023/4/12 17:20
     * @param materialApplyDTO
     * @param currentUser
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "创建工单")
    @PostMapping("/material/workOrderCreate")
    public JsonResult<Object> workOrderCreate(@RequestBody MaterialApplyDTO materialApplyDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        materialApplyDTO.setCreatorId(String.valueOf(currentUser.getUserId()));
        materialApplyDTO.setCreatorName(currentUser.getLoginName());
        materialApplyDTO.setOperatorId(String.valueOf(currentUser.getUserId()));
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        materialApplyDTO.setCustomerCode(merchantCode);
        materialApplyDTO.getData().setCustomerCode(merchantCode);
        return crmWorkOrderService.workOrderCreate(materialApplyDTO);
    }

    /**
     * TODO 是否要改造？加仓
     * 查询物料种类，需要根据仓还有业务账号
     *
     * @param warehouseCode
     * @param currentUser
     * @return
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "查询物料种类")
    @GetMapping("/material/getMaterialType/{warehouseCode}")
    public JsonResult<Object> getMaterielType(@PathVariable("warehouseCode") String warehouseCode, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        return crmWorkOrderService.getMaterielType(warehouseCode);
    }

    /***
     * //  更新工单
     * <AUTHOR>
     * @date 2023/4/12 17:38
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "更新工单")
    @PostMapping("/material/workOrderRecord")
    public JsonResult<Object> workOrderRecord(@RequestBody MaterialApplyDTO materialApplyDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        materialApplyDTO.setOperatorId(String.valueOf(currentUser.getUserId()));
        materialApplyDTO.setOperatorName(currentUser.getLoginName());
        materialApplyDTO.setCreatorId(String.valueOf(currentUser.getUserId()));
        materialApplyDTO.setCreatorName(currentUser.getLoginName());
        materialApplyDTO.setOperatorId(String.valueOf(currentUser.getUserId()));
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        materialApplyDTO.setCustomerCode(merchantCode);
        if(materialApplyDTO.getData()!=null){
            materialApplyDTO.getData().setCustomerCode(merchantCode);
        }
        return crmWorkOrderService.workOrderRecord(materialApplyDTO);
    }

    /***
     * //  工单信息查询
     * <AUTHOR>
     * @date 2023/4/12 17:48
     * @param map  入参
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "工单信息查询")
    @PostMapping("/material/selectOrderList")
    public JsonResult<Object> selectOrderList(@RequestBody Map<String, Object> map, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        map.put("merchantCode", merchantCode);
        return crmWorkOrderService.selectOrderList(map);
    }

    /***
     * //TODO  物料申请
     * <AUTHOR>
     * @date 2022/11/7 11:48
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "物料申请")
    @PostMapping("/material/orderDetail")
    public JsonResult<Object> orderDetail(@RequestBody Map<String, Object> map, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        map.put("customerCode", merchantCode);
        return crmWorkOrderService.orderDetail(map);
    }

    /***
     * //TODO  获取司机位置
     * <AUTHOR>
     * @date 2022/11/7 11:48
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "获取司机位置")
    @PostMapping("/material/getDriverPosition")
    public JsonResult<Object> getDriverPosition(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.getDriverPosition(map);
    }

    /***
     * // 揽收点信息跟司机信息
     * <AUTHOR>
     * @date 2023/4/13 9:46
     * @param map 入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "获取揽收点信息")
    @PostMapping("/material/getCollectionPoint")
    public JsonResult getCollectionPoint(@RequestBody Map<String, Object> map, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        map.put("customerNo", merchantCode);
        return crmWorkOrderService.getCollectionPoint(map);
    }


    /**
     * 获取物料申请状态汇总
     *
     * @param currentUser
     * @return
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "获取物料申请状态")
    @PostMapping("/material/selMaterialsStatusNum")
    public JsonResult selMaterialsStatusNum(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Map<String, Object> map = new HashMap<>(2);
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        map.put("merchantCode", merchantCode);
        return crmWorkOrderService.selMaterialsStatusNum(map);
    }

    /**
     * 查询发票申请列表
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票申请列表")
    @PostMapping("/invoiceApplication/search")
    public JsonResult searchInvoiceData(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.searchInvoiceData(map);
    }


    /**
     * 查询发票申请状态列表
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票申请状态")
    @PostMapping("/invoiceApplication/selectInvoiceStatusNumber")
    public JsonResult searchInvoiceDataNumber(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.searchInvoiceDataNumber(map);
    }


    /**
     * 查询发票工单申请详情
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票申请详情")
    @PostMapping("/invoiceApplication/detail")
    public JsonResult searchInvoiceDataDetail(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.searchInvoiceDataDetail(map);
    }


    /**
     * 获取发票申请账期详情
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票账期详情")
    @PostMapping("/invoiceApplication/period")
    public JsonResult searchInvoiceDataPeriod(@RequestBody Map<String, Object> map, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo merchantCode = getMerchantCode(currentUser.getUserCode());
        map.put("merchantCode", merchantCode.getMerchantCode());
        map.put("source", "portal");
        return crmWorkOrderService.period(map);
    }

    public PacketApplyInfoVo getMerchantCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo;
    }

    /**
     * 获取发票申请注册地址
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票申请注册地址")
    @PostMapping("/invoiceApplication/address")
    public JsonResult searchInvoiceDataAddress(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.registerAddress(map);
    }

    /**
     * 获取发票申请注册邮箱
     *
     * @param map
     * @return
     */
    @Logger(module = Module.COMMON, name = "发票申请注册邮箱")
    @PostMapping("/invoiceApplication/mail")
    public JsonResult searchInvoiceDataMail(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.registerMail(map);
    }

    /***
     * //  新建发票申请
     * <AUTHOR>
     * @date 2023/6/20 14:44
     * @param map 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.COMMON, name = "新建发票申请")
    @PostMapping("/invoiceApplication/createInvoice")
    public JsonResult createInvoice(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.createInvoice(map);
    }


    /***
     * //  重新申请发票工单
     * <AUTHOR>
     * @date 2023/6/20 14:44
     * @param map 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.COMMON, name = "重新申请发票")
    @PostMapping("/invoiceApplication/reCreateInvoice")
    public JsonResult reCreateInvoice(@RequestBody Map<String, Object> map) {
        return crmWorkOrderService.reCreateInvoice(map);
    }

    /***
     * //  查询当前商户是否为开票商户
     * <AUTHOR>
     * @date 2023/6/20 14:44
     * @param currentUser  登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.COMMON, name = "查询商户是否为开票商户")
    @PostMapping("/invoiceApplication/invoicedMerchant")
    public JsonResult invoicedMerchant(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotNull(message = "业务类型不能为空") @JsonParam Integer businessType) {
        try {
            return crmWorkOrderService.invoicedMerchant(indexService.getMerchantCode(currentUser.getUserCode(), businessType));
        } catch (BusinessException e) {
            log.error("查询当前商户是否为开票商户异常：{}", e.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("查询当前商户是否为开票商户异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2023/12/19 11:12
     * @description: 获取揽收地址
     */
    @PostMapping("/material/getCollectAddress")
    public JsonResult getCollectAddress(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotBlank(message = "业务类型不能为空") @JsonParam String warehouseCode) {
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        List<CollectAddressVo> collectAddress = crmWorkOrderService.getCollectAddress(merchantCode, warehouseCode);
        return JsonResult.success(collectAddress);
    }
}
