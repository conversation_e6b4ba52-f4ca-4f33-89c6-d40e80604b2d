package com.ywwl.customer.center.api.international;

import com.alibaba.fastjson2.JSONArray;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.constant.PaymentConstant;
import com.ywwl.customer.center.modules.common.provider.service.IndexService;
import com.ywwl.customer.center.modules.international.dto.InvoiceCreateDTO;
import com.ywwl.customer.center.modules.international.dto.InvoiceSearchDTO;
import com.ywwl.customer.center.modules.international.dto.WhetherExistDTO;
import com.ywwl.customer.center.modules.international.service.InvoiceService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.StraightCrmService;
import com.ywwl.customer.center.modules.international.vo.InvoiceMerchantVo;
import com.ywwl.customer.center.modules.international.vo.InvoiceNumberVo;
import com.ywwl.customer.center.modules.international.vo.InvoiceSearchVo;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;
import java.util.Objects;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/4/11
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/invoice")
public class InvoiceController extends BaseController {
    @Resource
    private InvoiceService invoiceService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private StraightCrmService straightCrmService;
    @Resource
    private IndexService indexService;

    /***
     * //  商户是否有发票可开
     * <AUTHOR>
     * @date 2023/4/11 15:09
     * @param whetherExistDTO 参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("whether/exist")
    public JsonResult<Object> invoiceWhetherExist(@RequestBody WhetherExistDTO whetherExistDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfo = getPacketApplyInfo(currentUser.getUserCode());
        whetherExistDTO.setMerchantCode(packetApplyInfo.getMerchantCode());
        return invoiceService.invoiceWhetherExist(whetherExistDTO);
    }

    /***
     * //发票申请创建
     * <AUTHOR>
     * @date 2023/4/11 15:59
     * @param invoiceCreateDTO 参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/create")
    public JsonResult<Object> create(@Valid @RequestBody InvoiceCreateDTO invoiceCreateDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        invoiceCreateDTO.setUserCode(currentUser.getUserCode());
        invoiceCreateDTO.setMerchantCode(indexService.getMerchantCode(currentUser.getUserCode(), invoiceCreateDTO.getBusinessType()));
        invoiceCreateDTO.setCreatorId(currentUser.getUserId());
        return invoiceService.create(invoiceCreateDTO, currentUser);
    }

    /***
     * //重新申请开票
     * <AUTHOR>
     * @date 2023/4/11 16:19
     * @param orderId 工单编号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/reapply")
    public JsonResult<Object> reapply(@NotBlank(message = "工单编号不能为空") @JsonParam String orderId) {
        return invoiceService.reapply(orderId);
    }

    public PacketApplyInfoVo getPacketApplyInfo(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo) || StringUtils.isEmpty(packetApplyInfo.getMerchantCode())) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo;
    }

    /***
     * // 发票工单列表查询
     * <AUTHOR>
     * @date 2023/4/11 16:40
     * @param invoiceSearchDTO 列表入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/search")
    public JsonResult<Object> search(@RequestBody InvoiceSearchDTO invoiceSearchDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            invoiceSearchDTO.setMerchantCode(indexService.getMerchantCode(currentUser.getUserCode(), invoiceSearchDTO.getBusinessType()));
            return invoiceService.search(invoiceSearchDTO);
        } catch (BusinessException e) {
            log.error("发票工单列表查询异常：{}", e.getMessage());
            //单独处理未开通业务线的异常错误
            if (e.getResponseCode().getCode().equals(ResponseCode.PORTAL_6103.getCode()) || e.getResponseCode().getCode().equals(ResponseCode.PORTAL_9102.getCode())) {
                InvoiceSearchVo invoiceSearchVo = new InvoiceSearchVo();
                invoiceSearchVo.setData(new JSONArray());
                invoiceSearchVo.setTotalCount("0");
                return JsonResult.success(invoiceSearchVo);
            } else {
                return JsonResult.error(e.getResponseCode());
            }
        } catch (Exception e) {
            log.error("发票工单列表查询异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  发票详情
     * <AUTHOR>
     * @date 2023/4/11 17:30
     * @param orderId 工单编号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("detail")
    public JsonResult<Object> detail(@NotBlank(message = "工单编号不能为空") @JsonParam String orderId) {
        return invoiceService.detail(orderId);
    }

    /***
     * //  根据商户号查询开票列表状态数量
     * <AUTHOR>
     * @date 2023/4/11 18:02
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/selectInvoiceStatusNumber")
    public JsonResult<Object> selectInvoiceStatusNumber(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotNull(message = "业务类型不能为空") @JsonParam Integer businessType) {
        String merchantCode = null;
        try {
            merchantCode = indexService.getMerchantCode(currentUser.getUserCode(), businessType);
        } catch (BusinessException e) {
            log.error("根据商户号查询开票列表状态数量异常:{}", e.getMessage(), e);
            return JsonResult.error(e.getMessage());
        }
        return invoiceService.selectInvoiceStatusNumber(merchantCode);
    }

    /***
     * //TODO  发票申请商户信息展示
     * <AUTHOR>
     * @date 2022/12/29 10:42
     * @param currentUser 用户参数
     * @return com.cmhb.common.JsonResult<java.lang.Object>
     */
    @PostMapping("/invoiceMerchant")
    public JsonResult<Object> searchInvoiceMerchant(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @NotBlank(message = "业务类型不能为空") @JsonParam String businessType) {
        InvoiceMerchantVo invoiceMerchant = straightCrmService.getInvoiceMerchant(businessType, currentUser);
        return JsonResult.success(invoiceMerchant);
    }

    /**
     * 获取所有可开账期列表
     *
     * @param map
     * @return
     */
    @PostMapping("period")
    public JsonResult<Object> period(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody Map<String, Object> map) {
        map.put("source", "portal");
        int businessType = Integer.parseInt((String) map.get("businessType"));
        map.put("merchantCode", indexService.getMerchantCode(currentUser.getUserCode(), businessType));
        return invoiceService.period(map);
    }

    /***
     * //已开通业务线是否有账期
     * <AUTHOR>
     * @date 2023/10/30 11:02
     * @param currentUser
     * @param map
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("exist/period")
    public JsonResult<Object> existPeriod(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody Map<String, Object> map) {
        map.put("source", "portal");
        return invoiceService.existPeriod(map, currentUser.getUserCode());
    }

    /***
     * //查询地址信息
     * <AUTHOR>
     * @date 2023/4/12 10:10
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/address")
    public JsonResult<Object> address(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfo = getPacketApplyInfo(currentUser.getUserCode());
        return invoiceService.address(packetApplyInfo.getMerchantCode());
    }

    /***
     * //  获取邮箱地址
     * <AUTHOR>
     * @date 2023/4/12 10:17
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/mail")
    public JsonResult<Object> mail(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfo = getPacketApplyInfo(currentUser.getUserCode());
        return invoiceService.getMail(packetApplyInfo.getMerchantCode());
    }
}
