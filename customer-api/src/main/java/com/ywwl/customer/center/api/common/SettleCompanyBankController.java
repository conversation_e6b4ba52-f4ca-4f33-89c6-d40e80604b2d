package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.modules.fba.constant.FbaConstant;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.vo.YwBankVO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.YWEApplyInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/5/4 11:05
 */
@RestController
@RequestMapping("settleBankAccount")
public class SettleCompanyBankController extends BaseController {
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private YWEOverseaService yWEOverseaService;

    /**
     * <AUTHOR>
     * @description 查询结算银行信息
     * @date 2023/5/4 11:10
     **/
    @GetMapping("getBankAccountList")
    public JsonResult getBankAccountList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        List<YwBankVO> ywBankList;
        if (Objects.isNull(packetApplyInfo) || StringUtils.isBlank(packetApplyInfo.getSettleCompanyCode())) {
            ywBankList = new ArrayList<>(0);
        } else {
            ywBankList = commonCrmService.getYwBankList(packetApplyInfo.getSettleCompanyCode(), BusinessTypeEnum.STRAIGHT.getValue());
        }
        return JsonResult.success(ywBankList);
    }
    /**
     * <AUTHOR>
     * @description 查询fba结算银行信息
     * @date 2023/6/2 15:05
     **/
    @GetMapping("getFbaBankAccountList")
    public JsonResult getFbaBankAccountList(){
        List<YwBankVO>  ywBankList = commonCrmService.getYwBankList(FbaConstant.FBA_ACC_SETTLE_COMPANY, BusinessTypeEnum.FBA.getValue());
        return JsonResult.success(ywBankList);
    }

    @RequestMapping("getYWEBankAccountList")
    public JsonResult getYWEBankAccountList(){
        YWEApplyInfoVo yweApplyAllInfoVo = yWEOverseaService.getYWEApplyAllInfoVo(getUserCode());
        List<YwBankVO> ywBankList;
        if (Objects.isNull(yweApplyAllInfoVo) || StringUtils.isBlank(yweApplyAllInfoVo.getSettleCompanyCode())) {
            ywBankList = new ArrayList<>(0);
        } else {
            ywBankList = commonCrmService.getYwBankList(yweApplyAllInfoVo.getSettleCompanyCode(),BusinessTypeEnum.YWE_WAREHOUSE.getValue());
            YwBankVO ywBankVO = ywBankList.get(0);
            ywBankList = new ArrayList<>(2);
            ywBankList.add(ywBankVO);
        }
        return JsonResult.success(ywBankList);
    }
}
