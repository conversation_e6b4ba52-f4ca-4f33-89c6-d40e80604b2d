package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.holder.ReadRowHolder;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.api.ejf.vo.Ids;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.config.MatchNameConfig;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.business.dto.BusinessChannelDTO;
import com.ywwl.customer.center.modules.business.service.BusinessService;
import com.ywwl.customer.center.modules.ejf.component.excel.ColorWriteHandler;
import com.ywwl.customer.center.modules.ejf.component.excel.MergeCellsWriteHandler;
import com.ywwl.customer.center.modules.ejf.dto.ListKycDTO;
import com.ywwl.customer.center.modules.ejf.dto.USAAddressVerifyImportDTO;
import com.ywwl.customer.center.modules.ejf.entity.EjfKyc;
import com.ywwl.customer.center.modules.ejf.entity.USReceiverType;
import com.ywwl.customer.center.modules.ejf.entity.ValidateUSAddressResponseType;
import com.ywwl.customer.center.modules.ejf.enums.ImageStatus;
import com.ywwl.customer.center.modules.ejf.service.EjfKycService;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.ejf.util.ExcelImportUtil;
import com.ywwl.customer.center.modules.ejf.vo.USAAddressVerifyExportVO;
import com.ywwl.customer.center.modules.ejf.vo.USARateVO;
import com.ywwl.customer.center.modules.international.dto.CalculateCostDTO;
import com.ywwl.customer.center.modules.international.dto.USACalRateDTO;
import com.ywwl.customer.center.modules.international.enums.AbnormalEnum;
import com.ywwl.customer.center.modules.international.enums.TaxCodeEnum;
import com.ywwl.customer.center.modules.international.service.CalcService;
import com.ywwl.customer.center.modules.international.service.StraightCrmService;
import com.ywwl.customer.center.modules.international.vo.CalTaxVO;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.international.vo.USAHtsTaxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * EJF_KYC控制器类
 *
 * <AUTHOR>
 * @date 2022/05/16 15:14
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/ejf/kyc")
public class KycController extends BaseController {

    public static final DateTimeFormatter FORMATTER = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd [[HH][:mm][:ss]]")
            .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
            .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
            .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
            .parseDefaulting(ChronoField.MILLI_OF_SECOND, 0)
            .toFormatter();
    private static final String FILE_NAME_EXCEL = "印度KYC已备案信息-%s.xlsx";
    /**
     * 中文正则匹配
     */
    Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");
    @Resource
    private EjfKycService service;
    @Resource
    private StraightCrmService straightCrmService;
    @Value("${usaTaxRate.url}")
    private String usaTaxRateUrl;
    @Resource
    private MatchNameConfig matchNameConfig;
    @Resource
    private BusinessService businessService;
    @Resource
    private CalcService calcService;

    /**
     * 查询已上传列表
     */
    @RequestMapping("/queryUploadedList")
    @Logger(module = Module.EJF, name = "KYC查询上传列表")
    public JsonResult<?> queryUploadedList(@RequestBody ListKycDTO param, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        param.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        param.setStatus(Collections.singletonList(3));
        final JsonResult<?> result = service.queryUploadedList(param);
        // 获取已导入总数
        param.setStatus(ImmutableList.of(0, 1, 2));
        result.put("importCount", getTotal(param));
        return result;
    }

    /**
     * 导出已上传数据
     */
    @RequestMapping("/exportUploadedData")
    @Logger(module = Module.EJF, name = "KYC导出已上传数据")
    public JsonResult<Object> exportUploadedData(@RequestBody ListKycDTO param, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            final JsonResult<?> result = queryUploadedList(param, currentUser);
            Map<String, Object> dataMap = result.getValue(JsonResult.DATA_TAG);
            if (MapUtils.isNotEmpty(dataMap)) {
                List<?> ejfKycList = (List<?>) dataMap.get("records");
                if (CollectionUtils.isNotEmpty(ejfKycList)) {
                    // 设置下载HTTP头
                    String fileName = String.format(FILE_NAME_EXCEL, LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.PURE_DATETIME_PATTERN)));
                    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                        ExcelImportUtil.writeExcel(ejfKycList, EjfKyc.class, "已备案", outputStream);
                        String base64 = EJFUtil.byteToString(outputStream);
                        JsonResult<Object> derivedResult = JsonResult.success("导出成功", base64);
                        derivedResult.put("fileName", fileName);
                        return derivedResult;
                    }
                }
            }
            return JsonResult.error("无数据");
        } catch (IOException e) {
            throw BusinessException.msg(e.getMessage());
        }
    }

    /**
     * 上传数据备案
     */
    @RequestMapping("/uploadDataForRecord")
    @Validated
    @Logger(module = Module.EJF, name = "KYC上传数据备案")
    public JsonResult<?> uploadDataForRecord(@RequestBody Ids<Integer> ids) {
        final Collection<Integer> id = ids.getId();
        if (CollectionUtils.isNotEmpty(id)) {
            return service.uploadDataForRecord(id);
        }
        return JsonResult.error("数据不存在");
    }

    private static final String errorFileNameTips = "【%s】文件名不合规,KYC图片请以税号_1（正面）和税号_2（反面）命名进行上传";
    private static final String withChineseTips = "【%s】请以税号编号命名，不允许含中文字样";

    @RequestMapping("/addKYC")
    @Logger(module = Module.EJF, name = "KYC新增")
    public JsonResult<?> addKYC(MultipartFile[] file, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Hashtable<String, EjfKyc> kycList = new Hashtable<>();
        final String merchantCode = getMerchantCode(currentUser.getUserCode());
        // 错误文件名数据
        List<String> errorList = new Vector<>(file.length);
        Arrays.stream(file).parallel().forEach(f -> {
            // file Arrays
            try {
                // 验证
                String[] taxNoNum = verification(f);
                // 税号
                String key = merchantCode.concat(taxNoNum[0]);
                EjfKyc kyc;
                if (!kycList.containsKey(key)) {
                    kyc = new EjfKyc(currentUser, merchantCode);
                    kyc.setTaxno(taxNoNum[0]);
                    kycList.put(key, kyc);
                } else {
                    kyc = kycList.get(key);
                }
                // 设置正反面照片
                if (Objects.equals(taxNoNum[1], "1")) {
                    kyc.setPositiveFile(f);
                } else {
                    kyc.setReverseFile(f);
                }
            } catch (BusinessException e) {
                errorList.add(e.getMessage());
            }
        });
        List<EjfKyc> result = ImmutableList.copyOf(kycList.values());
        // 先上传文件
        service.uploadFile(result);
        // 成功上传文件的
        final List<EjfKyc> success = result.stream().filter(kyc -> Objects.equals(kyc.getStatus(), ImageStatus.SUCCESS.getStatus())).collect(Collectors.toList());
        // 错误上传文件的
        final List<String> errorMsg = result.stream().filter(kyc -> Objects.equals(kyc.getStatus(), ImageStatus.ERROR.getStatus())).map(EjfKyc::getMsg).collect(Collectors.toList());
        errorList.addAll(errorMsg);
        JsonResult<?> results = JsonResult.success();
        if (CollectionUtils.isNotEmpty(success)) {
            // 再调KYC新增
            results = service.addKyc(success);
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            // 构建提示信息
            String msg = String.join(" \n", errorList);
            results.setMessage(msg);
        }
        return results;
    }

    private String[] verification(MultipartFile f) {
        // 1 验证文件名称是否正常
        final String[] taxNo = getTaxNo(f);
        // 2 验证文件格式和大小
        final JsonResult<?> jsonResult = service.validateFileType(f);
        if (jsonResult.getSuccess()) {
            return taxNo;
        } else {
            throw BusinessException.msg(jsonResult.getMessage());
        }
    }

    private static final ImmutableSet<String> DIRECTION = ImmutableSet.of("1", "2");

    private String[] getTaxNo(MultipartFile f) {
        String originalFilename = f.getOriginalFilename();
        String name = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        String[] taxNumArray = name.split("_");
        if (taxNumArray.length != 2) {
            throw BusinessException.msg(String.format(errorFileNameTips, originalFilename));
        }
        // 去掉空格
        taxNumArray[0] = taxNumArray[0].trim();
        taxNumArray[1] = taxNumArray[1].trim();
        // 验证中文
        if (chinesePattern.matcher(taxNumArray[0]).find()) {
            throw BusinessException.msg(String.format(withChineseTips, originalFilename));
        }
        if (!DIRECTION.contains(taxNumArray[1])) {
            throw BusinessException.msg(String.format(errorFileNameTips, originalFilename));
        }
        return taxNumArray;
    }

    @RequestMapping("/updateKYC/{id}/{type}")
    @Logger(module = Module.EJF, name = "KYC更新")
    public JsonResult<?> updateKYC(MultipartFile file, @PathVariable Integer type, @PathVariable Integer id, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = getMerchantCode(currentUser.getUserCode());
        final JsonResult<?> jsonResult = service.validateFileType(file);
        if (jsonResult.getSuccess()) {
            return service.update(file, type, id, merchantCode);
        }
        return jsonResult;
    }

    @RequestMapping("/deleteKYC")
    @Logger(module = Module.EJF, name = "KYC删除")
    public JsonResult<?> deleteKYC(@RequestBody Ids<Integer> ids, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        if (CollectionUtils.isNotEmpty(ids.getId())) {
            return service.deleteKyc(ids.getId());
        }
        return JsonResult.success();
    }

    @RequestMapping("/listKYC")
    @Logger(module = Module.EJF, name = "KYC查询")
    public JsonResult<?> listKYC(@RequestBody ListKycDTO param, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        // 状态为空时查询0，1，2状态的数据
        if (CollectionUtils.isEmpty(param.getStatus())) {
            param.setStatus(ImmutableList.of(0, 1, 2));
        }
        // 获取业务账号
        param.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        final JsonResult<?> result = service.queryUploadedList(param);
        // 获取已上传总数
        param.setStatus(ImmutableList.of(3));
        result.put("uploadCount", getTotal(param));
        return result;
    }

    private long getTotal(ListKycDTO param) {
        // 如果有条件则查询CRM
        param.setSize(1);
        param.setCurrent(1);
        JsonResult<?> result = service.queryUploadedList(param);
        if (result.getSuccess()) {
            Map value = (Map) result.getData();
            String total = (String) value.getOrDefault("total", "0");
            return Long.parseLong(total);
        }
        return 0;
    }

    /**
     * @author: dinghy
     * @createTime: 2024/11/19 15:35
     * @description: 美国地址批量校验
     */
    @RequestMapping("/USAddressDataVerification")
    @Logger(module = Module.EJF, name = "美国地址校验")
    @Validated
    public JsonResult<?> USAddressDataVerification(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody List<USReceiverType.ReceiverInfoDTO> list) {
        if (list == null || list.isEmpty()) {
            return JsonResult.error("请输入或者导入需要校验的地址数据");
        }
        if (list.size() > 50) {
            return JsonResult.error("一次校验最大支持50条");
        }
        List<USReceiverType.ReceiverInfoDTO> resultList = new LinkedList<>();
        AtomicInteger index = new AtomicInteger(1);
        list.forEach(address -> {
            USReceiverType usReceiverType = new USReceiverType();
            address.setId(index.getAndIncrement());
            usReceiverType.setReceiverInfo(address);
            JsonResult<ValidateUSAddressResponseType.DataDTO> dataDTOJsonResult = service.USAddressDataVerification(getMerchantCode(currentUser.getUserCode()), currentUser, usReceiverType);
            USReceiverType.ReceiverInfoDTO returnAddress = new USReceiverType.ReceiverInfoDTO();
            returnAddress.setId(address.getId());
            if (dataDTOJsonResult.getSuccess()) {
                ValidateUSAddressResponseType.DataDTO data = dataDTOJsonResult.getData();
                returnAddress.setAddress(data.getReceiverInfo().getAddress());
                returnAddress.setCity(data.getReceiverInfo().getCity());
                returnAddress.setState(data.getReceiverInfo().getState());
                returnAddress.setZipCode(data.getReceiverInfo().getZipCode5() + "-" + data.getReceiverInfo().getZipCode4());
            } else {
                returnAddress.setMessage(true);
                returnAddress.setZipCode(dataDTOJsonResult.getMessage());
            }
            resultList.add(address);
            resultList.add(returnAddress);
        });
        return JsonResult.success(resultList);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/11/19 15:52
     * @description: 导入美国地址校验
     */
    @RequestMapping("importUSAAddressData")
    public JsonResult getUSAAddressVerifyTemplate(MultipartFile file) {
        if (file == null) {
            return JsonResult.error("上传文件不能为空");
        }
        List<USAAddressVerifyImportDTO> list = new LinkedList<>();
        try {
            EasyExcel.read(file.getInputStream(), USAAddressVerifyImportDTO.class, new ReadListener<USAAddressVerifyImportDTO>() {
                @Override
                public void invoke(USAAddressVerifyImportDTO o, AnalysisContext analysisContext) {
                    ReadRowHolder readRowHolder = analysisContext.readRowHolder();
                    int rowNum = readRowHolder.getRowIndex() + 1;
                    if (StringUtils.isBlank(o.getZipCode()) || StringUtils.isBlank(o.getCity()) || StringUtils.isBlank(o.getState()) || StringUtils.isBlank(o.getAddress())) {
                        throw new BusinessException("第" + rowNum + "行，有数据为空，请检查");
                    }
                    list.add(o);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }

            }).doReadAllSync();
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            return JsonResult.error("文件解析失败");
        }

        return JsonResult.success(list);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/11/21 11:05
     * @description: 导出数据
     */
    @PostMapping("exportUSAAddressData")
    public JsonResult exportKycData(@RequestBody List<USReceiverType.ReceiverInfoDTO> list) throws IOException {
        List<USAAddressVerifyExportVO> dataList = new LinkedList<>();
        if (list != null) {
            list.forEach(x -> {
                USAAddressVerifyExportVO usaAddressVerifyExportVO = new USAAddressVerifyExportVO();
                BeanUtils.copyProperties(x, usaAddressVerifyExportVO);
                usaAddressVerifyExportVO.setId(String.valueOf(x.getId()));
                dataList.add(usaAddressVerifyExportVO);
            });
        }
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream, USAAddressVerifyExportVO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new ColorWriteHandler())
                    .registerWriteHandler(new MergeCellsWriteHandler())
                    .sheet("sheet1")
                    .doWrite(dataList);
            String base64 = com.ywwl.customer.center.common.utils.EJFUtil.byteToString(outputStream);
            return JsonResult.success(base64);
        } catch (Exception e) {
            e.printStackTrace();
            return JsonResult.error("导出失败");
        }
    }


    public String getMerchantCode(String id) {
        final PacketApplyInfoVo info = straightCrmService.getPacketApplyInfo(id);
        final String code = info.getMerchantCode();
        if (StringUtils.isBlank(code)) {
            throw new BusinessException("商户信息有误!");
        }
        return code;
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/1 10:48
     * @description: 查询美国税率
     */
    @PostMapping("getUSATaxRateList")
    public JsonResult getUSATaxRateList(@JsonParam String hsCode, @JsonParam String productDescription) {
        if (StringUtils.isBlank(hsCode) && StringUtils.isBlank(productDescription)) {
            return JsonResult.error("海关编码和商品英文描述至少填写一个");
        }
        Map<String, String> param = new HashMap<>();
        param.put("hts_number", hsCode);
        param.put("description", productDescription);
        JSONObject jsonObject;
        try {
            jsonObject = HttpUtil.doPost(usaTaxRateUrl, param, JSONObject.class);
        } catch (Exception e) {
            return JsonResult.error("查询海关编码服务异常");
        }
        if (jsonObject == null) {
            return JsonResult.error("查询海关编码服务异常");
        }
        JSONArray jsonArray = jsonObject.getJSONArray("data");
        if (jsonArray != null) {
            List<USARateVO> javaList = jsonArray.toJavaList(USARateVO.class);
            return JsonResult.success(javaList);
        }
        return JsonResult.success(new ArrayList<>());
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/12 15:48
     * @description: 根据中文品名查询税率
     */
    @RequestMapping(value = "getUSAHTSRateList")
    public JsonResult getUSAHTSRateList(@JsonParam String productName, @JsonParam String declaredValue) {
        if (StringUtils.isBlank(productName)) {
            return JsonResult.error("中文品名不能为空");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("queryName", productName);
        String string = jsonObject.toString();
        Map<String, String> header = new HashMap<>();
        String sign = matchNameConfig.getSign(string);
        header.put("sign", sign);
        header.put("accessKey", matchNameConfig.getAccessKey());
        JSONObject res = HttpUtil.doPost(matchNameConfig.getUrl() + AbnormalEnum.HTS_LIST.value(), header, string, JSONObject.class, null);
        if (res != null && res.getInteger("code").equals(0)) {
            JSONArray data = res.getJSONArray("data");
            if (data != null && !data.isEmpty()) {
                List<USAHtsTaxVO> javaList = data.toJavaList(USAHtsTaxVO.class);
                USAHtsTaxVO usaHtsTaxVO = javaList.get(0);
                // 查丁戈接口
                String htsCode = usaHtsTaxVO.getHtsCode();
                Map<String, String> param = new HashMap<>();
                param.put("hts_number", htsCode);
                try {
                    jsonObject = HttpUtil.doPost(usaTaxRateUrl, param);
                } catch (Exception e) {
                    return JsonResult.error("查询海关编码服务异常");
                }
                if (jsonObject == null) {
                    return JsonResult.error("查询海关编码服务异常");
                }
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                if (jsonArray != null) {
                    List<USARateVO> usARateVOList = jsonArray.toJavaList(USARateVO.class);
                    Optional<USARateVO> any = usARateVOList.stream().filter(x -> StringUtils.isNotBlank(x.getHtsNumber()) && htsCode.equals(x.getHtsNumber().replace(".", ""))).findAny();
                    if (any.isPresent()) {
                        USARateVO usaRateVO = any.get();
                        usaHtsTaxVO.setUsARateVO(usaRateVO);
                    }
                }
                return JsonResult.success(javaList);
            }
        }
        return JsonResult.error("当前品名库不存在，请联系销售/客服处理");
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/14 16:12
     * @description: 查询美国产品
     */
    @RequestMapping(value = "getUSAProductList")
    public JsonResult getUSAProductList() {
        PacketApplyInfoVo info = straightCrmService.getPacketApplyInfo(getUserCode());
        final BusinessChannelDTO channel = businessService.getBaseChannel(info.getMerchantCode(), info.getWarehouseCode(), "115");
        if (channel.getSuccess()) {
            final List<BusinessChannelDTO.DataDTO> channelData = channel.getData();
            return JsonResult.success(channelData);
        }
        return JsonResult.error(channel.getMessage());
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/14 16:49
     * @description: 试算汇率
     */
    @PostMapping("getUSACalculateRate")
    public JsonResult getUSACalculateRate(@Valid @RequestBody USACalRateDTO usaCalRateDTO) {
        JSONObject queryHtsParam = new JSONObject();
        queryHtsParam.put("queryName", usaCalRateDTO.getProductName());
        Map<String, String> header = new HashMap<>();
        String sign = matchNameConfig.getSign(queryHtsParam.toString());
        header.put("sign", sign);
        header.put("accessKey", matchNameConfig.getAccessKey());
        JSONObject res = HttpUtil.doPost(matchNameConfig.getUrl() + AbnormalEnum.HTS_LIST.value(), header, queryHtsParam.toString(), JSONObject.class, null);
        if (res != null && res.getInteger("code").equals(0)) {
            JSONArray data = res.getJSONArray("data");
            if (data == null || data.isEmpty()) {
                return JsonResult.error("当前品名库不存在，请联系销售/客服处理");
            }
            List<USAHtsTaxVO> javaList = data.toJavaList(USAHtsTaxVO.class);
            USAHtsTaxVO usaHtsTaxVO = javaList.get(0);
            String htsCode = usaHtsTaxVO.getHtsCode();
            Map<String, Object> param = new HashMap<>(2);
            param.put("htsCodes", Arrays.asList(htsCode));
            List<CalTaxVO> calTaxVOS = calcService.getCalTax(param);
            CalTaxVO calTaxVO = calTaxVOS.get(0);
            if (StringUtils.isNotBlank(calTaxVO.getError())) {
                return JsonResult.error(calTaxVO.getError());
            }

            // 基础关税
            usaHtsTaxVO.setGeneralRateOfDutyProportion(formatTaxRate(calTaxVO.sumGeneralRateOfDutyFields()));
            // 设置301关税
            usaHtsTaxVO.setRateOfDuty301(formatTaxRate(calTaxVO.getRateOfDuty301()));

            // 设置反补贴税(trump税)
            usaHtsTaxVO.setTrumpRateOfDuty(formatTaxRate(calTaxVO.getTrumpRateOfDuty()));

            // 设置反倾销税
            usaHtsTaxVO.setAntiDumpingRateOfDuty(formatTaxRate(calTaxVO.getAntiDumpingRateOfDuty()));

            // 设置芬太尼关税
            usaHtsTaxVO.setFentanylRateOfDuty(formatTaxRate(calTaxVO.getFentanylRateOfDuty()));

            // 设置对等关税
            usaHtsTaxVO.setEquivalentTariffRateOfDuty(formatTaxRate(calTaxVO.getEquivalentTariffRateOfDuty()));

            // 设置钢铝税
            usaHtsTaxVO.setSteelAndAluminumRateOfDuty(formatTaxRate(calTaxVO.getSteelAndAluminumRateOfDuty()));
            if (StringUtils.isNotBlank(usaCalRateDTO.getChannelCode())) {
                if(StringUtils.isBlank(usaCalRateDTO.getWeight())||StringUtils.isBlank(usaCalRateDTO.getDeclaredValue())){
                    return JsonResult.error("申报价值和重量不能为空");
                }
                JsonResult<Object> calResult = calculateRateValue(usaCalRateDTO, htsCode, usaHtsTaxVO);
                if (!calResult.getSuccess()) return calResult;
            }
            return JsonResult.success(javaList);
        }
        return JsonResult.error("当前品名库不存在，请联系销售/客服处理");
    }

    private String formatTaxRate(String rate) {
        if (rate == null) {
            return null;
        }
        // Multiply by 100 and add % sign
        return new BigDecimal(rate).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
    }

    private JsonResult<Object> calculateRateValue(USACalRateDTO usaCalRateDTO, String htsCode, USAHtsTaxVO usaHtsTaxVO) {
        // 计算费用
        PacketApplyInfoVo info = straightCrmService.getPacketApplyInfo(getUserCode());
        CalculateCostDTO calculateCostDTO = new CalculateCostDTO();
        calculateCostDTO.setProductCode(usaCalRateDTO.getChannelCode());
        calculateCostDTO.setChargingTime(LocalDateTime.now());
        calculateCostDTO.setCustomerCode(info.getMerchantCode());
        calculateCostDTO.setWeight(String.valueOf(usaCalRateDTO.getWeight()));
        calculateCostDTO.setUnit("g");
        calculateCostDTO.setLength(0);
        calculateCostDTO.setHigh(0);
        calculateCostDTO.setWidth(0);
        calculateCostDTO.setCompanyCodeFrom(info.getWarehouseCode());
        calculateCostDTO.setCountryId("115");
        calculateCostDTO.setIsTaxCollection(true);
        CalculateCostDTO.NameDTO nameDTO = new CalculateCostDTO.NameDTO();
        nameDTO.setOrderName(usaCalRateDTO.getProductName());
        nameDTO.setHsCode(htsCode);
        nameDTO.setPiece(usaCalRateDTO.getQuality());
        nameDTO.setDeclaredValue(usaCalRateDTO.getDeclaredValue());
        nameDTO.setWeight(usaCalRateDTO.getWeight());
        nameDTO.setDeclaredCurrencyId("1");
        calculateCostDTO.setNameList(Arrays.asList(nameDTO));
        JSONObject jsonObject = calcService.forecastCost(calculateCostDTO);
        Boolean result = jsonObject.getBoolean("result");
        if (result) {
            JSONObject calcData = jsonObject.getJSONObject("data");
            if (calcData != null) {
                JSONArray expenseItems = calcData.getJSONArray("expenseItems");
                if (expenseItems != null && !expenseItems.isEmpty()) {
                    expenseItems.forEach(item -> {
                        JSONObject jsonItem = (JSONObject) item;
                        String itemCode = jsonItem.getString("itemCode");
                        JSONArray taxdetails = jsonItem.getJSONArray("taxdetails");
                        if (taxdetails != null && !taxdetails.isEmpty()) {
                            JSONObject detail = taxdetails.getJSONObject(0);
                            String moneyTax = detail.getString("moneyTax");
                            if (StringUtils.isNotBlank(moneyTax)) {
                                TaxCodeEnum taxCodeEnum = TaxCodeEnum.getTaxCodeEnum(itemCode);
                                switch (taxCodeEnum) {
                                    case TARIFF_301:
                                        usaHtsTaxVO.setRateOfDuty301Value(moneyTax);
                                        break;
                                    case EQUAL_TARIFF:
                                        usaHtsTaxVO.setEquivalentTariffRateOfDutyValue(moneyTax);
                                        break;
                                    case Trump_TARIFF:
                                        usaHtsTaxVO.setTrumpRateOfDutyValue(moneyTax);
                                        break;
                                    case GENERAL_TARIFF:
                                        usaHtsTaxVO.setGeneralRateOfDutyProportionValue(moneyTax);
                                        break;
                                    case FENTANYL_TARIFF:
                                        usaHtsTaxVO.setFentanylRateOfDutyValue(moneyTax);
                                        break;
                                    case AntiDumping_TARIFF:
                                        usaHtsTaxVO.setAntiDumpingRateOfDutyValue(moneyTax);
                                        break;
                                    case TARIFF_232_STEEL_ALUMINUM:
                                        usaHtsTaxVO.setSteelAndAluminumRateOfDutyValue(moneyTax);
                                        break;
                                    default:
                                        break;
                                }
                            }
                            usaHtsTaxVO.calculateTotalDutyValue();
                        }
                    });
                }
            }

        } else {
            log.error("计费失败信息:{}", jsonObject);
            return JsonResult.error(jsonObject.getString("message"));
        }
        return JsonResult.success();
    }


}
