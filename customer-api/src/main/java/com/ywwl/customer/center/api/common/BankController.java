package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.common.provider.constant.PaymentConstant;
import com.ywwl.customer.center.modules.common.provider.service.BankService;
import com.ywwl.customer.center.modules.common.provider.vo.BankVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/5/13
 */
@RestController
@RequestMapping("/bank")
public class BankController extends BaseController {
    @Resource
    private BankService bankService;

    /***
     * //查询所有银行信息
     * <AUTHOR>
     * @date 2023/5/13 15:00
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/list")
    public JsonResult<Object> getBankList() {
        Map<String, String> hashMap = new HashMap<>(8);
        //查询银行列表数据
        hashMap.put("param1", "1");
        List<BankVo> bankVos = bankService.bankList(hashMap, "1");
        return JsonResult.success(bankVos);
    }

    /***
     * //
     * <AUTHOR>
     * @date 2023/5/13 15:01

     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/thirdPaymentList")
    public JsonResult<Object> thirdPayment() {
        Map<String, String> hashMap = new HashMap<>(8);
        //查询银行列表数据
        hashMap.put("param1", "2");
        List<BankVo> bankVos = bankService.bankList(hashMap, "2");
        List<BankVo> filteredBankVos = bankVos.stream()
                .filter(bankVo -> !PaymentConstant.ALIPAY_NAME.equals(bankVo.getPaymentModeName()))
                .collect(Collectors.toList());
        return JsonResult.success(filteredBankVos);
    }
}
