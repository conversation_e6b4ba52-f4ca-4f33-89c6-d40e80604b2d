package com.ywwl.customer.center.api.im;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.im.service.WebIMService;
import com.ywwl.customer.center.modules.im.service.dto.WebImBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * WebIM 控制器类
 */
@Slf4j
@RestController
@RequestMapping("/im")
public class WebIMController extends BaseController {

	WebIMService webIMService;

	@Autowired
	public WebIMController(WebIMService webIMService) {
		this.webIMService = webIMService;
	}

	@PostMapping("/getIMParam")
	public JsonResult<?> getIMParam(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
		try {
			if (Objects.nonNull(currentUser.getUserId())) {
				WebImBean webImParams = webIMService.getWebImParams(currentUser);
				return success(webImParams);
			}
			return error("用户未登录!");
		} catch (Throwable e) {
			log.error(e.getMessage(),e);
			return error(e.getMessage());
		}
	}


	private JsonResult<?> success(WebImBean webImBean) {
        return JsonResult.success(webImBean);
	}


	private JsonResult<?> error(String e) {
        return JsonResult.error(e);
	}

}
