package com.ywwl.customer.center.api.international;


import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.config.AttachmentFileCloudConfig;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.SendWingService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.international.vo.SendWingMerchantConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/6/29 10:13
 */
@Slf4j
@Controller
@Validated
@RequestMapping("/sendWing")
public class SendWingController extends BaseController {
    @Resource
    private SendWingService sendWingService;
    @Resource
    public AttachmentFileCloudConfig config;

    @Autowired
    private PacketBusinessApplyService packetBusinessApplyService;

    /**
     * @description 根据条件获取派送翼订单
     * <AUTHOR>
     * @date 2021/7/6 14:00
     */
    @RequestMapping("getList")
    @ResponseBody
    public JsonResult getList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody JSONObject jsonObject) {
        JsonResult jsonResult = null;
        try {
            jsonResult = new JsonResult();
            String orderNumbers = jsonObject.getString("orderNumbers");
            Integer orderStatus = jsonObject.getInteger("orderStatus");
            String startTime = jsonObject.getString("startTime");
            String endTime = jsonObject.getString("endTime");
            String productCode = jsonObject.getString("productCode");
            Integer pageSize = jsonObject.getInteger("pageSize");
            Integer current = jsonObject.getInteger("current");
            Map<String, Object> param = new HashMap<>(8);
            if (StringUtils.isBlank(orderNumbers)) {
                if (StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime) || StringUtils.isBlank(productCode)) {
                    return JsonResult.error(ResponseCode.PORTAL_5010);
                }
                // 获取商户号
                String businessCode = getBusinessCode(currentUser.getUserCode());
                if (orderStatus != null) {
                    param.put("OrderState", orderStatus);
                }
                if (current == null) {
                    current = 1;
                }
                if (pageSize == null) {
                    pageSize = 20;
                }
                param.put("CustomerCode", businessCode);
                param.put("BeginTime", startTime);
                param.put("EndTime", endTime);
                param.put("Page", current);
                param.put("Count", pageSize);
                param.put("ProductCode", productCode);

            } else {
                param.put("OrderNumber", orderNumbers);
                param.put("Page", 1);
                param.put("Count", 20);
            }
            jsonResult = sendWingService.getSendWingOrders(JSONObject.toJSONString(param));
        } catch (BusinessException businessException) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(businessException.getMessage());
        } catch (Exception e) {
            log.error("查询海外派订单异常,参数:{},原因:{}", jsonObject, e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("查询异常");
        }
        return jsonResult;
    }

    /**
     * @description 创建导出任务
     * <AUTHOR>
     * @date 2021/8/25 14:02
     */
    @Logger(module = Module.HWP,name="创建导出任务")
    @RequestMapping("addExportTask")
    @ResponseBody
    public JsonResult addExportTask(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody JSONObject jsonObject) {
        JsonResult jsonResult = new JsonResult();
        try {
            String startTime = jsonObject.getString("startTime");
            String endTime = jsonObject.getString("endTime");
            String productCode = jsonObject.getString("productCode");
            Integer orderStatus = jsonObject.getInteger("orderStatus");
            if (StringUtils.isBlank(productCode) || StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)) {
                return JsonResult.error(ResponseCode.PORTAL_5010);
            }
            // 获取商户号
            String businessCode = getBusinessCode(currentUser.getUserCode());
            Map<String, Object> param = new HashMap<>(6);
            param.put("CustomerCode", businessCode);
            param.put("BeginTime", startTime);
            param.put("EndTime", endTime);
            param.put("ProductCode", productCode);
            if (orderStatus != null) {
                param.put("OrderState", orderStatus);
            } else {
                param.put("OrderState", "");
            }
            jsonResult = sendWingService.createExportTask(JSONObject.toJSONString(param));
        } catch (BusinessException businessException) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(businessException.getMessage());
        } catch (Exception e) {
            log.error("创建导出海外派导出任务异常,参数:{},原因:{}", jsonObject, e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("创建导出任务异常");
        }
        return jsonResult;
    }

    @RequestMapping("getExportTaskList")
    @ResponseBody
    public JsonResult getExportTaskList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody JSONObject jsonObject) {
        JsonResult jsonResult = new JsonResult();
        try {
            Integer current = Optional.ofNullable(jsonObject.getInteger("current")).orElse(1);
            Integer pageSize = Optional.ofNullable(jsonObject.getInteger("pageSize")).orElse(10);
            String productCode = jsonObject.getString("productCode");
            Integer orderStatus = jsonObject.getInteger("orderStatus");
            Map<String, Object> param = new HashMap<>(6);
            String businessCode = getBusinessCode(currentUser.getUserCode());
            param.put("CustomerCode", businessCode);
            param.put("Page", current);
            param.put("Count", pageSize);
            if (StringUtils.isNotBlank(productCode)) {
                param.put("ProductCode", productCode);
            }
            if (orderStatus != null) {
                param.put("OrderState", orderStatus);
            }
            Map<String, Object> map = sendWingService.getExportTaskList(JSONObject.toJSONString(param));
            jsonResult.setSuccess(true);
            jsonResult.setData(map);
        } catch (BusinessException businessException) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(businessException.getMessage());
        } catch (Exception e) {
            log.error("查询导出海外派订单任务异常,参数:{},原因:{}", jsonObject, e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("查询导出任务异常");
        }
        return jsonResult;
    }

    /**
     * @description 进入海外派订单首页, 获取海外派的数据
     * <AUTHOR>
     * @date 2021/9/1 9:56
     */
    @RequestMapping("sendWingIndex")
    @ResponseBody
    public JsonResult sendWingIndex(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult jsonResult = new JsonResult();
        try {
            String businessCode = getBusinessCode(currentUser.getUserCode());
            List<SendWingMerchantConfigVo> list = sendWingService.getSendWingMerchantConfig(businessCode);
            jsonResult.setSuccess(true);
            jsonResult.setData(list);
        } catch (BusinessException e) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
            return jsonResult;
        } catch (Exception e) {
            log.error("查询pp商户配置信息异常,原因:{}", e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("服务异常");
            return jsonResult;
        }
        return jsonResult;
    }

    /**
     * 根据pp的地址来补全下载地址
     * <AUTHOR>
     * @date 2021/9/6 15:29
     */
    @Logger(module = Module.HWP,name="下载海外派订单状态模块数据")
    @PostMapping("download")
    public void downLoadExportExcel(@JsonParam @NotBlank(message = "url不能为空") String url,
                                    @JsonParam @NotBlank(message = "fileName不能为空") String fileName,
                                    HttpServletResponse response) {
        HttpUtil.download(url, response, fileName, true);
    }

    /**
     * <AUTHOR>
     * @description 获取业务账号
     * @date 2023/4/10 11:09
     **/
    public String getBusinessCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        String merchantCode = packetApplyInfo.getMerchantCode();
        if (StringUtils.isBlank(merchantCode)) {
            throw new BusinessException("业务账号为空");
        }
        return merchantCode;
    }
}
