package com.ywwl.customer.center.api.fba;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.constant.FileConstant;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.RequestMode;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.aspect.ParameterValidationAspect;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.ejf.entity.ProductDetails;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.fba.constant.FbaStatusEnum;
import com.ywwl.customer.center.modules.fba.dto.*;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.service.FbaService;
import com.ywwl.customer.center.modules.fba.vo.*;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.international.service.CalcService;
import com.ywwl.customer.center.modules.upload.service.FileBusinessService;
import com.ywwl.customer.center.modules.upload.service.FilePathResolver;
import com.ywwl.customer.center.modules.upload.vo.FileUploadVo;
import com.ywwl.customer.center.system.enums.UploadObjNameEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ywwl.customer.center.common.domain.ResponseCode.PORTAL_9101;
import static com.ywwl.customer.center.framework.enums.Module.FBA;

/**
 * FBA控制器
 *
 * <AUTHOR>
 * @date 2023/05/16 15:11
 **/
@RequestMapping("/fba")
@RestController
@Slf4j
@Validated
public class FbaController extends BaseController {

    /**
     * FBA服务类
     */
    @Resource
    FbaService fbaService;

    @Resource
    OrderService orderService;

    @Resource
    CalcService calcService;

    @Resource
    FbaApplyService fbaApplyService;

    @Resource
    CmccService cmccService;

    private static final String ROW = "第%s行：";

    @Value("${fba.url}")
    private String fbaUrl;

    @Resource
    FilePathResolver filePathResolver;
    @Resource
    FileBusinessService fileBusinessService;

    /**
     * 新增收件人地址
     *
     * @param dto 收件人地址实体
     */
    @PostMapping("/addReceiverAddress")
    @Logger(module = Module.FBA, name = "新增收件人地址")
    public JsonResult<?> addReceiverAddress(@RequestBody ReceiverAddressDTO dto) {
        dto.setUserCode(getUserCode());
        fbaService.addReceiverAddress(dto);
        return JsonResult.success();
    }

    /**
     * 删除收件人地址
     *
     * @param id 收件人地址ID
     */
    @PostMapping("/deleteReceiverAddress")
    @Logger(module = Module.FBA, name = "删除收件人地址")
    public JsonResult<?> deleteReceiverAddress(@JsonParam @NotNull(message = "id不能为空") Long id) {
        fbaService.deleteReceiverAddress(id, getUserCode());
        return JsonResult.success();
    }

    /**
     * 更新收件人地址
     *
     * @param dto 收件人地址实体
     */
    @PostMapping("/updateReceiverAddress")
    @Logger(module = Module.FBA, name = "更新收件人地址")
    public JsonResult<?> updateReceiverAddress(@RequestBody ReceiverAddressDTO dto) {
        dto.setUserCode(getUserCode());
        fbaService.updateReceiverAddress(dto);
        return JsonResult.success();
    }

    /**
     * 获取收件人地址
     *
     * @return 收件人地址列表
     */
    @PostMapping("/getReceiverAddress")
    @Logger(module = Module.FBA, name = "获取收件人地址")
    public JsonResult<List<ReceiverAddressDTO>> getReceiverAddress() {
        return JsonResult.success(fbaService.getReceiverAddress(getUserCode()));
    }

    /**
     * 新增进口信息
     *
     * @param dto 进口信息实体
     */
    @PostMapping("/addImportInformation")
    @Logger(module = Module.FBA, name = "新增进口信息")
    public JsonResult<?> addImportInformation(@RequestBody ImportInformationDTO dto) {
        dto.setUserCode(getUserCode());
        fbaService.addImportInformation(dto);
        return JsonResult.success();
    }

    /**
     * 删除进口信息
     *
     * @param id 进口信息ID
     */
    @PostMapping("/deleteImportInformation")
    @Logger(module = Module.FBA, name = "删除进口信息")
    public JsonResult<?> deleteImportInformation(@JsonParam @NotNull(message = "id不能为空") Long id) {
        fbaService.deleteImportInformation(id, getUserCode());
        return JsonResult.success();
    }

    /**
     * 更新进口信息
     *
     * @param dto 进口信息实体
     */
    @PostMapping("/updateImportInformation")
    @Logger(module = Module.FBA, name = "更新进口信息")
    public JsonResult<?> updateImportInformation(@RequestBody ImportInformationDTO dto) {
        dto.setUserCode(getUserCode());
        fbaService.updateImportInformation(dto);
        return JsonResult.success();
    }

    /**
     * 获取进口信息
     *
     * @return 进口信息列表
     */
    @PostMapping("/getImportInformation")
    @Logger(module = Module.FBA, name = "获取进口信息")
    public JsonResult<List<ImportInformationDTO>> getImportInformation() {
        return JsonResult.success(fbaService.getImportInformation(getUserCode()));
    }


    /**
     * 支持的缴税模式
     *
     * @param param 参数
     * @return 产品详情
     */
    @RequestMapping("/vat")
    @ResponseBody
    @Logger(module = Module.FBA, name = "支持的缴税模式")
    public JsonResult<?> vat(@RequestBody ProductDetailsDTO param) {
        ProductDetails.DataDTO productDetails = orderService.productDetails(param.getProductNumber());
        List<ProductDetails.DataDTO.PayTaxesMode> payTaxesMode = productDetails.getPayTaxesMode();
        if (CollectionUtil.isEmpty(payTaxesMode)) {
            payTaxesMode = Collections.emptyList();
        }
        return JsonResult.success(payTaxesMode);
    }

//    /**
//     * 获取试算配置
//     *
//     * @param countryId 国家
//     * @return 试算配置
//     */
//    @RequestMapping("/getCalcConfig")
//    @ResponseBody
//    @Logger(module = Module.FBA, name = "获取试算配置")
//    public JsonResult<CalcConfigDTO> getCalcConfig(@JsonParam @NotBlank(message = "国家ID不能为空") String countryId) {
//        final List<CalcConfigDTO> config = fbaService.getCalcConfig(Collections.singletonList(countryId));
//        return JsonResult.success(CollectionUtil.emptyIfNull(config).stream().findFirst().orElse(null));
//    }

    @RequestMapping("/fbaDataExcelImport")
    @ResponseBody
    public JsonResult<FbaOrderVO> fbaDataExcelImport(MultipartFile attach, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) throws IOException {
        JsonResult<FbaOrderVO> jsonResult = new JsonResult<>();
        List<Map<Integer, String>> fbaInfo = EasyExcel.read(attach.getInputStream())
                .sheet(0)
                .headRowNumber(0)
                .doReadSync();
        // 获取FBA视图对象
        FbaOrderVO result = getFbaOrderVO(fbaInfo, jsonResult);
        // 获取FBA发货账号并设置
        final String customerCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        result.setCustomerCode(customerCode);
        List<FbaBoxVO> fbaBoxVoList = new ArrayList<>(fbaInfo.size());
        List<FbaDeclareVO> fbaDeclareVoList = new ArrayList<>(fbaInfo.size());
        for (int i = 10; i < fbaInfo.size(); i++) {
            fbaBoxVoList.add(getFbaBoxVO(fbaInfo, i));
            fbaDeclareVoList.add(getFbaDeclareVO(fbaInfo, i));
        }

        // 验证FBA对象是否符合要求
        List<String> errorMsgList = validateFBAObjects(result, currentUser.getUserId(), fbaBoxVoList, fbaDeclareVoList);

        if (CollectionUtils.isNotEmpty(errorMsgList)) {
            jsonResult.setMessage(String.join(StringUtils.LF, errorMsgList));
            jsonResult.setSuccess(false);
            return jsonResult;
        }

        // FBA箱单信息去重
        fbaBoxVoList = fbaBoxVoList.stream().distinct().collect(Collectors.toList());
        result.setFbaBoxVoList(fbaBoxVoList);
        result.setFbaDeclareVoList(fbaDeclareVoList);
        jsonResult.setData(result);
        jsonResult.setSuccess(true);
        return jsonResult;
    }

    /**
     * 验证FBA对象是否符合要求
     *
     * @param result
     * @param fbaBoxVoList
     * @param fbaDeclareVoList
     * @return
     */
    private List<String> validateFBAObjects(FbaOrderVO result, Long userId, List<FbaBoxVO> fbaBoxVoList, List<FbaDeclareVO> fbaDeclareVoList) {
        List<String> errorMsgList = new ArrayList<>(fbaBoxVoList.size() * 2);
        List<String> constraintViolation = ParameterValidationAspect.getConstraintViolation(result);
        // 验证自送仓库
        if (Objects.equals(result.getShipperType(), 1)) {
            if (Strings.isNullOrEmpty(result.getCompanyCode())) {
                constraintViolation.add("客户自送时【仓库】不能为空");
            }
        }
        // 验证
        if (Objects.equals(result.getWarehouseType(), 1)) {
            // 检查是否填写货件追踪ID
            if (CollectionUtils.isNotEmpty(result.getFbaBoxVoList())) {
                for (FbaBoxVO fbaBoxVo : result.getFbaBoxVoList()) {
                    if (StringUtils.isBlank(fbaBoxVo.getAmazonReferenceId())) {
                        constraintViolation.add("【Reference ID】不能为空(地址类型为亚马逊仓库时，Reference ID必填)");
                        break;
                    }
                }
            }
            if (Strings.isNullOrEmpty(result.getWarehouseCode())) {
                constraintViolation.add("【仓库代码】不能为空(地址类型为亚马逊仓库时，仓库代码必填)");
            }
        } else if (Objects.equals(result.getWarehouseType(), 2)) {
            if (Strings.isNullOrEmpty(result.getReceiverPhone())) {
                constraintViolation.add("【电话】不能为空(地址类型为私人仓库时，电话必填)");
            }
        }
        if (CollectionUtils.isNotEmpty(constraintViolation)) {
            errorMsgList.add("基础订单信息错误：");
            errorMsgList.add(String.join(", ", constraintViolation));
        }
        for (int i = 0; i < fbaBoxVoList.size(); i++) {
            List<String> fbaBoxViolation = ParameterValidationAspect.getConstraintViolation(fbaBoxVoList.get(i));
            fbaBoxViolation.addAll(ParameterValidationAspect.getConstraintViolation(fbaDeclareVoList.get(i)));
            if (CollectionUtils.isNotEmpty(fbaBoxViolation)) {
                if (!errorMsgList.contains("箱单货物信息错误：")) {
                    errorMsgList.add("箱单货物信息错误：");
                }
                errorMsgList.add(String.format(ROW, i + 11).concat(String.join(", ", fbaBoxViolation)));
            }
        }
        return errorMsgList;
    }

    /**
     * 确认账单
     *
     * @param epCodes 运单号集合
     * @return 结果
     */
    @Logger(module = FBA, name = "确认账单")
    @RequestMapping("/confirmBill")
    public JsonResult<?> confirmBill(@RequestBody List<String> epCodes) {
        // 设置制单账号
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        final List<Map.Entry<String, ConfirmTheBillDTO>> resultList = epCodes.stream()
                        .map(k -> MapUtil.entry(k, fbaService.confirmBill(k ,businessCode)))
                        .collect(Collectors.toList());
        // 分析请求结果
        final List<Map.Entry<String, ConfirmTheBillDTO>> error = resultList.stream()
                .filter(x -> Boolean.TRUE.equals(x.getValue().getHasError()) || Boolean.FALSE.equals(x.getValue().getData()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(error)) {
            return JsonResult.success();
        }
        // 如果有错误
        final String errorMsg = error.stream().map(v -> StrUtil.format("{} {}{}",
                        v.getKey(),
                        v.getValue().getErrorMsg(),
                        v.getValue().getDetailMsg()))
                .collect(Collectors.joining(StringUtils.LF));
        return JsonResult.error(errorMsg);

    }

    /**
     * 下载账单
     *
     * @param epCodes    运单号集合
     * @param response   响应
     */
    @RequestMapping("/downloadBill")
    public void downloadBill(@RequestBody List<String> epCodes, HttpServletResponse response) {
        // 下载账单
        HttpUtil.url(StrUtil.format("{}/api/fbaBillManagement/exportBillExcelByProtal", fbaUrl))
                .response(response)
                .body(ImmutableMap.of("EpCode", epCodes))
                .mode(RequestMode.POST)
                .timeout(20)
                .download();
    }

    /**
     * 获取账单
     *
     * @param epCode 运单号
     * @return 结果
     */
    @Logger(module = FBA, name = "获取账单")
    @RequestMapping("/getBill")
    public JsonResult<?> getBill(@JsonParam String epCode) {
        // 下载账单
        final BillDTO dto = fbaService.getBill(epCode);
        if (Boolean.FALSE.equals(dto.getHasError())) {
            return JsonResult.success(dto.getData());
        }
        return JsonResult.error(StrUtil.format("{} {}"), dto.getErrorMsg(), dto.getDetailMsg());
    }

    @RequestMapping("/fbaDataExcelDownload")
    public void fbaDataExcelDownload(HttpServletResponse response) throws IOException {
        FFSImportBean importBean = HttpUtil.doGet(fbaUrl.concat("/api/trackManagement/getTemplateLink"), FFSImportBean.class);
        if (Objects.isNull(importBean)) {
            log.error("FBA模板文件下载失败，原因：响应为null，日志已打印上方");
            return;
        }
        if (importBean.getHasError()) {
            log.error("FBA模板文件下载失败，原因：{} {}", importBean.getErrorMsg(), importBean.getDetailMsg());
            return;
        }
        if (Strings.isNullOrEmpty(importBean.getData())) {
            log.error("FBA模板文件下载失败，原因：URL为空 {}", JSONObject.toJSONString(importBean));
            return;
        }
        HttpUtil.download(importBean.getData(), response, "FBA导入模板.xlsx");
    }

    private FbaOrderVO getFbaOrderVO(List<Map<Integer, String>> fbaInfo, JsonResult<FbaOrderVO> jsonResult) {
        // 发货账号
        // String customerCode = getValue(fbaInfo, 0, 1);
        // 地址
        String personalAddress2 = getValue(fbaInfo, 0, 3);
        // 客户单号
        String orderNumber = getValue(fbaInfo, 0, 5);
        // 发货方式 1 客户自送 2 燕文揽收
        String shipperTypeName = getValue(fbaInfo, 1, 1);
        Integer shipperType = null;
        if (StringUtils.isNotBlank(shipperTypeName)) {
            switch (shipperTypeName) {
                case "燕文揽收":
                    shipperType = 2;
                    break;
                case "客户自送":
                    shipperType = 1;
                    break;
            }
        }
        // 城市
        String receiverCity = getValue(fbaInfo, 1, 3);
        // 仓库
        String companyCode = getValue(fbaInfo, 2, 1);
        // 邮编
        String postcode = getValue(fbaInfo, 2, 3);
        // 产品名称
        String channelName = getValue(fbaInfo, 3, 1);
        // 税号
        String vatNumber = getValue(fbaInfo, 3, 3);
        // 国家
        String countryName = getValue(fbaInfo, 4, 1);
        // 公司
        String receiverCompany = getValue(fbaInfo, 4, 3);
        // 地址类型 1 : 亚马逊2 : 私人仓库
        String warehouseTypeName = getValue(fbaInfo, 5, 1);
        Integer warehouseType = null;
        if (StringUtils.isNotBlank(warehouseTypeName)) {
            switch (warehouseTypeName) {
                case "私人仓库":
                    warehouseType = 2;
                    break;
                case "亚马逊仓库":
                    warehouseType = 1;
                    break;
            }
        }
        // 州省
        String receiverProvince = getValue(fbaInfo, 5, 3);
        // 仓库代码
        String warehouseCode = getValue(fbaInfo, 6, 1);
        // 电话
        String receiverPhone = getValue(fbaInfo, 6, 3);
        // 收件人姓名
        String receiverName = getValue(fbaInfo, 7, 1);
        // 邮箱
        String receiverEmail = getValue(fbaInfo, 7, 3);
        // Reference ID
        String amazonReferenceId = getValue(fbaInfo, 8, 3);
        // 地址1
        String personalAddress1 = getValue(fbaInfo, 8, 1);

        return FbaOrderVO.builder()//.customerCode(customerCode)
                .personalAddress1(personalAddress1)
                .personalAddress2(personalAddress2)
                .shipperType(shipperType).receiverCity(receiverCity)
                .companyCode(companyCode).postcode(postcode)
                .channelCode(channelName).vatNumber(vatNumber)
                .countryName(countryName).receiverCompany(receiverCompany)
                .warehouseType(warehouseType).receiverProvince(receiverProvince)
                .warehouseCode(warehouseCode).receiverPhone(receiverPhone)
                .receiverName(receiverName).receiverEmail(receiverEmail)
                .amazonReferenceId(amazonReferenceId).orderNumber(orderNumber).build();
    }

    private static Map<String, Integer> statusMap;

    static {
        statusMap = new HashMap<>();
        statusMap.put("无品牌", 1);
        statusMap.put("境内自主品牌", 2);
        statusMap.put("境内收购品牌", 3);
        statusMap.put("境外品牌（贴牌生产）", 4);
        statusMap.put("境外品牌（其他）", 5);
    }

    private FbaDeclareVO getFbaDeclareVO(List<Map<Integer, String>> fbaInfo, int i) {
        String tagNumber = getValue(fbaInfo, i, 0);
        String chineseName = getValue(fbaInfo, i, 6);
        String englishName = getValue(fbaInfo, i, 7);
        String declareUnitPrice = getValue(fbaInfo, i, 8);
        String quantity = getValue(fbaInfo, i, 9);
        String hsCode = getValue(fbaInfo, i, 10);
        String texture = getValue(fbaInfo, i, 11);
        String type = getValue(fbaInfo, i, 12);
        String purpose = getValue(fbaInfo, i, 13);
        String brand = getValue(fbaInfo, i, 14);
        String brandTypeStr = getValue(fbaInfo, i, 15);
        String saleLinkUrl = getValue(fbaInfo, i, 16);
        return FbaDeclareVO.builder().chineseName(chineseName)
                .tagNumber(tagNumber).englishName(englishName)
                .declareUnitPrice(declareUnitPrice).quantity(quantity).hsCode(hsCode).texture(texture)
                .type(type).purpose(purpose).brand(brand).brandTypeStr(brandTypeStr)
                .brandType(statusMap.get(StringUtils.trim(brandTypeStr)))
                .saleLinkUrl(saleLinkUrl)
                .build();
    }

    private FbaBoxVO getFbaBoxVO(List<Map<Integer, String>> fbaInfo, int i) {
        String tagNumber = getValue(fbaInfo, i, 0);
        String amazonReferenceId = getValue(fbaInfo, i, 1);
        String length = getValue(fbaInfo, i, 2);
        String width = getValue(fbaInfo, i, 3);
        String height = getValue(fbaInfo, i, 4);
        String boxWeight = getValue(fbaInfo, i, 5);
        return FbaBoxVO.builder()
                .tagNumber(tagNumber)
                .length(length)
                .amazonReferenceId(amazonReferenceId)
                .width(width)
                .height(height)
                .boxWeight(boxWeight)
                .build();
    }

    private String getValue(List<Map<Integer, String>> fbaInfos, int index, int key) {
        if (fbaInfos.size() > index) {
            return fbaInfos.get(index).get(key);
        } else {
            return null;
        }
    }

    /**
     * @description 解析箱单excel数据
     * <AUTHOR>
     * @date 2021/9/13 15:34
     */
    @RequestMapping("analysisBoxExcel")
    @ResponseBody
    @Logger(module = Module.FBA, name = "解析箱单excel数据")
    public JsonResult<?> analysisBoxExcel(MultipartFile attach) {
        JsonResult jsonResult = new JsonResult();
        try {
            String fileName = attach.getOriginalFilename();
            String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
            if (!".xlsx".equals(suffix)) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("按照模板上传文件");
                return jsonResult;
            }
            // 5M
            long size = 5 * 1024 * 1024;
            if (attach.getSize() > size) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("请上传规定大小文件");
                return jsonResult;
            }
            List<FbaBoxVO> fbaBoxVos = fbaService.analysisBoxExcel(attach.getInputStream());
            jsonResult.setData(fbaBoxVos);
            jsonResult.setSuccess(true);
        } catch (BusinessException e) {
            log.error("提交FBA订单信息异常,原因:{}", e.getMessage(), e);
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("解析FBA箱单excel异常,原因:{}", e.getMessage(), e);
            jsonResult.setSuccess(false);
            jsonResult.setMessage("解析文件异常");
        }
        return jsonResult;
    }

    /**
     * 获取总价值
     *
     * @param fbaDeclareVoList 申报信息文件
     * @return
     */
    private BigDecimal getTotalPrice(List<FbaDeclareVO> fbaDeclareVoList) {
        try {
            if (!org.springframework.util.CollectionUtils.isEmpty(fbaDeclareVoList)) {
                return fbaDeclareVoList.stream()
                        .map(dec -> EJFUtil.getBigDecimal(dec.getDeclareUnitPrice()).multiply(EJFUtil.getBigDecimal(dec.getQuantity())))
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            }
        } catch (NumberFormatException ignored) {
        }
        return BigDecimal.ZERO;
    }

    /**
     * @description 解析FBA申报信息文件
     * <AUTHOR>
     * @date 2021/9/14 16:54
     */
    @RequestMapping("analysisDeclareExcel")
    @ResponseBody
    @Logger(module = Module.FBA, name = "解析FBA申报信息文件")
    public JsonResult<?> analysisDeclareExcel(MultipartFile attach) {
        JsonResult jsonResult = new JsonResult();
        try {
            String fileName = attach.getOriginalFilename();
            String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
            if (!".xlsx".equals(suffix)) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("按照模板上传文件");
                return jsonResult;
            }
            // 5M
            long size = 5 * 1024 * 1024;
            if (attach.getSize() > size) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("请上传规定大小文件");
                return jsonResult;
            }
            List<FbaDeclareVO> fbaDeclareVos = fbaService.analysisDeclareExcel(attach.getInputStream());
            // 修改brandType为ID
            fbaDeclareVos.forEach(fbaDeclareVo -> fbaDeclareVo.setBrandType(statusMap.get(StringUtils.trim(fbaDeclareVo.getBrandTypeStr()))));
            jsonResult.setData(ImmutableMap.of("totalPrice", getTotalPrice(fbaDeclareVos), "list", fbaDeclareVos));
            jsonResult.setSuccess(true);
        } catch (BusinessException e) {
            log.error("解析FBA申报信息excel异常,原因:{}", e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("解析FBA申报信息excel异常,原因:{}", e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("解析文件异常");
        }
        return jsonResult;
    }

    /**
     * 检测是否支持揽收
     *
     * @param param 参数
     * @return
     */
    @RequestMapping("detectionSupportCollection")
    @ResponseBody
    @Logger(module = Module.FBA, name = "检测是否支持揽收")
    public JsonResult<?> detectionSupportCollection(@RequestBody DetectionSupportCollectionDTO param) {
        // 检测是否支持揽收
        fbaService.detectionSupportCollection(param.getCustomerCode());
        return JsonResult.success("该发货账号支持揽收", null);
    }

    /**
     * @description 提交订单
     * <AUTHOR>
     * @date 2021/9/15 16:55
     */
    @RequestMapping("submitOrder")
    @ResponseBody
    @Logger(module = Module.FBA, name = "提交订单", type = Type.WAYBILL_NUMBER, rsp = "data")
    public JsonResult<?> submitOrder(@RequestBody FbaOrderVO fbaOrderVo) {
        try {
            // 设置制单账号
            final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
            fbaOrderVo.setCustomerCode(businessCode);
            // 检测申报总价值是否超过限度
            fbaService.declareValueVerification(fbaOrderVo);
            // 检测是否支持揽收
            fbaService.detectionSupportCollection(fbaOrderVo);
            return JsonResult.success(fbaService.submitOrder(fbaOrderVo));
        } catch (BusinessException e) {
            log.error("提交FBA订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("提交FBA订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error("提交失败");
        }
    }

    /**
     * @description 下载模板
     * <AUTHOR>
     * @date 2021/9/15 17:09
     */
    @GetMapping("downloadTemplate")
    public void downloadTemplate(HttpServletResponse response, Integer type) {
        type = Optional.ofNullable(type).orElse(1);
        String filename = type == 1 ? "箱单信息模板.xlsx" : "申报信息模板.xlsx";
        ClassPathResource resource = new ClassPathResource("data/" + filename);
        InputStream in = null;
        try {
            // 设置强制下载不打开
            in = resource.getInputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(filename, "UTF-8"));
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                response.getOutputStream().write(buffer, 0, len);
            }
        } catch (IOException e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            JsonResult<?> jsonResult = new JsonResult<>();
            jsonResult.setSuccess(false);
            jsonResult.setMessage("下载模板失败");
            try {
                response.getOutputStream().write(JSON.toJSONBytes(jsonResult));
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @description 根据国家获取亚马逊仓库
     * <AUTHOR>
     * @date 2021/9/15 17:37
     */
    @GetMapping("getAmazonWarehouse")
    @ResponseBody
    public JsonResult<?> getAmazonWarehouse(String countryId) {
        try {
            return JsonResult.success(fbaService.getWarehouseList(countryId));
        } catch (BusinessException e) {
            log.error("提交FBA订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据国家查询亚马逊仓库信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error("服务异常");
        }
    }

    /**
     * @description 获取国家列表
     * <AUTHOR>
     * @date 2021/9/16 15:02
     */
    @GetMapping("getCountryList")
    @ResponseBody
    public JsonResult<?> getCountryList(String channelCode) {
        List<CountryVO> countryList = new LinkedList<>();
        try {
            countryList = Optional.ofNullable(channelCode)
                    .filter(StringUtils::isNotBlank)
                    .map(fbaService::getCountryList)
                    .orElseGet(fbaService::getCountryList);
        } catch (Exception e) {
            log.error("FBA查询国家信息异常,原因:{}", e.getMessage());
        }
        return JsonResult.success(countryList);
    }

    /**
     * @description 获取订单列表
     * <AUTHOR>
     * @date 2021/9/16 15:43
     */
    @RequestMapping("/getList")
    @ResponseBody
    @Validated
    @Logger(module = Module.FBA, name = "获取订单列表")
    public JsonResult<?> getFbaOrderList(@RequestBody FbaListParamFrontDTO param) {
        try {
            // 设置制单账号
            final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
            param.setCustomerCode(businessCode);
            switch (FbaStatusEnum.to(param.getStatus())) {
                case DRAFT:
                    break;
                case PLACED_AN_ORDER:
                case SEND_BACK:
                    // 调老接口
                    return JsonResult.success(fbaService.getOrderList(param));
                case ALL:
                case WAREHOUSED:
                case WAIT_VERIFY:
                case VERIFY:
                case WAITING_FOR_DEDUCTION:
                case CUSTOMER_HAS_CONFIRMED:
                case HAVE_BEEN_TAKEN_OUT_OF_STORAGE:
                case HAVE_BEEN_DELIVERED:
                case CANCELED:
                    // 调新接口
                    return JsonResult.success(fbaService.getOrderList(param.toDTO()).toVO());
            }
            return JsonResult.error(PORTAL_9101);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("查询fba订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error("服务异常");
        }
    }

    /**
     * @description 查询详情
     * <AUTHOR>
     * @date 2021/9/16 15:54
     */
    @RequestMapping("getDetail")
    @ResponseBody
    @Logger(module = Module.FBA, name = "查询详情")
    public JsonResult<?> getDetail(@NotBlank(message = "expressNumber不能为空") String expressNumber) {
        try {
            // 设置制单账号
            final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
            final FbaOrderVO detail = fbaService.getOrderDetail(expressNumber, businessCode);
            // 设置箱单详细测量信息
            setBoxMeasurementInformation(detail);
            return JsonResult.success(detail);
        } catch (Exception e) {
            log.error("查询fba订单信息异常,原因:{}", e.getMessage());
            return JsonResult.error("服务异常");
        }
    }

    /**
     * 设置FBA实际测量信息
     *
     * @param detail 订单详情
     */
    private void setBoxMeasurementInformation(FbaOrderVO detail) {
        final String expressNumber = detail.getExpressNumber();
        try {
            // 获取箱单详细测量信息
            final List<FbaBillManagementDTO> dto = fbaService.fbaBillManagement(expressNumber);
            if (CollectionUtil.isNotEmpty(dto) &&
                    CollectionUtil.isNotEmpty(detail.getFbaBoxVoList())) {
                // 详细测量信息转map
                final Map<String, FbaBillManagementDTO> dtoMap = dto.stream()
                        .collect(Collectors.toMap(FbaBillManagementDTO::getFbaLabelCode, Function.identity()));
                detail.getFbaBoxVoList().forEach(box ->
                        Optional.ofNullable(dtoMap.get(box.getTagNumber()))
                                .ifPresent(box::setFbaBillManagementDTO));
            }
        } catch (Throwable e) {
            log.error("设置实际测量信息失败 {}", expressNumber, e);
        }
    }

    /**
     * @description 上传附件
     * <AUTHOR>
     * @date 2021/9/16 16:20
     */
    @RequestMapping("uploadAttach")
    @ResponseBody
    @Logger(module = Module.FBA, name = "上传附件")
    public JsonResult<?> uploadAttach(MultipartFile attach) {
        UserAgent currentUser = getUser();
        JsonResult<String> jsonResult = new JsonResult<>();
        String fileName = attach.getOriginalFilename().replace(".jpeg", ".jpg").replace(".JPEG", ".jpg");
        long fileSize = attach.getSize();

        String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length()).toLowerCase();
        if (!FileConstant.TYPE_ZIP.equals(suffix) && !FileConstant.TYPE_JPG.equals(suffix) && !FileConstant.TYPE_PNG.equals(suffix)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("仅支持上传图片和zip压缩包");
            return jsonResult;
        }
        // 如果是压缩包,大小为5M
        if (FileConstant.TYPE_ZIP.equals(suffix)) {
            long zipMax = 5 * 1024 * 1024;
            if (zipMax < fileSize) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("压缩包最大为5M");
                return jsonResult;
            }
        } else {
            long picMax = 1024 * 1024;
            if (picMax < fileSize) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("图片最大为1M");
                return jsonResult;
            }
        }
        FileUploadVo fileUploadVo = new FileUploadVo();
        fileUploadVo.setAttach(attach);
        fileUploadVo.setObjId(currentUser.getMerchantNo());
        fileUploadVo.setObjName(UploadObjNameEnum.FBA_ATTACH.getValue());
        fileUploadVo.setUserId(currentUser.getUserId());
        fileUploadVo.setSingleFile(true);
        String res = filePathResolver.uploadFile(fileUploadVo);
        jsonResult.setSuccess(true);
        jsonResult.setData(filePathResolver.appendSignature(res));
        jsonResult.setMessage("操作成功");
        return jsonResult;
    }

    /**
     * @description 生成入库单
     * <AUTHOR>
     * @date 2021/9/16 17:33
     */
    @RequestMapping("generateExpressSlip")
    @ResponseBody
    @Logger(module = Module.FBA, name = "生成入库单")
    public ResponseEntity generateExpressSlip(@RequestBody JSONObject jsonObject) {
        String expressNumbers = jsonObject.getString("expressNumbers");
        // 设置制单账号
        String customerCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        List<String> expressNumbersList = Arrays.asList(expressNumbers.split(","));
        try {
            byte[] data = fbaService.getDeliverySlip(expressNumbersList, customerCode);
            HttpHeaders headers = new HttpHeaders();
            StringBuilder stringBuilder = new StringBuilder("入库单_").append(LocalDate.now()).append(".xls");
            String fileName = stringBuilder.toString();
            try {
                fileName = URLEncoder.encode(stringBuilder.toString(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.add("Content-Disposition", "attachment;filename=" + fileName);
            return new ResponseEntity<>(data, headers, HttpStatus.OK);
        } catch (BusinessException e) {
            log.error("fba生成入库单异常,异常原因:{}", e.getMessage());
            return new ResponseEntity<>(JsonResult.error(e.getMessage()), HttpStatus.OK);
        } catch (Exception e) {
            log.error("fba生成入库单异常,异常原因:{}", e.getMessage());
            return new ResponseEntity<>(JsonResult.error("服务异常"), HttpStatus.OK);
        }
    }

    /**
     * @description 生成入库签, 支持生成多个
     * <AUTHOR>
     * @date 2021/9/26 15:58
     */
    @RequestMapping("generateExpressLabel")
    @Logger(module = Module.FBA, name = "生成入库签", type = Type.WAYBILL_NUMBER, req = "v[0].expressNumbers")
    public void generateExpressLabel(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        String expressNumbers = jsonObject.getString("expressNumbers");
        // 设置制单账号
        String customerCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        String[] expressNumbersList = expressNumbers.split(",");
        Document document;
        PdfReader reader = null;
        try {
            List<byte[]> lists = new LinkedList<>();
            for (String expressNumber : expressNumbersList) {
                byte[] bytes = fbaService.getDeliveryLabel(expressNumber, customerCode);
                lists.add(bytes);
            }
            document = new Document();
            String fileName = "入库签_" + LocalDate.now() + ".pdf";
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition",
                    "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
            PdfCopy copy = new PdfCopy(document, response.getOutputStream());
            document.open();
            PdfImportedPage importPage;
            for (byte[] bytes : lists) {
                reader = new PdfReader(bytes);
                for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                    importPage = copy.getImportedPage(reader, i);
                    copy.addPage(importPage);
                }
            }
            if (reader != null) {
                reader.close();
            }
            document.close();
        } catch (BusinessException e) {
            JsonResult<?> jsonResult = JsonResult.error(e.getMessage());
            log.error("fba生成入库签异常,原因:{}", e.getMessage(), e);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getOutputStream().write(JSON.toJSONBytes(jsonResult));
            } catch (IOException e1) {
                log.error(e1.getMessage(), e1);
            }
        } catch (Exception e) {
            log.error("fba生成入库签异常,原因:{}", e.getMessage(), e);
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            JsonResult<?> jsonResult = JsonResult.error("fba生成入库签异常");
            try {
                response.getOutputStream().write(JSON.toJSONBytes(jsonResult));
            } catch (IOException e1) {
                log.error(e1.getMessage(), e1);
            }
        }
    }

    /**
     * @description 获取渠道
     * <AUTHOR>
     * @date 2021/10/8 17:52
     */
    @RequestMapping("getChannelList")
    @ResponseBody
    @Logger(module = Module.FBA, name = "获取渠道")
    public JsonResult<?> getChannelList(String countryId) {
        try {
            List<FbaChannelVO> channelList =
                    Optional.ofNullable(countryId)
                            .filter(StringUtils::isNotBlank)
                            .map(fbaService::getChannelList)
                            .orElseGet(fbaService::getChannelList);
            // 按照中文首字母进行排序
            channelList.sort(CompareUtil.comparingPinyin(FbaChannelVO::getChannelName));
            return JsonResult.success(channelList);
        } catch (BusinessException e) {
            log.error("从Fba获取渠道异常,异常原因:{}", e.getMessage(), e);
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("从Fba获取渠道异常,异常原因:{}", e.getMessage(), e);
            return JsonResult.error("服务异常");
        }
    }


    /**
     * @description 查询fba发货账号列表
     * <AUTHOR>
     * @date 2021/10/22 16:01
     */
    @GetMapping("/shipper/list")
    @ResponseBody
    @Logger(module = Module.FBA, name = "查询fba发货账号列表")
    public JsonResult<?> getFbaShipperList() {
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        return JsonResult.success(ListUtil.toList(ImmutableMap.of("accountCode", businessCode)));
    }

    /**
     * @description 获取计算体积重系数
     * <AUTHOR>
     * @date 2021/11/17 9:36
     */
    @RequestMapping("getCalculateCoefficient")
    @ResponseBody
    @Logger(module = Module.FBA, name = "获取计算体积重系数")
    public JsonResult<?> getCalculateCoefficient(String countId, String productCode) {
        try {
            List<FbaWarehouseVO> warehouseList = fbaService.getWarehouseList(countId);
            FbaCalculateVO fbaCalculateVo = fbaService.getCalculateCoefficient(countId, productCode);
            Map<String, Object> resultMap = new HashMap<>(2);
            resultMap.put("ration", fbaCalculateVo.getRation());
            resultMap.put("boxLimit", fbaCalculateVo.getBoxLimit());
            resultMap.put("declareCurrency", fbaCalculateVo.getDeclareCurrency());
            resultMap.put("declareCurrencyName", fbaCalculateVo.getDeclareCurrencyName());
            resultMap.put("warehouseList", warehouseList);
            return JsonResult.success(resultMap);
        } catch (BusinessException e) {
            log.error("获取计算体积重系数,异常,原因:{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("获取计算体积重系数,异常,原因:{}", e.getMessage());
            return JsonResult.error("出现异常");
        }
    }

    /**
     * 申报总价值校验
     *
     * @param fbaOrderVO 源数据
     */
    @PostMapping("/declareValueVerification")
    @ResponseBody
    @Logger(module = Module.FBA, name = "申报总价值校验")
    public JsonResult<?> declareValueVerification(@RequestBody FbaOrderVO fbaOrderVO) {
        return fbaService.declareValueVerification(fbaOrderVO);
    }


    /**
     * @description 更新fba订单
     * <AUTHOR>
     * @date 2021/12/8 13:49
     */
    @RequestMapping("updateOrder")
    @ResponseBody
    @Logger(module = Module.FBA, name = "更新fba订单", type = Type.WAYBILL_NUMBER, req = "mainNo")
    public JsonResult<?> updateOrder(@RequestBody FbaOrderVO fbaOrderVo) {
        try {
            // 设置制单账号
            final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
            fbaOrderVo.setCustomerCode(businessCode);
            // 检测申报总价值是否超过限度
            fbaService.declareValueVerification(fbaOrderVo);
            return JsonResult.success(fbaService.updateOrder(fbaOrderVo));
        } catch (BusinessException e) {
            log.error("更新FBA订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新FBA订单信息异常,原因:{}", e.getMessage(), e);
            return JsonResult.error("提交失败");
        }
    }

    /**
     * 获取FBA轮播图
     *
     * @return FBA轮播图
     */
    @GetMapping("/getFbaBannerList")
    @Logger(module = Module.FBA, name = "获取FBA轮播图")
    public JsonResult<?> getFbaBannerList() {
        final List<FbaBannerDTO> bannerList = fbaService.getFbaBannerList();
        bannerList.sort(Comparator.comparing(FbaBannerDTO::getSort));
        return JsonResult.success(bannerList);
    }

    /**
     * 获取FBA报价单
     *
     * @return FBA报价单
     */
    @GetMapping("/getQuotation")
    @Logger(module = Module.FBA, name = "获取FBA报价单")
    public JsonResult<?> getQuotation(FbaQuotationParamDTO dto) throws IOException {
        final FbaQuotationDTO fbaQuotationDTO = fbaService.getQuotationInfoList(dto);
        // 存储本地文件
        final String signature = filePathResolver.appendSignature(fbaQuotationDTO.getFileAddress());
        String tempFileName = fileBusinessService.tempFileSave(signature, File.separator.concat("fba"), null);
        // 获取文件URL
        String tempFileUrl = fileBusinessService.getTempFileUrl(tempFileName);
        fbaQuotationDTO.setFileAddress(signature);
        return JsonResult.success(fbaQuotationDTO.toVO(tempFileUrl));
    }

    /**
     * 获取FBA揽收仓
     *
     * @return FBA揽收仓
     */
    @GetMapping("/getFbaReceiveWarehouseList")
    @Logger(module = Module.FBA, name = "获取FBA揽收仓")
    public JsonResult<?> getFbaReceiveWarehouseList() {
        final List<FbaReceiveWarehouseDTO> fbaReceiveWarehouseList = fbaService.getFbaReceiveWarehouseList();
        return JsonResult.success(
                fbaReceiveWarehouseList.stream()
                        .map(FbaReceiveWarehouseDTO::toVO)
                        .collect(Collectors.toList())
        );
    }

    /**
     * 新增揽收地址
     */
    @PostMapping("/addCollectAddress")
    @Logger(module = Module.FBA, name = "新增揽收地址")
    public JsonResult<?> addCollectAddress(@RequestBody AddressDTO addressDTO) {
        final UserAgent user = getUser();
        addressDTO.setUserCode(user.getUserCode());
        // 获取FBA申请信息
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        addressDTO.setMerchantCode(businessCode);
        addressDTO.setCreateUser(user.getUsername());
        fbaService.addCollectAddress(addressDTO);
        return JsonResult.success();
    }

    /**
     * 删除揽收地址
     */
    @PostMapping("/deleteCollectAddress")
    @Logger(module = Module.FBA, name = "删除揽收地址")
    public JsonResult<?> deleteCollectAddress(@JsonParam @NotBlank(message = "id不能为空") String id) {
        final UserAgent user = getUser();
        fbaService.deleteCollectAddress(id, user.getUsername());
        return JsonResult.success();
    }

    /**
     * 修改揽收地址
     */
    @PostMapping("/updateCollectAddress")
    @Logger(module = Module.FBA, name = "修改揽收地址")
    public JsonResult<?> updateCollectAddress(@RequestBody AddressDTO addressDTO) {
        final UserAgent user = getUser();
        addressDTO.setUserCode(user.getUserCode());
        // 获取FBA申请信息
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        addressDTO.setMerchantCode(businessCode);
        addressDTO.setCreateUser(user.getUsername());
        fbaService.updateCollectAddress(addressDTO);
        return JsonResult.success();
    }

    /**
     * 根据商户编码查询地址
     */
    @PostMapping("/getCollectAddress")
    @Validated
    public JsonResult<CollectAddressDTO> getCollectAddress(@RequestBody GetCollectAddressDTO dto) {
        if (Objects.equals(dto.getPageSize(), -1)) {
            // 如果是-1，则查询所有值
            dto.setPageSize(5000);
        }
        // 获取FBA信息
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        dto.setMerchantCode(businessCode);
        CollectAddressDTO addressDTO = fbaService.getCollectAddress(dto);
        // ID更换为名称
        final List<WareHouseVo> warehouses = cmccService.getWarehouses();
        addressDTO.getData().forEach(data -> warehouses.stream()
                .filter(warehouse -> warehouse.getCode().equals(data.getWarehouse()))
                .findFirst()
                .map(WareHouseVo::getName)
                .ifPresent(data::setWarehouseName));
        return JsonResult.success(addressDTO);
    }

    /**
     * 获取FBA订单数量
     *
     * @return FBA订单数量
     */
    @PostMapping("/quantityStatistics")
    @Logger(module = Module.FBA, name = "获取FBA订单数量")
    public JsonResult<?> quantityStatistics() {
        // 获取FBA申请信息
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        final QuantityStatisticsParamDTO param = QuantityStatisticsParamDTO.builder()
                .createDate(ListUtil.of(
                        DateUtil.offsetDay(DateUtil.date(), -30).toString(),
                        DateUtil.date().toString()
                ))
                .userId(ListUtil.of(businessCode))
                .build();
        return JsonResult.success(fbaService.quantityStatistics(param));
    }

    /**
     * @param productCode 产品ID
     * @return JsonResult
     * @description 获取fba运单配置信息
     */
    @PostMapping("/waybillConfig")
    @ResponseBody
    @Logger(module = Module.FBA, name = "获取fba运单配置信息")
    public JsonResult<?> waybillConfig(@JsonParam @NotNull(message = "productCode不能为空") String productCode) {
        return JsonResult.success(fbaService.waybillConfig(productCode));
    }

    /**
     * FBA订单终止
     *
     * @param dto 订单终止参数
     * @return 终止结果
     */
    @Validated
    @PostMapping("/orderCancel")
    @Logger(module = Module.FBA, name = "FBA订单终止", type = Type.WAYBILL_NUMBER, req = "epCodes")
    public JsonResult<?> orderCancel(@RequestBody OrderCancelDTO dto) {
        // 删除无权限，无法获取到详情的运单
        dto.getEpCodes().removeIf(ep -> Boolean.FALSE.equals(getDetail(ep).getSuccess()));
        // 设置用户信息
        final UserAgent user = getUser();
        dto.setOperateName(user.getUsername());
        dto.setOperatorId(String.valueOf(user.getUserId()));
        // 调用订单终止接口
        fbaService.orderCancel(dto.getParam());
        return JsonResult.success();
    }

    /**
     * FBA获取箱单信息图片
     *
     * @param epCode            运单号
     * @param YWWarehouseNumber 燕文入仓号
     */
    @PostMapping("/fbaBillManagementImage")
    public JsonResult<?> fbaBillManagementImage(@JsonParam @NotBlank(message = "运单号不能为空") String epCode,
                                                @JsonParam @NotBlank(message = "燕文入仓号不能为空") String YWWarehouseNumber) {
        return JsonResult.success(fbaService.fbaBillManagementImage(epCode, YWWarehouseNumber));
    }

    /**
     * FBA箱单信息导出
     *
     * @param epCode   运单号
     * @param response 响应
     */
    @PostMapping("/fbaBillManagementExport")
    public void fbaBillManagementExport(@JsonParam @NotBlank(message = "运单号不能为空") String epCode,
                                        HttpServletResponse response) {
        fbaService.fbaBillManagementExport(epCode, response);
    }

    /**
     * <AUTHOR>
     * @date 2023/10/30 10:09
     * @description 导出发货证明
     */
    @Logger(module = Module.FBA, name = "fba发货证明下载")
    @PostMapping("fbaProofExport")
    public void fbaProofExport(@JsonParam @Size(min = 1, message = "运单号数据量支持1-100", max = 100) List<String> epCodes, HttpServletResponse response) {
        fbaService.fbaProofExport(epCodes, getUserCode(), response);
    }

    /**
     * <AUTHOR>
     * @date 2023/10/30 10:09
     * @description 获取计费配置
     */
    @Logger(module = Module.FBA, name = "获取计费配置")
    @PostMapping("/getCalcConfig")
    public JsonResult<?> getCalcConfig(@JsonParam String countryId) {
        final List<CalcConfigDTO> config = fbaService.getCalcConfig(ListUtil.toList(countryId));
        return JsonResult.success(CollectionUtil.getFirst(config));
    }

    /**
     * 批量运价试算
     *
     * @param param 参数
     * @return 批量运价试算结果
     */
    @Validated
    @PostMapping("/manyPieceBatch")
    @Logger(module = FBA, name = "FBA运价试算")
    public JsonResult<?> manyPieceBatch(@RequestBody ManyPieceParamBatchDTO param) {
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        // 产品列表
        final List<FbaChannelVO> channelList = fbaService.getChannelList(param.getCountryId());
        if (CollectionUtil.isEmpty(channelList)) {
            return JsonResult.error("您所输入的查询信息未找到适配产品，请更新查询信息重试或咨询销售！");
        }
        // 产品名称Map
        final Map<String, String> productNameMap =
                channelList.stream().collect(Collectors.toMap(
                        FbaChannelVO::getChannelCode,
                        FbaChannelVO::getChannelName,
                        (o1,o2) -> o1));
        final List<ManyPieceParamDTO> paramList = param.getManyPieceParamDTO(productNameMap.keySet(), businessCode);
        // 进行运价试算
        final List<Map.Entry<String, ManyPieceDTO>> manyPieceList =
                paramList.parallelStream().map(v -> MapUtil.entry(v.getProductCode(), fbaService.manyPiece(v)))
                        .filter(v -> v.getValue().getResult())
                            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(manyPieceList)) {
            return JsonResult.error("您所输入的查询信息未找到适配产品，请更新查询信息重试或咨询销售！");
        }
        // 获取产品信息
        final List<ManyPieceVO> vos = manyPieceList.stream().map(many -> {
            final JSONObject productParam = new JSONObject();
            final String productCode = many.getKey();
            final ManyPieceDTO.DataDTO item = many.getValue().getData();
            productParam.put("productCode", productCode);
            productParam.put("countryIds", ListUtil.toList(param.getCountryId()));
            // 总费用
            final BigDecimal money = item.getExpenseItems().stream()
                    .map(ManyPieceDTO.DataDTO.ExpenseItemsDTO::getCnyMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            final ManyPieceVO vo = ManyPieceVO.builder()
                    .productCode(productCode)
                    .productName(productNameMap.get(productCode))
                    .money(money)
                    .build();
            // 获取产品详情
            final JSONObject productDetail = calcService.productDetail(productParam);
            if (productDetail != null &&
                    productDetail.getBoolean("result") &&
                    productDetail.getJSONObject("data") != null) {
                JSONObject data = productDetail.getJSONObject("data");
                JSONObject remark = data.getJSONObject("priceDirectoryRemark");
                JSONArray topRemarks = data.getJSONArray("priceDirectoryRemarkTopc");
                // 设置天数
                final String referAging = topRemarks.stream().map(t -> (JSONObject) t)
                        .filter(t -> Objects.equals(t.getString("countryId"), param.getCountryId()))
                        .findFirst()
                        .map(t -> t.getString("referAging"))
                        .orElse(null);
                final JSONObject priceDirectory = data.getJSONObject("priceDirectory");
                vo.setClassName(priceDirectory.getString("className"));
                vo.setRemark(remark);
                vo.setReferAging(referAging);
            }
            return vo;
        }).collect(Collectors.toList());
        return JsonResult.success(vos);
    }

    /**
     * 运价试算
     *
     * @param param 参数
     * @return 运价试算结果
     */
    @Validated
    @PostMapping("/manyPiece")
    @Logger(module = FBA, name = "FBA单笔运价试算")
    public JsonResult<?> manyPiece(@RequestBody ManyPieceParamDTO param) {
        // 试算配置检查
        final String businessCode = fbaApplyService.getFbaBusinessCode(getUserCode());
        // 如果揽收仓为空则取addressId的仓
        if (StringUtils.isBlank(param.getCompanyCodeFrom())) {
            final String address = param.getAddress();
            Assert.notEmpty(address, "【揽收地址】和【送货仓】不能同时为空");
            final AddressDTO dto = fbaService.getFbaReceiveAddressByAddressId(address);
            // 设置揽收仓
            param.setCompanyCodeFrom(dto.getWarehouse());
        }
        // 设置计费单位
        param.getItems().forEach(v -> v.setUnit("kg"));
        // 设置计费时间
        param.setChargingTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        // 默认不缴
        param.setIsCustoms(false);
        param.setIsMagnetic(false);
        param.setIsSpecialName(false);
        // 设置制单账号
        param.setCustomerCode(businessCode);
        final ManyPieceDTO dto = fbaService.manyPiece(param);
        if (dto.getResult()) {
            final ManyPieceDTO.DataDTO data = dto.getData();
            final List<ManyPieceDTO.DataDTO.ExpenseItemsDTO> expenseItems = data.getExpenseItems();
            // 计费项
            if (CollectionUtil.isNotEmpty(expenseItems)) {
                final BigDecimal totalMoney = expenseItems.stream()
                        .map(ManyPieceDTO.DataDTO.ExpenseItemsDTO::getCnyMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                return JsonResult.success(totalMoney);
            }
            return JsonResult.error("无计费项，试算失败");
        }
        return JsonResult.error(dto.getMessage());
    }

    /**
     * 试算配置检查
     *
     * @param param 参数
     */
    private void calcConfig(ManyPieceParamDTO param) {
        final List<CalcConfigDTO> calcConfig = fbaService.getCalcConfig(Collections.singletonList(param.getCountryId()));
        if (CollectionUtil.isNotEmpty(calcConfig)) {
            final CalcConfigDTO dto = calcConfig.get(0);
            final String check = "1";
            // 地址类型
            if (Objects.equals(dto.getAddressType(), check) && StringUtils.isBlank(param.getAddressType())) {
                throw ResponseCode.PORTAL_9207.getError();
            }
            // 邮编
            if (Objects.equals(dto.getPostCode(), check) && StringUtils.isBlank(param.getPostCode())) {
                throw ResponseCode.PORTAL_9208.getError();
            }
            // 交税模式
            if (Objects.equals(dto.getTaxMode(), check) && StringUtils.isBlank(param.getTaxType())) {
                throw ResponseCode.PORTAL_9209.getError();
            }
            // 箱数
            if (Objects.equals(dto.getBoxNumber(), check) && CollectionUtil.isEmpty(param.getItems())) {
                throw ResponseCode.PORTAL_9210.getError();
            }
            // 单箱重量
            if (Objects.equals(dto.getBoxWeight(), check) &&
                    CollectionUtil.emptyIfNull(param.getItems())
                            .stream()
                            .map(ManyPieceParamDTO.ItemsDTO::getWeight)
                            .anyMatch(StringUtils::isBlank)) {
                throw ResponseCode.PORTAL_9203.getError();
            }
            // 单箱尺寸
            if (Objects.equals(dto.getBoxSize(), check)) {
                if (CollectionUtil.emptyIfNull(param.getItems())
                        .stream()
                        .map(ManyPieceParamDTO.ItemsDTO::getWidth)
                        .anyMatch(StringUtils::isBlank)) {
                    throw ResponseCode.PORTAL_9205.getError();
                }
                if (CollectionUtil.emptyIfNull(param.getItems())
                        .stream()
                        .map(ManyPieceParamDTO.ItemsDTO::getHigh)
                        .anyMatch(StringUtils::isBlank)) {
                    throw ResponseCode.PORTAL_9206.getError();
                }
                if (CollectionUtil.emptyIfNull(param.getItems())
                        .stream()
                        .map(ManyPieceParamDTO.ItemsDTO::getLength)
                        .anyMatch(StringUtils::isBlank)) {
                    throw ResponseCode.PORTAL_9204.getError();
                }
            }
        }
    }

}
