package com.ywwl.customer.center.api.open;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.international.dto.CalcPriceParamDTO;
import com.ywwl.customer.center.modules.international.service.CalcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/wechatMini")
public class WechatMiniApiController {

    @Resource
    private CalcService service;

    /**
     * @author: dinghy
     * @createTime: 2024/11/15 16:20
     * @description: 给微信小程序提供运价试算接口
     */
    @Logger(module = Module.EJF, name = "微信小程序运价试算")
    @PostMapping("getCalculateInfo")
    public JsonResult<?> getCalculateInfo(@RequestBody CalcPriceParamDTO calcPriceParam) {
        JsonResult<?> result;
        try {
            if ("0".equals(calcPriceParam.getWeight())) {
               return JsonResult.error("重量不能为空");
            }
            JSONObject json = service.calcPrice(calcPriceParam);
            if (json != null) {
                result = JsonResult.success(json);
            } else {
                result = JsonResult.error();
            }
        } catch (Exception e) {
           log.error("微信小程序调用运价试算异常:{}",e);
            return JsonResult.error("试算失败");
        }
         return result;
    }

}
