package com.ywwl.customer.center.api.system;

/**
 * @author: dinghy
 * @date: 2023/3/31 15:49
 */

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ywwl.customer.center.common.constant.SysUserConstants;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.system.constant.SysConstants;
import com.ywwl.customer.center.system.service.BaseIdStore;
import com.ywwl.customer.center.system.service.RegisterService;
import com.ywwl.customer.center.system.service.ResetPasswordService;
import com.ywwl.customer.center.system.service.UserService;
import com.ywwl.customer.center.system.utils.SessionCacheUtil;
import com.ywwl.customer.center.system.vo.RetrievePasswordVo;
import com.ywwl.customer.center.system.vo.SendVerifyCodeVo;
import com.ywwl.customer.center.system.vo.SetPassWordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RequestMapping("forget")
@Controller
public class ForgetController {
    @Resource
    private RegisterService registerService;
    @Resource
    private UserService userService;
    @Resource
    private SessionCacheUtil sessionCacheUtil;
    @Resource
    private ResetPasswordService resetPasswordService;

    /***
     * //  发送邮件和手机号
     * <AUTHOR>
     * @date 2023/2/23 16:50
     * @param sendVerifyCodeVo 手机号参数
     * @param req 响应头
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @PostMapping("sendVerifyCode")
    @ResponseBody
    public JsonResult<Object> sendVerifyCode(@RequestBody SendVerifyCodeVo sendVerifyCodeVo, HttpServletRequest req) {
        if (sendVerifyCodeVo.getRetrieveType() == 1) {
            if(StringUtils.isBlank(sendVerifyCodeVo.getPointX())){
                return JsonResult.error(ResponseCode.PORTAL_5001);
            }
            String id = req.getSession().getId();
            JsonResult<Object> jsonResult = registerService.validateSliderCaptcha(sendVerifyCodeVo.getPointX(), id);
            if (!jsonResult.getSuccess()) {
                return JsonResult.error(ResponseCode.PORTAL_5001);
            }
            Object phone = sessionCacheUtil.getSessionValue(req.getSession().getId(), BaseIdStore.FORGET_PASSWORD_USER_PHONE);
            if (phone == null) {
                return JsonResult.error(SysUserConstants.PAGE_AUTH_ERROR);
            }
            sendVerifyCodeVo.setPhone(phone.toString());
        } else {
            String id = req.getSession().getId();
            JsonResult<Object> jsonResult = registerService.validateSliderCaptcha(sendVerifyCodeVo.getPointX(), id);
            if (!jsonResult.getSuccess()) {
                return JsonResult.error(ResponseCode.PORTAL_5001);
            }
        }
        sendVerifyCodeVo.setPcId(IdWorker.get32UUID());
        JsonResult<Object> result = userService.validateSendVerifyCodeDTO(sendVerifyCodeVo);
        if (!result.getSuccess()) {
            return result;
        }

        log.debug("请求找回密码. {}", sendVerifyCodeVo);
        if (sendVerifyCodeVo.getRetrieveType() == 1) {
            result = resetPasswordService.sendResetPasswordSms(sendVerifyCodeVo.getPhone(), req);
        } else {
            result = resetPasswordService.verifyCaptchaAndSendRestPasswordEmail(sendVerifyCodeVo.getLoginName(),
                    sendVerifyCodeVo.getCaptchaCode(), sendVerifyCodeVo.getPcId(), req);
        }
        return result;
    }


    /***
     * //  校验手机短信
     * <AUTHOR>
     * @date 2023/2/23 17:43
     * @param retrievePasswordVo 参数
     * @param req 相应头
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @PostMapping("verifySMSCode")
    @ResponseBody
    public JsonResult<Object> verifySmsCode(@RequestBody RetrievePasswordVo retrievePasswordVo, HttpServletRequest req) {
        String smmId = sessionCacheUtil.getSessionValue(req.getSession().getId(), BaseIdStore.FORGET_PASSWORD_SMS_CODE_KEY);
        if (StringUtils.isBlank(smmId)) {
            return JsonResult.error(ResponseCode.PORTAL_5007);
        }
        if (StringUtils.isBlank(retrievePasswordVo.getLoginName())
                || Objects.isNull(retrievePasswordVo.getRetrieveType())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        retrievePasswordVo.setSmsId(smmId.toString());
        log.debug("验证手机短信验证码. {}", retrievePasswordVo);
        return resetPasswordService.verifyCode(retrievePasswordVo.getLoginName(),
                retrievePasswordVo.getSmsId(), retrievePasswordVo.getCheckCode());
    }


    /***
     * //  忘记密码
     * <AUTHOR>
     * @date 2023/2/23 16:22
     * @param req  响应
     * @param retrievePasswordVo  密码
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @PostMapping("getPhone")
    @ResponseBody
    public JsonResult<Object> getUserPhone(@RequestBody RetrievePasswordVo retrievePasswordVo, HttpServletRequest req) {
        if (StringUtils.isBlank(retrievePasswordVo.getLoginName())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        log.info("用户通过手机号找回密码,用户名为:{}", retrievePasswordVo.getLoginName());
        String id = req.getSession().getId();
        JsonResult<Object> jsonResult = registerService.validateSliderCaptcha(retrievePasswordVo.getPointX(), id);
        if (!jsonResult.getSuccess()) {
            return JsonResult.error(ResponseCode.PORTAL_5001);
        }
        if (StringUtils.isBlank(retrievePasswordVo.getLoginName())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        JSONObject json = registerService.getForgetPhone(retrievePasswordVo.getLoginName());
        String result = json.getString("result");
        boolean parseBoolean = Boolean.parseBoolean(result);
        if (!parseBoolean) {
            return JsonResult.error(json.getString("message"));
        }
        String mobile = json.getString("mobile");
        if (StringUtils.isBlank(mobile)) {
            return JsonResult.error(ResponseCode.PORTAL_5013);
        }
        //发送短信
        sessionCacheUtil.setSessionValue(req.getSession().getId(), BaseIdStore.FORGET_PASSWORD_USER_PHONE, mobile);
        return JsonResult.success(ResponseCode.PORTAL_200, PrivacyDimmer.maskMobile(mobile));
    }


    /***
     * //  重置密码
     * <AUTHOR>
     * @date 2023/2/24 9:32
     * @param setPassWordVo 密码
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.USER,name = "重置密码", recordRsp = false, recordReq = false)
    @ResponseBody
    @PostMapping("setPassword")
    public JsonResult<Object> setPassword(@Valid @RequestBody SetPassWordVo setPassWordVo) {
        try {
            return resetPasswordService.setPassword(setPassWordVo.getRand(), setPassWordVo.getPassword());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("重置密码异常：{}", e.getMessage());
            return JsonResult.error(SysConstants.SERVICE_OPERATION_ERROR);
        }
    }


    /***
     * //  邮箱找回密码判断baseId是否过期
     * <AUTHOR>
     * @date 2023/2/27 17:16
     * @param baseId id
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER,name = "通过邮箱重置密码", recordRsp = false)
    @ResponseBody
    @GetMapping("/email/resetPassWord")
    public JsonResult<Object> verifyEmail(@RequestParam("baseid") String baseId) {
        String setPasswordBaseId = resetPasswordService.verifyEmail(baseId);
        if (StringUtils.isBlank(setPasswordBaseId)) {
            return JsonResult.error(ResponseCode.PORTAL_5014);
        }
        String loginName = resetPasswordService.verifyAndReturnLoginName(setPasswordBaseId);
        if (StringUtils.isBlank(loginName)) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        Map<String, Object> map = new HashMap<>(4);
        map.put("loginName", loginName);
        map.put("rand", setPasswordBaseId);
        return JsonResult.success(SysUserConstants.SUCCEED_STATUS, map);
    }


}
