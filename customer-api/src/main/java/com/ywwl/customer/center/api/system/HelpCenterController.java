package com.ywwl.customer.center.api.system;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@RestController
@RequestMapping("/help")
public class HelpCenterController extends BaseController {

    @Value("${help-center.url}")
    public String url;

    @RequestMapping("/url")
    public JsonResult<?> getUrl(HttpSession session, HttpServletRequest request) throws UnsupportedEncodingException {
        UserAgent userAgent = getUser();
        // 获取所有请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();
        StringBuilder queryParams = new StringBuilder();

        // 遍历参数并拼接
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            String[] values = entry.getValue();

            // 如果参数是数组（如 ?a=1&a=2），则拼接所有值
            for (String value : values) {
                if (queryParams.length() > 0) {
                    queryParams.append("&");
                }
                queryParams.append(key).append("=").append(URLEncoder.encode(value, StandardCharsets.UTF_8.toString()));
            }
        }

        // 拼接 sessionId 和 loginName
        String finalUrl = url + "?sessionId=" + session.getId() + "&name=" + userAgent.getLoginName();

        // 如果有额外参数，则拼接
        if (queryParams.length() > 0) {
            finalUrl += "&" + queryParams;
        }

        return JsonResult.success("success", finalUrl);
    }

}
