package com.ywwl.customer.center.api.open;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.system.service.WechatService;
import com.ywwl.customer.center.system.vo.WechatScanVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: dinghy
 * @date: 2023/4/12 15:35
 */
@Slf4j
@RestController
@RequestMapping("api/wechat")
public class WechatApiController {
    @Resource
    private WechatService wechatService;

    /**
     * <AUTHOR>
     * @description 获取扫描二维码
     * @date 2023/4/12 15:47
     **/
    @Logger(module = Module.USER,name = "获取微信二维码")
    @GetMapping("getScanCode")
    public JsonResult<WechatScanVo> getScanCode() {
        WechatScanVo scanVo = wechatService.getLoginScan();
        return JsonResult.success(scanVo);
    }

    /**
     * <AUTHOR>
     * @description 扫码回调接口
     * @date 2023/4/13 10:22
     **/
    @RequestMapping("scanCallback")
    public JsonResult<Object> scanCallback(@JsonParam String openId, @JsonParam String scanKey) {
        if (StringUtils.isBlank(scanKey) || StringUtils.isBlank(openId)) {
            return JsonResult.error("参数缺失");
        }
        // 判断是否key存在
        Boolean aBoolean = CacheUtil.redis().hasKey(CacheKeyEnum.WECHAT_SCAN,scanKey);
        if(Boolean.FALSE.equals(aBoolean)){
            return JsonResult.error("scanKey已经过时");
        }
        CacheUtil.redis().setValue(CacheKeyEnum.WECHAT_SCAN, scanKey, openId);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 查询扫码结果
     * @date 2023/4/14 10:11
     **/
    @PostMapping("getScanResult")
    public JsonResult<Integer> getScanResult(@JsonParam String scanKey) {
        Boolean aBoolean = CacheUtil.redis().hasKey(CacheKeyEnum.WECHAT_SCAN, scanKey);
        // 如果扫码的key不存在,则说明二维码已经过期了
        if (!aBoolean) {
            return JsonResult.success(-1);
        }
        // -1是二维码过期,0是未扫码,1是已经绑定,2是未绑定商户
        int data = 0;
        String value = CacheUtil.redis().getValue(CacheKeyEnum.WECHAT_SCAN, scanKey);
        // 如果缓存有值,则说明客户已经扫码完成了,根据openId查询是否已经绑定微信
        if (StringUtils.isNotBlank(value)) {
            try {
                Boolean res = wechatService.checkOpenIdBind(value);
                if (res) {
                    data = 1;
                } else {
                    data = 2;
                }
            } catch (Exception e) {
                log.error("查询微信是否绑定修改异常,原因:{}", e);
            }
        }
        return JsonResult.success(data);
    }
}
