package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.dto.SelfSendForecastApplyDTO;
import com.ywwl.customer.center.modules.international.dto.SelfSendForecastCancelDTO;
import com.ywwl.customer.center.modules.international.dto.SelfSendForecastQueryDTO;
import com.ywwl.customer.center.modules.international.dto.SelfSendWaybillNumberDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.SelfSendForecastService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.international.vo.SelfSendForecastListVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @createTime: 2024/8/27 17:20
 * @description: 自寄自送预报
 */
@RequestMapping("selfSendForecast")
@RestController
public class SelfSendForecastController extends BaseController {
    @Resource
    private SelfSendForecastService selfSendForecastService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private AccountService accountService;

    /**
     * @author: dinghy
     * @createTime: 2024/8/27 17:22
     * @description: 查询自寄自送列表
     */
    @RequestMapping("selectList")
    public JsonResult selectList(@Valid @RequestBody SelfSendForecastQueryDTO selfSendForecastQueryDTO){
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        selfSendForecastQueryDTO.setMerchantCode(packetMerchantCode);
        selfSendForecastQueryDTO.setBale(1);
        SelfSendForecastListVo selfSendForecastListVo = selfSendForecastService.selectList(selfSendForecastQueryDTO);
        return JsonResult.success(selfSendForecastListVo);
    }

    /**
     * @author: dinghy 
     * @createTime: 2024/8/29 16:46
     * @description: 查询详情
     */
    @PostMapping("selectDetail")
    public JsonResult selectDetail(@RequestBody SelfSendForecastQueryDTO selfSendForecastQueryDTO){
        String packageNumber = selfSendForecastQueryDTO.getPackageNumber();
        selfSendForecastQueryDTO.setWaybillNumber(null);
        selfSendForecastQueryDTO.setOrderTimeEnd(null);
        selfSendForecastQueryDTO.setOrderTimeStart(null);
        selfSendForecastQueryDTO.setPredictionTimeEnd(null);
        selfSendForecastQueryDTO.setPredictionTimeStart(null);
        selfSendForecastQueryDTO.setCustomerCode(null);
        selfSendForecastQueryDTO.setOrderTimeStart(null);
        selfSendForecastQueryDTO.setPageIndex(1);
        selfSendForecastQueryDTO.setPageSize(10000);
        if(StringUtils.isBlank(packageNumber)){
            return JsonResult.error("打包号/快递单号不能为空");
        }
        selfSendForecastQueryDTO.setBale(0);
        SelfSendForecastListVo selfSendForecastListVo = selfSendForecastService.selectList(selfSendForecastQueryDTO);
        return JsonResult.success(selfSendForecastListVo);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/8/27 17:24
     * @description: 预报添加
     */
    @Logger(module = Module.SELF_SEND_FORECAST, name = "添加自寄自送预报")
    @RequestMapping("addForecast")
    public JsonResult forecast(@Valid @RequestBody SelfSendForecastApplyDTO selfSendForecastApplyDTO){
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        selfSendForecastApplyDTO.setPredictionName(getUser().getLoginName());
        selfSendForecastApplyDTO.setMerchantCode(packetMerchantCode);
        JsonResult jsonResult=selfSendForecastService.forecast(selfSendForecastApplyDTO);
        return jsonResult;
    }

    /**
     * @author: dinghy
     * @createTime: 2024/8/27 17:25
     * @description: 取消预报
     */
    @Logger(module = Module.SELF_SEND_FORECAST, name = "运单维度取消自寄自送预报")
    @RequestMapping("cancelForecast")
    public JsonResult cancelForecast(@RequestBody SelfSendForecastCancelDTO selfSendForecastCancelDTO){
        selfSendForecastService.cancelForecast(selfSendForecastCancelDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/12 10:33
     * @description: 直接取消大包预报
     */
    @Logger(module = Module.SELF_SEND_FORECAST, name = "大包维度取消自寄自送预报")
    @RequestMapping("cancelPackageForecast")
    public JsonResult cancelPackageForecast(@JsonParam String packageNumber){
        SelfSendForecastQueryDTO selfSendForecastQueryDTO=new SelfSendForecastQueryDTO();
        selfSendForecastQueryDTO.setState(2);
        selfSendForecastQueryDTO.setPackageNumber(packageNumber);
        selfSendForecastQueryDTO.setPageSize(10000);
        selfSendForecastQueryDTO.setPageIndex(1);
        String merchantCode=packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        selfSendForecastQueryDTO.setMerchantCode(merchantCode);
        selfSendForecastService.cancelPackageForecast(selfSendForecastQueryDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/4 17:57
     * @description: 导入
     */
    @Logger(module = Module.SELF_SEND_FORECAST, name = "导入自寄自送预报运单")
    @RequestMapping("importData")
    public JsonResult importData(MultipartFile attach,String customerCode){
        if(attach==null||StringUtils.isBlank(customerCode)){
            return JsonResult.error("参数缺失");
        }
        List shippingAccounts=Arrays.asList(customerCode);
        if (!accountService.existAccount(shippingAccounts, getUser(), 0)) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        List<String> list=new LinkedList<>();
        try {
            EasyExcel.read(attach.getInputStream(), new AnalysisEventListener<Map>(){
                @Override
                public void invoke(Map strings, AnalysisContext analysisContext) {
                    String string = String.valueOf(strings.get(0));
                    if(StringUtils.isNotBlank(string)){
                        list.add(string.trim());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {

                }
            }).doReadAllSync();
        } catch (Exception e) {
           return JsonResult.error("文件解析失败");
        }
        if(list.isEmpty()){
            return JsonResult.error("导入数据为空");
        }
        List<String> errorList=selfSendForecastService.validateWaybillNumber(list,customerCode);
        if(errorList.isEmpty()){
            return JsonResult.success(list);
        }
        return JsonResult.error("制单账号与运单未匹配数据："+list);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/11 14:46
     * @description: 生成交接单
     */
    @Logger(module = Module.SELF_SEND_FORECAST, name = "生成自寄自送预报运单交接单")
    @RequestMapping("generateSelfSendHandoverSlip")
    public void generateSelfSendHandoverSlip(@Valid @NotBlank(message = "大包号不能为空") @JsonParam String packageNumber, HttpServletResponse response) throws IOException {
        HttpUtil.setDownloadHeader(response, "自寄自送交接单"+ LocalDate.now()+".xlsx", HttpUtil.DOWNLOAD, "UTF-8" , null);
        SelfSendForecastQueryDTO selfSendForecastQueryDTO=new SelfSendForecastQueryDTO();
        selfSendForecastQueryDTO.setState(2);
        selfSendForecastQueryDTO.setPackageNumber(packageNumber);
        selfSendForecastQueryDTO.setPageSize(10000);
        selfSendForecastQueryDTO.setPageIndex(1);
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(getUserCode());
        if(packetApplyInfo==null){
            throw new BusinessException(ResponseCode.PORTAL_415);
        }
        selfSendForecastQueryDTO.setMerchantCode(selfSendForecastQueryDTO.getMerchantCode());
        selfSendForecastService.generateHandoverSlip(selfSendForecastQueryDTO,packetApplyInfo.getMerchantName(),response.getOutputStream());
    }



    /**
     * @author: dinghy
     * @createTime: 2024/9/12 11:32
     * @description: 导出运单号
     */
    @RequestMapping("exportSelfSendWaybillNumber")
    public void exportSelfSendWaybillNumber(@JsonParam String packageNumber,HttpServletResponse response) throws IOException {
        SelfSendForecastQueryDTO selfSendForecastQueryDTO=new SelfSendForecastQueryDTO();
        selfSendForecastQueryDTO.setPackageNumber(packageNumber);
        selfSendForecastQueryDTO.setPageSize(10000);
        selfSendForecastQueryDTO.setPageIndex(1);
        String merchantCode=packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        selfSendForecastQueryDTO.setMerchantCode(merchantCode);
        SelfSendForecastListVo selfSendForecastListVo = selfSendForecastService.selectList(selfSendForecastQueryDTO);
        List<SelfSendForecastListVo.SelfDetailDTO> list = selfSendForecastListVo.getList();
        if(list==null){
            list=new ArrayList<>();
        }
        List<List<String>> collect = list.stream().map(
                x-> Arrays.asList(x.getWaybillNumber())
        ).collect(Collectors.toList());

        HttpUtil.setDownloadHeader(response, "自寄自送预报导出运单"+ LocalDate.now()+".xlsx", HttpUtil.DOWNLOAD, "UTF-8" , null);
        List<List<String>> header = new ArrayList<>();
        List<String> headList=new ArrayList<>();
        headList.add("运单号");
        header.add(headList);
        EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))
                .sheet("Sheet1")
                .head(header)
                .doWrite(collect);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/10/21 17:01
     * @description: 校验运单是否已经在工单系统
     */
    @RequestMapping("validateSelfSendWaybillNumber")
    public JsonResult validateSelfSendWaybillNumber(@RequestBody List<SelfSendWaybillNumberDTO> list){
        if(list==null||list.isEmpty()){
            return JsonResult.success();
        }
        SelfSendForecastQueryDTO selfSendForecastQueryDTO=new SelfSendForecastQueryDTO();
        String merchantCode=packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        selfSendForecastQueryDTO.setMerchantCode(merchantCode);
        StringBuilder stringBuilder=new StringBuilder();
        list.forEach(x-> stringBuilder.append(x.getWaybillNumber()).append(","));
        selfSendForecastQueryDTO.setWaybillNumber(stringBuilder.toString());
        selfSendForecastQueryDTO.setPageSize(list.size());
        SelfSendForecastListVo selfSendForecastListVo = selfSendForecastService.selectList(selfSendForecastQueryDTO);
        List<SelfSendForecastListVo.SelfDetailDTO> selectList = selfSendForecastListVo.getList();
        Map<String, Boolean> waybillMap = new HashMap<>();
        selectList.forEach(x-> waybillMap.put(x.getWaybillNumber(),false));
        if(!waybillMap.isEmpty()){
            list.forEach(x->{
                Boolean aBoolean = waybillMap.get(x.getWaybillNumber());
                if(aBoolean!=null){
                    x.setSelectState(false);
                }
            });
        }

        return JsonResult.success(list);
    }
}
