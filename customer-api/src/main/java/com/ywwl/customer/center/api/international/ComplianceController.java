package com.ywwl.customer.center.api.international;


import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.international.dto.*;
import com.ywwl.customer.center.modules.international.service.ComplianceService;
import com.ywwl.customer.center.modules.international.vo.CurrencyVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * // 9610合规化
 *
 * <AUTHOR>
 * @date 2022/11/7
 */
@Slf4j
@RestController
@RequestMapping("/compliance")
public class ComplianceController extends BaseController {
    @Resource
    private ComplianceService complianceService;
    @Resource
    private CmccService cmccService;

    /***
     * //  9610商户信息申请
     * <AUTHOR>
     * @date 2022/9/21 16:20
     * @param merchantApplyDTO 商户信息
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/merchant/apply")
    @Logger(module = Module.SMALL_PACKAGE, name = "9610商户信息申请")
    public JsonResult<Object> complianceMerchantSave(@RequestBody MerchantApplyDTO merchantApplyDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.complianceMerchantSave(merchantApplyDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610商户信息申请提交异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /**
     * // 申请信息查询
     *
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2022/9/21 16:20
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "9610申请信息查询")
    @GetMapping("/merchant/applyPage")
    public JsonResult<Object> applyPage(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.applyPage(currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610申请信息查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  商户申请编辑
     * <AUTHOR>
     * @date 2022/9/28 14:54
     * @param merchantApplyDTO 商户申请编辑参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610商户申请重新提交")
    @PostMapping("/merchant/applyUpdate")
    public JsonResult<Object> applyUpdate(@RequestBody MerchantApplyDTO merchantApplyDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.applyUpdate(merchantApplyDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610申请信息查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  9610商品信息列表查询
     * <AUTHOR>
     * @date 2022/9/22 16:18
     * @param commodityRecordDTO 列表查询
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610列表商品查询")
    @PostMapping("/commodityRecord/page")
    public JsonResult<Object> commodityRecord(@RequestBody CommodityRecordDTO commodityRecordDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            if(commodityRecordDTO.getSkuId()!=null){
                commodityRecordDTO.setSkuId(commodityRecordDTO.getSkuId().stream().map(String::trim).collect(Collectors.toList()));
            }
            return complianceService.commodityRecord(commodityRecordDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610商品信息列表查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610列表导入商品列表查询")
    @PostMapping("/import/list")
    public JsonResult<Object> importList(@RequestBody CommodityImportListDTO commodityImportListDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.commodityImportList(commodityImportListDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610列表导入商品列表查询询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  商品备案信息新增
     * <AUTHOR>
     * @date 2022/9/23 10:28
     * @param saveDTO 备案信息新增
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610商品备案新增")
    @PostMapping("/commodity/save")
    public JsonResult<Object> commoditySave(@RequestBody CommoditySaveDTO saveDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.commoditySave(saveDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610商品备案信息新增异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  商品备案信息编辑提交
     * <AUTHOR>
     * @date 2022/9/23 10:52
     * @param saveDTO 商品备案信息编辑
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610商品备案信息编辑")
    @PostMapping("/commodity/update")
    public JsonResult<Object> commodityUpdate(@RequestBody CommoditySaveDTO saveDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.commodityUpdate(saveDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("9610商品备案信息编辑提交异常:{}", e.getMessage());
            return JsonResult.error();
        }
    }

    /***
     * //  9610文件模板
     * <AUTHOR>
     * @date 2022/9/23 14:59
     * @param response 文件模板
     * @return com.alibaba.fastjson2.JSONObject
     */
    @SneakyThrows
    @GetMapping("/templateDownload")
    public JsonResult<Object> templateDownload(HttpServletResponse response) {
        try {
            return complianceService.templateDownload(response);
        } catch (Exception e) {
            log.error("9610商品备案信息模板下载异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }

    }

    /**
     * //  报关单列表查询
     *
     * @param fileListDTO 报关单列表参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     * <AUTHOR>
     * @date 2022/9/23 16:45
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610报关单查询")
    @PostMapping("/fileList")
    public JsonResult<Object> fileList(@RequestBody FileListDTO fileListDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.fileList(fileListDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610报关单列表查询异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  查询商品备案信息列表对应数量
     * <AUTHOR>
     * @date 2022/9/26 10:24
     * @param commodityRecordDTO 列表对应数量
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610商品备案数量")
    @PostMapping("/commodity/num")
    public JsonResult<Object> nums(@RequestBody CommodityRecordDTO commodityRecordDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            if(commodityRecordDTO.getSkuId()!=null){
                commodityRecordDTO.setSkuId(commodityRecordDTO.getSkuId().stream().map(String::trim).collect(Collectors.toList()));
            }
            return complianceService.nums(commodityRecordDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610商品备案信息列表对应数量异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  币种列表
     * <AUTHOR>
     * @date 2022/9/26 13:59
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "9610查询币种")
    @GetMapping("/getCurrency")
    public JsonResult<Object> getCurrencyList() {
        try {
            List<CurrencyVo> currencyS = cmccService.getCurrencyList();
            if (null == currencyS) {
                return JsonResult.error("查询币种异常!", null);
            }
            return JsonResult.success(currencyS);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage(), null);

        } catch (Exception e) {
            log.error("查询中数据中心币种异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  批量删除已拒绝的数据
     * <AUTHOR>
     * @date 2022/9/26 14:19
     * @param batchDeleteDTO 批量删除参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610批量删除已拒绝")
    @PostMapping("/batchDelete")
    public JsonResult<Object> batchDelete(@RequestBody BatchDeleteDTO batchDeleteDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return complianceService.batchDelete(batchDeleteDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610批量删除已拒绝的数据异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }


    /***
     * //  批量编辑
     * <AUTHOR>
     * @date 2022/9/26 15:42
     * @param commodityBatchSaveDTO 批量编辑参数
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @Logger(module = Module.SMALL_PACKAGE, name = "9610批量编辑")
    @PostMapping("/commodity/batchUpdate")
    public JsonResult<Object> commodityBatchUpdate(@RequestBody @Valid  CommodityBatchSaveDTO commodityBatchSaveDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {

        try {
            return complianceService.commodityBatchUpdate(commodityBatchSaveDTO, currentUser);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610备案信息批量编辑异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }

    /***
     * //  文件上传
     * <AUTHOR>
     * @date 2022/6/29 13:49
     * @param file 文件
     * @param currentUser 登录信息
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "9610文件上传")
    @SneakyThrows
    @RequestMapping("/uploading")
    public JsonResult<Object> upload(MultipartFile file, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        return complianceService.commodityBatchSave(file, currentUser);
    }


    /***
     * //  批量报关单下载
     * <AUTHOR>
     * @date 2022/10/9 10:19
     * @param customsIdsDTO 报关单
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */

    @PostMapping("/batchCustom/download")
    public JsonResult<Object> batchCustomsDownload(@RequestBody CustomsIdsDTO customsIdsDTO) {
        try {
            return complianceService.batchCustomsDownload(customsIdsDTO);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage(), null);
        } catch (Exception e) {
            log.error("9610批量报关单下载异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_500);
        }
    }
}
