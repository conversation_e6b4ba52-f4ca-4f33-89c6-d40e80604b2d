package com.ywwl.customer.center.api.international;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.provider.constant.ShareConstant;
import com.ywwl.customer.center.modules.common.provider.domain.ClientReadRecord;
import com.ywwl.customer.center.modules.common.provider.service.ClientReadRecordService;
import com.ywwl.customer.center.modules.common.provider.vo.MessageProductVo;
import com.ywwl.customer.center.modules.international.dto.TaxCodeConfigAddDTO;
import com.ywwl.customer.center.modules.international.dto.TaxCodeConfigLogDTO;
import com.ywwl.customer.center.modules.international.dto.TaxCodeConfigQueryDTO;
import com.ywwl.customer.center.modules.international.dto.TaxCodeConfigUpdateDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.TaxCodeConfigService;
import com.ywwl.customer.center.modules.international.vo.TaxCodeCodeVo;
import com.ywwl.customer.center.modules.international.vo.TaxCodeConfigLogVo;
import com.ywwl.customer.center.system.service.SmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * @author: dinghy
 * @createTime: 2024/4/15 16:32
 * @description: 客户税号配置
 */
@RequestMapping("TaxCodeConfig")
@RestController
public class TaxCodeConfigController extends BaseController {
    @Resource
    private TaxCodeConfigService taxCodeConfigService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private SmsService smsService;
    @Resource
    private ClientReadRecordService clientReadRecordService;

    /**
     * @author: dinghy
     * @createTime: 2024/4/15 16:36
     * @description: 添加税号配置
     */
    @Logger(module = Module.TAX_CONFIG, name = "添加税号配置")
    @RequestMapping("addTaxCodeConfig")
    public JsonResult addTaxCodeConfig(@Valid @RequestBody TaxCodeConfigAddDTO taxCodeConfigAddDTO) {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        if (StringUtils.isBlank(taxCodeConfigAddDTO.getApplyReason())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        taxCodeConfigAddDTO.setOperator(user.getLoginName());
        taxCodeConfigAddDTO.setMerchantCode(packetBusinessApplyService.getPacketMerchantCode(user.getUserCode()));
        taxCodeConfigService.add(taxCodeConfigAddDTO);
        return JsonResult.success();
    }


    /**
     * @author: dinghy
     * @createTime: 2024/4/16 11:19
     * @description: 查询税号配置列表
     */
    @RequestMapping("listTaxCodeConfig")
    public JsonResult listTaxCodeConfig(@RequestBody TaxCodeConfigQueryDTO taxCodeConfigQueryDTO) {
        taxCodeConfigQueryDTO.setMerchantCode(packetBusinessApplyService.getPacketMerchantCode(getUserCode()));
        if (StringUtils.isNotBlank(taxCodeConfigQueryDTO.getProductCode())) {
            taxCodeConfigQueryDTO.setProductCodeList(Arrays.asList(taxCodeConfigQueryDTO.getProductCode().split(",")));
            taxCodeConfigQueryDTO.setProductCode(null);
        }
        TaxCodeCodeVo taxCodeCodeVo = taxCodeConfigService.list(taxCodeConfigQueryDTO);
        return JsonResult.success(taxCodeCodeVo);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/16 11:25
     * @description: 无效税号配置
     */
    @Logger(module = Module.TAX_CONFIG, name = "无效税号配置")
    @RequestMapping("dropTaxCodeConfig")
    public JsonResult dropTaxCodeConfig(@JsonParam List<String> ids, @JsonParam String code, HttpServletRequest request) {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        if (ids == null || ids.isEmpty()) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        smsService.checkSmsCode(request.getSession().getId(), user.getPhone(), code);
        taxCodeConfigService.dropTaxCodeConfig(ids, getUser().getLoginName());
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/26 10:01
     * @description: 查询产品
     */
    @RequestMapping("getProductList")
    public JsonResult getProductList() {
        List<MessageProductVo> list = taxCodeConfigService.getProductList();
        return JsonResult.success(list);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/26 14:40
     * @description: 判断是否要通知
     */
    @RequestMapping("showTaxConfigNotice")
    public JsonResult showTaxConfigNotice() {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error();
        }
        LambdaQueryWrapper<ClientReadRecord> recordLambdaQueryWrapper = Wrappers.<ClientReadRecord>lambdaQuery().eq(ClientReadRecord::getUserId, user.getUserId()).eq(ClientReadRecord::getType, ShareConstant.TAX_CONFIG);
        List<ClientReadRecord> list = clientReadRecordService.list(recordLambdaQueryWrapper);
        if (!list.isEmpty()) {
            return JsonResult.error();
        }
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(user.getUserCode());
        TaxCodeConfigQueryDTO taxCodeConfigQueryDTO = new TaxCodeConfigQueryDTO();
        taxCodeConfigQueryDTO.setMerchantCode(packetMerchantCode);
        TaxCodeCodeVo taxCodeCodeVo = taxCodeConfigService.list(taxCodeConfigQueryDTO);
        if (taxCodeCodeVo.getTotal() == 0) {
            return JsonResult.success();
        }
        return JsonResult.error();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/26 16:02
     * @description: 不再提示税号配置
     */
    @Idempotent
    @RequestMapping("notNotice")
    public JsonResult notNotice() {
        UserAgent user = getUser();
        ClientReadRecord clientReadRecord = ClientReadRecord.builder().userId(user.getUserId()).typeId("1").type(ShareConstant.TAX_CONFIG).build();
        clientReadRecordService.save(clientReadRecord);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/5/16 9:40
     * @description: 更新税号配置
     */
    @RequestMapping("updateTaxConfig")
    public JsonResult updateTaxConfig(@RequestBody TaxCodeConfigUpdateDTO taxCodeConfigAddDTO) {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        if (StringUtils.isBlank(taxCodeConfigAddDTO.getId())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        taxCodeConfigAddDTO.setOperator(user.getLoginName());
        taxCodeConfigAddDTO.setMerchantCode(packetBusinessApplyService.getPacketMerchantCode(user.getUserCode()));
        taxCodeConfigService.updateTaxConfig(taxCodeConfigAddDTO);
        return JsonResult.success();
    }
    
    /**
     * @author: dinghy 
     * @createTime: 2024/6/7 11:29
     * @description: 查询税号配置日志
     */
    @RequestMapping("logList")
    public JsonResult logList(@RequestBody TaxCodeConfigLogDTO taxCodeConfigLogDTO) {
        taxCodeConfigLogDTO.setMerchantCode(packetBusinessApplyService.getPacketMerchantCode(getUserCode()));
        TaxCodeConfigLogVo taxCodeConfigLogVo = taxCodeConfigService.logList(taxCodeConfigLogDTO);
        return JsonResult.success(taxCodeConfigLogVo);
    }


}
