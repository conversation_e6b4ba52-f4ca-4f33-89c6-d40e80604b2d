package com.ywwl.customer.center.api.ejf;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.ejf.dto.ChannelDTO;
import com.ywwl.customer.center.modules.ejf.service.BaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * EJF基础信息控制器
 * <AUTHOR>
 * @data	2022/1/27
 */
@RestController
@RequestMapping("/ejf/base")
public class BaseInfoController {

	/**
	 * 基础信息服务类
	 */
	BaseInfoService baseInfoService;

	@Autowired
	public BaseInfoController(BaseInfoService baseInfoService) {
		this.baseInfoService = baseInfoService;
	}

	/**
	 * 获取产品信息
	 *
	 * @param param 获取产品参数
	 * @return 基础信息
	 */
	@RequestMapping("/getChannel")
	@Valid
	public JsonResult<?> getChannel(@RequestBody ChannelDTO param) {
		return JsonResult.success(baseInfoService.getChannel(param));
	}

	/**
	 * 获取币种信息
	 *
	 * @return 基础信息
	 */
	@RequestMapping("/getCurrency")
	public JsonResult<?> getCurrency() {
		return JsonResult.success(baseInfoService.getCurrency());
	}

	/**
	 * 根据产品ID获取城市信息
	 * @param channelId	产品ID
	 * @return 基础信息
	 */
	@RequestMapping("/getCountry/{channelId}")
	public JsonResult<?> getCountry(@PathVariable("channelId") String channelId) {
		return JsonResult.success(baseInfoService.getCountry(channelId));
	}

	/**
	 * 获取城市信息
	 * @return 基础信息
	 */
	@RequestMapping("/getCountry")
	public JsonResult<?> getCountry() {
		return JsonResult.success(baseInfoService.getCacheCountry());
	}


}
