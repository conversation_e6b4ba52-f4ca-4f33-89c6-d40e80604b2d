package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.international.dto.ConditionQuotationPageDTO;
import com.ywwl.customer.center.modules.international.dto.QuotationApplyDTO;
import com.ywwl.customer.center.modules.international.dto.QuotationQueryDTO;
import com.ywwl.customer.center.modules.international.service.CalcService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.QuotationDownloadCountService;
import com.ywwl.customer.center.modules.international.service.QuotationService;
import com.ywwl.customer.center.modules.international.vo.PageQuotationVO;
import com.ywwl.customer.center.modules.international.vo.PlatformVo;
import com.ywwl.customer.center.modules.international.vo.QuotationQueryListVo;
import com.ywwl.customer.center.modules.international.vo.WarehouseOption;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: dinghy
 * @date: 2023/5/15 14:33
 */
@RequestMapping("quotation")
@RestController
public class QuotationController extends BaseController {
    @Resource
    private QuotationService quotationService;
    @Resource
    private QuotationDownloadCountService quotationDownloadCountService;
    @Resource
    private CalcService calcService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    /**
     * <AUTHOR>
     * @description 查询揽收仓
     * @date 2023/5/15 15:14
     **/
    @GetMapping("index")
    public JsonResult<Object> index(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        List<WarehouseOption> list = quotationService.listSortedOptions(currentUser);
        List<PlatformVo> platforms = quotationService.getPlatforms();
        Map<String,Object> res=new HashMap<>(2);
        res.put("platformList",platforms);
        res.put("warehouseOptions",list);
        return JsonResult.success(res);
    }

    /**
     * <AUTHOR>
     * @description 分页查询
     * @date 2023/5/15 15:55
     **/
    @PostMapping("/page")
    public JsonResult<Object> page(@RequestBody ConditionQuotationPageDTO condition, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser){
        condition.setUserId(currentUser.getUserId());
        PageQuotationVO pageQuotationVO=quotationService.page(condition);
        return JsonResult.success(pageQuotationVO);
    }

   /**
    * <AUTHOR>
    * @description 报价单下载次数增加
    * @date 2023/5/15 18:05
    **/
    @GetMapping("/downloadCount/increase")
    public void increase(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestParam String quotationId) {
        quotationDownloadCountService.increase(currentUser.getUserId(), quotationId);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/8/14 15:25
     * @description: 报价单申请
     */
    @PostMapping("applyQuotation")
    public JsonResult applyQuotation(@Valid @RequestBody QuotationApplyDTO quotationApplyDTO){
        quotationApplyDTO.setCreateBy(getUser().getLoginName());
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        quotationApplyDTO.setMerchantCode(merchantCode);
        calcService.applyQuotation(quotationApplyDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/8/14 15:30
     * @description: 查询报价单列表
     */
    @PostMapping("listQuotation")
    public JsonResult listQuotation(@Valid @RequestBody QuotationQueryDTO quotationQueryDTO){
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        quotationQueryDTO.setMerchantCode(merchantCode);
        QuotationQueryListVo quotationQueryListVo = quotationService.listQuotation(quotationQueryDTO);
        return JsonResult.success(quotationQueryListVo);
    }
}
