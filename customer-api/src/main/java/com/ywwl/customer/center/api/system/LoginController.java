package com.ywwl.customer.center.api.system;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessApplyStatusEnum;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.enums.LoginTypeEnum;
import com.ywwl.customer.center.common.enums.PageStateEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.common.utils.RedisTemplate;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.auth.enums.ApplyTypeEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAreaEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerTypeEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.CompanyAuthVo;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerVo;
import com.ywwl.customer.center.modules.common.auth.vo.PersonAuthVo;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.vo.ContractVo;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.overseas.service.OverseaService;
import com.ywwl.customer.center.system.enums.CustomerSyncStatusEnum;
import com.ywwl.customer.center.system.service.CustomerSyncService;
import com.ywwl.customer.center.system.shiro.RemoteAuthcToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.*;

/**
 * // 登录
 *
 * <AUTHOR>
 * @date 2023/2/22
 */
@Slf4j
@Controller
@Validated
public class LoginController extends BaseController {

    @Value("${oldPortal.url}")
    private String oldPortalUrl;
    @Resource
    private CustomerSyncService customerSyncService;
    @Value("${oldPortal.font}")
    private String oldPortalFrontUrl;
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private OverseaService overseaService;

    /**
     * 单点登录
     *
     * @return 老系统页面地址
     */
    @ResponseBody
    @RequestMapping("/singleSignOn")
    public JsonResult<Object> singleSignOn() {
        final UserAgent user = getUser();
        Integer newCustomerStatus = customerSyncService.getNewCustomerStatus(user.getUserCode(), user.getMerchantNo());
        if (CustomerSyncStatusEnum.CUSTOMER_APPLY.value().equals(newCustomerStatus)) {
            return JsonResult.error("当前处于实名认证流程中，不支持返回旧版，请完成实名认证后，再返回旧版");
        }
        // 构建参数
        Long userId = user.getUserId();
        String merchantNo = user.getMerchantNo();
        String accessToken = user.getAccessToken();
        final ImmutableMap<String, ? extends Serializable> param = ImmutableMap.of("userId", userId, "merchantNo", merchantNo, "accessToken", accessToken, "loginType", LoginTypeEnum.SINGLE_SIGN_ON.value());
        // 请求老系统
        final String url = StringUtils.join(oldPortalUrl, "/server/api/singleSignOn/set");
        final JsonResult<?> result = HttpUtil.doPost(url, param, JsonResult.class);
        // 返回结果
        if (result.getSuccess()) {
            logout();
            return JsonResult.success(StringUtils.join(oldPortalFrontUrl, "/user/login", "?key=", result.getData()));
        }
        return JsonResult.error(result.getMessage());
    }

    /**
     * 退出登录
     */
    private static void logout() {
        Subject currentUser = SecurityUtils.getSubject();
        currentUser.logout();
    }

    /**
     * 单点登录
     *
     * @return 老系统页面地址
     */
    @ResponseBody
    @RequestMapping("/api/singleSignOn/set")
    @Logger(module = Module.COMMON, name = "单点登录操作")
    public JsonResult<Object> singleSignOn(@RequestBody RemoteAuthcToken dto) {
        final RedisTemplate redis = CacheUtil.redis();
        final String key = IdWorker.get32UUID();
        redis.setValue(CacheKeyEnum.SINGLE_SIGN_ON, key, dto);
        return JsonResult.success(key);
    }

    @Logger(module = Module.USER, name = "客户登录操作")
    @PostMapping("/login")
    @ResponseBody
    public JsonResult<Object> login(HttpServletRequest req) {
        Map<String, Object> resultMap = new HashMap<>(4);
        //获取filter认证失败信息
        String errorMessage = (String) req.getAttribute("shiroLoginFailure");
        String pwComplexMessage = (String) req.getAttribute("pwComplexMessage");
        String codeErrorMessage = (String) req.getAttribute("codeErrorMessage");
        String authDiffMessage = (String) req.getAttribute("authDiffMessage");
        if (StringUtils.isNotBlank(errorMessage)) {
            return JsonResult.error(errorMessage);
//            return JsonResult.error(ResponseCode.PORTAL_5012);
        } else if (StringUtils.isNotBlank(pwComplexMessage)) {
            return JsonResult.error(errorMessage, null, "mp");
        } else if (StringUtils.isNotBlank(codeErrorMessage)) {
            return JsonResult.error(ResponseCode.PORTAL_5001);
        } else if (StringUtils.isNotBlank(authDiffMessage)) {
            return JsonResult.error(errorMessage, authDiffMessage, "authDiff");
        } else {
            //清除上一个存在HttpServletRequest  中的请求session
            Subject subject = SecurityUtils.getSubject();
            Object principal = subject.getPrincipal();
            UserAgent user = JsonUtils.parse((String) principal, UserAgent.class);
            user.setAccessToken(UUID.randomUUID().toString());
            //初始化用户密码
            if (!user.isMobileVerify()) {
                resultMap.put("user", user);
                resultMap.put("pageState", PageStateEnum.INIT_PASSWORD.getPageState());
            } else {
//                //获取商户的状态
                resultMap.put("pageState", PageStateEnum.NORMAL_MERCHANT.getPageState());
            }
            resultMap.put("user", user);
            log.info("登录成功！" + user.getAccessToken() + user.getUserId());
            return JsonResult.success(resultMap);
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2024/2/20 14:27
     * @description: 校验是否为海外派商户
     */
    public boolean checkOversea(String userCode){
        return overseaService.checkOverseaMerchant(userCode);
    }

    public void setPageState(String userCode,Map<String, Object> resultMap){
        // 海外派
        if(checkOversea(userCode)){
            resultMap.put("pageState", PageStateEnum.OVERSEA_MERCHANT.getPageState());
            return;
        }
        Integer packetStatus = packetBusinessApplyService.getPacketStatus(userCode);
        if (BusinessApplyStatusEnum.SIGNED.getValue().equals(packetStatus)) {
            List<ContractVo> contractList = commonCrmService.getContractList(userCode);
            if(checkPaperContract(contractList)){
                return;
            }
            JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
            CustomerVo customer = data.getObject("customer", CustomerVo.class);
            if (customer != null && ApplyTypeEnum.APPLY_SUCCESS.getValue().equals(customer.getApplyType())) {
                Integer customerArea = customer.getCustomerArea();
                Integer customerType = customer.getCustomerType();
                if (CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea)) {
                    // 大陆个人
                    if(CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)){
                        PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                        if(personAuthVo==null){
                            resultMap.put("pageState", PageStateEnum.NOT_ESIGN.getPageState());
                            return;
                        }
                    }else{
                        // 大陆企业
                        CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
                        if(companyAuthVo==null){
                            resultMap.put("pageState", PageStateEnum.NOT_ESIGN.getPageState());
                            return;
                        }
                    }
                    if(!checkContract(contractList)){
                        resultMap.put("pageState", PageStateEnum.NOT_SIGN_CONTRACT.getPageState());
                    }
                }
            }
        }
    }

   public boolean checkPaperContract(List<ContractVo> contractList){
       try {
           if (!contractList.isEmpty()) {
               Optional<ContractVo> any = contractList.stream().filter(x -> {
                   String attach = x.getAttach();
                   Integer accountType = x.getAccountType();
                   Integer contractType = x.getContractType();
                   if (0 == accountType && 0 == contractType && StringUtils.isNotBlank(attach) && attach.contains("yanwentech.com")) {
                       return true;
                   }
                   return false;
               }).findAny();
               return any.isPresent();
           }
       } catch (Exception e) {

       }
       return true;
   }

    public boolean checkContract(List<ContractVo> contractList) {
        try {
            if (!contractList.isEmpty()) {
                Optional<ContractVo> any = contractList.stream().filter(x -> {
                    String attach = x.getAttach();
                    Integer accountType = x.getAccountType();
                    Integer contractType = x.getContractType();
                    if (0 == accountType && 0 == contractType && StringUtils.isNotBlank(attach) && attach.contains("/api/contract/download")) {
                        return true;
                    }
                    return false;
                }).findAny();
                return any.isPresent();
            }
        } catch (Exception e) {

        }
        return true;
    }
}
