package com.ywwl.customer.center.api.international;

import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.crm.dto.AddPlatformDTO;
import com.ywwl.customer.center.modules.general.crm.dto.GetPlatformDTO;
import com.ywwl.customer.center.modules.general.crm.dto.UpdatePlatformDTO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.international.constant.PlatformEnum;
import com.ywwl.customer.center.modules.international.constant.PlatformsConstant;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PlatformsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 平台账号 前端控制器
 *
 * <AUTHOR>
 * @since 2023年3月23日17:58:41
 */

@RestController
@RequestMapping("/platform")
@Validated
@Slf4j
public class PlatformAccountController extends BaseController {

    /**
     * 公共CRM服务类
     */
    @Resource
    CrmService crmService;
    /**
     * 主数据服务类
     */
    @Resource
    CmccService cmccService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /**
     * 获取新增平台账号需要的参数
     *
     * @return 参数
     */
    @PostMapping("/addPlatformAccountPage")
    public JsonResult<?> addPlatformAccountPage() {
        // 获取所有仓
        List<WareHouseVo> warehouses = packetBusinessApplyService.getPacketWarehouse(getUserCode());
        // 获取平台
        final List<PlatformsVO> platforms = PlatformsConstant.VO(PlatformEnum.PLATFORM_BUNDLING);
        return JsonResult.success(ImmutableMap.of("warehouses", warehouses, "platforms", platforms));
    }

    /**
     * 新增平台账号
     *
     * @param dto 平台账号参数
     * @return 结果
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "添加平台账号")
    @PostMapping("/addPlatformAccount")
    @Validated
    public JsonResult<?> addPlatformAccount(
            @RequestBody AddPlatformDTO dto,
            @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        // 设置操作人
        dto.setOperator(currentUser.getUsername());
        crmService.addPlatform(dto);
        return JsonResult.success();
    }

    /**
     * 编辑平台账号
     *
     * @param dto 平台账号参数
     * @return 结果
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "编辑平台账号")
    @PostMapping("/updatePlatformAccount")
    @Validated
    public JsonResult<?> updatePlatformAccount(
            @RequestBody UpdatePlatformDTO dto,
            @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        // 设置操作人
        dto.setOperator(currentUser.getUsername());
        crmService.updatePlatform(dto);
        return JsonResult.success();
    }

    /**
     * 查询平台账号
     *
     * @return 平台账号信息
     */
    @PostMapping("/getPlatformAccounts")
    public JsonResult<?> getPlatformAccounts() {
        final GetPlatformDTO dto = GetPlatformDTO.builder()
                .userCode(getUserCode())
                .source(10)
                .build();
        return JsonResult.success(crmService.getPlatform(dto));
    }

}
