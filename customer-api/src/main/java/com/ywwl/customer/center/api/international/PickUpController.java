package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.international.config.DispatchConfig;
import com.ywwl.customer.center.modules.international.enums.DispatcherEnum;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2018/8/17
 */
@Slf4j
@Controller
@RequestMapping("/collect")
public class PickUpController extends BaseController {
    @Autowired
    private DispatchConfig dispatchConfig;

    @Autowired
    private PacketBusinessApplyService packetBusinessApplyService;
    @ResponseBody
    @GetMapping("/pickup")
    public JsonResult getQRCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletRequest request) {
        JsonResult result = new JsonResult();

        String result_url = dispatchConfig.getUrl() + DispatcherEnum.SECRETLOGIN;
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());

        if (currentUser != null && merchantCode != null) {
            String customerNo = merchantCode + "";
            String accessToken = currentUser.getAccessToken();
            String userid = currentUser.getUserId() + "";
            result_url += "?userid=" + userid + "&accessToken=" + accessToken + "&customerNo=" + customerNo;
            return JsonResult.success(result_url);
        } else {
            return JsonResult.error(ResponseCode.PORTAL_413);
        }
    }
}

