package com.ywwl.customer.center.api.common;

import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.modules.general.cmcc.enums.WarehouseClassifyTypeEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.plm.entity.SysWarehouse;
import com.ywwl.customer.center.modules.general.plm.service.PLMService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * plm数据控制器
 *
 * <AUTHOR>
 * @date 2023/04/14 16:45
 **/
@RequestMapping("/plm")
@RestController
public class PLMController extends BaseController {

    @Resource
    private PLMService service;
    @Resource
    CmccService cmccService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    @PostMapping("/products")
    public JsonResult<?> products() {
        return JsonResult.success(service.getProducts());
    }

    /**
     * 获取所有国家
     * @return  国家信息
     */
    @PostMapping("/countries")
    public JsonResult<?> countries() {
        return JsonResult.success(service.getCountry());
    }
    /**
     * 获取所有仓
     * @return  国家信息
     */
    @PostMapping("/warehouses")
    public JsonResult<?> warehouses(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser,@JsonParam Integer businessType) {
        // 获取揽收仓
        List<WareHouseVo> warehouse=new LinkedList<>();
        if(businessType!=null){
             warehouse = cmccService.getWarehouseByType(WarehouseClassifyTypeEnum.PACKET_WAREHOUSE.getType());
        }else{
             warehouse = packetBusinessApplyService.getPacketWarehouse(currentUser().getUserCode());
        }
        List<SysWarehouse> warehouses = warehouse.stream()
                .map(SysWarehouse::new)
                .collect(Collectors.toList());
        // 获取默认揽收仓
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        String warehouseCode;
        if(Objects.isNull(packetApplyInfo)){
            warehouseCode = warehouses.get(0).getWarehouseCode();
        }else {
            warehouseCode  = packetApplyInfo.getWarehouseCode();
        }
        // 响应前端
        return JsonResult.success(ImmutableMap.of("warehouses",warehouses,"pickWarehouseId",warehouseCode));

    }



}
