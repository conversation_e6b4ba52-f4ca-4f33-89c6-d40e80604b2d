package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.provider.dto.BusinessContactUpdateDTO;
import com.ywwl.customer.center.modules.common.provider.service.BusinessContactsService;
import com.ywwl.customer.center.modules.common.provider.vo.BusinessContactsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/3 15:42
 */
@Slf4j
@RequestMapping("businessContact")
@RestController
public class BusinessContactController extends BaseController {
    @Resource
    private BusinessContactsService businessContactsService;

    /**
     * <AUTHOR>
     * @description 查询所有的业务联系人
     * @date 2023/4/3 15:36
     **/
    @GetMapping("getAllContact")
    public JsonResult<List> getAllBusinessContact(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        List<BusinessContactsVo> businessContactsForAll = businessContactsService.getBusinessContactsForAll();
        return JsonResult.success(businessContactsForAll);
    }

    /**
     * <AUTHOR>
     * @description 修改办公地址业务联系人
     * @date 2023/4/4 18:10
     **/
    @Logger(module=Module.COMMON,name = "修改业务线办公地址和业务联系人")
    @PostMapping("updateContact")
    public JsonResult updateContact(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser,@Valid @RequestBody BusinessContactUpdateDTO businessContactUpdateDTO) {
        businessContactUpdateDTO.setUserCode(currentUser.getUserCode());
        businessContactsService.updateContact(businessContactUpdateDTO);
        return JsonResult.success();
    }

}
