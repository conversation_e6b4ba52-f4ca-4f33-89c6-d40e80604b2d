package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.dto.ActivateCancelDTO;
import com.ywwl.customer.center.modules.common.provider.dto.FastCreateDTO;
import com.ywwl.customer.center.modules.common.provider.dto.FastWorkOrderDTO;
import com.ywwl.customer.center.modules.common.provider.dto.ThawDetailDTO;
import com.ywwl.customer.center.modules.common.provider.service.FastFreightService;
import com.ywwl.customer.center.modules.common.provider.vo.ExpressQueryListVo;
import com.ywwl.customer.center.modules.common.provider.vo.ExpressQueryVo;
import com.ywwl.customer.center.modules.common.provider.vo.ThawDetailVo;
import com.ywwl.customer.center.modules.common.provider.vo.ThawStatusVo;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.Map;
import java.util.Objects;

/**
 * // 用户工单
 *
 * <AUTHOR>
 * @date 2023/3/28
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/workOrder")
public class FastFreightController extends BaseController {
    @Resource
    private FastFreightService workOrderService;

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /***
     * //查件工单创建
     * <AUTHOR>
     * @param currentUser 登录信息
     * @param createDTO 创建参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快件工单创建")
    @PostMapping("/inquiry/create")
    public JsonResult<Object> inquiryCreate(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody FastCreateDTO createDTO) {
        createDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        try {
            workOrderService.inquiryCreate(createDTO);
            return JsonResult.success();
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("查件工单创建异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查件工单激活
     * <AUTHOR>
     * @date 2023/3/28 14:17
     * @param currentUser 登录信息
     * @param activateCancelDTO 激活信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快件工单激活接口")
    @PostMapping("/inquiry/activate")
    public JsonResult<Object> inquiryActivate(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ActivateCancelDTO activateCancelDTO) {
        try {
            workOrderService.inquiryActivate(activateCancelDTO);
            return JsonResult.success();
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("查件工单激活异常异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    /***
     * //  查件工单结案
     * <AUTHOR>
     * @date 2023/3/28 14:22
     * @param currentUser 登录信息
     * @param activateCancelDTO 结案信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快件工单结案")
    @PostMapping("/inquiry/settle")
    public JsonResult<Object> inquirySettle(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ActivateCancelDTO activateCancelDTO) {
        try {
            workOrderService.inquirySettle(activateCancelDTO);
            return JsonResult.success();
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("查件工单结案异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查件工单列表
     * <AUTHOR>
     * @date 2023/3/28 14:22
     * @param currentUser 登录信息
     * @param fastWorkOrderDTO 结案信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快建工单列表")
    @PostMapping("/inquiry/search")
    public JsonResult<Object> inquirySearch(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody FastWorkOrderDTO fastWorkOrderDTO) {
        try {
            ExpressQueryListVo expressQueryListVo = workOrderService.inquirySearch(fastWorkOrderDTO, currentUser.getUserCode());
            return JsonResult.success(expressQueryListVo.getData());
        } catch (BusinessException businessException) {
            log.error("查件工单结案异常:{}", businessException.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("查件工单结案异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查件工单反馈列表
     * <AUTHOR>
     * @date 2023/3/28 14:22
     * @param currentUser 登录信息
     * @param orderId 工单编号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快件工单反馈信息查询")
    @PostMapping("/inquiry/record")
    public JsonResult<Object> inquiryRecord(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @NotBlank(message = "工单编号不得为空") @JsonParam String orderId,@Valid @NotBlank(message = "运单号不得为空") @JsonParam String waybillNumber) {
        try {
            ExpressQueryVo expressQueryVos = workOrderService.inquiryRecord(orderId,waybillNumber);
            return JsonResult.success(expressQueryVos);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("查件工单结案异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查件工单继续沟通
     * <AUTHOR>
     * @date 2023/3/28 14:22
     * @param currentUser 登录信息
     * @param activateCancelDTO 结案信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "快件工单继续沟通")
    @PostMapping("/inquiry/communication")
    public JsonResult<Object> inquiryCommunication(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ActivateCancelDTO activateCancelDTO) {
        try {
            workOrderService.inquiryCommunication(activateCancelDTO);
            return JsonResult.success();
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("查件工单继续沟通异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  创建解冻申请
     * <AUTHOR>
     * @date 2023/3/28 14:45
     * @param currentUser 登录信息
     * @param map 解冻信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "创建解冻申请")
    @PostMapping("/thaw/create")
    public JsonResult<Object> thawCreate(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody Map<String, Object> map) {
        try {
            map.put("merchantCode", getMerchantCode(currentUser.getUserCode()));
            workOrderService.thawCreate(currentUser, map);
            return JsonResult.success();
        } catch ( ResponseCode.ResponseException | AssertionError|BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("创建解冻工单异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  解冻详情
     * <AUTHOR>
     * @date 2023/3/28 14:45
     * @param currentUser 登录信息
     * @param thawDetailDTO 解冻详情
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "解冻详情")
    @PostMapping("/thaw/detail")
    public JsonResult<Object> thawDetail(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody ThawDetailDTO thawDetailDTO) {
        try {
            thawDetailDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
            ThawDetailVo thawDetailVo = workOrderService.thawDetail(thawDetailDTO);
            return JsonResult.success(thawDetailVo);
        } catch (BusinessException businessException) {
            log.error("创建解冻工单异常:{}", businessException.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("创建解冻工单异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  解冻状态
     * <AUTHOR>
     * @date 2023/3/28 14:45
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "解冻状态查询")
    @GetMapping("/thaw/status")
    public JsonResult<Object> thawStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            ThawStatusVo thawStatusVo = workOrderService.thawStatus(getMerchantCode(currentUser.getUserCode()));
            return JsonResult.success(thawStatusVo.getData());
        } catch (BusinessException businessException) {
            log.error("创建解冻工单异常:{}", businessException.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("创建解冻工单异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  获取工单编号
     * <AUTHOR>
     * @date 2023/3/28 14:45
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "获取工单编号")
    @GetMapping("/getNumberId")
    public JsonResult<Object> getNumberId(@NotBlank(message = "工单类型不得为空") String type) {

        try {
            String workOrderNumber = workOrderService.getWorkOrderNumber(type);
            return JsonResult.success(workOrderNumber);
        } catch (BusinessException businessException) {
            return JsonResult.error(businessException.getMessage());
        } catch (Exception e) {
            log.error("创建解冻工单异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    public String getMerchantCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo) || StringUtils.isEmpty(packetApplyInfo.getMerchantCode())) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo.getMerchantCode();
    }
}
