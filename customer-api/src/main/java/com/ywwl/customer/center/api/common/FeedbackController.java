package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.service.FeedbackService;
import com.ywwl.customer.center.modules.international.dto.EvaluateDTO;
import com.ywwl.customer.center.modules.international.dto.FeedbackListDTO;
import com.ywwl.customer.center.modules.international.dto.FeedbackSaveDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/4/6
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/feedback")
public class FeedbackController extends BaseController {
    @Resource
    private FeedbackService feedbackService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /***
     * //投诉建议类型列表
     * <AUTHOR>
     * @date 2023/4/6 16:25
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "投诉建议类型列表")
    @GetMapping("/complaintType")
    public JsonResult<Object> complaintType() {
        try {
            return feedbackService.complaintType();
        } catch (Exception e) {
            log.error("投诉建议类型异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    public String getMerchantCode(String userCode) {
        return packetBusinessApplyService.getPacketMerchantCode(userCode);
    }

    /***
     * // 创建投诉工单
     * <AUTHOR>
     * @date 2023/4/6 16:33
     * @param feedbackSaveDTO 创建信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "创建投诉工单")
    @PostMapping("/save")
    public JsonResult<Object> save(@Validated @RequestBody FeedbackSaveDTO feedbackSaveDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            feedbackSaveDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
            return feedbackService.save(feedbackSaveDTO);
        } catch (Exception e) {
            log.error("创建投诉工单异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //列表
     * <AUTHOR>
     * @date 2023/4/6 17:09
     * @param feedbackListDTO 列表参数
     * @param currentUser  登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "投诉列表")
    @PostMapping("/list")
    public JsonResult<Object> list(@Valid @RequestBody FeedbackListDTO feedbackListDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            feedbackListDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
            return feedbackService.list(feedbackListDTO);
        } catch (BusinessException e) {
            log.error("工单列表异常：{}", e.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            log.error("工单列表异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查询投诉类型
     * <AUTHOR>
     * @date 2023/4/7 9:33
     * @param typeCode 类型code
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "查询投诉类型")
    @PostMapping("/complaintReasons")
    public JsonResult<Object> getComplaintReasons(@NotBlank(message = "类型id不得为空") @JsonParam String typeCode) {
        try {
            return feedbackService.getComplaintReasons(typeCode);
        } catch (Exception e) {
            log.error("查询投诉类型异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  评价
     * <AUTHOR>
     * @date 2023/4/7 9:33
     * @param evaluateDTO 评价内容
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "投诉建议评价")
    @PostMapping("/evaluate")
    public JsonResult<Object> evaluate(@Valid @RequestBody EvaluateDTO evaluateDTO) {
        try {
            return feedbackService.evaluate(evaluateDTO);
        } catch (Exception e) {
            log.error("评价异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //撤销
     * <AUTHOR>
     * @date 2023/4/7 9:33
     * @param orderId 工单编号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "投诉建议撤销")
    @PostMapping("/cancel")
    public JsonResult<Object> cancel(@NotBlank(message = "工单编号不得卫空") @JsonParam String orderId) {
        try {
            return feedbackService.cancel(orderId);
        } catch (Exception e) {
            log.error("撤销异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }
}
