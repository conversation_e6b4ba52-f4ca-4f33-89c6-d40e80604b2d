package com.ywwl.customer.center.api.open;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.system.service.CustomerSyncService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: dinghy
 * @date: 2023/6/6 17:40
 */
@RestController
@RequestMapping("/api/customer")
public class CustomerController {
    @Resource
    private CustomerSyncService customerSyncService;


    /**
     * <AUTHOR>
     * @description 查询客户申请状态
     * @date 2023/6/6 17:47
     **/
    @RequestMapping("queryCustomerApplyStatus")
    public JsonResult queryCustomerApplyStatus(@JsonParam String no){
        Integer customerAuthStatus = customerSyncService.getNewCustomerStatusByNo(no);
       return JsonResult.success(customerAuthStatus);
    }
}
