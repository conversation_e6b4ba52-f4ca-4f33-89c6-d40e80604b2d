package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.modules.general.sale.service.SaleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/5/4 15:14
 */
@RestController
@RequestMapping("sale")
public class SaleController extends BaseController {
    @Resource
    private SaleService saleService;

    /**
     * <AUTHOR>
     * @description 判断是在售前主销售为该业务销售
     * @date 2023/5/4 15:40
     **/
    @RequestMapping("checkMainSaleByType")
    public JsonResult checkMainSaleByType(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam Integer type) {
        Assert.isTrue(currentUser.isAdmin(),ResponseCode.PORTAL_5027.getMessage());
        String sale = saleService.checkBusinessSale(type, currentUser.getPhone(),currentUser.getUserCode());
        return JsonResult.success(sale);
    }

    /**
     * <AUTHOR>
     * @description 校验销售
     * @date 2023/5/4 17:26
     **/
    @RequestMapping("validateSale")
    public JsonResult validateSale(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam Integer type,@JsonParam String salePhone){
        Assert.isTrue(currentUser.isAdmin(),ResponseCode.PORTAL_5027.getMessage());
        Assert.isTrue(Objects.nonNull(type),"业务类型为空");
        Assert.isTrue(StringUtils.isNotBlank(salePhone),"销售手机号为空");
        String sale=saleService.validateSale(type,salePhone);
        return JsonResult.success(sale);
    }
}
