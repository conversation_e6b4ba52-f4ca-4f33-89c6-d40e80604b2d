package com.ywwl.customer.center.api.open;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.modules.common.provider.service.AlipayPaymentOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/31 17:01
 */
@Slf4j
@Controller
@RequestMapping("/api/alipay/")
public class AlipayController {
    @Resource
    private AlipayPaymentOrderService alipayPaymentOrderService;

    /**
     * @description 支付宝支付完成后回调
     * <AUTHOR>
     * @date 2021/6/1 12:30
     */
    @RequestMapping("notify/callback")
    public void notifyCallback(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, String[]> parameterMap = request.getParameterMap();
            log.info("支付宝回调参数:{}", JSONObject.toJSONString(parameterMap));
            Map<String, String> map = new HashMap<>(parameterMap.size());
            parameterMap.forEach((key, value) -> {
                map.put(key, value[0]);
            });
            log.info("支付宝回调处理之后参数:{}", JSONObject.toJSONString(map));
            Boolean aBoolean = alipayPaymentOrderService.notifyCallback(map);
            if (aBoolean) {
                response.getWriter().print("success");
            }
        } catch (Exception e) {
            log.error("支付宝支付回调异常:{}", e.getMessage());
            DingTalkClient.sendMessage("支付宝支付回调异常:" + e.getMessage());
        }

    }

    /**
     * @description 处理数据异常时候使用
     * <AUTHOR>
     * @date 2021/6/18 11:45
     */
    @RequestMapping("innerNotify/callback")
    @ResponseBody
    public JsonResult innerNotifyCallback(@RequestBody Map<String, String> param) {
        JsonResult jsonResult = new JsonResult();
        try {
            Boolean aBoolean = alipayPaymentOrderService.notifyCallback(param);
            jsonResult.setSuccess(aBoolean);
        } catch (Exception e) {
            e.printStackTrace();
            jsonResult.setSuccess(false);
            jsonResult.setMessage(e.getMessage());
        }
        return jsonResult;
    }

}
