package com.ywwl.customer.center.api.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.common.provider.domain.SysDownloadTask;
import com.ywwl.customer.center.modules.common.provider.dto.CreateDownloadTaskDTO;
import com.ywwl.customer.center.modules.common.provider.dto.QueryDownloadTaskDTO;
import com.ywwl.customer.center.modules.common.provider.enums.DownloadTaskStatusEnum;
import com.ywwl.customer.center.modules.common.provider.service.SysDownloadTaskService;
import com.ywwl.customer.center.modules.common.provider.vo.DownloadTaskVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RequestMapping("downloadTask")
@RestController
public class DownloadTaskController extends BaseController {
    @Resource
    private SysDownloadTaskService sysDownloadTaskService;


    /**
     * @author: dinghy
     * @createTime: 2024/3/4 16:50
     * @description: 创建任务
     */
    @PostMapping("createTask")
    public JsonResult createTask(@RequestBody CreateDownloadTaskDTO downloadTaskDTO) {
        downloadTaskDTO.setUserId(getUser().getUserId());
        downloadTaskDTO.setUserCode(getUserCode());
        String downloadTaskId = sysDownloadTaskService.createDownloadTask(downloadTaskDTO);
        return JsonResult.success(downloadTaskId);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/6 16:34
     * @description: 删除下载任务
     */
    @GetMapping("deleteTask")
    public JsonResult deleteTask(@RequestParam String taskId) {
        sysDownloadTaskService.deleteTask(taskId);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/6 17:02
     * @description: 下载
     */
    @GetMapping("download")
    public void download(@RequestParam String taskId, HttpServletResponse response) {
        sysDownloadTaskService.download(taskId, response);
    }


    /**
     * @author: dinghy
     * @createTime: 2024/3/6 17:01
     * @description: 查询分页
     */
    @PostMapping("list")
    public JsonResult list(@RequestBody QueryDownloadTaskDTO queryDownloadTaskDTO) {
        queryDownloadTaskDTO.setUserId(getUser().getUserId());
        DownloadTaskVO downloadTaskVO = sysDownloadTaskService.listPage(queryDownloadTaskDTO);
        return JsonResult.success(downloadTaskVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/12 11:30
     * @description: 查询任务状态
     */
    @GetMapping("queryTaskState")
    public JsonResult queryTaskState(String taskId) {
        LambdaQueryWrapper<SysDownloadTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysDownloadTask::getTaskId, taskId);
        SysDownloadTask downloadTaskServiceOne = sysDownloadTaskService.getOne(lambdaQueryWrapper);
        Map<String,Object> res=new HashMap<>(2);
        boolean flag;
        if (downloadTaskServiceOne == null) {
            return JsonResult.error();
        }
        if (DownloadTaskStatusEnum.LOADING.value().equals(downloadTaskServiceOne.getStatus())) {
            flag = false;
        } else if (DownloadTaskStatusEnum.SUCCESS.value().equals(downloadTaskServiceOne.getStatus())) {
            flag = true;
            res.put("fileName",downloadTaskServiceOne.getFileName());
        } else {
            return JsonResult.error();
        }
        res.put("res",flag);
        return JsonResult.success(res);
    }

}
