package com.ywwl.customer.center.api.system;

import cn.hutool.http.ContentType;
import com.ywwl.customer.center.modules.upload.service.FileBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 临时文件控制器
 */
@RestController
@Slf4j
@RequestMapping("/file/business")
public class FileBusinessController {

	FileBusinessService fileBusinessService;

	@Autowired
	public FileBusinessController(FileBusinessService fileBusinessService) {
		this.fileBusinessService = fileBusinessService;
	}

	private static final String ENCODE = "UTF-8";

	@GetMapping("/fileView")
	public void fileView(HttpServletRequest request, HttpServletResponse response) {
		String fileName = request.getParameter("name");
		String fbaTempFilePath = fileBusinessService.getTempFilePath();
		try (FileInputStream fileInputStream = new FileInputStream(fbaTempFilePath.concat(File.separator).concat(fileName))) {
			setDownloadResponse(response, fileName);
			ServletOutputStream outputStream = response.getOutputStream();
			byte[] temp = new byte[10240];
			int size;
			while ((size = fileInputStream.read(temp, 0, temp.length)) != -1) {
				outputStream.write(temp, 0, size);
			}
			outputStream.flush();
		} catch (IOException e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 设置下载的响应头
	 *
	 * @param response 响应实体
	 * @throws UnsupportedEncodingException
	 */
	private void setDownloadResponse(HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
		// 下载文件能正常显示中文
		response.setHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode(fileName, ENCODE));
		response.setContentType(ContentType.OCTET_STREAM.getValue());
	}

}
