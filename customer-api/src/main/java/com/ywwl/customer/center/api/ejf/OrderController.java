package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.itextpdf.text.DocumentException;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.common.utils.MimeTypeUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import com.ywwl.customer.center.modules.ejf.dto.*;
import com.ywwl.customer.center.modules.ejf.entity.Order;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.ejf.vo.OrderVO;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * EJF订单控制器
 *
 * <AUTHOR>
 * @data 2022/1/27
 */
@Slf4j
@RestController
@RequestMapping("/ejf/order")
public class OrderController extends BaseController {
	public static String FILE_NAME_EXCEL = "E键发运单_%s_%s.xlsx";
	public static String TRANSFER_ORDER = "交接单_%s.xlsx";
	/**
	 * 签收照片_模板名称
	 */
	public static String TRANSFER = "签收照片_{}.zip";
	@Resource
    OrderService orderService;
	@Resource
	AccountService accountService;
	@Resource
	private CrmService crmService;

	/**
	 * 获取发货账号
	 *
	 * @param scene    业务场景
	 * @return 基础信息
	 */
	@RequestMapping("/getShippingAccount")
	@Logger(module = Module.EJF,name = "获取发货账号")
	public JsonResult<?> getShippingAccount(@JsonParam Integer scene) {
		UserAgent currentUser = getUser();
		AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
		accountGetReqDTO.setUserCode(currentUser.getUserCode());
		// 0 直发业务
		accountGetReqDTO.setAccountType(0);
		// 设置业务场景
		if (Objects.nonNull(scene)) {
			accountGetReqDTO.setScene(scene);
		}else {
			// 默认为1
			accountGetReqDTO.setScene(1);
		}
		List<AccountGetResVO> accounts = accountService.getAccounts(accountGetReqDTO);
		accounts.forEach(item->{
			if(item.getEnableStatus()!=0||item.getSearchStatus()!=0){
				item.setApiToken(null);
			}
		});
		return JsonResult.success(accounts);
	}

	@RequestMapping("/printInfo")
	@Logger(module = Module.EJF,name = "导出运单")
	public void printInfo(@RequestBody WaybillKeysDTO key, HttpServletResponse response) throws IOException {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		UserAgent currentUser = getUser();
		// 设置下载HTTP头
		EJFUtil.setDownloadResponse(response,
				String.format(FILE_NAME_EXCEL,
						key.getUserId(),
						LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))));
		key.setPortalUserId(currentUser.getUserId());
		key.setNeedExchange(false);
		orderService.printInfo(key, response.getOutputStream());
	}

	/**
	 * 根据 发货账号和订单号获取订单
	 *
	 * @param param    订单参数
	 * @return 基础信息
	 */
	@RequestMapping("/getOrderById")
	@Valid
	@Logger(module = Module.EJF,name = "根据ID获取运单详情")
	public JsonResult<?> getOrderById(@RequestBody WaybillKeyDTO param) {
		// 验证权限
		accountService.existAccountThrow(param.getUserId());
		return JsonResult.success(orderService.getOrderVOById(param));
	}

	/**
	 * 获取所有订单
	 *
	 * @param pageDTO 分页参数
	 * @return 基础信息
	 */
	@RequestMapping("/getOrder")
	@Valid
	@Logger(module = Module.EJF,name = "查询运单")
	public JsonResult<?> getOrder(@RequestBody PageDTO pageDTO) {
		// 验证权限
		accountService.existAccountThrow(pageDTO.getUserId());
		return JsonResult.success(orderService.getOrderListVO(pageDTO));
	}

	/**
	 * 创建订单
	 *
	 * @param order 订单信息
	 * @return 基础信息
	 */
	@RequestMapping("/createOrder")
	@Validated(value = {Default.class, Input.class})
	@Logger(module = Module.EJF,name = "创建运单",type = Type.WAYBILL_NUMBER,rsp = "data.waybillNumber")
	public JsonResult<?> createOrder(@RequestBody Order order) {
		final UserAgent user = getUser();
		// 验证权限
		accountService.existAccountThrow(order.getUserId());
		// 验证商户状态
		if (!crmService.checkMerchantStatus(user.getMerchantNo(), user.getUserCode())) {
			return JsonResult.error("您的账号存在未清款项，请联系客服核实！");
		}
		return orderService.createOrder(order);
	}

	/**
	 * 取消运单
	 *
	 * @param param 取消运单参数
	 * @return 基础信息
	 */
	@RequestMapping("/cancelOrder")
	@Valid
	@Logger(module = Module.EJF,name = "取消运单",type = Type.WAYBILL_NUMBER,req = "waybillNumbers")
	public JsonResult<?> cancelOrder(@RequestBody WaybillKeysDTO param) {
		// 验证权限
		accountService.existAccountThrow(param.getUserId());
		return orderService.cancelOrder(param);
	}

	/**
	 * 取消运单
	 *
	 * @param param 取消运单参数
	 * @return 基础信息
	 */
	@RequestMapping("/cancelOrderWithDebitPage")
	@Valid
	@Logger(module = Module.EJF,name = "取消运单(扣款)",type = Type.WAYBILL_NUMBER,req = "waybillKeyList.waybillNumber")
	public JsonResult<?> cancelOrder(@RequestBody CancelWaybillDTO param) {
		List<WaybillKeyDTO> keyList = param.getWaybillKeyList();
		// 验证权限
		List<String> userIds = keyList.stream()
				.map(WaybillKeyDTO::getUserId)
				.distinct().collect(Collectors.toList());
		accountService.existAccountThrow(userIds);
		// 运单号
		List<OrderVO> vos = keyList.stream().map(orderService::getOrderVOById).collect(Collectors.toList());
		List<String> successWay = new ArrayList<>();
		List<String> errorWay = new ArrayList<>();
		final List<String> needList = Arrays.asList("0", "1");
		for (OrderVO vo : vos) {
			if (needList.contains(vo.getStatus())) {
				successWay.add(vo.getWaybillNumber());
			}else {
				errorWay.add(vo.getWaybillNumber());
			}
		}
		keyList = keyList.stream().filter(x -> successWay.contains(x.getWaybillNumber())).collect(Collectors.toList());
		JsonResult<Order> result = orderService.cancelOrder(keyList);
		if (CollectionUtil.isEmpty(errorWay)) {
			// 友好提示
			result.setMessage(StringUtils.replace(
					result.getMessage(),
					"取消运单失败，原因 此运单已处理",
					"已取消，金额解冻中，请稍后再查询！"));
		}else {
			result.setMessage(errorWay+"非（已制单/已确认发货）状态，不支持取消。");
		}
		return result;
	}

	/**
	 * 编辑运单
	 *
	 * @param order 运单参数
	 * @return 基础信息
	 */
	@RequestMapping("/editOrder")
	@Validated(value = {Default.class, Input.class})
	@Logger(module = Module.EJF,name = "编辑运单",type = Type.WAYBILL_NUMBER,req = "waybillNumber")
	public JsonResult<?> editOrder(@RequestBody Order order) {
		// 验证权限
		accountService.existAccountThrow(order.getUserId());
		return orderService.editOrder(order);
	}

	@RequestMapping("/printLabels")
	@Valid
	@Logger(module = Module.EJF,name = "打印标签",type = Type.WAYBILL_NUMBER,req = "waybillNumbers", recordRsp = false)
	public JsonResult<?> printLabels(@RequestBody LabelsDTO key) throws DocumentException, IOException {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		return orderService.printLabelsResult(key);
	}

	@RequestMapping("/orderConfirmationDelivery")
	@Valid
	@Logger(module = Module.EJF,name = "运单确认发货",type = Type.WAYBILL_NUMBER,req = "waybillNumbers")
	public JsonResult<?> orderConfirmationDelivery(@RequestBody WaybillKeysDTO key) {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		return orderService.orderConfirmationDelivery(key);
	}

	@RequestMapping("/cancelConfirmationDelivery")
	@Valid
	@Logger(module = Module.EJF,name = "运单取消确认发货",type = Type.WAYBILL_NUMBER,req = "waybillNumbers")
	public JsonResult<?> cancelConfirmationDelivery(@RequestBody WaybillKeysDTO key) {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		return orderService.cancelConfirmationDelivery(key);
	}

	/**
	 * 获取签收照片
	 *
	 * @param userId        制单账号
	 * @param waybillNumber    运单号
	 * @return 签收证明
	 */
	@PostMapping("/getSignature")
	@Valid
	@ResponseBody
	public JsonResult<?> getSignature(@JsonParam @NotBlank(message = "userId不能为空") String userId,
									  @JsonParam @NotBlank(message = "waybillNumber不能为空") String waybillNumber) {
		List<String> signature = orderService.getSignature(userId, waybillNumber);
		if (CollectionUtils.isEmpty(signature)) {
			signature = orderService.getSignature(trackAuthorization, waybillNumber);
			if (CollectionUtil.isEmpty(signature)) {
				return JsonResult.error(ResponseCode.PORTAL_6216);
			}
		}
		return JsonResult.success(signature);
	}

	/**
	 * 追踪验证
	 */
	@Value("${track.Authorization}")
	String trackAuthorization;

	/**
	 * 下载签收照片
	 *
	 * @param userId         制单账号
	 * @param waybillNumbers 运单号
	 * @return 签收证明
	 */
	@PostMapping("/downloadSignature")
	@Valid
	@ResponseBody
	public JsonResult<?> downloadSignature(
			@JsonParam @NotBlank(message = "userId不能为空") String userId,
			@JsonParam @NotNull(message = "waybillNumber不能为空")
			@Size(min = 1, message = "waybillNumber不能为空") List<String> waybillNumbers) throws IOException {
		final int size = CollectionUtil.size(waybillNumbers);
		if (size > 50) {
			return JsonResult.error("单次最多50笔，当前勾选数量" + size);
		}
		// 验证权限
		// accountService.existAccountThrow(userId);
		boolean exists = false;
		try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			 ZipOutputStream zipOutputStream = new ZipOutputStream(outputStream)) {
			for (String waybillNumber : waybillNumbers) {
				List<String> signature = orderService.getSignature(userId, waybillNumber);
				if (CollectionUtil.isNotEmpty(signature)) {
					exists = true;
					for (int i = 0; i < signature.size(); i++) {
						String sign = signature.get(i);
						try (final Response image = HttpUtil.clazz(Response.class).url(sign).get()) {
							final okhttp3.ResponseBody body = image.body();
							if (body != null) {
								String suffix = Optional
										.ofNullable(body.contentType())
										.map(MimeTypeUtil::getSuffix)
										.orElse(StrUtil.EMPTY);

								final String fileName = StrUtil.format("{}_({}张)/{}_{}.{}", waybillNumber, signature.size(), waybillNumber, (i+1), suffix);
								write(fileName, zipOutputStream, body.bytes());
							} else {
								throw new HttpException("响应体为空");
							}
						} catch (Throwable e) {
							// 下载异常也有文件说明
							final String fileName = waybillNumber + "_(" + signature.size() + "张)" + "/" + waybillNumber + ".txt";
							final byte[] body = (sign + " 文件下载失败 " + e.getMessage()).getBytes();
							write(fileName, zipOutputStream, body);
							log.error("文件下载失败 {}", sign, e);
						}
					}
				} else {
					// 下载异常也有文件说明
					final String fileName = waybillNumber + "_(0张)" + "/" + System.currentTimeMillis() + ".txt";
					final byte[] body = ("无签收图片").getBytes();
					write(fileName, zipOutputStream, body);
				}
			}
			zipOutputStream.finish();
			if (exists) {
				final String base64 = EJFUtil.byteToString(outputStream);
				return JsonResult.success(ImmutableMap.of(
						"base64", base64,
						"fileName", StrUtil.format(TRANSFER, DateUtil.date().toString(DatePattern.CHINESE_DATE_TIME_FORMAT)))
				);
			} else {
				return JsonResult.error("所选运单无签收图片");
			}
		}
	}

	/**
	 * 写入zip文件
	 *
	 * @param waybillNumber        文件夹名称
	 * @param zipOutputStream    输出流
	 * @param body                文件体
	 * @throws IOException        异常
	 */
	private static void write(String waybillNumber,
							  ZipOutputStream zipOutputStream,
							  byte[] body) throws IOException {
		ZipEntry zipEntry = new ZipEntry(waybillNumber);
		zipOutputStream.putNextEntry(zipEntry);
		zipOutputStream.write(body);
		zipOutputStream.closeEntry();
	}

	@RequestMapping("/generateTransferOrder")
	@Valid
	@Logger(module = Module.EJF,name = "生成交接单",type = Type.WAYBILL_NUMBER,req = "v[0].waybillNumbers")
	public void generateTransferOrder(@RequestBody WaybillKeysDTO key, HttpServletResponse response, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) throws IOException {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		EJFUtil.setDownloadResponse(response
				,String.format(TRANSFER_ORDER,LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
		key.setPortalUserId(currentUser.getUserId());
		orderService.generateTransferOrder(key,currentUser.getUserCode(), response.getOutputStream());
	}

	/**
	 * 打印揽收大包
	 *
	 * @param userId	用户ID
	 * @param weight	重量
	 * @return	标签
	 */
	@RequestMapping("/HKPrintLabelData")
	@Valid
	@Logger(module = Module.EJF, name = "打印揽收大包", recordRsp = false)
	public JsonResult<?> packageHKPickupCreate(@NotBlank(message = "用户ID不能为空") @JsonParam String userId,
											   @NotBlank(message = "重量不能为空") @JsonParam String weight) {
		final ImmutableMap<String, String> param = ImmutableMap.of("channelId", "1716", "weight", weight);
		final JSONObject transfer = EJFUrl.transfer(userId, EJFUrl.INNER_PACKAGE_HK_PICKUP_CREATE, param);
		final HKPackageDTO dto = transfer.toJavaObject(HKPackageDTO.class);
		if (!dto.getSuccess()) {
			return JsonResult.error(dto.getMessage());
		}
		final HKPackageDTO.DataDTO data = dto.getData();
		if (Objects.isNull(data)) {
			return JsonResult.error("返回数据为空");
		}
		if (!data.getIsSuccess()) {
			return JsonResult.error(data.getErrorMsg());
		}
		final String base64String = data.getBase64String();
		if (StrUtil.isBlank(base64String)) {
			return JsonResult.error("返回数据为空");
		}
		return JsonResult.success(data);
	}

	/**
	 * 下载郵政局資料
	 *
	 * @param response 		响应
	 * @throws IOException	异常
	 */
	@RequestMapping("/postOfficeInformation/download")
	public void postOfficeInformation(HttpServletResponse response) throws IOException {
		ClassPathResource classPathResource = new ClassPathResource("data/郵政局資料.xls");
		HttpUtil.setDownloadHeader(response, "郵政局資料.xls", HttpUtil.DOWNLOAD, "UTF-8" , null);
		try(final InputStream inputStream = classPathResource.getInputStream()) {
			IoUtil.copy(inputStream, response.getOutputStream());
		}
	}


	@Resource
	CmccService cmccService;

	/**
	 * 获取海外交货地
	 *
	 * @return 海外交货地
	 */
	@RequestMapping("/getOverseasDeliveryLocation")
	public JsonResult<?> getOverseasDeliveryLocation() {
		return JsonResult.success(cmccService.getOverseasDeliveryLocation());
	}

}
