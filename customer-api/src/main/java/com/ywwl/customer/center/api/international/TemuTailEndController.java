package com.ywwl.customer.center.api.international;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.utils.EJFUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.common.provider.dto.MessageProductDTO;
import com.ywwl.customer.center.modules.common.provider.enums.MessageProductEnum;
import com.ywwl.customer.center.modules.common.provider.vo.PlmProductVo;
import com.ywwl.customer.center.modules.general.plm.service.PLMService;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import com.ywwl.customer.center.modules.international.dto.TemuExcelDTO;
import com.ywwl.customer.center.modules.international.dto.TemuImportDTO;
import com.ywwl.customer.center.modules.international.dto.TemuLabelDeleteDTO;
import com.ywwl.customer.center.modules.international.dto.TemuLabelOrderQueryDTO;
import com.ywwl.customer.center.modules.international.service.TemuOrderService;
import com.ywwl.customer.center.modules.international.vo.DownloadFileVo;
import com.ywwl.customer.center.modules.international.vo.TemuOrderLabelListVO;
import com.ywwl.customer.center.modules.international.vo.TemuPdfVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RequestMapping("/temu")
@RestController
public class TemuTailEndController extends BaseController {
    @Resource
    private TemuOrderService temuOrderService;
    @Resource
    private AccountService accountService;
    @Resource
    private PLMService plmService;
    @Value("${usaTaxRate.pdfOcr}")
    private String pdfOcrUrl;

    /**
     * @author: dinghy
     * @createTime: 2025/5/22 10:01
     * @description: 查询temu订单列表
     */
    @PostMapping("getOrderLabelList")
    public JsonResult getLabelOrderList(@Validated @RequestBody TemuLabelOrderQueryDTO queryDTO) {
        queryDTO.setUserId(queryDTO.getCustomerCode());
        TemuOrderLabelListVO temuOrderLabelListVO = temuOrderService.getLabelOrderList(queryDTO);
        return JsonResult.success(temuOrderLabelListVO);
    }

    @Valid
    @RequestMapping("getOrderLabelDetail")
    public JsonResult getLabelDetail(@NotBlank(message = "运单号不能为空") @JsonParam String waybillNumber, @NotBlank(message = "客户号不能为空") @JsonParam String customerCode) {
        String label = temuOrderService.getLabelDetail(waybillNumber, customerCode);
        if (StringUtils.isBlank(label)) {
            return JsonResult.error("查询标签失败");
        }
        return JsonResult.success(label);
    }


    @Logger(name = "Temu尾程面单导入", module = Module.EJF)
    @PostMapping("importLabel")
    public JsonResult importLabel(@RequestBody List<TemuImportDTO> data) {
        List<String> errorMsg = new LinkedList<>();
        Map<String, String> accountTokens = new HashMap<>();
        data.forEach(datum -> {
            if (StringUtils.isBlank(datum.getCustomerCode())) {
                throw new BusinessException("制单账号不能为空");
            }
            if (StringUtils.isBlank(datum.getWaybillNumber())) {
                throw new BusinessException("运单号不能为空");
            }
            if (StringUtils.isBlank(datum.getTrackingNumber())) {
                throw new BusinessException("尾程单号不能为空");
            }
            if (StringUtils.isBlank(datum.getLabelBase64())) {
                throw new BusinessException("面单文件不能为空");
            }
        });
        for (TemuImportDTO datum : data) {
            try {
                String token = accountTokens.get(datum.getCustomerCode());
                if (StringUtils.isBlank(token)) {
                    AccountGetResVO account = accountService.getAccount(datum.getCustomerCode());
                    token = account.getApiToken();
                    accountTokens.put(datum.getCustomerCode(), token);
                }
                temuOrderService.importLabel(datum, token);
            } catch (Exception e) {
                errorMsg.add(datum.getWaybillNumber() + "导入失败，原因:" + e.getMessage());
            }
        }
        if (!errorMsg.isEmpty()) {
            return JsonResult.error(JSON.toJSONString(errorMsg));
        }
        return JsonResult.success();
    }

    @RequestMapping("exportLabelTemplate")
    public JsonResult exportLabelTemplate(@RequestBody TemuLabelOrderQueryDTO queryDTO) {
        List<TemuExcelDTO> list = new LinkedList<>();
        if (queryDTO.getWaybillNumberArray() != null && queryDTO.getWaybillNumberArray().size() > 0 && StringUtils.isNotBlank(queryDTO.getCustomerCode())) {
            queryDTO.setState(false);
            TemuOrderLabelListVO labelOrderList = temuOrderService.getLabelOrderList(queryDTO);
            labelOrderList.getRecords().forEach(item -> {
                TemuExcelDTO dto = new TemuExcelDTO();
                dto.setCustomerCode(item.getUserId());
                dto.setTrackingNumber(item.getTrackingNumber());
                dto.setWaybillNumber(item.getWaybillNumber());
                dto.setOrderNumber(item.getOrderNumber());
                list.add(dto);
            });
        }
        DownloadFileVo downloadFileVo = null;
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String fileName = "尾程面单导入模板.xlsx";
            EasyExcel.write(outputStream)
                    .head(TemuExcelDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("sheet1")
                    .doWrite(list);
            String base64 = EJFUtil.byteToString(outputStream);
            downloadFileVo = new DownloadFileVo();
            downloadFileVo.setFileName(fileName);
            downloadFileVo.setUrl(base64);
            return JsonResult.success(downloadFileVo);
        } catch (Exception e) {
            return JsonResult.error("导出失败");
        }
    }


    @PostMapping("importLabelFile")
    public JsonResult importLabelFile(MultipartFile file) {
        if (file == null) {
            return JsonResult.error("请选择文件");
        }
        try {
            List<TemuExcelDTO> list = EasyExcel.read(file.getInputStream(), TemuExcelDTO.class, new AnalysisEventListener<TemuExcelDTO>() {
                @Override
                public void invoke(TemuExcelDTO data, AnalysisContext context) {
                    if (StringUtils.isBlank(data.getCustomerCode())) {
                        throw new BusinessException("制单账号不能为空");
                    }
                    if (StringUtils.isBlank(data.getWaybillNumber())) {
                        throw new BusinessException("运单号不能为空");
                    }
                    if (StringUtils.isBlank(data.getTrackingNumber())) {
                        throw new BusinessException("尾程单号不能为空");
                    }
                    data.setCustomerCode(data.getCustomerCode().trim());
                    data.setWaybillNumber(data.getWaybillNumber().trim());
                    data.setTrackingNumber(data.getTrackingNumber().replaceAll("\\s", ""));
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {

                }
            }).sheet(0).doReadSync();

            List<String> accounts = list.stream().map(x -> x.getCustomerCode()).collect(Collectors.toList());
            accountService.existAccountThrow(accounts);
            return JsonResult.success(list);
        } catch (IOException e) {
            return JsonResult.error("导入失败");
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2025/6/3 15:52
     * @description: type=0，表示单个上传
     */
    @RequestMapping("analyzePdfLabelFile")
    public JsonResult analyzePdfLabelFile(MultipartFile file, Integer type) {
        if (file == null) {
            return JsonResult.error("请选择文件");
        }
        try {
            List<TemuImportDTO> list = new LinkedList<>();
            // 如果是单个文件，直接返回base64
            PdfReader reader = new PdfReader(file.getInputStream());
            int numberOfPages = reader.getNumberOfPages();
            if (numberOfPages == 0) {
                throw new BusinessException("文件解析失败，请检查文件");
            }
            List<String> trackingNumberList = new LinkedList<>();
            String firstContent = PdfTextExtractor.getTextFromPage(reader, 1);
            String firstTrackingNumber = extractTrackingNumber(firstContent);
            if (StringUtils.isBlank(firstTrackingNumber)) {
                // 调用丁戈接口解析文件
                JSONObject jsonObject = HttpUtil.clazz(JSONObject.class) // 返回类型
                        .url(pdfOcrUrl) // url
                        .mediaType(MediaType.MULTIPART_FORM_DATA) // 文件上传
                        .filePart("file", file.getOriginalFilename(), file.getBytes()) // 文件内容
                        .post();
                if (jsonObject == null) {
                    throw new BusinessException("文件解析失败");
                }
                TemuPdfVO temuPdfVO = jsonObject.toJavaObject(TemuPdfVO.class);
                List<TemuPdfVO.MatchesDTO> matches = temuPdfVO.getMatches();
                if (matches == null || matches.size() == 0) {
                    throw new BusinessException("文件解析失败");
                }
                trackingNumberList = temuPdfVO.getMatches().stream()
                        .map(TemuPdfVO.MatchesDTO::getTrackingNumber)
                        .filter(trackingNumber -> trackingNumber != null && !trackingNumber.isEmpty())
                        .distinct()
                        .collect(Collectors.toList());
                if (trackingNumberList.size() != numberOfPages) {
                    throw new BusinessException("请检查面单文件，一个尾程单号只允许有一个pdf文件");
                }
            } else {
                // 能正常解析文件
                for (int i = 1; i <= numberOfPages; i++) {
                    String pageContent = PdfTextExtractor.getTextFromPage(reader, i);
                    // 抽取尾程单号
                    String trackingNumber = extractTrackingNumber(pageContent);
                    if (StringUtils.isBlank(trackingNumber)) {
                        throw new BusinessException("解析到尾程单号失败");
                    }
                    trackingNumberList.add(trackingNumber);
                }
            }
            // 解析base64
            for (int i = 1; i <= numberOfPages; i++) {
                // Create a new document for each page
                Document document = new Document();
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                PdfCopy copy = new PdfCopy(document, baos);
                document.open();
                // Add the current page to the new document
                PdfImportedPage page = copy.getImportedPage(reader, i);
                copy.addPage(page);
                document.close();

                // Convert to Base64
                String base64 = Base64.getEncoder().encodeToString(baos.toByteArray());
                TemuImportDTO temuImportDTO = new TemuImportDTO();
                temuImportDTO.setLabelBase64(base64);
                temuImportDTO.setTrackingNumber(trackingNumberList.get(i - 1));
                list.add(temuImportDTO);
            }
            return JsonResult.success(list);
        } catch (Exception e) {
            return JsonResult.error("文件解析失败");
        }

    }

    public String extractTrackingNumber(String pageContent) {
        String trackingNumber = extract22DigitTrackingNumber(pageContent);
        if (StringUtils.isBlank(trackingNumber)) {
            trackingNumber = extractYWTrackingNumber(pageContent);
        }
        return trackingNumber;
    }

    public String convertMultipartFileToBase64(MultipartFile file) throws IOException {
        byte[] fileContent = file.getBytes();
        return Base64.getEncoder().encodeToString(fileContent);
    }

    public static String extract22DigitTrackingNumber(String text) {
        String cleaned = text.replaceAll("\\s+", "");
        Pattern pattern = Pattern.compile("9\\d{21}");
        Matcher matcher = pattern.matcher(cleaned);

        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }


    public static String extractYWTrackingNumber(String text) {
        String cleaned = text.replaceAll("\\s+", "");
        Pattern pattern = Pattern.compile("YW[A-Z\\d]{15}");
        Matcher matcher = pattern.matcher(cleaned);

        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    @RequestMapping("getProductList")
    public JsonResult getProductList() {
        MessageProductDTO productDTO = new MessageProductDTO();
        productDTO.setType(0);
        productDTO.setProductType(AbnormalConstant.CODE_1);
        //查询EJF产品
        productDTO.setName(EnumUtil.getBy(MessageProductEnum::getCode, MessageProductEnum.EJF.getCode(), MessageProductEnum.ERROR).getName());
        productDTO.setPlatform(MessageProductEnum.EJF.getCode());
        // TEMU头程
        productDTO.setYwProductGenealogyDetail("70");
        productDTO.setIsValid(1);
        List<PlmProductVo.DataDTO> originalProducts = plmService.getOriginalProducts(productDTO);
        return JsonResult.success(originalProducts);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/6/10 14:01
     * @description: Temu尾程面单删除
     */
    @Logger(name = "Temu尾程面单删除", module = Module.EJF)
    @PostMapping("deleteLabel")
    public JsonResult deleteLabel(@RequestBody @Validated TemuLabelDeleteDTO deleteDTO) {
        if(deleteDTO.getList().size()>200){
            return JsonResult.error("一次最多只能删除200条数据");
        }
        accountService.existAccountThrow(deleteDTO.getCustomerCode());
        AccountGetResVO account = accountService.getAccount(deleteDTO.getCustomerCode());
        List<String> errorMessage=new LinkedList<>();
        deleteDTO.getList().parallelStream().forEach(waybillNumber -> {
            try {
                TemuLabelDeleteDTO.LabelDeleteParamDTO labelDeleteParamDTO = new TemuLabelDeleteDTO.LabelDeleteParamDTO();
                labelDeleteParamDTO.setWaybillNumber(waybillNumber);
                temuOrderService.deleteLabel(labelDeleteParamDTO,deleteDTO.getCustomerCode(),account.getApiToken());
            } catch (Exception e) {
                errorMessage.add("运单号："+waybillNumber+" ，删除失败，原因-->"+e.getMessage());
            }
        });
        if(!errorMessage.isEmpty()){
            return JsonResult.error("部分数据处理失败："+ JSONObject.toJSONString(errorMessage));
        }
        return JsonResult.success();
    }


}
