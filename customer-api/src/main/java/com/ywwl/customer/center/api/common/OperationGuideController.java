package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.modules.common.account.service.SysOperationControllerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 单次操作控制器
 *
 * <AUTHOR>
 * @since 2023/12/22 15:44
 **/
@RestController
@RequestMapping("/operationGuide")
@Slf4j
public class OperationGuideController extends BaseController {

    @Resource
    private SysOperationControllerService operationGuideService;

    /**
     * 是否需要展示
     *
     * @param type type
     * @return JsonResult<?>
     */
    @RequestMapping("/doYouNeedToShow")
    public JsonResult<?> doYouNeedToShow(@JsonParam @NotBlank(message = "type不能为空") String type) {
        try {
            return JsonResult.success(operationGuideService.doYouNeedToShow(getUser().getUserId(), type));
        } catch (Exception e) {
            log.error("获取是否需要操作指引失败", e);
            return JsonResult.error("获取是否需要操作指引失败");
        }
    }

    /**
     * 查看完结
     *
     * @param type type
     * @return JsonResult<?>
     */
    @RequestMapping("/viewTheEnd")
    public JsonResult<?> viewTheEnd(@JsonParam @NotBlank(message = "type不能为空") String type) {
        try {
            operationGuideService.viewTheEnd(getUser().getUserId(), type);
            return JsonResult.success();
        } catch (Exception e) {
            log.error("更新操作指引状态失败", e);
            return JsonResult.error("更新操作指引状态失败");
        }
    }

}
