package com.ywwl.customer.center.api.international;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.dto.QueryContractDTO;
import com.ywwl.customer.center.modules.common.provider.constant.PaymentConstant;
import com.ywwl.customer.center.modules.common.provider.enums.ContractTypeEnum;
import com.ywwl.customer.center.modules.common.provider.enums.FreezeTypeEnum;
import com.ywwl.customer.center.modules.common.provider.service.CrmOldService;
import com.ywwl.customer.center.modules.common.provider.vo.EmployeeVo;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.general.crm.vo.SignContractVO;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.modules.international.dto.DirectContactDTO;
import com.ywwl.customer.center.modules.international.dto.PacketBusinessApplyDTO;
import com.ywwl.customer.center.modules.international.dto.UnFreezeMerchantDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.*;
import com.ywwl.customer.center.system.service.SmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @date: 2023/3/8 9:47
 */
@RequestMapping("packet")
@RestController
public class PacketBusinessApplyController extends BaseController {
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private CrmOldService crmOldService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private SmsService smsService;
    @Resource
    private CrmService crmService;

    /**
     * <AUTHOR>
     * @description 暂存或者提交小包业务申请
     * @date 2023/3/8 10:06
     **/
    @Logger(module = Module.SMALL_PACKAGE, name = "提交小包业务申请")
    @PostMapping("saveApply")
    public JsonResult<Object> saveApply(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody PacketBusinessApplyDTO packetBusinessApplyDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .provinceName(packetBusinessApplyDTO.getOfficeProvinceName())
                        .cityName(packetBusinessApplyDTO.getOfficeCityName())
                        .areaName(packetBusinessApplyDTO.getOfficeAreaName())
                        .address(packetBusinessApplyDTO.getOfficeAddress())
                        .desc("新开通小包业务线办公地址")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        packetBusinessApplyDTO.setUserCode(currentUser.getUserCode());
        packetBusinessApplyDTO.setNo(currentUser.getMerchantNo());
        packetBusinessApplyDTO.setAdminPhone(currentUser.getPhone());
        packetBusinessApplyDTO.setOperator(currentUser.getLoginName());
        packetBusinessApplyService.saveApply(packetBusinessApplyDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 查询小包业务申请信息
     * @date 2023/3/14 17:05
     **/
    @GetMapping("getPacketApplyInfo")
    public JsonResult<PacketApplyInfoVo> getPacketApplyInfo(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfoVo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        if (Objects.isNull(packetApplyInfoVo)) {
            packetApplyInfoVo = new PacketApplyInfoVo();
            packetApplyInfoVo.setBillMail(currentUser.getEmail());
        }
        return JsonResult.success(packetApplyInfoVo);
    }

    /**
     * 查询当前商户小包状态
     *
     * @param currentUser
     * @return
     */
    @GetMapping("getPacketApplyStatus")
    public JsonResult<String> getPacketApplyStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Integer packetStatus = packetBusinessApplyService.getNotLimitState(currentUser.getUserCode());
        if (Objects.isNull(packetStatus)) {
            return JsonResult.error();
        }

        return JsonResult.success(String.valueOf(packetStatus));
    }

    @GetMapping("getBasePacketInfo")
    public JsonResult<BasePacketInfoVo> getBasePacketInfo(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfoVo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        if (Objects.isNull(packetApplyInfoVo)) {
            packetApplyInfoVo = new PacketApplyInfoVo();
            packetApplyInfoVo.setBillMail(currentUser.getEmail());
        }
        BasePacketInfoVo basePacketInfoVo = new BasePacketInfoVo();
        BeanUtils.copyProperties(packetApplyInfoVo, basePacketInfoVo);

        //获取销售员信息
        if (StringUtils.isNotBlank(packetApplyInfoVo.getIntroducerSaleId())) {
            EmployeeVo saleEmployee = packetBusinessApplyService.getEmployeeInfo(packetApplyInfoVo.getIntroducerSaleId());
            basePacketInfoVo.setSalesman(saleEmployee);
        }

        //获取客服信息
        if (StringUtils.isNotBlank(packetApplyInfoVo.getReceiverId())) {
            EmployeeVo supporterEmployee = packetBusinessApplyService.getEmployeeInfo(packetApplyInfoVo.getReceiverId());
            if (supporterEmployee!=null){
                supporterEmployee.setPhone(null);
            }
            basePacketInfoVo.setSupportStaff(supporterEmployee);
        }
        basePacketInfoVo.setFrozenStatus(packetApplyInfoVo.getEjfStatus());
        DataIntegrity dataIntegrity = packetBusinessApplyService.getDataIntegrity(currentUser.getUserCode());
        basePacketInfoVo.setDataRate(dataIntegrity.getAllRate());
        return JsonResult.success(basePacketInfoVo);
    }

    @GetMapping("getFreezeStatus")
    public JsonResult<Object> getFreezeState(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfoVo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        if (Objects.isNull(packetApplyInfoVo)) {
            return JsonResult.error(ResponseCode.PORTAL_7008);
        } else {
            String freezeItem = packetApplyInfoVo.getFreezeItem();
            if (freezeItem != null && !freezeItem.isEmpty()) {
                List<String> list = Arrays.stream(freezeItem.split(",")).collect(Collectors.toList());
                if (list.size() != 1 || !list.contains(PaymentConstant.MYSELF_CODE1)) {
                    return JsonResult.success(ResponseCode.PORTAL_6211.getMessage(), PaymentConstant.CODE_2);
                }
            }
            return JsonResult.success(PaymentConstant.CODE_1);
        }
    }

    @GetMapping("getPacketMerchantCode")
    public JsonResult<String> getPacketMerchantCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
            return JsonResult.success(merchantCode);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * <AUTHOR>
     * @description 判断是否需要签署计费服务确认书
     * @date 2023/8/28 10:33
     **/
    @GetMapping("checkBillServiceStatus")
    public JsonResult checkBillServiceStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        BillServiceInfoVo billServiceBook = crmOldService.getBillServiceBook(merchantCode);
        Map<String, Object> res = new HashMap<>(2);
        res.put("billService", 0);
        if (billServiceBook != null) {
            // 1表示需要签署付款委托书
            res.put("billService", 1);
        }
        return JsonResult.success(res);
    }

    /**
     * <AUTHOR>
     * @description 获取计费服务确认书签署地址
     * @date 2023/8/28 13:52
     **/
    @PostMapping("getSignBillServiceBook")
    public JsonResult getSignBillServiceBook(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser,@JsonParam Boolean viewContract) {
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        BillServiceInfoVo billServiceBook = crmOldService.getBillServiceBook(merchantCode);
        if (billServiceBook == null) {
            throw new BusinessException(ResponseCode.PORTAL_6210);
        }
        QueryContractDTO queryContractDTO = new QueryContractDTO();
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        queryContractDTO.setContractType(ContractTypeEnum.SERVICE_CONTRACT.value());
        queryContractDTO.setExtraData(JSONObject.toJSONString(billServiceBook));
        queryContractDTO.setViewContract(viewContract);
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        Map<String,String> resMap=new HashMap<>();
        resMap.put("signUrl", signContractVO.getUrl());
        resMap.put("contractName", signContractVO.getContractName());
        return JsonResult.success(resMap);
    }

    /**
     * <AUTHOR>
     * @date 2023/11/8 14:59
     * @description 判断是否需要签署发货承诺书
     */
    @GetMapping("checkDeliveryPromiseStatus")
    public JsonResult checkDeliveryPromiseStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
        Map<String, Object> res = new HashMap<>(2);
        res.put("status", 0);
        if (packetApplyInfo != null && StringUtils.isNotBlank(packetApplyInfo.getSignType()) && packetApplyInfo.getSignType().equals("1")) {
            // 1表示需要签署付款委托书
            res.put("status", 1);
        }
        return JsonResult.success(res);
    }

    /**
     * <AUTHOR>
     * @date 2023/11/8 15:15
     * @description 获取发货承诺书
     */
    @PostMapping("getDeliveryPromiseBook")
    public JsonResult getDeliveryPromiseBook(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser,@JsonParam Boolean viewContract) {
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        String merchantCode = packetBusinessApplyService.getPacketMerchantCode(currentUser.getUserCode());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("accountCode", merchantCode);
        QueryContractDTO queryContractDTO = new QueryContractDTO();
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        queryContractDTO.setContractType(ContractTypeEnum.DELIVERY_COMMIT.value());
        queryContractDTO.setExtraData(jsonObject.toString());
        queryContractDTO.setViewContract(viewContract);
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        Map<String,String> resMap=new HashMap<>();
        resMap.put("signUrl", signContractVO.getUrl());
        resMap.put("contractName", signContractVO.getContractName());
        return JsonResult.success(resMap);
    }

    /**
     * <AUTHOR>
     * @date 2023/11/28 14:58
     * @description 查询各个资料是否完全,同时展示客户信息
     */
    @GetMapping("getCustomerData")
    public JsonResult getCustomerData(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser){
        CompleteDataVo completeDataVo=packetBusinessApplyService.getCustomerData(currentUser);
        return JsonResult.success(completeDataVo);
    }

    /**
     * <AUTHOR>
     * @date 2023/11/28 15:28
     * @description 解冻申请
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "商户解冻申请")
    @PostMapping("unFreezeMerchant")
    public JsonResult unFreezeMerchant(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletRequest request,@Valid @RequestBody UnFreezeMerchantDTO unFreezeMerchantDTO){
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        String id = request.getSession().getId();
        smsService.checkSmsCode(id, currentUser.getPhone(), unFreezeMerchantDTO.getSmsCode());
        unFreezeMerchantDTO.setUserCode(currentUser.getUserCode());
        unFreezeMerchantDTO.setNo(currentUser.getMerchantNo());
        List<String> types = unFreezeMerchantDTO.getTypes();
        types.remove(FreezeTypeEnum.MONEY.getCode());
        if(types.isEmpty()){
            return JsonResult.error("解冻类型不能为空");
        }
        for (String type : types) {
            unFreezeMerchantDTO.setType(type);
            // 如果是违禁品解冻，参数缺少，不直接解冻
            if("4".equals(type)&&(StringUtils.isBlank(unFreezeMerchantDTO.getApplyReason())||StringUtils.isBlank(unFreezeMerchantDTO.getMainProducts())||StringUtils.isBlank(unFreezeMerchantDTO.getManagementPlatform())||unFreezeMerchantDTO.getHasAirBanItems()==null)){
                   continue;
            }
            packetBusinessApplyService.unFreezeMerchant(unFreezeMerchantDTO);
        }
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @date 2023/12/1 9:37
     * @description 发起合同补签
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "商户合同补签")
    @GetMapping("contractSign")
    public JsonResult contractSign(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser){
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
        packetBusinessApplyService.contractSign(currentUser);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @date 2023/12/1 15:05
     * @description  修改紧急联系人和业务联系人信息
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "修改小包联系人信息")
    @PostMapping("updateContact")
    public JsonResult updateContact(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody DirectContactDTO directContactDTO){
        directContactDTO.setUserCode(currentUser.getUserCode());
        packetBusinessApplyService.updateContact(directContactDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/1/25 9:37
     * @description: 长期不发货解冻申请通知
     */
    @PostMapping("unFreezeNotice")
    public JsonResult unFreezeNotice(){
        UserAgent user = getUser();
        try {
            packetBusinessApplyService.unFreezeNotice(user.getUserCode());
        } catch (Exception e) {

        }
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/24 10:13
     * @description: 判断是否是补签合同不受限
     */
    @GetMapping("checkUnLimitNotice")
    public JsonResult checkResignUnLimitNotice(){
        String userCode = getUserCode();
        PacketResignUnLimitVo packetResignUnLimitVo=packetBusinessApplyService.checkResignUnLimitNotice(userCode);
        return JsonResult.success(packetResignUnLimitVo);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/10/21 14:44
     * @description: 校验法人付款账号是否缺失
     */
    @RequestMapping("validateCustomerPayment")
    public JsonResult validateCustomerPayment(){
      Boolean res= packetBusinessApplyService.validateCustomerPayment(getUserCode());
      return JsonResult.success(res);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/10/23 17:35
     * @description: 查询交货仓
     */
    @RequestMapping("getPacketWarehouse")
    public JsonResult getPacketWarehouse(){
        List<WareHouseVo> packetWarehouse = packetBusinessApplyService.getPacketWarehouse(getUserCode());
        return JsonResult.success(packetWarehouse);
    }
}
