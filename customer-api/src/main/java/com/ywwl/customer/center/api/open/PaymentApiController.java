package com.ywwl.customer.center.api.open;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ywwl.customer.center.api.common.CryptoController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.RequestMode;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.provider.constant.PaymentConstant;
import com.ywwl.customer.center.modules.common.provider.domain.PaymentAccount;
import com.ywwl.customer.center.modules.common.provider.dto.*;
import com.ywwl.customer.center.modules.common.provider.enums.PaymentApplyTypeEnum;
import com.ywwl.customer.center.modules.common.provider.service.ModificationDataService;
import com.ywwl.customer.center.modules.common.provider.service.PaymentAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/5/12
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class PaymentApiController {

    @Value("${register.url}")
    private String registerUrl;
    @Resource
    private PaymentAccountService paymentAccountService;
    @Resource
    private ModificationDataService modificationDataService;

    /**
     * 获取收件人地址
     *
     * @return 收件人地址列表
     */
    @RequestMapping("/textDownload")
    public void textDownload(HttpServletResponse response) {
        HttpUtil.url("http://accreport.yanwentech.com:665/exportbill.action?transType=BTC01&mainNo=BTC01_1_2023-10-40_2023-10-40_02_201184_CNY&currentPage=1&pageSize=10&waybillNumber=&opCode=expbilldetail")
                .timeout(600)
                .fileName("账单.xlsx")
                .response(response)
                .mode(RequestMode.GET)
                .download();
    }

    /***
     * // 接受客服发起补齐信息
     * <AUTHOR>
     * @date 2023/5/12 9:39
     * @param paymentAccountCrmDTO 补齐信息
     * @return JsonResult
     */
    @Logger(module = Module.COMMON, name = "接受客服发起补齐信息")
    @RequestMapping(value = "/serviceCommission", method = RequestMethod.POST)
    public JsonResult<Object> serviceCommission(@RequestBody PaymentAccountCrmDTO paymentAccountCrmDTO) {
        try {
            return paymentAccountService.serviceCommission(paymentAccountCrmDTO);
        } catch (Exception e) {
            log.error("接受客服发起补齐信息异常:{}", e.getMessage(),e);
            return JsonResult.error();
        }
    }

    /**
     * <AUTHOR>
     * @description 付款委托书签署成功回调
     * @date 2023/5/12 14:53
     **/
    @Logger(module = Module.COMMON, name = "接收付款委托书签署回调")
    @RequestMapping("paymentContractNotify")
    public JsonResult<Object> paymentContractNotify(@JsonParam String contractCode, @JsonParam String downloadUrl) {
        if (StringUtils.isBlank(contractCode)) {
            return JsonResult.error("合同编码为空");
        }
        if (StringUtils.isBlank(downloadUrl)) {
            return JsonResult.error("下载地址为空");
        }
        return paymentAccountService.handlePaymentCallback(contractCode, downloadUrl);
    }


    /***
     * //第三方付款账号审核结果
     * <AUTHOR>
     * @date 2021/8/23 14:40
     * @param thirdPartyAuditDTO 审核结果
     * @return com.cmhb.common.JsonResult
     */
    @Validated
    @Logger(module = Module.COMMON, name = "付款账号审核结果")
    @PostMapping("/update/thirdPartyAudit")
    public JsonResult<Object> thirdPartyAudit(@RequestBody ThirdPartyAuditDTO thirdPartyAuditDTO) {
        log.info("第三方付款账号审核通知:{}", thirdPartyAuditDTO);
        try {
            return paymentAccountService.thirdPartyAudit(thirdPartyAuditDTO);
        } catch (Exception e) {
            log.error("付款账号审核结果异常原因:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     * //对单个付款账号撤销补齐委托书
     * <AUTHOR>
     * @date 2021/8/23 16:41
     * @param annulAccountDTO 撤销参数
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.COMMON, name = "对单个付款账号撤销补齐委托书")
    @PostMapping("/cancel/account")
    public JsonResult<Object> cancelAccount(@Validated @RequestBody AnnulAccountDTO annulAccountDTO) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("对单个付款账号撤销补齐委托书-接口");
        try {
            JsonResult<Object> jsonResult = paymentAccountService.cancelAccount(annulAccountDTO);
            stopWatch.stop();
            log.info("对单个付款账号撤销补齐委托书-接口耗时为:{}", stopWatch.getTotalTimeMillis());
            return jsonResult;
        } catch (Exception e) {
            log.error("对单个付款账号撤销补齐委托书异常：{}", e.getMessage());
            return JsonResult.error();
        }
    }

    /**
     * 获取注册链接
     *
     * @param empCode 员工编号
     * @return JsonResult
     */
    @RequestMapping("/getRegistrationLink")
    public JsonResult<String> getRegistrationLinkThroughTheSalesmanSCellPhoneNumber(@JsonParam String empCode) {
        String target = CryptoController.DES_I.encryptHex(empCode);
        String uri = UriComponentsBuilder.fromUriString(registerUrl).queryParam("target", target).build().toUriString();
        return JsonResult.success("获取成功", uri);
    }

    /***
     * //  商户签约进行银行卡备案
     * <AUTHOR>
     * @date 2023/10/13 10:38
     * @param paymentRecordDTO 备案信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "商户签约进行银行卡备案")
    @PostMapping("/payment/recordSigning")
    public JsonResult<Object> recordSigning(@RequestBody PaymentRecordDTO paymentRecordDTO) {
        try {
            return paymentAccountService.recordSigning(paymentRecordDTO);
        } catch (BusinessException | ResponseCode.ResponseException e) {
            log.error("商户签约进行银行卡备案异常:{}", e.getMessage());
            return JsonResult.error();
        } catch (Exception e) {
            log.error("商户签约进行银行卡备案异常:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     * //付款账号查询
     * <AUTHOR>
     * @date 2023/10/20 14:06
     * @param paymentDTO  参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @RequestMapping("/paymentAccount/search")
    public JsonResult<Object> paymentAccountSearch(@RequestBody PaymentDTO paymentDTO) {
        log.info("付款账号查询参数:{}", paymentDTO);
        try {
            return modificationDataService.paymentAccountSearch(paymentDTO);
        } catch (Exception e) {
            log.error("付款账号查询异常:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     *   付款账号信息修改
     * <AUTHOR>
     * @date 2022/2/28 10:51
     * @param paymentDTO 付款参数
     * @return com.cmhb.common.JsonResult
     */
    @RequestMapping("/paymentAccount/modification")
    public JsonResult<Object> paymentAccountModification(@RequestBody PaymentDTO paymentDTO) {
        log.info("付款账号信息修改参数:{}", paymentDTO);
        try {
            return modificationDataService.paymentAccountModification(paymentDTO);
        } catch (Exception e) {
            log.error("付款账号信息修改异常:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     *  付款账号信息删除
     * <AUTHOR>
     * @date 2022/2/28 11:19
     * @param paymentDTO 账号参数
     * @return com.cmhb.common.JsonResult
     */
    @RequestMapping("/paymentAccount/delete")
    public JsonResult<Object> paymentAccountDelete(@RequestBody PaymentDTO paymentDTO) {
        log.info("付款账号信息删除参数:{}", paymentDTO);
        try {
            return modificationDataService.paymentAccountDelete(paymentDTO);
        } catch (Exception e) {
            log.error("付款账号信息删除异常:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     * //接收解冻工单的付款账号信息
     * <AUTHOR>
     * @date 2021/6/16 11:14
     * @param paymentAccount 账号信息
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.COMMON, name = "接收解冻工单的付款账号信息")
    @RequestMapping(value = "/receptionUnfreezeWorkOrder", method = RequestMethod.POST)
    @ResponseBody
    public JsonResult<Object> thawPayment(@RequestBody PaymentAccount paymentAccount) {
        log.info("接收crm解冻工单的付款账号信息:{}", paymentAccount);
        if (StringUtils.isBlank(paymentAccount.getBankCard())
                && StringUtils.isBlank(paymentAccount.getMerchantCode())
                && StringUtils.isBlank(paymentAccount.getAccountName())
                && StringUtils.isBlank(String.valueOf(paymentAccount.getPaymentType()))
                && StringUtils.isBlank(paymentAccount.getUserCode())
                && ObjectUtils.isEmpty(paymentAccount.getBusinessType())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        try {

            return paymentAccountService.thawPayment(paymentAccount);
        } catch (Exception e) {
            log.error("接收crm解冻工单的付款账号异常:{}", e.getMessage(), e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //撤销补齐委托书
     * <AUTHOR>
     * @date 2021/6/16 11:51
     * @param userCode 用户代码
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.COMMON, name = "撤销补齐委托书")
    @Validated
    @PostMapping("/revocationCommission")
    public JsonResult<Object> revocationCommission(@JsonParam @NotBlank(message = "用户代码不得为空") String userCode) {
        log.info("撤销补齐委托书:{}", userCode);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("撤销补齐委托书-接口");
        PaymentAccount account = new PaymentAccount();
        account.setApplicationType(PaymentApplyTypeEnum.APPLYTYPE_CODE3.value());
        account.setBusinessType(0);
        account.setStatus(PaymentConstant.CODE_0);
        account.setEffectivity(PaymentConstant.VALID_CODE);
        account.setUserCode(userCode);
        List<PaymentAccount> list = paymentAccountService.list(new QueryWrapper<>(account));
        for (PaymentAccount paymentAccount : list) {
            paymentAccount.setStatus(PaymentConstant.VALID_CODE);
            paymentAccount.setEffectivity(PaymentConstant.CODE_0);
            paymentAccount.setRemark(PaymentConstant.CRM_REVOCATION);
        }
        boolean b = paymentAccountService.updateBatchById(list);
        stopWatch.stop();
        log.info("撤销补齐委托书-接口耗时为:{}", stopWatch.getTotalTimeMillis());
        return JsonResult.success();
    }

    /***
     * <AUTHOR>
     * @date 2021/11/25 10:08
     * @param paymentAccount 入参
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.COMMON, name = "账务中心置无效账号时付款账号信息删除")
    @PostMapping("/deleteAccount")
    public JsonResult deleteAccount(@RequestBody PaymentAccount paymentAccount) {
        log.info("账务中心置无效账号时付款账号信息删除CRM传参:{}", paymentAccount);
        if (StringUtils.isNotBlank(paymentAccount.getBankCardId())) {
            PaymentAccount account = new PaymentAccount();
            account.setBankCardId(paymentAccount.getBankCardId());
            boolean remove = paymentAccountService.remove(new QueryWrapper<>(account));
            log.info("账务中心置无效账号时付款账号信息:{},{}", paymentAccount.getBankCardId(), remove);
            return JsonResult.success();
        }
        return JsonResult.error(ResponseCode.PORTAL_5010);
    }

    /***
     * //查询委托书状态
     * <AUTHOR>
     * @date 2021/10/15 15:58
     * @param jsonObject 状态数据
     * @return com.cmhb.common.JsonResult
     */
    @Logger(module = Module.COMMON, name = "查询委托书状态")
    @PostMapping("/query/accountStatus")
    public JsonResult<Object> queryAccountStatus(@RequestBody JSONObject jsonObject) {
        try {
            return paymentAccountService.queryAccountStatus(jsonObject);
        } catch (Exception e) {
            log.error("查询委托书状态异常:{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }

    /***
     *   撤销处于待确认的委托书
     * <AUTHOR>
     * @date 2022/12/13 10:07
     * @param annulAccountVo 撤销参数
     * @return com.cmhb.common.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/revocation/account")
    public JsonResult<Object> revocationAccount(@RequestBody AnnulAccountDTO annulAccountVo) {
        log.info("撤销处于待确认的委托书入参:{}", annulAccountVo);
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("撤销处于待确认的委托书-接口");
            JsonResult<Object> jsonResult = paymentAccountService.repealAccount(annulAccountVo);
            stopWatch.stop();
            log.info("撤销处于待确认的委托书-接口耗时为:{}", stopWatch.getTotalTimeMillis());
            return jsonResult;
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("撤销处于待确认的委托书异常：{}", e.getMessage(), e);
            return JsonResult.error();
        }
    }
}