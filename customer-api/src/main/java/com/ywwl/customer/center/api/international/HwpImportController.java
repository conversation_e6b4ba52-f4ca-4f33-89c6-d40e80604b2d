package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.international.domain.ForecastDetail;
import com.ywwl.customer.center.modules.international.domain.ForecastRecord;
import com.ywwl.customer.center.modules.international.dto.ForecastExportDto;
import com.ywwl.customer.center.modules.international.enums.ForecastStatusEnum;
import com.ywwl.customer.center.modules.international.service.ForecastDetailService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.ForecastExportVo;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/11/4 15:00
 */
@Slf4j
@Controller
@EnableAsync
@RequestMapping("/forecastImport")
public class HwpImportController extends BaseController {
    @Autowired
    private PacketBusinessApplyService packetBusinessApplyService;
    @Autowired
    private ForecastDetailService forecastDetailService;

    /**
     * @Description 下载模板
     * <AUTHOR>
     * @Date 2020/11/4 15:01
     */
    @Logger(module = Module.HWP,name="下载海外派导入预报模板")
    @GetMapping("downLoadTemplate")
    public void downLoadTemplate(HttpServletResponse response) {
        ClassPathResource resource = new ClassPathResource("data/海外派客户确认发货导入预报.xlsx");
        InputStream in = null;
        try {
            // 设置强制下载不打开
            in = resource.getInputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("海外派客户确认发货导入预报.xlsx", "UTF-8"));
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                response.getOutputStream().write(buffer, 0, len);
            }
        } catch (IOException e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getOutputStream().write("下载模板失败".getBytes());
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @Description 读取excel
     * <AUTHOR>
     * @Date 2020/11/5 10:43
     */
    @Logger(module = Module.HWP,name="解析海外派导入预报excel文件")
    @RequestMapping("analysisExcel")
    public @ResponseBody
    JsonResult analysisExcel(MultipartFile attach, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult jsonResult = new JsonResult();
        try {
            String fileName = attach.getOriginalFilename();
            String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
            if (!".xlsx".equals(suffix)) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("按照模板上传文件");
                return jsonResult;
            }
            String merchantCode = getBusinessCode(currentUser.getUserCode());
            forecastDetailService.importForecastData(merchantCode, attach.getInputStream(), attach.getOriginalFilename());
            jsonResult.setSuccess(true);
            jsonResult.setMessage("数据处理中,请稍等...");
        } catch (Exception e) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("服务异常");
            log.error("导入预报数据出现异常:{}", e.getMessage());

        }
        return jsonResult;
    }

    /**
     * @Description 查询预报数据
     * <AUTHOR>
     * @Date 2020/11/6 10:20
     */
    @PostMapping("searchForecastList")
    public @ResponseBody
    JsonResult searchForecastList(@RequestBody JSONObject jsonObject, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult jsonResult = new JsonResult();
        try {
            String searchBatchId = jsonObject.getString("searchBatchId");
            String searchMainNo = jsonObject.getString("searchMainNo");
            String searchExpressNo = jsonObject.getString("searchExpressNo");
            Date searchStartDeliveryTime = jsonObject.getDate("searchStartDeliveryTime");
            Date searchEndDeliveryTime = jsonObject.getDate("searchEndDeliveryTime");
            Date searchStartTakeTime = jsonObject.getDate("searchStartTakeTime");
            Date searchEndTakeTime = jsonObject.getDate("searchEndTakeTime");
            Date searchStartArriveTime = jsonObject.getDate("searchStartArriveTime");
            Date searchEndArriveTime = jsonObject.getDate("searchEndArriveTime");
            Integer pageNo = jsonObject.getInteger("pageNo");
            Integer pageSize = jsonObject.getInteger("pageSize");
            Integer searchStatus = jsonObject.getInteger("searchStatus");
            if (StringUtils.isBlank(searchMainNo) && StringUtils.isBlank(searchExpressNo) && searchStartDeliveryTime == null && searchEndDeliveryTime == null && searchStartTakeTime == null && searchEndTakeTime == null && searchStartArriveTime == null && searchEndArriveTime == null && searchStatus == null) {
                jsonResult.setSuccess(false);
                jsonResult.setMessage("参数缺失");
                return jsonResult;
            }
            Map<String, Object> param = new HashMap<>(8);
            if (StringUtils.isNotBlank(searchBatchId)) {
                param.put("batchId", searchBatchId);
            }
            if (StringUtils.isNotBlank(searchMainNo)) {
                param.put("mainNo", searchMainNo);
            }
            if (StringUtils.isNotBlank(searchExpressNo)) {
                param.put("expressNo", searchExpressNo);
            }
            if (searchStartDeliveryTime != null && searchEndDeliveryTime != null) {
                param.put("searchStartDeliveryTime", searchStartDeliveryTime);
                param.put("searchEndDeliveryTime", searchEndDeliveryTime);
            }
            if (searchStartTakeTime != null && searchEndTakeTime != null) {
                param.put("searchStartTakeTime", searchStartTakeTime);
                param.put("searchEndTakeTime", searchEndTakeTime);
            }
            if (searchStartArriveTime != null && searchEndArriveTime != null) {
                param.put("searchStartArriveTime", searchStartArriveTime);
                param.put("searchEndArriveTime", searchEndArriveTime);
            }
            if (searchStatus != null && searchStatus != -1) {
                param.put("status", searchStatus);
            }
            if (pageNo == null) {
                pageNo = 1;
            }
            if (pageSize == null) {
                pageSize = 10;
            }
            String merchantCode = getBusinessCode(currentUser.getUserCode());
            param.put("merchantCode",merchantCode);
            param.put("pageStart", (pageNo - 1) * pageSize);
            param.put("pageSize", pageSize);
            Integer total = forecastDetailService.getTotal(param);
            List<ForecastDetail> forecastDetailList;
            if (total.equals(0)) {
                forecastDetailList = new ArrayList<>();
            } else {
                forecastDetailList = forecastDetailService.getForecastDetailList(param);
            }
            Map<String, Object> result = new HashMap<>(2);
            result.put("total", total);
            result.put("list", forecastDetailList);
            jsonResult.setData(result);
            jsonResult.setSuccess(true);
        } catch (Exception e) {
            log.error("查询导入预报出错:{}", e.getMessage());
            jsonResult.setMessage(e.getMessage());
        }
        return jsonResult;
    }

    /**
     * @Description 获取导入记录
     * <AUTHOR>
     * @Date 2021/1/21 14:33
     */
    @RequestMapping("getForecastRecords")
    public @ResponseBody JsonResult getForecastRecords(@RequestBody JSONObject jsonObject, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult jsonResult = new JsonResult();
        Integer pageNo = jsonObject.getInteger("pageNo");
        Integer pageSize = jsonObject.getInteger("pageSize");
        if (pageNo == null) {
            pageNo = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        String merchantCode = getBusinessCode(currentUser.getUserCode());
        Map<String, Object> param = new HashMap<>(3);
        param.put("merchantCode", merchantCode);
        param.put("pageStart", (pageNo - 1) * pageSize);
        param.put("pageSize", pageSize);
        List<ForecastRecord> forecastRecordList = forecastDetailService.getForecastRecordList(param);
        Long forecastRecordTotal = forecastDetailService.getForecastRecordTotal(merchantCode);
        Map<String, Object> result = new HashMap<>(2);
        result.put("total", forecastRecordTotal);
        result.put("list", forecastRecordList);
        jsonResult.setData(result);
        jsonResult.setSuccess(true);
        jsonResult.setData(result);
        return jsonResult;
    }

    /**
     * @Description 导出数据
     * <AUTHOR>
     * @Date 2021/1/22 17:16
     */
    @Logger(module = Module.HWP,name="导出海外派导入预报数据")
    @GetMapping("exportForecastData")
    public void exportForecastData(ForecastExportDto forecastExportDto, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) {
        //默认设置为0
        try {
            Map<String, Object> param = new HashMap<>(8);
            JsonResult jsonResult = validateExportParam(forecastExportDto, currentUser.getUserCode(), param);
            if (!jsonResult.getSuccess()) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().print(jsonResult.getMessage());
                return;
            }
            if (StringUtils.isNotBlank(forecastExportDto.getSearchBatchId())) {
                param.put("batchId", forecastExportDto.getSearchBatchId());
            }
            if (StringUtils.isNotBlank(forecastExportDto.getSearchMainNo())) {
                param.put("mainNo", forecastExportDto.getSearchMainNo());
            }
            if (StringUtils.isNotBlank(forecastExportDto.getSearchExpressNo())) {
                param.put("expressNo", forecastExportDto.getSearchExpressNo());
            }
            if (forecastExportDto.getSearchStartArriveTime() != null && forecastExportDto.getSearchEndArriveTime() != null) {
                param.put("searchStartArriveTime", forecastExportDto.getSearchStartArriveTime());
                param.put("searchEndArriveTime", forecastExportDto.getSearchEndArriveTime());
            }
            if (forecastExportDto.getSearchStartTakeTime() != null && forecastExportDto.getSearchEndTakeTime() != null) {
                param.put("searchStartTakeTime", forecastExportDto.getSearchStartTakeTime());
                param.put("searchEndTakeTime", forecastExportDto.getSearchEndTakeTime());
            }
            if (forecastExportDto.getSearchStartDeliveryTime() != null && forecastExportDto.getSearchEndDeliveryTime() != null) {
                param.put("searchStartDeliveryTime", forecastExportDto.getSearchStartArriveTime());
                param.put("searchEndDeliveryTime", forecastExportDto.getSearchEndDeliveryTime());

            }
            param.put("status", forecastExportDto.getSearchStatus());
            String temp = "导入预报失败数据_" + LocalDate.now();
            String fileName = URLEncoder.encode(temp, "UTF-8");
            JsonResult<List<ForecastExportVo>> result = forecastDetailService.getExportData(param);
            if (!result.getSuccess()) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                try {
                    response.getWriter().print(result.getMessage());
                } catch (IOException ioException) {
                    log.error("导出预报数据发生IO异常");
                }
                return;
            }
            List<ForecastExportVo> exportData = result.getData();
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ForecastExportVo.class).sheet("sheet1").doWrite(exportData);
        } catch (Exception e) {
            log.error("导出预报数据时候失败,原因:{}", e.getMessage());
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getWriter().print("导出预报数据发生错误");
            } catch (IOException ioException) {
                log.error("导出预报数据发生IO异常");
            }
        }
    }

    /**
     * @Description 校验导出数据参数
     * <AUTHOR>
     * @Date 2021/1/22 17:45
     */
    public JsonResult validateExportParam(ForecastExportDto forecastExportDto, String userCode, Map<String, Object> param) {
        JsonResult<Object> jsonResult=new JsonResult<>();
        String merchantCode = getBusinessCode(userCode);
        boolean empty = StringUtils.isEmpty(forecastExportDto.getSearchStartArriveTime());
        boolean empty1 = StringUtils.isEmpty(forecastExportDto.getSearchEndArriveTime());
        boolean empty2 = StringUtils.isEmpty(forecastExportDto.getSearchEndDeliveryTime());
        boolean empty3 = StringUtils.isEmpty(forecastExportDto.getSearchStartDeliveryTime());
        boolean empty4 = StringUtils.isEmpty(forecastExportDto.getSearchStartTakeTime());
        boolean empty5 = StringUtils.isEmpty(forecastExportDto.getSearchEndTakeTime());
        boolean empty6 = StringUtils.isEmpty(forecastExportDto.getSearchMainNo());
        boolean empty7 = StringUtils.isEmpty(forecastExportDto.getSearchExpressNo());
        boolean empty8 = forecastExportDto.getSearchStatus() == null;
        boolean empty9 = StringUtils.isEmpty(forecastExportDto.getSearchBatchId());
        if (empty && empty1 && empty2 && empty3 && empty4 && empty5 && empty6 && empty7 && empty8 && empty9) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("参数缺失");
            return jsonResult;
        }
        if (forecastExportDto.getSearchStatus() != ForecastStatusEnum.VALIDATE_FAIL.getValue()) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("请选择确认失败所对应的状态");
            return jsonResult;
        }
        param.put("merchantCode", merchantCode);
        jsonResult.setSuccess(true);
        return jsonResult;
    }

    /**
     * 批量删除数据
     *
     * @param currentUser
     * @return com.cmhb.common.JsonResult
     * <AUTHOR>
     * @date 2021/4/14 14:08
     */
    @Logger(module = Module.HWP,name="海外派预报数据批量删除")
    @RequestMapping("deleteDetails")
    @ResponseBody
    public JsonResult<Object> batchDelete(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody JSONObject jsonObject) {
        JsonResult<Object> jsonResult=new JsonResult<>();
        String merchantCode = getBusinessCode(currentUser.getUserCode());
        jsonObject.put("merchantCode", merchantCode);
        try {
            jsonResult = forecastDetailService.batchDelete(jsonObject);
        } catch (Exception e) {
            log.error("导入预报删除失败,原因:{}", e.getMessage());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("删除失败");
        }
        return jsonResult;
    }

    /**
     * @description 批量修改数据
     * <AUTHOR>
     * @date 2021/4/14 15:34
     */
    @Logger(module = Module.HWP,name="海外派预报数据批量修改")
    @RequestMapping("updateDetails")
    @ResponseBody
    public JsonResult batchUpdate(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody JSONObject jsonObject) {
        JsonResult<Object> jsonResult=new JsonResult<>();
        String merchantCode = getBusinessCode(currentUser.getUserCode());
        jsonObject.put("merchantCode", merchantCode);
        try {
            jsonResult = forecastDetailService.batchUpdate(jsonObject);
        } catch (Exception e) {
            log.error("导入预报修改失败,原因:{}", e.getMessage());
            jsonResult.setMessage("修改失败");
            jsonResult.setSuccess(false);
            if (e instanceof BusinessException) {
                jsonResult.setMessage(e.getMessage());
            }
        }
        return jsonResult;
    }

    /**
     * @description 确认发货
     * <AUTHOR>
     * @date 2021/4/15 13:59
     */
    @Logger(module = Module.HWP,name="海外派预报数据确认发货")
    @RequestMapping("confirmSend")
    @ResponseBody
    public JsonResult<Object> confirmSend(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, String batchId) {
        JsonResult<Object> jsonResult=new JsonResult<>();
        String merchantCode = getBusinessCode(currentUser.getUserCode());
        if (StringUtils.isBlank(batchId)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("参数异常");
            return jsonResult;
        }
        try {
            return forecastDetailService.confirmSend(batchId, merchantCode);
        } catch (Exception e) {
            log.error("导入预报确认发货出现异常,原因:{}", e.getMessage());
            jsonResult.setMessage("确认发货失败");
            jsonResult.setSuccess(false);
            return jsonResult;
        }
    }

    /**
     * <AUTHOR>
     * @description 获取业务账号
     * @date 2023/4/10 11:09
     **/
    public String getBusinessCode(String userCode){
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        String merchantCode = packetApplyInfo.getMerchantCode();
        if(StringUtils.isBlank(merchantCode)){
            throw new BusinessException("业务账号为空");
        }
        return merchantCode;
    }
}
