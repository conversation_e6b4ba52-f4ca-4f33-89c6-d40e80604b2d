package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.auth.dto.CardAttachDTO;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAreaEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerTypeEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @author: dinghy
 * @date: 2023/4/6 14:22
 */
@RequestMapping("completeAttach")
@RestController
public class CompleteAttachController extends BaseController {
    @Resource
    private CustomerAuthService customerAuthService;

    /**
     * <AUTHOR>
     * @description 提交证件照信息
     * @date 2023/4/4 11:33
     **/
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交补齐证件照信息")
    @RequestMapping("submitAttach")
    public JsonResult<Object> submitAttach(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody CardAttachDTO cardAttachDTO) {
        cardAttachDTO.setUserCode(currentUser.getUserCode());
        Integer customerArea = cardAttachDTO.getCustomerArea();
        Integer customerType = cardAttachDTO.getCustomerType();
        if(customerArea!=null&&customerType!=null){
            if(CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea)&& CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)){
                if(StringUtils.isBlank(cardAttachDTO.getCardAttach())||StringUtils.isBlank(cardAttachDTO.getCardBackAttach())){
                    return JsonResult.error("请您上传身份证反面和正面");
                }
            }
        }
        customerAuthService.submitCustomerAttach(cardAttachDTO);
        return JsonResult.success();
    }
}
