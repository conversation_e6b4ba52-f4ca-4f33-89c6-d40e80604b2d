// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.international;

import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.international.dto.CalcPriceParamDTO;
import com.ywwl.customer.center.modules.international.service.CalcService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @Description
 * @Date 2020/8/18 16:46
 * @ModifyDate 2020/8/18 16:46
 * @Version 1.0
 */
@RestController
@RequestMapping("/web")
@Slf4j
public class CalcController extends BaseController {

    @Bean(name = "calc_pool", destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor pool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 设置线程池前缀：方便排查
        executor.setThreadNamePrefix("calc-");
        // 设置线程池的大小
        executor.setCorePoolSize(20);
        // 设置线程池的最大值
        executor.setMaxPoolSize(40);
        // 设置线程池的队列大小
        executor.setQueueCapacity(5000);
        // 设置线程最大空闲时间，单位：秒
        executor.setKeepAliveSeconds(3000);
        // 饱和策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return executor;
    }

    /**
     * 异步查询线程池
     */
    @Resource(name = "calc_pool")
    public ThreadPoolTaskExecutor calcPool;

    @Resource
    private CalcService service;

    @Resource
    private AccountService accountService;

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;


    @PostMapping("/chargePrice")
    @Valid
    public JsonResult<?> chargePrice(@RequestBody CalcPriceParamDTO calcPriceParam) {
        return service.calcAsync(getUser(), calcPriceParam);
    }

    /**
     * 海外价格计算接口
     *
     * @param calcPriceParam 价格计算参数DTO
     * @return 包含计算结果的JsonResult对象
     */
    @PostMapping("/chargePriceOverseas")
    public JsonResult<?> chargePriceOverseas(@RequestBody CalcPriceParamDTO calcPriceParam) {
        // 1. 获取当前登录用户信息
        final UserAgent user = getUser();

        // 2. 参数校验
        if (StrUtil.isBlank(calcPriceParam.getCustomerCode())) {
            return JsonResult.error("客户编码不能为空");
        }
        if (StrUtil.isBlank(calcPriceParam.getWeight())) {
            return JsonResult.error("重量不能为空");
        }

        // 3. 获取客户账户信息
        final String customerCode = calcPriceParam.getCustomerCode();
        final AccountGetResVO account = accountService.getAccountByCustomerCode(customerCode);
        if (account == null) {
            return JsonResult.error("未找到对应的客户账户信息");
        }

        // 设置产品为海外派
        calcPriceParam.setProductTypes("12");

        // 4. 根据账户类型设置默认的发货地和目的地
        switch (account.getAccountType()) {
            case 2:  // 海外派
                calcPriceParam.setCityId("03");      // 发货地: 深圳
                calcPriceParam.setCountryId("350"); // 目的地: 法国
                break;
            case 4:  // YWE海外派
                calcPriceParam.setCountryId("115");  // 目的地: 美国
                break;
            default:
                return JsonResult.error("业务账号必须为海外派或YWE海外派");
        }

        // 5. 调用异步计算服务
        return service.calcAsync(user, calcPriceParam);
    }


    @PostMapping("/chargePriceSimple")
    @Logger(module = Module.EJF, name = "制单运价试算")
    @Valid
    public DeferredResult<JsonResult<?>> chargePriceSimple(@RequestBody CalcPriceParamDTO calcPriceParam) {
        DeferredResult<JsonResult<?>> asyncResult = new DeferredResult<>(30000L, JsonResult.error("处理超时，请重试！"));
        final String userCode = getUserCode();
        final PacketApplyInfoVo applyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        try {
            // 检测产品
            if (StringUtils.isBlank(calcPriceParam.getProductCode())) {
                asyncResult.setResult(JsonResult.error("产品不能为空"));
            }
            // 检测产品
            if ("0".equals(calcPriceParam.getWeight())) {
                asyncResult.setResult(JsonResult.error("重量不能为空"));
            }
            // 设置默认发货账号
            if (StringUtils.isBlank(calcPriceParam.getCustomerCode())) {
                calcPriceParam.setCustomerCode(applyInfo.getMerchantCode());
            }
            // 设置默认交货仓
            if (StringUtils.isBlank(calcPriceParam.getCityId())) {
                final AccountGetResVO account = accountService.getAccount(calcPriceParam.getCustomerCode());
                if (Objects.nonNull(account)) {
                    calcPriceParam.setCityId(account.getWarehouseCode());
                }
            }
            calcPool.submit(() -> service.callBatchTrial(calcPriceParam, asyncResult));
        } catch (RejectedExecutionException e) {
            asyncResult.setResult(JsonResult.error("系统过于繁忙，请稍后重试！"));
        }
        return asyncResult;
    }

}
