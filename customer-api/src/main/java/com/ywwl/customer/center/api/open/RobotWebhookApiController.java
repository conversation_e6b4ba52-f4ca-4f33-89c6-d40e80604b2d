package com.ywwl.customer.center.api.open;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.ejf.dto.TrackInfoDTO;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.general.cmcc.enums.WarehouseClassifyTypeEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import com.ywwl.customer.center.modules.general.plm.service.PLMService;
import com.ywwl.customer.center.modules.international.dto.CalcPriceParamDTO;
import com.ywwl.customer.center.modules.international.dto.ComputingCenterCalcPriceResultDTO;
import com.ywwl.customer.center.modules.international.service.CalcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/api/webhook")
@RestController
public class RobotWebhookApiController {

    public static final String result = "answerContent";
    public static final String regex = "\\b(?:(?=.*\\d)[A-Za-z0-9]{7,}|[0-9]{7,})\\b";
    public static final String splitRegex = "[\\p{IsHan}!@#$%^&*(),，+=\\-\\s]+";
    public static final String lastRegex = "^派送商查询[ \\t]*([A-Za-z0-9]+)$";

    public static final String message = "<p>您好，您提供的单号无法识别，请核对单号后再次查询。</p><p>（注：中邮单号、快线类单号、非燕文单号、已取消订单、错误单号、多个单号不支持查询）</p>";

    public static final String lastMessage = "<p>您好，您提供的单号无法识别，请核对单号后按照“派送商查询+单号”的格式输入查询。</p><p>（注：非燕文单号、已取消订单、错误单号、多个单号不支持查询）</p>";

    @Resource
    private OrderService orderService;
    @Resource
    private PLMService plmService;
    /**
     * 追踪验证
     */
    @Value("${track.Authorization}")
    String trackAuthorization;
    @Resource
    private CalcService calcService;
    @Resource
    private CmccService cmccService;

    /**
     * @author: dinghy
     * @createTime: 2025/2/25 16:11
     * @description: 查询运价试算结果
     */
    @RequestMapping("getCalculateResult")
    public JsonResult<?> getCalculateResult(@RequestBody JSONObject jsonObject) {
        JsonResult<?> result;
        try {
            CalcPriceParamDTO calcPriceParam = jsonObject.toJavaObject(CalcPriceParamDTO.class);
            String cityName = calcPriceParam.getCityName();
            if (StringUtils.isBlank(cityName)) {
                return JsonResult.error("出发城市不能为空");
            }
            List<WareHouseVo> warehouses = cmccService.getWarehouseByType(WarehouseClassifyTypeEnum.PACKET_WAREHOUSE.getType());
            Optional<WareHouseVo> any = warehouses.stream().filter(x -> x.getName().contains(cityName)).findAny();
            if (!any.isPresent()) {
                return JsonResult.error("未查询到对应出发城市");
            }
            WareHouseVo wareHouseVo = any.get();
            calcPriceParam.setCityId(wareHouseVo.getCode());
            String destination = calcPriceParam.getDestination();
            if (StringUtils.isBlank(destination)) {
                return JsonResult.error("目的国不能为空");
            }
            List<CountryResultDTO.RegionListDTO> country = plmService.getCountry();
            Optional<CountryResultDTO.RegionListDTO> desCountry = country.stream().filter(x -> x.getChinesename().contains(destination)).findAny();
            if (!desCountry.isPresent()) {
                return JsonResult.error("未查询到对应的目的国");
            }
            calcPriceParam.setCountryId(desCountry.get().getId());
            String weight = calcPriceParam.getWeight();
            if (StringUtils.isBlank(weight) || "0".equals(calcPriceParam.getWeight())) {
                return JsonResult.error("重量不能为空");
            }
            calcPriceParam.setCustomerCode("100000");
            JSONObject json = calcService.calcPrice(calcPriceParam);
            if (json != null) {
                JSONObject jsonObject1 = json.getJSONObject("result");
                if (jsonObject1 != null) {
                    JSONArray items = jsonObject1.getJSONArray("items");
                    if (items != null && items.size() > 0) {
                        items.forEach(x -> {
                            ComputingCenterCalcPriceResultDTO.DataDTO.CalcsDTO object = (ComputingCenterCalcPriceResultDTO.DataDTO.CalcsDTO) x;
                            object.setRemark(null);
                            object.setTopcRemarks(null);
                        });
                    }
                }
                result = JsonResult.success(json);
            } else {
                result = JsonResult.error();
            }
        } catch (Exception e) {
            return JsonResult.error("试算失败");
        }
        return result;
    }

    /**
     * @author: dinghy
     * @createTime: 2025/5/6 16:01
     * @description: 给中通天鸿提供查询运单数据
     */
    @RequestMapping("getTrackDetail")
    public JsonResult getTrackDetail(String questionContent) {
        JsonResult jsonResult = JsonResult.success();
        jsonResult.setCode("0");
        if (StringUtils.isBlank(questionContent)) {
            jsonResult.setMessage(message);
            return jsonResult;
        }
        // 定义正则表达式
        List<String> strings = extractMatchingStrings(questionContent);
        if (strings.isEmpty()) {
            jsonResult.setMessage(message);
            return jsonResult;
        }
        String waybillNumber = strings.get(0);
        TrackInfoDTO trackInfoDTO = orderService.getTrackInfoDTO(trackAuthorization, waybillNumber);
        TrackInfoDTO.ResultDTO resultDTO = trackInfoDTO.getResult().get(0);
        if (resultDTO.getTrackingStatus().equals("NOTFOUND")) {
            jsonResult.setMessage(message);
            return jsonResult;
        }
        List<TrackInfoDTO.ResultDTO.CheckpointsDTO> checkPoint = trackInfoDTO.getResult().get(0).getCheckpoints();
        if (checkPoint.isEmpty()) {
            jsonResult.setMessage(message);
            return jsonResult;
        }
        checkPoint = checkPoint.stream().sorted(Comparator.comparing(this::convertToUtc).reversed()).collect(Collectors.toList());
        JSONObject data = new JSONObject();
        data.put("waybillNumber", resultDTO.getWaybillNumber());
        data.put("exchangeNumber", resultDTO.getExchangeNumber());
        TrackInfoDTO.ResultDTO.CheckpointsDTO node = checkPoint.get(0);
        StringBuilder lastNode=new StringBuilder();
        lastNode.append(ObjectUtil.defaultIfNull(node.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(node.getTimeZone()).append("]").append(" ").append(node.getMessage());
        data.put("lastNode", lastNode.toString());
        data.put("lastNodeCode", node.getTrackingStatus());
        checkPoint.remove(0);
        StringBuilder deliveryInfo = new StringBuilder();
        for (int i = 0; i < checkPoint.size(); i++) {
            TrackInfoDTO.ResultDTO.CheckpointsDTO x = checkPoint.get(i);
            deliveryInfo.append("").append(ObjectUtil.defaultIfNull(x.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(x.getTimeZone()).append("]").append(" ").append(x.getMessage());
            if (i < checkPoint.size() - 1) {
                deliveryInfo.append("\\n");
            }
        }
        data.put("deliveryInfo", deliveryInfo);
        jsonResult.setData(data);
        return jsonResult;
    }


    @RequestMapping("getTrackInfo")
    public JSONObject getTrackInfo(String questionContent) {
        JSONObject jsonObject = new JSONObject();
        // log.info("机器人查询追踪信息，参数：{}", jsonObject);
        //  String questionContent = jsonObject.getString("questionContent");
        try {
            jsonObject.clear();
            if (StringUtils.isBlank(questionContent)) {
                jsonObject.put(result, "运单号不能为空");
                return jsonObject;
            }
            // 定义正则表达式
            List<String> strings = extractMatchingStrings(questionContent);
            if (!strings.isEmpty()) {
                // 提取捕获组的内容
                String waybillNumber = strings.get(0);
                TrackInfoDTO trackInfoDTO = orderService.getTrackInfoDTO(trackAuthorization, waybillNumber);
                TrackInfoDTO.ResultDTO resultDTO = trackInfoDTO.getResult().get(0);
                if (resultDTO.getTrackingStatus().equals("NOTFOUND")) {
                    jsonObject.put(result, message);
                    return jsonObject;
                }
                List<TrackInfoDTO.ResultDTO.CheckpointsDTO> checkPoint = trackInfoDTO.getResult().get(0).getCheckpoints();
                if (checkPoint.isEmpty()) {
                    jsonObject.put(result, message);
                    return jsonObject;
                }
                StringBuilder htmlBuilder = new StringBuilder();
                htmlBuilder.append("<div>")
                        .append("<p>").append("运单号：").append(resultDTO.getWaybillNumber()).append("</p>")
                        .append("<p>").append("转单号：").append(resultDTO.getExchangeNumber()).append("</p>")
                        .append("</div>");

                htmlBuilder.append("<div>");
                checkPoint = checkPoint.stream().sorted(Comparator.comparing(this::convertToUtc).reversed()).collect(Collectors.toList());
                checkPoint.forEach(x -> {
                    htmlBuilder.append("<p style=\"color: #808080\">").append("").append(ObjectUtil.defaultIfNull(x.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(x.getTimeZone()).append("]").append(" ").append(x.getMessage()).append("<p>");
                });
                htmlBuilder.append("</div>");
                jsonObject.put("code", 0);
                jsonObject.put("message", "ok");
                jsonObject.put("status", 0);
                JSONObject data = new JSONObject();
                data.put("waybillNumber", resultDTO.getWaybillNumber());
                data.put("exchangeNumber", resultDTO.getExchangeNumber());
                StringBuilder deliveryInfo = new StringBuilder();
                for (int i = 0; i < checkPoint.size(); i++) {
                    TrackInfoDTO.ResultDTO.CheckpointsDTO x = checkPoint.get(i);
                    deliveryInfo.append("").append(ObjectUtil.defaultIfNull(x.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(x.getTimeZone()).append("]").append(" ").append(x.getMessage());
                    if (i < checkPoint.size() - 1) {
                        deliveryInfo.append("\\n");
                    }
                }
                data.put("deliveryInfo", checkPoint);
                jsonObject.put("data", data);
                jsonObject.put(result, "0");
            } else {
                jsonObject.put(result, "-1");
            }
        } catch (Exception e) {
            log.error("webhook查询轨迹信息异常，运单号：{}，异常：", questionContent, e);
            jsonObject.put(result, "-1");
        }
        return jsonObject;
    }

    @RequestMapping("getLastDetail")
    public JsonResult getLastDetail(String questionContent) {
        JsonResult jsonResult = JsonResult.success();
        jsonResult.setCode("0");
        if (StringUtils.isBlank(questionContent)) {
            jsonResult.setMessage(lastMessage);
            return jsonResult;
        }
       //  定义正则表达式
//        Pattern pattern = Pattern.compile(lastRegex);
//        Matcher matcher = pattern.matcher(questionContent);
//        if (!matcher.find()) {
//            jsonResult.setMessage(lastMessage);
//            return jsonResult;
//        }
//        // 提取捕获组的内容
//        String waybillNumber = matcher.group(1);
//        if (StringUtils.isBlank(waybillNumber)) {
//            jsonResult.setMessage(lastMessage);
//            return jsonResult;
//        }
        String waybillNumber = questionContent;
        TrackInfoDTO trackInfoDTO = orderService.getTrackInfoDTO(trackAuthorization, waybillNumber);
        TrackInfoDTO.ResultDTO resultDTO = trackInfoDTO.getResult().get(0);
        if (resultDTO.getTrackingStatus().equals("NOTFOUND")) {
            jsonResult.setMessage(lastMessage);
            return jsonResult;
        }
        List<TrackInfoDTO.ResultDTO.CheckpointsDTO> checkPoint = trackInfoDTO.getResult().get(0).getCheckpoints();
        if (checkPoint.isEmpty()) {
            jsonResult.setMessage(lastMessage);
            return jsonResult;
        }
        StringBuilder htmlBuilder = new StringBuilder();
        String countryName = resultDTO.getDestinationCountry();
        if (StringUtils.isNotBlank(countryName)) {
            CountryResultDTO.RegionListDTO country = plmService.getCountryByCode(countryName);// baseInfoService.getCountryByCode(countryName);
            if (country != null) {
                countryName = country.getChinesename();
            }
        }

        htmlBuilder.append("运单号：").append(resultDTO.getWaybillNumber()).append("\n")
                .append("转单号：").append(resultDTO.getExchangeNumber()).append("\n")
                .append("目的国：").append(countryName).append("\n")
                .append("尾程派送商：").append(resultDTO.getLastMileCarrier()).append("\n")
                .append("派送商网站：").append(resultDTO.getLastMileCarrierWebsite()).append("\n")
                .append("派送商联系方式：").append(resultDTO.getLastMileCarrierContactNumber()).append("\n");
        JSONObject data = new JSONObject();
        data.put("info", htmlBuilder.toString());
        checkPoint = checkPoint.stream().sorted(Comparator.comparing(this::convertToUtc).reversed()).collect(Collectors.toList());
        data.put("waybillNumber", resultDTO.getWaybillNumber());
        data.put("exchangeNumber", resultDTO.getExchangeNumber());
        TrackInfoDTO.ResultDTO.CheckpointsDTO node = checkPoint.get(0);
        StringBuilder lastNode=new StringBuilder();
        lastNode.append(ObjectUtil.defaultIfNull(node.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(node.getTimeZone()).append("]").append(" ").append(node.getMessage());
        data.put("lastNode", lastNode.toString());
        checkPoint.remove(0);
        StringBuilder deliveryInfo = new StringBuilder();
        for (int i = 0; i < checkPoint.size(); i++) {
            TrackInfoDTO.ResultDTO.CheckpointsDTO x = checkPoint.get(i);
            deliveryInfo.append("").append(ObjectUtil.defaultIfNull(x.getTimeStamp().replace("T", " "), "").replace("T", " ")).append("[GMT").append(x.getTimeZone()).append("]").append(" ").append(x.getMessage());
            if (i < checkPoint.size() - 1) {
                deliveryInfo.append("\\n");
            }
        }
        data.put("deliveryInfo", deliveryInfo);
        jsonResult.setData(data);
        return jsonResult;
    }

    @RequestMapping("getLastInfo")
    public JSONObject getLastInfo(@RequestBody JSONObject jsonObject) {
        log.info("机器人查询尾程信息，参数：{}", jsonObject);
        String questionContent = jsonObject.getString("questionContent");
        try {
            jsonObject.clear();
            if (StringUtils.isBlank(questionContent)) {
                jsonObject.put(result, "参数缺失");
                return jsonObject;
            }
            // 定义正则表达式
            Pattern pattern = Pattern.compile(lastRegex);
            Matcher matcher = pattern.matcher(questionContent);
            if (matcher.find()) {
                // 提取捕获组的内容
                String waybillNumber = matcher.group(1);
                if (StringUtils.isBlank(waybillNumber)) {
                    jsonObject.put(result, lastMessage);
                    return jsonObject;
                }
                TrackInfoDTO trackInfoDTO = orderService.getTrackInfoDTO(trackAuthorization, waybillNumber);
                TrackInfoDTO.ResultDTO resultDTO = trackInfoDTO.getResult().get(0);
                if (resultDTO.getTrackingStatus().equals("NOTFOUND")) {
                    jsonObject.put(result, lastMessage);
                    return jsonObject;
                }
                List<TrackInfoDTO.ResultDTO.CheckpointsDTO> checkPoint = trackInfoDTO.getResult().get(0).getCheckpoints();
                if (checkPoint.isEmpty()) {
                    jsonObject.put(result, lastMessage);
                    return jsonObject;
                }
                StringBuilder htmlBuilder = new StringBuilder();
                String countryName = resultDTO.getDestinationCountry();
                if (StringUtils.isNotBlank(countryName)) {
                    CountryResultDTO.RegionListDTO country = plmService.getCountryByCode(countryName);// baseInfoService.getCountryByCode(countryName);
                    if (country != null) {
                        countryName = country.getChinesename();
                    }
                }
                htmlBuilder.append("<div>")
                        .append("<p>").append("运单号：").append(resultDTO.getWaybillNumber()).append("</p>")
                        .append("<p>").append("转单号：").append(resultDTO.getExchangeNumber()).append("</p>")
                        .append("<p>").append("目的国：").append(countryName).append("</p>")
                        .append("<p>").append("尾程派送商：").append(resultDTO.getLastMileCarrier()).append("</p>")
                        .append("<p>").append("派送商网站：").append("<a href=\"").append(resultDTO.getLastMileCarrierWebsite()).append("\" target=\"_blank\">").append(resultDTO.getLastMileCarrierWebsite()).append("</a>").append("</p>")
                        .append("<p>").append("派送商联系方式：").append(resultDTO.getLastMileCarrierContactNumber()).append("</p>")
                        .append("</div>");
                htmlBuilder.append("<div>");

                htmlBuilder.append("</div>");
                jsonObject.put(result, htmlBuilder);
            } else {
                jsonObject.put(result, lastMessage);
            }
        } catch (Exception e) {
            log.error("webhook查询轨迹信息异常，运单号：{}，异常：", questionContent, e);
            jsonObject.put(result, lastMessage);
        }
        return jsonObject;
    }

    /**
     * @author: dinghy
     * @createTime: 2024/10/24 14:14
     * @description: 抽取运单号
     */
    public List<String> extractMatchingStrings(String input) {
        String[] results = input.split(splitRegex);
        Pattern pattern = Pattern.compile(regex);
        List<String> list = new LinkedList<>();
        for (String result : results) {
            Matcher matcher = pattern.matcher(result);
            if (matcher.matches()) {
                list.add(matcher.group());
            }
        }
        return list;
    }


    /**
     * 将时间戳和时区转换为 UTC 时间
     */
    public LocalDateTime convertToUtc(Object object) {
        TrackInfoDTO.ResultDTO.CheckpointsDTO checkpointsDTO = (TrackInfoDTO.ResultDTO.CheckpointsDTO) object;
        String timeStamp = checkpointsDTO.getTimeStamp();
        String timeZone = checkpointsDTO.getTimeZone();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(timeStamp, formatter);

        // 解析时区
        ZoneOffset offset = ZoneOffset.of(timeZone);
        ZonedDateTime zonedDateTime = localDateTime.atOffset(offset).toZonedDateTime();

        // 转换为 UTC 时间
        return zonedDateTime.withZoneSameInstant(ZoneOffset.UTC).toLocalDateTime();
    }
}
