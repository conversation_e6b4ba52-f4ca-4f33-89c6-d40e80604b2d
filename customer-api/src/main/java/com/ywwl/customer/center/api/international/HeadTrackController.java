package com.ywwl.customer.center.api.international;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.common.utils.YwPointUtils;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.international.dto.CheckPointGroup;
import com.ywwl.customer.center.modules.international.dto.ExpressDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping(value = "headTrack")
@RestController
public class HeadTrackController {
    /**
     * 追踪URL
     */
    @Value("${track.temu}")
    String trackTemu;
    /**
     * 追踪验证
     */
    @Value("${track.Authorization}")
    String trackAuthorization;
    public static final String TRACKING_STATUS = "tracking_status";

    public static final String TRACKING_STATUS_WAYBILL = "tracking_status_waybill";
    private static final String KEY_NAME = "time_stamp";
    private static final String STANDARD_TIME = "standard_time";
    private static final String TIME_CREATE = "time_create";
    private static final String KEY_NAME_SOURCE = "time_stamp_source";
    private static final String KEY_NAME_SOURCE_TIME = "time_stamp_source_time";
    @Resource
    private CmccService cmccService;

    @RequestMapping("getTrackData")
    public JsonResult getTrackData(@RequestBody List<String> epCodes) {
        List<String> waybillNumbers = epCodes.stream().map(String::trim).distinct().collect(Collectors.toList());
        String codes = waybillNumbers.stream().collect(Collectors.joining(", "));
        return getHeadTrack(codes);
    }



    public JsonResult getHeadTrack(String waybillNumbers) {
        JSONObject jsonObject = HttpUtil.doGet(trackTemu  + waybillNumbers, ImmutableMap.of("Authorization", trackAuthorization), null, JSONObject.class);
        if(jsonObject==null){
            return JsonResult.error("调用追踪失败");
        }
        if(!jsonObject.getString("code").equals("0")){
            return JsonResult.error(jsonObject.getString("message"));
        }
        JSONArray jsonArray = jsonObject.getJSONArray("result");
        JSONObject json = json(jsonArray, "1");
       return JsonResult.success(json);
    }


    public JSONObject copyData(JSONArray result, JSONObject json) {
        //所有的单号
        List<Map<String, Object>> ywOrder = new ArrayList<>();
        //所有的单号详细轨迹
        List<Map<String, Object>> numbersParticularTrack = new ArrayList<>();
        //所有的单号概要轨迹
        List<Map<String, Object>> numbersOutlineTrack = new ArrayList<>();
        String unknown = "NOTFOUND";
        result.stream().forEach(object -> {
            JSONObject oderPoints = (JSONObject) object;
            if (!unknown.equals(oderPoints.getString(TRACKING_STATUS))) {
                StringBuilder parcelBuilder = new StringBuilder();
                String countryEn = oderPoints.getString("destination_country");
                String countryCn = oderPoints.getString("destinationCountryCN");
                StringBuilder OutlineTrackBuilder = new StringBuilder();
                List<JSONObject> checkpoints = oderPoints.getObject("checkpoints", List.class);
                String trackingNumber = oderPoints.getString("waybill_number");
                String exchangeNumber = oderPoints.getString("exchange_number");
                String orderNumber = oderPoints.getString("order_number");
                String status = oderPoints.getString("status");
                //封装运单号和尾程单号
                HashMap<String, Object> ywOrderMap = new HashMap<>(3);
                ywOrderMap.put("运单号", trackingNumber);
                ywOrderMap.put("尾程单号", exchangeNumber);
                ywOrderMap.put("订单号", orderNumber);
                ywOrderMap.put("状态", status);
                ywOrder.add(ywOrderMap);
                //所有的单号详细轨迹
                Map<String, Object> numbersParticularTrackMap = new HashMap<>(6);
                parcelBuilder.append(oderPoints.getString("status")).append(" (").append(oderPoints.getString("elapsedTime")).append("天)");
                numbersParticularTrackMap.put("运单号", trackingNumber);
                numbersParticularTrackMap.put("尾程单号", exchangeNumber);
                numbersParticularTrackMap.put("订单号", orderNumber);
                numbersParticularTrackMap.put("包裹状态", parcelBuilder);
                numbersParticularTrackMap.put("国家", countryEn);
                numbersParticularTrackMap.put("国家中文", countryCn);
                numbersParticularTrackMap.put("状态", status);
                List<StringBuilder> list = new ArrayList<>();
                for (int i = 0; i < checkpoints.size(); i++) {
                    StringBuilder trackBuilder = new StringBuilder();
                    String timeStamp = checkpoints.get(i).getString("time_stamp");
                    String location = checkpoints.get(i).getString("location");
                    String message = checkpoints.get(i).getString("message");
                    trackBuilder.append(timeStamp).append(" ").append(location).append(" ").append(message);
                    list.add(trackBuilder);
                }
                numbersParticularTrackMap.put("轨迹", list);
                numbersParticularTrack.add(numbersParticularTrackMap);
                //封装概要轨迹
                Map<String, Object> numbersOutlineTrackMap = new HashMap<>(5);
                numbersOutlineTrackMap.put("运单号", trackingNumber);
                numbersOutlineTrackMap.put("订单号", orderNumber);
                numbersOutlineTrackMap.put("尾程单号", exchangeNumber);
                numbersOutlineTrackMap.put("包裹状态", parcelBuilder);
                numbersOutlineTrackMap.put("国家", countryEn);
                numbersOutlineTrackMap.put("国家中文", countryCn);
                numbersOutlineTrackMap.put("状态", status);
                // 尾程信息
                final String lastMileCarrier = oderPoints.getString("last_mile_carrier");
                final String lastMileCarrierWebsite = oderPoints.getString("last_mile_carrier_website");
                final String lastMileCarrierContactNumber = oderPoints.getString("last_mile_carrier_contact_number");
                numbersOutlineTrackMap.put("Distributor", lastMileCarrier);
                numbersOutlineTrackMap.put("Distributor_Website", lastMileCarrierWebsite);
                numbersOutlineTrackMap.put("Contact_Number", lastMileCarrierContactNumber);
                String timeStamp = checkpoints.get(0).getString("time_stamp");
                String location = checkpoints.get(0).getString("message");
                OutlineTrackBuilder.append(timeStamp).append(" ").append(location);
                numbersOutlineTrackMap.put("概要轨迹", OutlineTrackBuilder);
                numbersOutlineTrack.add(numbersOutlineTrackMap);
            } else {
                String trackingNumber = oderPoints.getString("tracking_number");
                HashMap<String, Object> map = new HashMap<>(3);
                map.put("运单号", trackingNumber);
                map.put("尾程单号", "");
                map.put("状态", "查询不到");
                map.put("queryStatus", "1");
                ywOrder.add(map);
                Map<String, Object> numbersParticularTrackMap = new HashMap<>(6);
                numbersParticularTrackMap.put("运单号", trackingNumber);
                numbersParticularTrackMap.put("尾程单号", "");
                numbersParticularTrackMap.put("包裹状态", "");
                numbersParticularTrackMap.put("国家", "");
                numbersParticularTrackMap.put("轨迹", null);
                numbersParticularTrackMap.put("queryStatus", "1");
                numbersParticularTrackMap.put("状态", "查询不到");
                numbersParticularTrack.add(numbersParticularTrackMap);
                Map<String, Object> numbersOutlineTrackMap = new HashMap<>(6);
                numbersOutlineTrackMap.put("运单号", trackingNumber);
                numbersOutlineTrackMap.put("包裹状态", "");
                numbersOutlineTrackMap.put("国家", "");
                numbersOutlineTrackMap.put("概要轨迹", "");
                numbersOutlineTrackMap.put("queryStatus", "1");
                numbersOutlineTrackMap.put("状态", "查询不到");
                numbersOutlineTrack.add(numbersOutlineTrackMap);
            }
        });
        json.put("YwOrder", ywOrder);
        json.put("numbersParticularTrack", numbersParticularTrack);
        json.put("numbersOutlineTrack", numbersOutlineTrack);
        return json;
    }


    public JSONObject json(JSONArray result, String timeZone) {
        //根据时间排序
        sort(result, timeZone);
        //投递成功数据集合
        List<JSONObject> succeedYwOrder = new ArrayList<>();
        //投递失败数据集合
        List<JSONObject> failYwOrder = new ArrayList<>();
        //到达待取数据集合
        List<JSONObject> unclaimedYwOrder = new ArrayList<>();
        //运输途中数据集合
        List<JSONObject> transportingYwOrder = new ArrayList<>();
        //制单完成数据集合
        List<JSONObject> orderDoneYwOrder = new ArrayList<>();
        //追踪结束数据集合
        List<JSONObject> traceEndYwOrder = new ArrayList<>();
        //查询不到数据集合
        List<JSONObject> unknownYwOrder = new ArrayList<>();
        //正在派送数据集合
        List<JSONObject> outOfDeliveryYwOrder = new ArrayList<>();
        //包裹异常数据集合
        List<JSONObject> exceptionYwOrder = new ArrayList<>();
        //包裹退回数据集合
        List<JSONObject> returnedYwOrder = new ArrayList<>();

        //主数据获取标准节点信息
        Map<Object,Object> transportationInfos = cmccService.getTransportationInfo();

        String unknown = "NOTFOUND";
        result.forEach(object -> {
            JSONObject oderPoints = (JSONObject) object;
            String status = oderPoints.getString(TRACKING_STATUS);
            if (!unknown.equals(status)) {
                // 清除签收图片
                List<JSONObject> points = oderPoints.getJSONArray("checkpoints").toJavaList(JSONObject.class);
                for (JSONObject point : points) {
                    // 构建节点Key
                    point.put("key", getKey(point));
                    if (Objects.nonNull(point.getJSONObject("extraProperties"))) {
                        JSONObject extra = point.getJSONObject("extraProperties");
                        String images = extra.getString("Images");
                        // 如果有签收图片
                        if (StringUtils.isNotBlank(images)) {
                            point.put("confirm", true);
                            continue;
                        }
                    }
                    point.put("confirm", false);
                }
                JSONObject trackingStatusObj = oderPoints.getJSONObject(TRACKING_STATUS_WAYBILL);
                String trackingStatus;
                if(Objects.isNull(trackingStatusObj)){
                    trackingStatus = YwPointUtils.convertTransportStatus(status);
                }else {
                    trackingStatus = Optional.ofNullable(trackingStatusObj.getString("level1"))
                            .orElse(YwPointUtils.convertTransportStatus(status));
                }
                List<JSONObject> checkpoints = oderPoints.getObject("checkpoints", List.class);

                String destinationCountryCN = cmccService.getCountryChineseName(oderPoints.getString("destination_country"));
                String originCountryCN = cmccService.getCountryChineseName(oderPoints.getString("origin_country"));

                //单独处理目的国为空的情况
                oderPoints.put("destination_country",Optional.ofNullable(oderPoints.getString("destination_country")).orElse("Unknown"));
                oderPoints.put("destinationCountryCN", Optional.ofNullable(destinationCountryCN).orElse("未知"));


                oderPoints.put("originCountryCN", originCountryCN);

                String time1 = inputTime(oderPoints.getString("waybill_number"),checkpoints);
                String rangeNode = "";
                if (!checkpoints.isEmpty()) {
                    rangeNode = YwPointUtils.rangeNode(checkpoints, status,trackingStatus);
                }
                checkpoints.forEach(x -> {
                    String message = x.getString("message");
                    message = message.replace("<", "&lt;").replace(">", "&gt;");
                    x.put("message", message);
                });
                Long elapsedTime = null;
                //当为投递成功时,计算时间取节点的最新时间
                if (YwPointUtils.ywSendSuccess(trackingStatus) || YwPointUtils.ywSendFail(trackingStatus)
                        || YwPointUtils.trackStop(trackingStatus)) {
                    elapsedTime = time(time1, checkpoints.get(0).getString(KEY_NAME));
                } else {
                    elapsedTime = time(time1, null);
                }
                String message = formatTime(checkpoints.get(0).getString(KEY_NAME) + " [GMT"+checkpoints.get(0).getString("time_zone")+"] ") + checkpoints.get(0).getString("message");
                oderPoints.put("timeMessage", message);
                for (int i = 0; i < checkpoints.size(); i++) {
                    String formatTime = formatTime(checkpoints.get(i).getString(KEY_NAME));
                    String code = "2";
                    if (code.equals(timeZone)) {
                        checkpoints.get(i).put(KEY_NAME, formatTime);
                        checkpoints.get(i).put(KEY_NAME_SOURCE, formatTime);
                        checkpoints.get(i).put(KEY_NAME_SOURCE_TIME, formatTime.substring(formatTime.indexOf(" ")+1));
                    } else {
                        String timezone = (String) checkpoints.get(i).get("time_zone");
                        final String time = formatTime + " [GMT" + timezone + "]";
                        checkpoints.get(i).put(KEY_NAME, time);
                        checkpoints.get(i).put(KEY_NAME_SOURCE, formatTime);
                        checkpoints.get(i).put(KEY_NAME_SOURCE_TIME, time.substring(time.indexOf(" ")+1));
                    }

                }
                oderPoints.put("elapsedTime", elapsedTime);
                oderPoints.put("rangeNode", rangeNode);
            }
        });
        for (int i = 0; i < result.size(); i++) {
            String status = result.getJSONObject(i).getString(TRACKING_STATUS);

           JSONObject trackingStatusObj = (JSONObject)result.getJSONObject(i).get(TRACKING_STATUS_WAYBILL);
            String trackingStatus;
            if(Objects.isNull(trackingStatusObj)){
                trackingStatus = YwPointUtils.convertTransportStatus(status);
            }else {
                trackingStatus = Optional.ofNullable(trackingStatusObj.getString("level1"))
                        .orElse(YwPointUtils.convertTransportStatus(status));
            }

            if(Objects.isNull(transportationInfos.get(trackingStatus))){
                trackingStatus = YwPointUtils.convertTransportStatus(status);
            }

            JSONObject jsonObject = result.getJSONObject(i);
            //填充前端需要展示信息
            fillOrderInfo(transportationInfos,trackingStatus,jsonObject,status);
            if (YwPointUtils.ywSendSuccess(trackingStatus)) {
                succeedYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywSendFail(trackingStatus)) {
                failYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywNotGet(trackingStatus)) {
                unclaimedYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywTransporting(trackingStatus)) {
                transportingYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywSubmitOrderDone(trackingStatus)) {
                orderDoneYwOrder.add(jsonObject);
            } else if (YwPointUtils.trackStop(trackingStatus)) {
                traceEndYwOrder.add(jsonObject);
            } else if (YwPointUtils.outOfDelivery(trackingStatus)) {
                outOfDeliveryYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywBagException(trackingStatus)) {
                exceptionYwOrder.add(jsonObject);
            } else if (YwPointUtils.ywBagReturn(trackingStatus)) {
                returnedYwOrder.add(jsonObject);
            } else if (unknown.equals(status)) {
                unknownYwOrder.add(jsonObject);
            }
            JSONArray checkpointsArray = jsonObject.getJSONArray("checkpoints");
            if (Objects.nonNull(checkpointsArray)) {
                final List<ExpressDTO.CheckpointsDTO> checkpoints = jsonObject.getJSONArray("checkpoints")
                        .toJavaList(ExpressDTO.CheckpointsDTO.class);
                final Map<String, List<ExpressDTO.CheckpointsDTO>> checkPointMap =
                        checkpoints.stream().collect(Collectors.groupingBy(ExpressDTO.CheckpointsDTO::date));
                final List<CheckPointGroup> pointGroupList = MapUtil.sort(checkPointMap)
                        .entrySet()
                        .stream()
                        .map(x -> CheckPointGroup.builder().date(x.getKey()).checkpoints(x.getValue()).build())
                        .collect(Collectors.toList());
                jsonObject.put("checkpointGroups", CollectionUtil.reverse(pointGroupList));
                jsonObject.put("lastInfo", CollectionUtil.getFirst(checkpoints));
                jsonObject.put("additional", false);
                final List<String> statusList = checkpoints.stream()
                        .map(ExpressDTO.CheckpointsDTO::getTrackingStatus).collect(Collectors.toList());
                final String lastMileCarrier = jsonObject.getString("last_mile_carrier");
                final String lastMileCarrierWebsite = jsonObject.getString("last_mile_carrier_website");
                final String lastMileCarrierContactNumber = jsonObject.getString("last_mile_carrier_contact_number");
                if (StrUtil.isNotBlank(lastMileCarrier) ||
                        StrUtil.isNotBlank(lastMileCarrierWebsite) ||
                        StrUtil.isNotBlank(lastMileCarrierContactNumber)) {
                    jsonObject.put("additional", true);
                }
                // 设置最新节点状态
                if (statusList.contains("LM40")) jsonObject.put("statusStep", "5");
                else if (statusList.contains("LH40")) jsonObject.put("statusStep", "4");
                else if (statusList.contains("LH30")) jsonObject.put("statusStep", "3");
                else if (statusList.contains("LH20")) jsonObject.put("statusStep", "2");
                else if (statusList.contains("SC10")) jsonObject.put("statusStep", "1");
            }
        }
        String jsonString =JSON.toJSONString(result, JSONWriter.Feature.ReferenceDetection);
        JSONObject json = new JSONObject();
        json.put("succeedYwOrder", succeedYwOrder);
        json.put("failYwOrder", failYwOrder);
        json.put("unclaimedYwOrder", unclaimedYwOrder);
        json.put("transportingYwOrder", transportingYwOrder);
        json.put("orderDoneYwOrder", orderDoneYwOrder);
        json.put("traceEndYwOrder", traceEndYwOrder);
        json.put("outOfDeliveryYwOrder", outOfDeliveryYwOrder);
        json.put("exceptionYwOrder", exceptionYwOrder);
        json.put("returnYwOrder", returnedYwOrder);
        json.put("unknownYwOrder", unknownYwOrder);
        final Object parse = JSON.parse(jsonString);
        json.put("allYwOrder", parse);
        json.put("ywTransInfos",transportationInfos);
        return json;
    }


    public static Long time(String time, String now) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeEnd = formatTime(time);

        try {
            Date beginTime;
            Date nowNode;
            final BigDecimal divisor = BigDecimal.valueOf(1000 * 60 * 60 * 24);
            if (now == null) {
                beginTime = df.parse(timeEnd);
                long newestMs = System.currentTimeMillis();
                Date date = new Date(newestMs);
                String format = df.format(date);
                Date nowTime = df.parse(format);
                long diff = nowTime.getTime() - beginTime.getTime();
                return Math.abs(BigDecimal.valueOf(diff)
                        .divide(divisor, 0, RoundingMode.UP)
                        .longValue());
            } else {
                //当为投递成功是取最新节点时间
                String nowNodeTime = formatTime(now);
                beginTime = df.parse(timeEnd);
                nowNode = df.parse(nowNodeTime);
                long diff = nowNode.getTime() - beginTime.getTime();
                return Math.abs(BigDecimal.valueOf(diff)
                        .divide(divisor, 0, RoundingMode.UP)
                        .longValue());
            }
        } catch (ParseException e) {
        }
        return 0L;
    }


    /**
     * 根据主数据填充运单基础信息
     * @param ywTrans
     * @param ywTransStatus
     * @param obj
     * @return
     */
    public void fillOrderInfo(Map<Object,Object> ywTrans, String ywTransStatus, JSONObject obj, String status){
        if(!"NOTFOUND".equals(status)){
            JSONObject tBaseData = (JSONObject)ywTrans.get(ywTransStatus);
            obj.put("status", tBaseData.getString("nameZh"));
            obj.put("englishStatus", tBaseData.getString("nameEn"));
            obj.put("status_code", "status_0".concat(tBaseData.getString("code")));
        }else {
            obj.put("status", "查询不到");
            obj.put("englishStatus", "Not Found");
            obj.put("status_code", "status_01");
        }
    }


    public void sort(JSONArray result, String timeZone) {
        String unknown = "NOTFOUND";
        String code = "2";
        //为标准时区时进行时间换算 UTC标准时间 = 本地时间 - 时区
        result.forEach(object -> {
            JSONObject oderPoints = (JSONObject) object;
            if (!unknown.equals(oderPoints.getString(TRACKING_STATUS))) {
                List<JSONObject> checkpoints = oderPoints.getObject("checkpoints", List.class);
                for (int i = 0; i < checkpoints.size(); i++) {
                    Long time_zone = checkpoints.get(i).getLong("time_zone");
                    String time = formatTime(checkpoints.get(i).getString(KEY_NAME));
                    //转换类型进行加减时间
                    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime onDutyBegin = LocalDateTime.parse(time, dateTimeFormatter);
                    LocalDateTime localDateTime = onDutyBegin.minusHours(time_zone);
                    String format = dateTimeFormatter.format(localDateTime);
                    checkpoints.get(i).put(STANDARD_TIME, format);
                    if (code.equals(timeZone)) {
                        //替换成标准时区显示时间
                        checkpoints.get(i).put(KEY_NAME, format);
                    }
                }
            }
        });
        result.forEach(object -> {
            JSONObject oderPoints = (JSONObject) object;
            oderPoints.put("status", "");
            if (!unknown.equals(oderPoints.getString(TRACKING_STATUS))) {
                List<JSONObject> checkpoints = oderPoints.getObject("checkpoints", List.class);
                Collections.sort(checkpoints, new Comparator<JSONObject>() {
                    String string1;
                    String string2;

                    @Override
                    public int compare(JSONObject a, JSONObject b) {
                        try {
                            string1 = a.getString(STANDARD_TIME).replaceAll("[-T: ]", "");
                            string2 = b.getString(STANDARD_TIME).replaceAll("[-T: ]", "");
                        } catch (Exception e) {
                            e.getMessage();
                        }
                        return -(string1.compareTo(string2));
                    }
                });
                oderPoints.put("checkpoints",checkpoints);
            }
        });
    }

    public static String formatTime(Object time) {
        String format = String.valueOf(time).replaceAll("T", " ");
        // 去除毫秒数
        return format.split("\\.")[0];
    }

    private static String getKey(JSONObject point) {
        final String trackingStatus = point.getString("tracking_status");
        final String timeStamp = point.getString("time_stamp");
        return StringUtils.join(trackingStatus, timeStamp);
    }

    public String inputTime(String num, List<JSONObject> checkpoints) {
        String sc10 = "SC10";
        String sc10Time = "";
        String lm10 = "LM10";
        String lm10Time = "";
        for (int i = 0; i < checkpoints.size(); i++) {
            if (sc10.equals(checkpoints.get(i).getString("tracking_status"))) {
                sc10Time = checkpoints.get(i).getString("time_stamp");
            }
            if (lm10.equals(checkpoints.get(i).getString("tracking_status"))) {
                lm10Time = checkpoints.get(i).getString("time_stamp");
            }
        }
        return StrUtil.isNotBlank(sc10Time) ? sc10Time : lm10Time;
    }

}
