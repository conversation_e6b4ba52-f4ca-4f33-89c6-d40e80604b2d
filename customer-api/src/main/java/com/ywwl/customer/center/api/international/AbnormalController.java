package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import com.ywwl.customer.center.modules.international.dto.*;
import com.ywwl.customer.center.modules.international.listener.easyexcel.EasyExcelListener;
import com.ywwl.customer.center.modules.international.service.AbnormalWorkOrderService;
import com.ywwl.customer.center.modules.international.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * // 问题件
 *
 * <AUTHOR>
 * @date 2023/3/10
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/abnormal")
public class AbnormalController extends BaseController {
    @Resource
    private AbnormalWorkOrderService abnormalWorkOrderService;
    @Resource
    private AccountService accountService;

    /***
     * //查询
     * <AUTHOR>
     * @date 2023/3/13 13:39
     * @param currentUser 登录信息
     * @param abnormalListDTO  查询条件
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件列表")
    @PostMapping("/list")
    public JsonResult<Object> abnormalList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody AbnormalListDTO abnormalListDTO) {
        try {
            List<String> shippingAccounts = abnormalListDTO.getShippingAccounts();
            if (null != shippingAccounts && !shippingAccounts.isEmpty()) {
                if (!accountService.existAccount(shippingAccounts, currentUser, 0)) {
                    return JsonResult.error(ResponseCode.PORTAL_5060);
                }
            }
            abnormalListDTO.setUserCode(currentUser.getUserCode());
            return abnormalWorkOrderService.abnormalList(abnormalListDTO);
        } catch (Exception e) {
            log.info("问题件查询异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  历史工单
     * <AUTHOR>
     * @date 2023/3/13 13:40
     * @param currentUser 登录信息
     * @param workOrderDTO 查询条件
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.OldWorkOrderVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件历史工单")
    @PostMapping("/oldList")
    public JsonResult<OldWorkOrderVo> oldWorkOrder(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody OldWorkOrderDTO workOrderDTO) {
        if (!accountService.existAccount(workOrderDTO.getCustomerCode(), currentUser, 0)) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        return abnormalWorkOrderService.oldWorkOrder(workOrderDTO);
    }

    /***
     * //  异常处理方案配置
     * <AUTHOR>
     * @date 2023/3/13 13:39

     * @return com.ywwl.customer.center.common.domain.JsonResult<java.util.List < com.ywwl.customer.center.modules.international.vo.DisposeSchemeVo>>
     */
    @GetMapping("/getHandleMode")
    public JsonResult<List<DisposeSchemeVo>> getHandleMode() {
        return abnormalWorkOrderService.getHandleMode();
    }

    /***
     * //  所有异常原因
     * <AUTHOR>
     * @date 2023/3/13 14:34

     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.AbnormalCauseVo>
     */
    @GetMapping("/AllAbnormalCause")
    public JsonResult<AbnormalCauseVo> allAbnormalCause() {
        return abnormalWorkOrderService.allAbnormalCause();
    }

    /***
     * // 异常件处理
     * <AUTHOR>
     * @date 2023/3/15 14:15
     * @param currentUser 登录信息
     * @param abnormalHandleList 处理信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件处理", type = Type.WAYBILL_NUMBER, req = "v[1].waybillNumber")
    @PostMapping("/chooseHandleType")
    @Validated
    public JsonResult<Object> abnormalHandle(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody List<AbnormalHandleDTO> abnormalHandleList) {
        try {
            return abnormalWorkOrderService.abnormalHandle(currentUser, abnormalHandleList);
        } catch (BusinessException e) {
            log.error("异常件处理异常：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("异常件处理异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //异常件导出
     * <AUTHOR>
     * @date 2023/3/15 16:19
     * @param response 响应
     * @param abnormalDownloadDTO 参数
     * @param currentUser 登录信息
     * @return com.alibaba.fastjson2.JSONObject
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件导出")
    @PostMapping("/problemParcel/download")
    @Validated
    public JsonResult<Object> abnormalDownload(HttpServletResponse response, @RequestBody AbnormalDownloadDTO abnormalDownloadDTO,
                                               @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        return abnormalWorkOrderService.abnormalDownload(response, abnormalDownloadDTO, currentUser);
    }

    /***
     * //  文件导入
     * <AUTHOR>
     * @date 2023/3/16 9:48
     * @param file 文件
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件导入")
    @PostMapping("/problemParcel/uploading")
    public JsonResult<Object> abnormalImport(MultipartFile file, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            EasyExcel.read(file.getInputStream(), new EasyExcelListener(abnormalWorkOrderService, currentUser)).sheet().headRowNumber(2).doRead();
        } catch (BusinessException e) {
            log.error("导入异常件异常：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (IOException e) {
            log.error("导入异常件异常：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
        return JsonResult.success();
    }

    /***
     * // 页面进行修改信息
     * <AUTHOR>
     * @date 2023/3/16 11:08
     * @param list 修改数据
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件订单信息处理修改", type = Type.WAYBILL_NUMBER, req = "v[0].WaybillNumber")
    @PostMapping("/orderInformation")
    @Validated
    public JsonResult<Object> orderInformation(@Valid @RequestBody List<AbnormalInfoDTO> list, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return abnormalWorkOrderService.orderAlter(list, currentUser);
        } catch (BusinessException e) {
            log.error("修改信息失败：{}", e.getMessage());
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("修改信息失败：{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /***
     * //  查看异常件数量
     * <AUTHOR>
     * @date 2023/3/16 15:56
     * @param statisticsDTO 查询条件
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.StatisticsVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "问题件数量")
    @PostMapping("/statisticsNumber")
    @Validated
    public JsonResult<StatisticsVo> getStatisticsNumber(@RequestBody StatisticsDTO statisticsDTO) {
        statisticsDTO.setSourceIdS(AbnormalConstant.ABNORMAL_LIST_PLATFORM);
        try {
            return abnormalWorkOrderService.getStatisticsNumber(statisticsDTO);
        } catch (Exception e) {
            log.error("查看异常件数量异常", e);
            return JsonResult.success();
        }
    }

    /***
     * //修改寄件人税号
     * <AUTHOR>
     * @date 2023/11/2 16:30
     * @param list 税号入参
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.StatisticsVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "修改寄件人税号", type = Type.WAYBILL_NUMBER, req = "v.waybillNumber")
    @PostMapping("edit/taxationNumber")
    @Validated
    public JsonResult<Object> taxationNumber(@RequestBody List<TaxationNumberDTO> list) {
        list.stream().forEach(taxationNumberDTO -> {
            if (StringUtils.isBlank(taxationNumberDTO.getTaxationNumber()) && StringUtils.isBlank(taxationNumberDTO.getReceiverTaxNumber())) {
                throw new BusinessException("寄件人税号和收件人税号不能同时为空");
            }
        });
        return abnormalWorkOrderService.taxationNumber(list);
    }

    /***
     * //计算异常费用
     * <AUTHOR>
     * @date 2023/11/2 16:30
     * @param param 计算异常费用
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.international.vo.StatisticsVo>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "计算异常费用", type = Type.WAYBILL_NUMBER, req = "v.waybillNumber")
    @PostMapping("/computational")
    @Validated
    public JsonResult<Object> getComputational(@RequestBody List<AbnormalListVo.ListDTO> param) {
        try {
            List<AbnormalListVo.ListDTO> collect = param.stream().filter(listDTO -> AbnormalConstant.EXTRAORDINARY_CODE.contains(listDTO.getExceptionTypeId())).collect(Collectors.toList());
            List<CalcSimpleDTO> computational = abnormalWorkOrderService.getComputational(collect);
            return JsonResult.success(computational);
        } catch (Exception e) {
            log.error("计算费用异常", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }


    @Logger(module = Module.SMALL_PACKAGE, name = "查询退件地址", type = Type.WAYBILL_NUMBER, req = "v.waybillNumber")
    @PostMapping("/getAddress")
    @Validated
    public JsonResult<Object> getAddress(@RequestBody List<AbnormalAddressDTO> list, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return JsonResult.success(abnormalWorkOrderService.getAddress(list, currentUser.getUserCode()));
        } catch (Exception e) {
            log.error("查询退件地址", e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2024/6/12 11:10
     * @description: 查询品名匹配
     */
    @PostMapping("getMatchNameList")
    public JsonResult getMatchNameList(@JsonParam String name) {
        if(StringUtils.isBlank(name)){
            return JsonResult.success(new ArrayList<>(0));
        }
        List<MatchNameVo> list = abnormalWorkOrderService.getMatchNameList(name);
        return JsonResult.success(list);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/6/12 11:10
     * @description: 获取异常件操作日志
     */
    @RequestMapping("getOperaLog")
    public JsonResult getOperaLog(@JsonParam String workOrderNo){
        if(StringUtils.isBlank(workOrderNo)){
            return JsonResult.success(new ArrayList<>(0));
        }
       List<AbnormalLogVo> list=abnormalWorkOrderService.getOperaLog(workOrderNo);
       return JsonResult.success(list);
    }


}
