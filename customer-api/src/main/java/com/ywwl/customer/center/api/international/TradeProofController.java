package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.dto.TradeHandleDTO;
import com.ywwl.customer.center.modules.international.dto.TradeProofQueryDTO;
import com.ywwl.customer.center.modules.international.service.TradeProofService;
import com.ywwl.customer.center.modules.international.vo.TradeProofListVO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @author: dinghy
 * @createTime: 2024/7/16 9:44
 * @description: 爱尔兰交易证明
 */
@RequestMapping("tradeProof")
@RestController
public class TradeProofController extends BaseController {
    @Resource
    private TradeProofService tradeProofService;
    @Resource
    private AccountService accountService;

    /**
     * @author: dinghy
     * @createTime: 2024/7/16 10:28
     * @description: 查询列表
     */
    @PostMapping("list")
    public JsonResult list(@RequestBody TradeProofQueryDTO tradeProofQueryDTO){
        List<String> stringList = Arrays.asList(tradeProofQueryDTO.getCustomerCode().split(","));
        accountService.existAccountThrow(stringList);
        TradeProofListVO tradeProofListVO = tradeProofService.list(tradeProofQueryDTO);
        return JsonResult.success(tradeProofListVO);
    }
    
    /**
     * @author: dinghy 
     * @createTime: 2024/7/16 11:09
     * @description: 交易证明操作
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "交易证明客户操作", type = Type.WAYBILL_NUMBER, req = "v[0].WaybillNumber")
    @PostMapping("handleTradeProof")
    public JsonResult handleTradeProof(@RequestBody TradeHandleDTO tradeHandleDTO){
        tradeHandleDTO.setOperatorName(getUser().getLoginName());
        return tradeProofService.handleTradeProof(tradeHandleDTO);
    }

}
