package com.ywwl.customer.center.api.international;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.international.dto.TaxRecordDTO;
import com.ywwl.customer.center.modules.international.dto.TaxRecordQueryDTO;
import com.ywwl.customer.center.modules.international.dto.UpdateTaxSensitiveDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.TaxManageService;
import com.ywwl.customer.center.modules.international.vo.QueryTaxCodeVo;
import com.ywwl.customer.center.modules.international.vo.TaxManageVo;
import com.ywwl.customer.center.system.service.SmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @createTime: 2024/4/16 13:44
 * @description: 税号管理
 */
@RequestMapping("taxManage")
@RestController
public class TaxManageController extends BaseController {
    @Resource
    private TaxManageService taxManageService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private SmsService smsService;

    /**
     * @author: dinghy
     * @createTime: 2024/4/16 13:45
     * @description: 税号备案
     */
    @Logger(module = Module.TAX_RECORD, name = "税号备案添加")
    @RequestMapping("taxRecord")
    public JsonResult taxRecord(@RequestBody TaxRecordDTO taxRecordDTO) {
        UserAgent user = getUser();
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(user.getUserCode());
        taxRecordDTO.setMerchantCode(packetMerchantCode);
        taxRecordDTO.setCreateId(user.getLoginName());
        if (taxRecordDTO.getType().equals(1)) {
            if (StringUtils.isBlank(taxRecordDTO.getPlatform())) {
                return JsonResult.error("类型不能为空");
            }
        } else {
            if (StringUtils.isBlank(taxRecordDTO.getCountryCode())) {
                return JsonResult.error("国家不能为空");
            }
        }
        taxManageService.taxRecord(taxRecordDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/16 14:08
     * @description: 查询税号备案列表
     */
    @RequestMapping("listRecord")
    public JsonResult listRecord(@RequestBody TaxRecordQueryDTO taxRecordQueryDTO) {
        taxRecordQueryDTO.setMerchantCode(packetBusinessApplyService.getPacketMerchantCode(getUserCode()));
        TaxManageVo taxManageVo = taxManageService.listRecord(taxRecordQueryDTO);
        taxManageVo.getList().stream().forEach(x->{
            if(x.getSensitive()==1){
                x.setTaxId(PrivacyDimmer.maskTaxCode(x.getTaxId()));
            }
        });
        return JsonResult.success(taxManageVo);
    }


    /**
     * @author: dinghy
     * @createTime: 2024/4/25 15:53
     * @description: 删除税号备案
     */
    @Logger(module = Module.TAX_RECORD, name = "税号备案删除")
    @RequestMapping("dropTaxRecord")
    public JsonResult dropTaxRecord(@JsonParam List<String> taxIds, @JsonParam String code, HttpServletRequest request) {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        if (taxIds == null || taxIds.isEmpty() || StringUtils.isBlank(code)) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        smsService.checkSmsCode(request.getSession().getId(), user.getPhone(), code);
        taxManageService.dropTaxRecord(taxIds, getUser().getLoginName());
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/28 16:05
     * @description: 查询国家
     */
    @RequestMapping("getCountry")
    public JsonResult getCountry() {
        JSONObject country = taxManageService.getCountry();
        return JsonResult.success(country);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/30 14:38
     * @description: 查询有效税号
     */
    @RequestMapping("queryValidityList")
    public JsonResult queryValidityList(@Valid @NotBlank(message = "参数不能为空") @JsonParam Integer type, @JsonParam String keyword) {
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        List<TaxManageVo.Detail> details = taxManageService.queryValidityTaxCode(type, packetMerchantCode);
        List<QueryTaxCodeVo> list = details.stream().filter(x -> {
            if (StringUtils.isNotBlank(keyword)) {
                return x.getTaxId().contains(keyword);
            }
            return true;
        }).map(x->{
            QueryTaxCodeVo queryTaxCodeVo = new QueryTaxCodeVo();
            if(x.getSensitive()==1){
                queryTaxCodeVo.setSensitiveTax(PrivacyDimmer.maskTaxCode(x.getTaxId()));
            }else{
                queryTaxCodeVo.setSensitiveTax(x.getTaxId());
            }
            if(StringUtils.isNotBlank(x.getAlias())){
                queryTaxCodeVo.setSensitiveTax(queryTaxCodeVo.getSensitiveTax()+" "+x.getAlias());
            }
            queryTaxCodeVo.setTaxId(x.getTaxId());
            return queryTaxCodeVo;
        }).collect(Collectors.toList());
        return JsonResult.success(list);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/5/11 16:02
     * @description: 设置税号脱敏
     */
    @Logger(module = Module.TAX_RECORD,name = "修改税号脱敏")
    @RequestMapping("updateSensitive")
    public JsonResult updateSensitive(@RequestBody @Valid UpdateTaxSensitiveDTO updateTaxSensitiveDTO,HttpServletRequest request){
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        // 脱敏时候需要手机验证码
        if(updateTaxSensitiveDTO.getSensitive()==0){
            if(StringUtils.isBlank(updateTaxSensitiveDTO.getCode())){
                return JsonResult.error(ResponseCode.PORTAL_5010);
            }
            smsService.checkSmsCode(request.getSession().getId(), user.getPhone(), updateTaxSensitiveDTO.getCode());
        }
        updateTaxSensitiveDTO.setUpdateId(user.getLoginName());
        taxManageService.updateSensitive(updateTaxSensitiveDTO);
        return JsonResult.success();
    }
}
