package com.ywwl.customer.center.api.international;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.international.dto.ProductNameChangeHandleDTO;
import com.ywwl.customer.center.modules.international.dto.ProductNameChantQueryDTO;
import com.ywwl.customer.center.modules.international.service.ProductNameChangeService;
import com.ywwl.customer.center.modules.international.vo.ProductNameChangeExportVO;
import com.ywwl.customer.center.modules.international.vo.ProductNameChangeListVO;
import com.ywwl.customer.center.modules.international.vo.ProductNameChangeLogVO;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * @author: dinghy
 * @createTime: 2024/11/8 11:08
 * @description: 申报品名修改
 */
@RequestMapping("productNameChange")
@RestController
public class ProductNameChangeController extends BaseController {
    @Resource
    private ProductNameChangeService productNameChangeService;
    @Resource
    private AccountService accountService;

    /**
     * @author: dinghy
     * @createTime: 2024/11/8 11:11
     * @description: 查询列表字段
     */
    @PostMapping("queryList")
    public JsonResult queryList(@Valid @RequestBody ProductNameChantQueryDTO productNameChantQueryDTO) {
        accountService.existAccountThrow(Arrays.asList(productNameChantQueryDTO.getCustomerCode().split(",")));
        ProductNameChangeListVO productNameChangeListVO = productNameChangeService.queryList(productNameChantQueryDTO);
        return JsonResult.success(productNameChangeListVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/11/8 14:11
     * @description: 批量处理
     */
    @Logger(module = Module.PRODUCT_NAME_CHANGE, name = "品名工单批量处理")
    @PostMapping("batchHandle")
    public JsonResult batchHandle(@NotEmpty(message = "参数不能为空") @Valid @RequestBody List<ProductNameChangeHandleDTO> list) {
        return productNameChangeService.batchHandle(list, currentUser());
    }

    @Logger(module = Module.PRODUCT_NAME_CHANGE, name = "品名工单导出")
    @RequestMapping("exportData")
    public void exportData(@Valid @RequestBody ProductNameChantQueryDTO productNameChantQueryDTO, HttpServletResponse response) throws IOException {
        productNameChantQueryDTO.setExport(1);
        accountService.existAccountThrow(Arrays.asList(productNameChantQueryDTO.getCustomerCode().split(",")));
        ProductNameChangeListVO productNameChangeListVO = productNameChangeService.queryList(productNameChantQueryDTO);
        HttpUtil.setDownloadHeader(response, "品名工单导出数据" + LocalDate.now() + ".xlsx", HttpUtil.DOWNLOAD, "UTF-8", null);
        List<ProductNameChangeListVO.DataDTO> list = productNameChangeListVO.getList();
        List<ProductNameChangeExportVO> data = new LinkedList<>();
        for (ProductNameChangeListVO.DataDTO dataDTO : list) {
            ProductNameChangeExportVO productNameChangeExportVO = new ProductNameChangeExportVO();
            BeanUtils.copyProperties(dataDTO, productNameChangeExportVO);
            data.add(productNameChangeExportVO);
        }
        EasyExcel.write(response.getOutputStream(), ProductNameChangeExportVO.class).sheet("Sheet1").doWrite(data);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/11/15 17:15
     * @description: 查询操作日志
     */
    @PostMapping("getOperatorLog")
    public JsonResult getOperatorLog(@JsonParam String orderId) {
        if (StringUtils.isBlank(orderId)) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        ProductNameChangeLogVO productNameChangeLogVO = productNameChangeService.getOperatorLog(orderId);
        return JsonResult.success(productNameChangeLogVO);
    }
}
