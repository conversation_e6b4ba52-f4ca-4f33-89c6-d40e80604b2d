package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.bill.dto.*;
import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import com.ywwl.customer.center.modules.common.bill.service.InternationalBillService;
import com.ywwl.customer.center.modules.common.bill.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @date: 2023/4/18 17:05
 */
@Validated
@RestController
@RequestMapping("/bill")
public class BillController extends BaseController {
    @Resource
    private InternationalBillService internationalBillService;

    /**
     * <AUTHOR>
     * @description 获取账单余额
     * @date 2023/4/18 17:07
     **/
    @PostMapping("getBillBalance")
    public JsonResult<Object> getBillBalance(@JsonParam @NotNull(message = "业务类型不能为空") Integer businessType, @JsonParam String customerCode, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        PacketBalanceVO packetBalance = internationalBillService.getPacketBalance(getUser(), businessType, customerCode);
        return JsonResult.success(packetBalance);
    }

    /**
     * <AUTHOR>
     * @description 查询运单明细
     * @date 2023/4/18 17:46
     **/
    @RequestMapping("getNumberList")
    public JsonResult<Object> getNumberList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody QueryBillWaybillDTO queryBillWaybillDTO) {
        queryBillWaybillDTO.setUserCode(currentUser.getUserCode());
        List<String> waybillNumbers = queryBillWaybillDTO.getWaybillNumbers();
        if (waybillNumbers != null && !waybillNumbers.isEmpty()) {
            waybillNumbers.removeIf(StringUtils::isBlank);
            if (!waybillNumbers.isEmpty()) {
                List<String> collect = waybillNumbers.stream().map(String::trim).collect(Collectors.toList());
                queryBillWaybillDTO.setWaybillNumbers(collect);
            }
        }
        WaybillVO waybillVO = internationalBillService.getNumberList(queryBillWaybillDTO);
        return JsonResult.success(waybillVO);
    }

    /**
     * <AUTHOR>
     * @description 运单明细导出
     * @date 2023/4/19 11:03
     **/
    @Logger(module = Module.BILL, name = "下载运单明细")
    @PostMapping("waybillExport")
    public HttpEntity<?> waybillExport(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody QueryBillWaybillDTO queryBillWaybillDTO) {
        queryBillWaybillDTO.setUserCode(currentUser.getUserCode());
        return internationalBillService.waybillExport(queryBillWaybillDTO);
    }

    /**
     * <AUTHOR>
     * @description 查询账期
     * @date 2023/4/19 15:53
     **/
    @PostMapping("getAccountPeriodList")
    public JsonResult<Object> getAccountPeriodList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryAccountPeriodDTO queryAccountPeriodDTO) {
        queryAccountPeriodDTO.setUserCode(currentUser.getUserCode());
        AccountPeriodVo accountPeriod = internationalBillService.getAccountPeriods(queryAccountPeriodDTO);
        return JsonResult.success(accountPeriod);
    }

    /**
     * <AUTHOR>
     * @description 查询历史账单
     * @date 2023/4/20 15:14
     **/
    @PostMapping("historicalBill/list")
    public JsonResult<Object> getHistoricalBillList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryHistoryDTO queryHistoryDTO) {
        queryHistoryDTO.setUserCode(currentUser.getUserCode());
        HistoryBillVO historyBill = internationalBillService.getHistoryBill(queryHistoryDTO);
        return JsonResult.success(historyBill);
    }

    /**
     * <AUTHOR>
     * @description 根据类型查询账单
     * @date 2023/4/21 10:45
     **/
    @PostMapping("getHistoryListByType")
    public JsonResult<Object> getHistoryListByType(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryHistoryDTO queryHistoryDTO) {
        queryHistoryDTO.setUserCode(currentUser.getUserCode());
        HistoryBillVO historyBill = internationalBillService.getHistoryListByType(queryHistoryDTO);
        return JsonResult.success(historyBill);
    }

    /**
     * <AUTHOR>
     * @description 查询账单明细
     * @date 2023/4/21 13:47
     **/
    @PostMapping("/detail/list")
    public JsonResult<Object> getBillDetail(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryDetailDTO queryDetailDTO) {
        queryDetailDTO.setUserCode(currentUser.getUserCode());
        BillDetailVO billDetailVO = internationalBillService.getBillDetail(queryDetailDTO);
        return JsonResult.success(billDetailVO);
    }

    /**
     * <AUTHOR>
     * @description 账单详情导出
     * @date 2023/4/21 16:12
     **/
    @Logger(module = Module.BILL, name = "下载账单详情明细")
    @GetMapping("/detail/export")
    public void exportBillDetail(QueryDetailDTO queryDetailDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) {
        queryDetailDTO.setUserCode(currentUser.getUserCode());
        internationalBillService.exportDetail(queryDetailDTO, response);
    }

    /**
     * <AUTHOR>
     * @description 查询未出账单明细
     * @date 2023/4/21 16:21
     **/
    @PostMapping("/unBilledBillList/list")
    public JsonResult<Object> unBilledBillList(@RequestBody QueryUnBillDTO queryUnBillDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        queryUnBillDTO.setUserCode(currentUser.getUserCode());
        UnBilledVo unBilledVo = internationalBillService.getUnBilledBillList(queryUnBillDTO);
        return JsonResult.success(unBilledVo);
    }

    /**
     * @author: dinghy 
     * @createTime: 2025/5/15 18:04
     * @description: 未出账单支持查询税金明细
     */
    @PostMapping("/unBilledTaxBill/list")
    public JsonResult<Object> unBilledTaxBillList(@RequestBody QueryUnBillDTO queryUnBillDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser){
        queryUnBillDTO.setUserCode(currentUser.getUserCode());
        queryUnBillDTO.setOpCode(BillOpCodeEnum.UN_BILLED_TAX_BILL.value());
        UnBillTaxVO unBillTaxVO = internationalBillService.getUnBilledTaxBillList(queryUnBillDTO);
        return JsonResult.success(unBillTaxVO);
    }

    /**
     * <AUTHOR>
     * @description 未出账单明细导出
     * @date 2023/4/21 17:01
     **/
    @Logger(module = Module.BILL, name = "下载未出账单明细")
    @GetMapping("/unBilledBill/export")
    public void exportUnBillDetail(QueryUnBillDTO queryUnBillDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) {
        queryUnBillDTO.setUserCode(currentUser().getUserCode());
        internationalBillService.exportUnBill(queryUnBillDTO, response);
    }

    /**
     * <AUTHOR>
     * @description 查询派送翼的预扣款明细
     * @date 2023/4/27 17:21
     **/
    @PostMapping("/without/sendWinglist")
    @ResponseBody
    public JsonResult<Object> getSendWingBill(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryHwpBillDTO queryHwpBillDTO) {
        queryHwpBillDTO.setUserCode(currentUser.getUserCode());
        SendWingBillVO sendWingBillVO = internationalBillService.getSendWingBill(queryHwpBillDTO);
        return JsonResult.success(sendWingBillVO);
    }

    /**
     * <AUTHOR>
     * @description 下载海外派订单
     * @date 2023/4/28 10:01
     **/
    @Logger(module = Module.BILL, name = "下载海外派订单预扣款明细")
    @GetMapping("/sendWing/export")
    public void exportSendWingBill(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, HttpServletResponse response) {
        QueryHwpBillDTO queryHwpBillDTO = new QueryHwpBillDTO();
        queryHwpBillDTO.setUserCode(currentUser.getUserCode());
        internationalBillService.exportSendWingBill(queryHwpBillDTO, response);
    }

    /**
     * <AUTHOR>
     * @description 查询预扣款明细
     * @date 2023/4/28 10:21
     **/
    @PostMapping("/without/list")
    @ResponseBody
    public JsonResult<Object> withOutBillDetailList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryWithoutBillDTO queryWithoutBillDTO) {
        queryWithoutBillDTO.setUserCode(currentUser.getUserCode());
        WithoutBillVO withoutBillVO = internationalBillService.geWithoutBill(queryWithoutBillDTO);
        return JsonResult.success(withoutBillVO);
    }

    /**
     * <AUTHOR>
     * @description 导出预扣款明细
     * @date 2023/4/28 14:14
     **/
    @Logger(module = Module.BILL, name = "下载预扣款明细")
    @GetMapping("/without/export")
    public void withoutExport(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, String wayBillList, String startTime,String endTime,Integer businessType, String merchantCode, HttpServletResponse response) {
        QueryWithoutBillDTO queryWithoutBillDTO = new QueryWithoutBillDTO();
        queryWithoutBillDTO.setUserCode(currentUser.getUserCode());
        queryWithoutBillDTO.setStartCalcTime(startTime);
        queryWithoutBillDTO.setBusinessType(businessType);
        queryWithoutBillDTO.setMerchantCode(merchantCode);
        queryWithoutBillDTO.setEndCalcTime(endTime);
        if (StringUtils.isNotBlank(wayBillList)) {
            queryWithoutBillDTO.setWaybillNumbers(Arrays.asList(wayBillList.split(",")));
        }
        internationalBillService.exportWithoutBill(queryWithoutBillDTO, response);
    }

    /**
     * <AUTHOR>
     * @description 查询充值记录
     * @date 2023/5/6 13:57
     **/
    @RequestMapping("getChargeRecordList")
    @ResponseBody
    public JsonResult<Object> getChargeRecordList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody QueryHistoryDTO queryHistoryDTO) {
        queryHistoryDTO.setUserCode(currentUser.getUserCode());
        ChargeRecordVO chargeRecordVO = internationalBillService.getChargeRecordList(queryHistoryDTO);
        return JsonResult.success(chargeRecordVO);
    }

    /**
     * <AUTHOR>
     * @description 查询付款到期日, 查询应付金额
     * @date 2023/7/17 15:24
     **/
    @RequestMapping("getPayableAmount")
    public JsonResult<Object> getPayableAmount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        QueryAccountPeriodDTO queryAccountPeriodDTO = new QueryAccountPeriodDTO();
        queryAccountPeriodDTO.setChkStatus("2");
        queryAccountPeriodDTO.setUserCode(currentUser.getUserCode());
        PayableAmountVO payableAmountVO = null;
        try {
            payableAmountVO = internationalBillService.getPayableAmount(queryAccountPeriodDTO);
        } catch (Exception e) {
            payableAmountVO = new PayableAmountVO();
        }
        return JsonResult.success(payableAmountVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/2/28 17:19
     * @description: 查询电子账单
     */
    @RequestMapping("getEBillList")
    public JsonResult<Object> getEBillList(@RequestBody QueryEBillDTO queryEBillDTO) {
        queryEBillDTO.setUserCode(getUserCode());
        EBillVO eBillVO = internationalBillService.getEBillList(queryEBillDTO);
        return JsonResult.success(eBillVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/6 17:08
     * @description: 创建扫码支付订单并获取二维码
     */
    @RequestMapping("createAlipayScanOrder")
    public JsonResult createAlipayScanOrder(@Valid @RequestBody CreateScanOrderDTO createScanOrderDTO) {
        ScanOrderVO alipayScanOrder = internationalBillService.createAlipayScanOrder(createScanOrderDTO, getUser());
        return JsonResult.success(alipayScanOrder);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/6 17:10
     * @description: 查询订单状态
     */
    @RequestMapping("queryOrderState")
    public JsonResult queryOrderState(@JsonParam String outTradeNo) {
        return JsonResult.success(internationalBillService.queryOrderState(outTradeNo));
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/27 9:42
     * @description: 生成测试数据
     */
    @RequestMapping("generateExampleData")
    public JsonResult generateExampleData(@RequestBody BillExampleDTO billExampleDTO) {
        billExampleDTO.setUserCode(getUserCode());
        internationalBillService.generateExampleData(billExampleDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/27 10:32
     * @description: 添加接受账单配置
     */
    @Logger(module = Module.BILL, name = "添加接受账单配置")
    @RequestMapping("addReceiverConfig")
    public JsonResult addReceiverConfig(@Valid @RequestBody BillReceiverConfigDTO billReceiverConfigDTO) {
        billReceiverConfigDTO.setCurrentUser(getUser());
        internationalBillService.addReceiverConfig(billReceiverConfigDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/9/30 14:00
     * @description: 查询接受账单配置
     */
    @RequestMapping("queryReceiverConfig")
    public JsonResult queryReceiverConfig(@Valid @RequestBody QueryReceiverConfigDTO queryReceiverConfigDTO) {
        BillReceiveConfigVO billReceiveConfigVO = internationalBillService.queryReceiverConfig(queryReceiverConfigDTO, getUser());
        return JsonResult.success(billReceiveConfigVO);
    }


}
