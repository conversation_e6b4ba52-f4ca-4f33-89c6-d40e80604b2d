package com.ywwl.customer.center.api.international;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.international.dto.ProofBatchDownDTO;
import com.ywwl.customer.center.modules.international.dto.QueryProofPageDTO;
import com.ywwl.customer.center.modules.international.dto.SubmitProofApplyDTO;
import com.ywwl.customer.center.modules.international.service.DeliveryProofService;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.Objects;


/**
 * // 证明申请
 *
 * <AUTHOR>
 * @date 2023/3/30
 */
@Validated
@Slf4j
@RestController
@RequestMapping("/deliveryProof")
public class DeliveryProofController extends BaseController {
    @Resource
    private DeliveryProofService deliveryProofService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;

    /***
     * //  批量下载打印证明
     * <AUTHOR>
     * @date 2023/3/30 9:43
     * @param currentUser 登录信息
     * @param proofBatchDownDTO 下载参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @RequestMapping("batchDownloadProof")
    public JsonResult<Object> batchDownloadProof(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ProofBatchDownDTO proofBatchDownDTO) {
        proofBatchDownDTO.setMerchantNo(currentUser.getMerchantNo());
        proofBatchDownDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        String url = deliveryProofService.getBatchDownloadProofUrl(proofBatchDownDTO);
        return JsonResult.success(url);
    }

    /***
     * //  提交证明申请信息
     * <AUTHOR>
     * @date 2023/3/30 10:13
     * @param currentUser 登录名
     * @param submitProofApplyDTO 申请信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.SMALL_PACKAGE, name = "提交发货证明申请")
    @RequestMapping("submitProofApply")
    public JsonResult<Object> submitProofApply(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody SubmitProofApplyDTO submitProofApplyDTO) {
        if (submitProofApplyDTO.getProofType().equals(1) && StringUtils.isBlank(submitProofApplyDTO.getApplyReason())) {
            return JsonResult.error(ResponseCode.PORTAL_5042);
        }
        submitProofApplyDTO.setMerchantNo(currentUser.getMerchantNo());
        submitProofApplyDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        submitProofApplyDTO.setCreateId(getMerchantCode(currentUser.getUserCode()));
        deliveryProofService.submitProofApply(submitProofApplyDTO);
        return JsonResult.success();
    }

    /***
     * //  获取打印证明工单列标
     * <AUTHOR>
     * @date 2023/3/30 10:50
     * @param currentUser 登录信息
     * @param queryProofPageDTO 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @RequestMapping("getProofWorkOrderList")
    @ResponseBody
    public JsonResult<Object> getProofWorkOrderList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody QueryProofPageDTO queryProofPageDTO) {
        queryProofPageDTO.setMerchantNo(currentUser.getMerchantNo());
        queryProofPageDTO.setMerchantCode(getMerchantCode(currentUser.getUserCode()));
        Map<String, Object> res = deliveryProofService.queryPageList(queryProofPageDTO);
        return JsonResult.success(res);
    }
    public String getMerchantCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo.getMerchantCode();
    }
}
