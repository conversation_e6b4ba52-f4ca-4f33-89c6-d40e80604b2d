package com.ywwl.customer.center.web.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ywwl.customer.center.common.utils.JacksonObjectMapper;
import com.ywwl.customer.center.framework.config.JsonPathArgumentResolver;
import com.ywwl.customer.center.web.Interceptor.CustomerAuthInterceptor;
import com.ywwl.customer.center.web.Interceptor.PacketAndFbaInterceptor;
import com.ywwl.customer.center.web.Interceptor.PacketInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/9
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Bean
    public ObjectMapper jacksonObjectMapper() {
        return new JacksonObjectMapper();
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new JsonPathArgumentResolver());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //过滤
        registry.addInterceptor(customerAuthInterceptor()).excludePathPatterns("/plm/**", "/sms/**", "/customerAuth/*", "/index/**", "/api/**", "/subUser/**", "/forget/**", "/user/**", "/account/getAccountForAll", "/file/**", "/contract/**",
                "/login", "/logout", "/completeAttach/*", "/businessContact/*", "/sys/content/getHomePageNoticeList", "/feedback/complaintType", "/feedback/complaintReasons", "/feedback/list", "/feedback/complaintType", "/workOrder/inquiry/search",
                "/workOrder/getNumberId", "/workOrder/thaw/status", "/bill/**", "/settleBankAccount/**", "/workOrder/thaw/detail",
                "/quotation/**", "/index/paymentAccountStatus", "/payment/list", "/bank/thirdPaymentList","/bank/list",
                "/workOrder/invoiceApplication/invoicedMerchant","/invoice/search",
                "/invoice/selectInvoiceStatusNumber","/invoice/detail","/cusRead/*",
                "/packet/getBasePacketInfo","/sys/content/getHomePageNoticeList",
                "/sys/content/getUnReadNoticeCount","/sys/content/getNoticeSearchList","/operationGuide/viewTheEnd",
                "/sys/content/table","/sys/content/notification/record","/sys/content/getNotification","/help/url",
                "/singleSignOn","/index/unfreezeAccount","/messageSubscription/list","/messageSubscription/pushList","/fba/getCountryList","/operationGuide/doYouNeedToShow",
                "/packet/checkDeliveryPromiseStatus","/packet/checkBillServiceStatus","/im/getIMParam",
                "/oversea/**","/fileTemplate/**","/tradeProof/**","/ejf/order/getShippingAccount","/ejf/order/cancelOrderWithDebitPage"
        );
        //拦截
        registry.addInterceptor(packetInterceptor()).addPathPatterns("/deliveryProof/**", "/web/chargePrice",
                "/collect/pickup", "/crypto/encodeMerchantCode", "/feedback/save", "/workOrder/thaw/create",
                "/workOrder/inquiry/create");
        registry.addInterceptor(packetAndFbaInterceptor()).addPathPatterns("/payment/create","/messageSubscription/save");
    }

    @Bean
    public CustomerAuthInterceptor customerAuthInterceptor() {
        return new CustomerAuthInterceptor();
    }

    @Bean
    public PacketInterceptor packetInterceptor() {
        return new PacketInterceptor();
    }

    @Bean
    public PacketAndFbaInterceptor packetAndFbaInterceptor() {
        return new PacketAndFbaInterceptor();
    }
}
