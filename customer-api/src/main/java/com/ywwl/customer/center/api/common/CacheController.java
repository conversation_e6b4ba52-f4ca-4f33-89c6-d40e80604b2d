package com.ywwl.customer.center.api.common;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.EnumUtil;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/5/31 17:01
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/cache")
public class CacheController {

    /**
     * 缓存清理
     *
     * @param modules 类型
     * @return 结果
     */
    @PostMapping("/clean")
    public JsonResult<?> clear(
            @NotNull(message = "modules不能为空")
            @Size(min = 1, message = "modules不能为空")
            @RequestBody List<String> modules) {
        CollectionUtil.emptyIfNull(modules)
                .stream()
                .map(key -> EnumUtil.getBy(CacheKeyEnum::getKey, key))
                .filter(Objects::nonNull)
                .forEach(cache -> {
                    CacheUtil.ehCache().delete(cache);
                    CacheUtil.redis().delete(cache);
                });
        return JsonResult.success("已清理");
    }

    /**
     * 缓存获取
     *
     * @param module 类型
     * @param key    key
     * @return 结果
     */
    @PostMapping("/get")
    public JsonResult<?> get(@JsonParam @NotBlank(message = "module不能为空") String module,
                               @JsonParam @NotBlank(message = "key不能为空") String key) {
        final CacheKeyEnum moduleEnum = EnumUtil.getBy(CacheKeyEnum::getKey, module);
        if (Objects.nonNull(moduleEnum)) {
            final Map<String, Object> result = MapUtil.ofEntries(
                    MapUtil.entry("redis", CacheUtil.redis().getValue(moduleEnum, key)),
                    MapUtil.entry("ehcache", CacheUtil.ehCache().getValue(moduleEnum, key))
            );
            return JsonResult.success(result);
        } else {
            return JsonResult.error("类型不存在");
        }
    }

}
