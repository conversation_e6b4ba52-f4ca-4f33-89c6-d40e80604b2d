package com.ywwl.customer.center.api;


import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.JsonUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.web.bind.annotation.ModelAttribute;

import java.util.Objects;


/**
 * 通用属性保存在session
 *
 * <AUTHOR>
 * @date 2018/5/26
 */

public abstract class BaseController {
    /**
     * session key
     * 当前用户
     */
    public static final String CURRENT_USER = "currentUser";

    @ModelAttribute(CURRENT_USER)
    public UserAgent currentUser() {
        return getUser();
    }

    /**
     * 获取用户
     * @return  当前登录用户信息
     */
    public UserAgent getUser() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            return JsonUtils.parse((String) principal, UserAgent.class);
        }
        return null;
    }

    /**
     * 获取MerchantNo
     * @return  MerchantNo
     */
    public String getMerchantNo() {
        final UserAgent user = getUser();
        if (Objects.nonNull(user)) {
            return user.getMerchantNo();
        }
        return null;
    }

    /**
     * 获取UserCode
     * @return  UserCode
     */
    public String getUserCode() {
        final UserAgent user = getUser();
        if (Objects.nonNull(user)) {
            return user.getUserCode();
        }
        return null;
    }
}
