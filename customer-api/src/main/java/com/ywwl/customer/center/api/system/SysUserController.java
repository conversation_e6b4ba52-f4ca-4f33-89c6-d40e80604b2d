package com.ywwl.customer.center.api.system;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.system.entity.SysUser;
import com.ywwl.customer.center.system.service.SysUserService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统用户获取控制器
 *
 * <AUTHOR>
 * @date 2023/02/16 17:31
 **/
@RestController
public class SysUserController {

	@Resource
	SysUserService sysUserService;

	@RequestMapping("/getUsers")
	public JsonResult<List<SysUser>> getUsers() {
		return JsonResult.success(sysUserService.getAllUsers());
	}

}
