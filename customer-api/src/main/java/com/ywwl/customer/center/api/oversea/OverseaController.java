package com.ywwl.customer.center.api.oversea;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.common.listener.easyexcel.FreezePaneHandler;
import com.ywwl.customer.center.common.utils.ExcelDownloadUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.dto.AccountEditReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.common.bill.dto.QueryOverseaRecordDTO;
import com.ywwl.customer.center.modules.common.bill.service.OverseaBillService;
import com.ywwl.customer.center.modules.common.bill.vo.OverseaCheckBillVO;
import com.ywwl.customer.center.modules.common.bill.vo.OverseaRecordVO;
import com.ywwl.customer.center.modules.common.provider.vo.PlmProductVo;
import com.ywwl.customer.center.modules.ejf.dto.CancelWaybillDTO;
import com.ywwl.customer.center.modules.ejf.dto.WaybillKeyDTO;
import com.ywwl.customer.center.modules.ejf.dto.WaybillKeysDTO;
import com.ywwl.customer.center.modules.ejf.entity.Order;
import com.ywwl.customer.center.modules.ejf.enums.SearchStatus;
import com.ywwl.customer.center.modules.ejf.service.BaseInfoService;
import com.ywwl.customer.center.modules.ejf.service.OrderService;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.ejf.vo.OrderVO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.overseas.dto.*;
import com.ywwl.customer.center.modules.overseas.enums.SelectTypeEnum;
import com.ywwl.customer.center.modules.overseas.service.OverseaService;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.*;
import com.ywwl.customer.center.modules.upload.entity.FileUploadRecord;
import com.ywwl.customer.center.modules.upload.enums.FileUploadRecordTypeEnum;
import com.ywwl.customer.center.modules.upload.service.FileUploadRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping("/oversea")
@RestController
public class OverseaController extends BaseController {

    /**
     * 海外派导出数据
     */
    private static String FILE_NAME = "海外派_{}.xlsx";

    @Resource
    private OverseaService overseaService;
    @Resource
    private OverseaBillService overseaBillService;
    @Resource
    private CrmService crmService;
    @Resource
    private FileUploadRecordService fileUploadRecordService;
    @Resource
    private AccountService accountService;
    @Resource
    private YWEOverseaService yWEOverseaService;
    @Resource
    private BaseInfoService baseInfoService;
    @Resource
    private OrderService orderService;

    /**
     * @author: dinghy
     * @createTime: 2024/2/23 10:23
     * @description: 查询海外派账号信息
     */
    @RequestMapping("getOverseaAccounts")
    public JsonResult getOverseaAccounts(Integer type) {
        if (type == null) {
            return JsonResult.error("参数缺失");
        }
        String userCode = getUserCode();
        List<OverseaAccountVO> overseaAccounts = overseaService.getOverseaAccounts(userCode,BusinessTypeEnum.OVERSEA.getValue());
        List<OverseaAccountVO> yewAccounts = overseaService.getOverseaAccounts(userCode,BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        overseaAccounts.addAll(yewAccounts);
        if (type == 1) {
            for (OverseaAccountVO overseaAccount : overseaAccounts) {
                BigDecimal overseaBalance = overseaBillService.getOverseaBalance(overseaAccount.getAccountCode());
                overseaAccount.setBalance(overseaBalance);
                if (overseaAccount.getExemptMoney() != null) {
                    overseaAccount.setAvailableBalance(overseaBalance.add(overseaAccount.getExemptMoney()));
                }
            }
        } else {
            for (OverseaAccountVO overseaAccount : overseaAccounts) {
                OverseaAccountEncryptVO overseaAccountEncrypt = crmService.getOverseaAccountEncrypt(overseaAccount.getAccountCode());
                overseaAccountEncrypt.setYanwenPublicKey(null);
                overseaAccount.setEncryptInfo(overseaAccountEncrypt);
            }

        }
        return JsonResult.success(overseaAccounts);

    }

    /**
     * @author: dinghy
     * @createTime: 2024/2/23 11:03
     * @description: 查询海外派商户信息
     */
    @GetMapping("getOverseaMerchantInfo")
    public JsonResult getOverseaMerchantInfo() {
        OverseaMerchantVO overseaMerchantInfo = overseaService.getOverseaMerchantInfo(getUserCode());
        YWEApplyInfoVo yweApplyAllInfoVo = yWEOverseaService.getYWEApplyAllInfoVo(getUserCode());
        if (yweApplyAllInfoVo != null) {
            yweApplyAllInfoVo.setOfficeProvinceName(yweApplyAllInfoVo.getOfficeProvinceName());
            yweApplyAllInfoVo.setOfficeCityName(yweApplyAllInfoVo.getOfficeCityName());
            yweApplyAllInfoVo.setOfficeAreaName(yweApplyAllInfoVo.getOfficeAreaName());
            yweApplyAllInfoVo.setOfficeAddress(yweApplyAllInfoVo.getOfficeAddress());
        }
        return JsonResult.success(overseaMerchantInfo);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/2/23 14:50
     * @description: 重置或者查询秘钥
     */
    @Logger(module = Module.HWP, name = "查询或重置秘钥")
    @RequestMapping("getOrResetKey")
    public JsonResult getOrResetKey(@JsonParam String accountCode, @JsonParam Boolean reset) {
        if (accountCode == null || reset == null) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        UserAgent user = getUser();
        List<OverseaAccountVO> overseaAccounts = getNewAccountList(user.getUserCode());
        Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> accountCode.equals(x.getAccountCode())).findAny();
        if (!any.isPresent()) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
//        boolean b = accountService.existAccount(Arrays.asList(accountCode), user.getUserCode(), BusinessTypeEnum.OVERSEA.getValue());
//        if (!b) {
//            return JsonResult.error(ResponseCode.PORTAL_5060);
//        }
        if (reset) {
            AccountEditReqDTO accountEditReqDTO = new AccountEditReqDTO();
            accountEditReqDTO.setAccountCode(accountCode);
            accountEditReqDTO.setOperator(user.getLoginName());
            accountEditReqDTO.setType("updateApiToken");
            return accountService.editAccount(accountEditReqDTO);
        } else {
            AccountGetResVO account = accountService.getAccount(accountCode);
            if (account == null) {
                return JsonResult.error(ResponseCode.PORTAL_5033);
            }
            return JsonResult.success(account.getApiToken());
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/1 11:17
     * @description: 查询海外派交易记录
     */
    @RequestMapping("getOverseaRecord")
    public JsonResult getOverseaRecord(@Valid @RequestBody QueryOverseaRecordDTO queryOverseaRecordDTO) {
        String userCode = getUserCode();
        List<OverseaAccountVO> overseaAccounts = getNewAccountList(userCode);
        Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> x.getAccountCode().equals(queryOverseaRecordDTO.getAccountCode())).findAny();
        if (!any.isPresent()) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        if (SelectTypeEnum.RECORD.getValue().equals(queryOverseaRecordDTO.getSelectType())) {
            OverseaRecordVO overseaRecord = overseaBillService.getOverseaRecord(queryOverseaRecordDTO);
            return JsonResult.success(overseaRecord);
        }
        if (SelectTypeEnum.CHECK_BILL.getValue().equals(queryOverseaRecordDTO.getSelectType())) {
            OverseaCheckBillVO checkBillRecord = overseaBillService.getCheckBillRecord(queryOverseaRecordDTO);
            return JsonResult.success(checkBillRecord);
        }
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/11 14:09
     * @description: 获取退款申请列表
     */
    @RequestMapping("getRefundList")
    public JsonResult getRefundList(@RequestBody QueryOverseaRefundDTO queryOverseaRefundDTO) {
        queryOverseaRefundDTO.setUserCode(getUserCode());
        OverseaRefundVO overseaRefundVO = overseaService.getRefundList(queryOverseaRefundDTO);
        return JsonResult.success(overseaRefundVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/6/24 14:53
     * @description: 导出退款文件
     */
    @PostMapping("exportRefundData")
    public JsonResult exportRefundData(@RequestBody QueryOverseaRefundDTO queryOverseaRefundDTO) {
        queryOverseaRefundDTO.setUserCode(getUserCode());
        queryOverseaRefundDTO.setExport(1);
        OverseaRefundVO overseaRefundVO = overseaService.getRefundList(queryOverseaRefundDTO);
        List<OverseaRefundVO.OverseaRefundDetail> list = overseaRefundVO.getList();
        if (list == null) {
            list = new ArrayList<>();
        }

        String fileName = "海外派退款工单_" + queryOverseaRefundDTO.getAccountCode() + "_" + LocalDate.now();
        final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        List<OverseaRefundExportVO> data = new LinkedList<>();
        list.forEach(item -> {
            OverseaRefundExportVO dataChild = new OverseaRefundExportVO();
            dataChild.setAccountCode(item.getAccountCode());
            dataChild.setWaybillNumber(item.getWaybillNumber());
            dataChild.setOrderAmount(item.getReturnAmount());
            dataChild.setCreateTime(item.getCreateTime());
            dataChild.setApprovalTime(item.getApprovalTime());
            dataChild.setStateName(item.getStateName());
            dataChild.setRefundReason(item.getRefuseReason());
            data.add(dataChild);
        });
        List<String> ignoreList = new ArrayList<>(2);
        if ("1".equals(queryOverseaRefundDTO.getState()) || "2".equals(queryOverseaRefundDTO.getState())) {
            ignoreList.add("refundReason");
        }
        EasyExcel.write(outputStream, OverseaRefundExportVO.class).excludeColumnFieldNames(ignoreList).sheet("sheet1").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(data);
        final String base64 = EJFUtil.byteToString(outputStream);
        return JsonResult.success(ImmutableMap.of("base64", base64, "fileName", fileName + ".xlsx"));
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/11 14:29
     * @description: 创建退款工单
     */
    @Logger(module = Module.HWP, name = "创建退款工单")
    @PostMapping("createRefundOder")
    public JsonResult createRefundOrder(@RequestBody CreateOverseaRefundOrderDTO createOverseaRefundOrderDTO) {
        UserAgent user = getUser();
        createOverseaRefundOrderDTO.setUserName(user.getLoginName());
        createOverseaRefundOrderDTO.setUserCode(user.getUserCode());
        overseaService.createRefundOrder(createOverseaRefundOrderDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/11 14:48
     * @description: 获取海外派退款工单模版
     */
    @Logger(module = Module.HWP, name = "获取海外派退款工单模版")
    @RequestMapping("getOverseaRefundTemplate")
    public void getOverseaRefundTemplate(HttpServletResponse response) {
        List<FileUploadRecord> activeFileUploadRecord = fileUploadRecordService.getActiveFileUploadRecord(FileUploadRecordTypeEnum.OVERSEA_REFUND);
        String url = activeFileUploadRecord.isEmpty() ? "" : activeFileUploadRecord.get(0).getUrl();
        HttpUtil.download(url, response, "退款工单模版.xlsx", true);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/13 15:05
     * @description: 海外派批量处理退款工单
     */
    @Logger(module = Module.HWP, name = "海外派批量处理退款工单")
    @RequestMapping("batchHandleRefund")
    public JsonResult batchHandle(MultipartFile attach) {
        String fileName = attach.getOriginalFilename();
        String suffix = StringUtils.substring(fileName, fileName.lastIndexOf("."), fileName.length());
        if (!".xlsx".equals(suffix)) {
            return JsonResult.error("按照模板上传文件");
        }
        try {
            overseaService.analysisExcel(attach.getInputStream(), getUser());
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            return JsonResult.error("海外派批量处理退款工单异常");
        }
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/11 15:19
     * @description: 查询索赔工单列表
     */
    @RequestMapping("getClaimDamageList")
    public JsonResult getClaimDamageList(@RequestBody QueryOverseaClaimDamageDTO queryOverseaClaimDamageDTO) {
        queryOverseaClaimDamageDTO.setUserCode(getUserCode());
        OverseaClaimDamageVO overseaClaimDamageVO = overseaService.getClaimDamageList(queryOverseaClaimDamageDTO);
        return JsonResult.success(overseaClaimDamageVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/11 15:21
     * @description: 创建海外派索赔工单
     */
    @RequestMapping("createOverseaClaimDamageOrder")
    public JsonResult createOverseaClaimDamageOrder(@RequestBody CreateOverseaClaimDamageDTO createOverseaClaimDamageDTO) {
        createOverseaClaimDamageDTO.setUserCode(getUserCode());
        createOverseaClaimDamageDTO.setCreaterName(getUser().getLoginName());
        overseaService.createOverseaClaimDamageOrder(createOverseaClaimDamageDTO);
        return JsonResult.success();
    }

    @PostMapping("exportClaimOrder")
    public JsonResult exportClaimOrder(@RequestBody QueryOverseaClaimDamageDTO queryOverseaClaimDamageDTO) {
        queryOverseaClaimDamageDTO.setUserCode(getUserCode());
        queryOverseaClaimDamageDTO.setExport(1);
        OverseaClaimDamageVO overseaClaimDamageVO = overseaService.getClaimDamageList(queryOverseaClaimDamageDTO);
        final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<OverseaClaimDamageVO.ClaimDamageDetail> list = overseaClaimDamageVO.getList();
        List<ClaimExportVO> exportData = new LinkedList<>();

        list.forEach(item -> {
            ClaimExportVO dataChild = new ClaimExportVO();
            dataChild.setAccountCode(item.getAccountCode());
            dataChild.setWaybillNumber(item.getWaybillNumber());
            dataChild.setOrderId(item.getOrderId());
            dataChild.setNote(item.getNote());
            dataChild.setReason(item.getReason());
            dataChild.setStateName(item.getStateName());
            dataChild.setCreateTime(item.getCreateTime());
            dataChild.setOrderNumber(item.getOrderNumber());
            exportData.add(dataChild);
        });
        EasyExcel.write(outputStream, ClaimExportVO.class).sheet("sheet1").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(exportData);
        final String base64 = EJFUtil.byteToString(outputStream);
        String fileName = "索赔工单导出";
        return JsonResult.success(ImmutableMap.of("base64", base64, "fileName", fileName + ".xlsx"));
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/18 16:12
     * @description: 导出索赔模版
     */
    @GetMapping("exportClaimTemplate")
    public JsonResult exportClaimTemplate() {
        final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<ImportClaimDataDTO> data = new ArrayList<>();
        EasyExcel.write(outputStream, ImportClaimDataDTO.class).sheet("sheet1").registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).doWrite(data);
        final String base64 = EJFUtil.byteToString(outputStream);
        String fileName = "索赔工单模版";
        return JsonResult.success(ImmutableMap.of("base64", base64, "fileName", fileName + ".xlsx"));
    }

    /**
     * @author: dinghy
     * @createTime: 2025/4/18 16:14
     * @description: 导入数据
     */
    @PostMapping("importClaimData")
    public JsonResult importClaimData(MultipartFile file) {
        try {
            List<ImportClaimDataDTO> list = EasyExcel.read(file.getInputStream())
                    .sheet(0)
                    .headRowNumber(0)
                    .head(ImportClaimDataDTO.class)
                    .doReadSync();
            String userCode = getUserCode();
            String loginName = getUser().getLoginName();
            List<OverseaAccountVO> newAccountList = getNewAccountList(userCode);
            List<String> accounts = newAccountList.stream()
                    .map(OverseaAccountVO::getAccountCode)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                throw new BusinessException("文件内容不能为空");
            }
            list.remove(0);
            list.forEach(importClaimDataDTO -> {
                if (StringUtils.isBlank(importClaimDataDTO.getMerchantCode())) {
                    throw new BusinessException("业务账号不能为空");
                }
                if (StringUtils.isBlank(importClaimDataDTO.getWaybillNumber())) {
                    throw new BusinessException("运单号不能为空");
                }
                if (StringUtils.isBlank(importClaimDataDTO.getNote())) {
                    throw new BusinessException("索赔原因不能为空");
                }
                if (!accounts.contains(importClaimDataDTO.getMerchantCode())) {
                    throw new BusinessException("业务账号不存在");
                }
            });
            BatchClaimRequestDTO batchClaimRequestDTO = new BatchClaimRequestDTO();
            batchClaimRequestDTO.setDatas(list);
            batchClaimRequestDTO.setUserCode(userCode);
            batchClaimRequestDTO.setCreaterName(loginName);
            overseaService.batchClaim(batchClaimRequestDTO);
            return JsonResult.success();
        } catch (IOException e) {
            return JsonResult.error("文件解析失败");
        }
    }

    /**
     * 获取海外派列表
     *
     * @param param 参数
     * @return 海外派列表
     */
    @RequestMapping("getOversea")
    @Validated
    public JsonResult<?> getOversea(@RequestBody OverseaParamDTO param) {
        if (CollectionUtil.size(param.getListNumber()) > 500) {
            return JsonResult.error("订单/运单号搜索最多输入 500 个订单/运单号，多单号请以逗号、空格或回车隔开");
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            Set<String> waybillEnumKey = SearchStatus.getSearchStatus(param.getStatus()).getWaybillEnumKey();
            param.setListStatus(waybillEnumKey);
        }
        return JsonResult.success(overseaService.getOversea(param));
    }

    @Logger(module = Module.YWE_OVERSEA, name = "海外派取消运单", type = Type.WAYBILL_NUMBER, req = "waybillKeyList.waybillNumber")
    @RequestMapping("cancelOverseaOrder")
    public JsonResult cancelOverseaOrder(@RequestBody WaybillKeysDTO param) {
        boolean validated = overseaService.validateOverseaAccount(param.getUserId(), getUserCode());
        if (!validated) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        return orderService.cancelOrder(param);
    }

    @Logger(module = Module.YWE_OVERSEA, name = "海外派取消运单(扣款)", type = Type.WAYBILL_NUMBER, req = "waybillKeyList.waybillNumber")
    @RequestMapping("/cancelOverseaOrderWithDebitPage")
    @Valid
    public JsonResult<?> cancelOrder(@RequestBody CancelWaybillDTO param) {
        List<WaybillKeyDTO> keyList = param.getWaybillKeyList();
        // 验证权限
        List<String> userIds = keyList.stream()
                .map(WaybillKeyDTO::getUserId)
                .distinct().collect(Collectors.toList());
        accountService.existAccountThrow(userIds, BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        // 运单号
        List<OrderVO> vos = keyList.stream().map(orderService::getOrderVOById).collect(Collectors.toList());
        List<String> successWay = new ArrayList<>();
        List<String> errorWay = new ArrayList<>();
        final List<String> needList = Arrays.asList("0", "1");
        for (OrderVO vo : vos) {
            if (needList.contains(vo.getStatus())) {
                successWay.add(vo.getWaybillNumber());
            } else {
                errorWay.add(vo.getWaybillNumber());
            }
        }
        keyList = keyList.stream().filter(x -> successWay.contains(x.getWaybillNumber())).collect(Collectors.toList());
        JsonResult<Order> result = orderService.cancelOrder(keyList);
        if (CollectionUtil.isEmpty(errorWay)) {
            // 友好提示
            result.setMessage(org.springframework.util.StringUtils.replace(
                    result.getMessage(),
                    "取消运单失败，原因 此运单已处理",
                    "已取消，金额解冻中，请稍后再查询！"));
        } else {
            result.setMessage(errorWay + "非（已制单/已确认发货）状态，不支持取消。");
        }
        return result;
    }

    /**
     * 查询海外派详情
     *
     * @param param 参数
     * @return 详情
     */
    @RequestMapping("getOverseaDetail")
    @Validated
    public JsonResult<?> getOverseaDetail(@RequestBody OverseaDetailParamDTO param) {
        if (StringUtils.isBlank(param.getWaybillNumber())) {
            return JsonResult.error("运单号不能为空");
        }
        param.setListNumber(Collections.singletonList(param.getWaybillNumber()));
        return JsonResult.success(CollectionUtil.getFirst(overseaService.getOverseaDetail(param)));
    }

    /**
     * 导出海外派详情
     *
     * @param param 参数
     * @return 详情
     */
    @RequestMapping("exportOverseaDetail")
    @Validated
    public JsonResult<?> exportOverseaDetail(@RequestBody OverseaDetailParamDTO param) throws IOException {
        if (CollectionUtil.isEmpty(param.getListNumber())) {
            return JsonResult.error("运单号不能为空");
        }
        // 获取详情数据
        final List<OverseaDetailParamDTO> detailParams = param.split(49);
        final List<OverseaDetailDTO.DataDTO> allDetails = detailParams.stream()
                .map(detailParam -> overseaService.getOverseaDetail(detailParam))
                .flatMap(List::stream).collect(Collectors.toList());
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 策略
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        final Integer size = allDetails.stream().map(OverseaDetailDTO.DataDTO::getParcelInfo)
                .map(OverseaDetailDTO.ParcelInfo::getProductList)
                .filter(CollectionUtil::isNotEmpty)
                .map(CollectionUtil::size)
                .max(Comparator.comparingInt(Integer::intValue))
                .orElse(0);
        if (!CollectionUtils.isEmpty(allDetails)) {
            final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .sheet("运单信息")
                    .head(head(size))
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezePaneHandler(0, 1))
                    .doWrite(data(allDetails));
            final String base64 = EJFUtil.byteToString(outputStream);
            return JsonResult.success(ImmutableMap.of("base64", base64, "fileName", StrUtil.format(FILE_NAME, DateUtil.today())));
        }
        return JsonResult.error("数据为空");
    }

    /**
     * 获取表头
     *
     * @return 表头
     */
    private List<List<String>> head(Integer length) {
        List<List<String>> head = new ArrayList<>();
        head.add(ListUtil.of("业务账号"));
        head.add(ListUtil.of("运单号"));
        head.add(ListUtil.of("订单号"));
        head.add(ListUtil.of("产品名称"));
        head.add(ListUtil.of("包裹送到carrier的仓库名"));
        head.add(ListUtil.of("订单来源"));
        head.add(ListUtil.of("运单状态"));
        head.add(ListUtil.of("是否打印"));
        head.add(ListUtil.of("下单时间"));
        head.add(ListUtil.of("收件人姓名"));
        head.add(ListUtil.of("收件人公司"));
        head.add(ListUtil.of("收件人电话"));
        head.add(ListUtil.of("收件人手机"));
        head.add(ListUtil.of("收件人邮箱"));
        head.add(ListUtil.of("收件人国家"));
        head.add(ListUtil.of("收件人州"));
        head.add(ListUtil.of("收件人城市"));
        head.add(ListUtil.of("邮编"));
        head.add(ListUtil.of("收件人区"));
        head.add(ListUtil.of("收件人税号"));
        head.add(ListUtil.of("收件人地址1"));
        head.add(ListUtil.of("收件人地址2"));
        head.add(ListUtil.of("发件人姓名"));
        head.add(ListUtil.of("发件人电话"));
        head.add(ListUtil.of("发件人手机"));
        head.add(ListUtil.of("发件人公司"));
        head.add(ListUtil.of("发件人邮箱"));
        head.add(ListUtil.of("发件人国家"));
        head.add(ListUtil.of("发件人州"));
        head.add(ListUtil.of("发件人城市"));
        head.add(ListUtil.of("发件人区"));
        head.add(ListUtil.of("发件人门牌号"));
        head.add(ListUtil.of("发件人邮编"));
        head.add(ListUtil.of("发件人地址1"));
        head.add(ListUtil.of("发件人地址2"));
        head.add(ListUtil.of("退件人姓名"));
        head.add(ListUtil.of("退件人电话"));
        head.add(ListUtil.of("退件人手机"));
        head.add(ListUtil.of("退件人公司"));
        head.add(ListUtil.of("退件人邮箱"));
        head.add(ListUtil.of("退件人国家"));
        head.add(ListUtil.of("退件人州"));
        head.add(ListUtil.of("退件人城市"));
        head.add(ListUtil.of("退件人区"));
        head.add(ListUtil.of("退件人门牌号"));
        head.add(ListUtil.of("退件人邮编"));
        head.add(ListUtil.of("退件人地址1"));
        head.add(ListUtil.of("退件人地址2"));
        head.add(ListUtil.of("包裹总重量(g)"));
        head.add(ListUtil.of("总申报价值"));
        head.add(ListUtil.of("申报币种"));
        head.add(ListUtil.of("包裹高度"));
        head.add(ListUtil.of("包裹宽度"));
        head.add(ListUtil.of("包裹长度"));
        head.add(ListUtil.of("是否含电"));
        head.add(ListUtil.of("商品数量总和"));
        for (int i = 1; i <= length; i++) {
            head.add(ListUtil.of("中文品名" + i));
            head.add(ListUtil.of("英文品名" + i));
            head.add(ListUtil.of("单票数量" + i));
            head.add(ListUtil.of("重量(g)" + i));
            head.add(ListUtil.of("申报价值" + i));
            head.add(ListUtil.of("商品材质" + i));
            head.add(ListUtil.of("商品海关编码" + i));
            head.add(ListUtil.of("商品链接" + i));
        }
        return head;
    }


    /**
     * 获取数据
     *
     * @param details 源数据
     * @return 数据
     */
    public List<List<Object>> data(List<OverseaDetailDTO.DataDTO> details) {
        if (CollectionUtil.isEmpty(details)) {
            return Collections.emptyList();
        }
        return details.stream().map(dto -> {
            final List<Object> objects = new ArrayList<>();
            objects.add(dto.getUserId());
            objects.add(dto.getWaybillNumber());
            objects.add(dto.getOrderNumber());
            objects.add(dto.getChannelName());
            objects.add(dto.getInductionFacility());
            objects.add(dto.getOrderSource());
            objects.add(StrUtil.blankToDefault(EJFUtil.STATUS_MAP.get(dto.getStatus()), ""));
            objects.add(dto.getIsPrint() == 1 ? "是" : dto.getIsPrint() == 0 ? "否" : "");
            objects.add(dto.getCreateTime());
            OverseaDetailDTO.ReceiverInfo receiverInfo = dto.getReceiverInfo();
            if (Objects.isNull(receiverInfo)) {
                receiverInfo = new OverseaDetailDTO.ReceiverInfo();
            }
            objects.add(receiverInfo.getName());
            objects.add(receiverInfo.getCompany());
            objects.add(receiverInfo.getPhone());
            objects.add(receiverInfo.getMobile());
            objects.add(receiverInfo.getEmail());
            objects.add(receiverInfo.getCountryName());
            objects.add(receiverInfo.getState());
            objects.add(receiverInfo.getCity());
            objects.add(receiverInfo.getZipCode());
            objects.add(receiverInfo.getDistrict());
            objects.add(receiverInfo.getTaxNumber());
            objects.add(receiverInfo.getAddress1());
            objects.add(receiverInfo.getAddress2());
            OverseaDetailDTO.SenderInfo senderInfo = dto.getSenderInfo();
            if (Objects.isNull(senderInfo)) {
                senderInfo = new OverseaDetailDTO.SenderInfo();
            }
            objects.add(senderInfo.getName());
            objects.add(senderInfo.getPhone());
            objects.add(senderInfo.getMobile());
            objects.add(senderInfo.getCompany());
            objects.add(senderInfo.getEmail());
            objects.add(senderInfo.getCountryName());
            objects.add(senderInfo.getState());
            objects.add(senderInfo.getCity());
            objects.add(senderInfo.getDistrict());
            objects.add(senderInfo.getHouseNumber());
            objects.add(senderInfo.getZipCode());
            objects.add(senderInfo.getAddress1());
            objects.add(senderInfo.getAddress2());
            OverseaDetailDTO.ReturnInfo returnInfo = dto.getReturnInfo();
            if (Objects.isNull(returnInfo)) {
                returnInfo = new OverseaDetailDTO.ReturnInfo();
            }
            objects.add(returnInfo.getName());
            objects.add(returnInfo.getPhone());
            objects.add(returnInfo.getMobile());
            objects.add(returnInfo.getCompany());
            objects.add(returnInfo.getEmail());
            objects.add(returnInfo.getCountryName());
            objects.add(returnInfo.getState());
            objects.add(returnInfo.getCity());
            objects.add(returnInfo.getDistrict());
            objects.add(returnInfo.getHouseNumber());
            objects.add(returnInfo.getZipCode());
            objects.add(returnInfo.getAddress1());
            objects.add(returnInfo.getAddress2());
            OverseaDetailDTO.ParcelInfo parcelInfo = dto.getParcelInfo();
            if (Objects.isNull(parcelInfo)) {
                parcelInfo = new OverseaDetailDTO.ParcelInfo();
            }
            objects.add(parcelInfo.getTotalWeight());
            objects.add(parcelInfo.getTotalDeclareValue());
            objects.add(parcelInfo.getDeclareCurrency());
            objects.add(parcelInfo.getHeight());
            objects.add(parcelInfo.getWidth());
            objects.add(parcelInfo.getLength());
            objects.add(Objects.equals(parcelInfo.getHasBattery(), "1") ? "是" : "否");
            objects.add(parcelInfo.getTotalQuantity());
            final List<OverseaDetailDTO.Product> productList = Optional.ofNullable(parcelInfo.getProductList()).orElse(Collections.emptyList());
            for (OverseaDetailDTO.Product product : productList) {
                objects.add(product.getGoodsNameCh());
                objects.add(product.getGoodsNameEn());
                objects.add(product.getQuantity());
                objects.add(product.getWeight());
                objects.add(product.getPrice());
                objects.add(product.getMaterial());
                objects.add(product.getHsCode());
                objects.add(product.getProductUrl());
            }
            return objects;
        }).collect(Collectors.toList());
    }


    @PostMapping("updateEncryptInfo")
    public JsonResult updateEncryptInfo(@Valid @RequestBody OverseaEncryptUpdateDTO overseaEncryptUpdateDTO) {
        UserAgent user = getUser();
        List<OverseaAccountVO> overseaAccounts = getNewAccountList(user.getUserCode());
        if (overseaAccounts.isEmpty()) {
            return JsonResult.error(ResponseCode.PORTAL_415);
        }
        Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> overseaEncryptUpdateDTO.getMerchantCode().equals(x.getAccountCode())).findAny();
        if (!any.isPresent()) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        if (overseaEncryptUpdateDTO.getHasS3Encrypt().equals("1") && StringUtils.isBlank(overseaEncryptUpdateDTO.getEncryptField())) {
            return JsonResult.error("加密字段不能为空");
        }
        if (overseaEncryptUpdateDTO.getHasS3Encrypt().equals("0")) {
            overseaEncryptUpdateDTO.setEncryptField(null);
        }
        overseaEncryptUpdateDTO.setOptUser(user.getLoginName());
        crmService.updateEncryptInfo(overseaEncryptUpdateDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/4 16:53
     * @description: 获取秘钥
     */
    @PostMapping("getPublicKey")
    public JsonResult getPublicKey(@JsonParam String merchantCode) {
        UserAgent user = getUser();
        List<OverseaAccountVO> overseaAccounts = getNewAccountList(user.getUserCode());
        if (overseaAccounts.isEmpty()) {
            return JsonResult.error(ResponseCode.PORTAL_415);
        }
        Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> merchantCode.equals(x.getAccountCode())).findAny();
        if (!any.isPresent()) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        OverseaAccountEncryptVO overseaAccountEncrypt = crmService.getOverseaAccountEncrypt(merchantCode);
        if (overseaAccountEncrypt == null) {
            return JsonResult.error("获取公钥失败");
        }
        String publicKey = overseaService.getPublicKey(overseaAccountEncrypt.getYanwenPublicKey());
        if (StringUtils.isBlank(publicKey)) {
            return JsonResult.error("获取公钥失败");
        }
        return JsonResult.success(publicKey);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/19 17:25
     * @description: 添加预报
     */
    @Logger(name = "海外派添加数据预报接口", module = Module.OVERSEA_FORECAST)
    @PostMapping("addForecast")
    public JsonResult addForecast(@RequestBody OverseaForecastDTO forecastDTO) {
        JsonResult jsonResult = validateForecast(forecastDTO);
        if (!jsonResult.getSuccess()) {
            return jsonResult;
        }
        List<OverseaForecastDTO.DetailsInfoDTO> detailsInfo = forecastDTO.getDetailsInfo();
        if (detailsInfo.isEmpty()) {
            return JsonResult.error("预报数据不能为空");
        }
        if (detailsInfo.size() > 500) {
            return JsonResult.error("一次预报数据不能超过500条");
        }
        // 对数据进行去重
        Map<String, Set<String>> bagNoMap = new HashMap<>();
        detailsInfo.forEach(detail -> {
            Set<String> stringSet = bagNoMap.get(detail.getBagNo());
            if (stringSet == null) {
                stringSet = new HashSet<>();
                stringSet.add(detail.getWaybillNumber());
                bagNoMap.put(detail.getBagNo(), stringSet);
            } else {
                stringSet.add(detail.getWaybillNumber());
            }
        });
        List<String> errorMessage=new LinkedList<>();
        AccountGetResVO account = accountService.getAccount(forecastDTO.getCustomerCode());
        bagNoMap.keySet().parallelStream().forEach(
                bagNo -> {
                    try {
                        List<OverseaForecastDTO.DetailsInfoDTO> detailsInfoList = new LinkedList<>();
                        OverseaForecastDTO.DetailsInfoDTO detailsInfoDTO = new OverseaForecastDTO.DetailsInfoDTO();
                        detailsInfoDTO.setBagNo(bagNo);
                        Set<String> strings = bagNoMap.get(bagNo);
                        List<OverseaForecastDTO.ParcelDTO> waybillNumbers = new LinkedList<>();
                        strings.forEach(waybillNumber -> {
                            OverseaForecastDTO.ParcelDTO parcelDTO = new OverseaForecastDTO.ParcelDTO();
                            parcelDTO.setWaybillNumber(waybillNumber);
                            waybillNumbers.add(parcelDTO);
                        });
                        detailsInfoDTO.setParcels(waybillNumbers);
                        detailsInfoList.add(detailsInfoDTO);
                        forecastDTO.setDetailsInfo(detailsInfoList);
                        overseaService.addForecast(forecastDTO,account.getApiToken());
                    } catch (Exception e) {
                        errorMessage.add("大包号："+bagNo+"-->运单数据预报异常:"+e.getMessage());
                    }
                }
        );
        if(!errorMessage.isEmpty()){
            return JsonResult.error("部分数据处理失败："+ JSONObject.toJSONString(errorMessage));
        }
        return JsonResult.success();
    }

    private JsonResult validateForecast(OverseaForecastDTO forecastDTO) {
        if (StringUtils.isBlank(forecastDTO.getCustomerCode())) {
            return JsonResult.error("客户号不能为空");
        }
        if (StringUtils.isBlank(forecastDTO.getBatchCode())) {
            return JsonResult.error("送货批次不能为空");
        }
        if (StringUtils.isBlank(forecastDTO.getPredictArriveDate())) {
            return JsonResult.error("预计送达时间不能为空");
        }
        if (!validateDate(forecastDTO.getPredictArriveDate())) {
            return JsonResult.error("预计送达时间格式不正确");
        }
        if (StringUtils.isBlank(forecastDTO.getChannelId())) {
            return JsonResult.error("产品不能为空");
        }
        if (StringUtils.isBlank(forecastDTO.getChannelName())) {
            return JsonResult.error("产品不能为空");
        }
        if (StringUtils.isBlank(forecastDTO.getCompanyCode())) {
            return JsonResult.error("住入仓不能为空");
        }
        if (!validateDate(forecastDTO.getEtd())) {
            return JsonResult.error("预计起飞时间格式不正确");
        }
        if (!validateDate(forecastDTO.getEta())) {
            return JsonResult.error("预计到达时间格式不正确");
        }
        List<OverseaForecastDTO.DetailsInfoDTO> detailsInfo = forecastDTO.getDetailsInfo();
        if (detailsInfo == null || detailsInfo.isEmpty()) {
            return JsonResult.error("批次明细不能为空");
        }
        if (detailsInfo.size() > 500) {
            return JsonResult.error("批次明细不能超过500条");
        }
        Set<String> waybillNumbers = new HashSet<>();
        Set<String> errorList = new HashSet<>();
        detailsInfo.forEach(x -> {
            if (waybillNumbers.contains(x.getWaybillNumber())) {
                errorList.add(x.getWaybillNumber());
            } else {
                waybillNumbers.add(x.getWaybillNumber());
            }
            if (StringUtils.isBlank(x.getWaybillNumber())) {
                throw new BusinessException("运单号不能为空");
            }
            if (StringUtils.isBlank(x.getWaybillNumber())) {
                throw new BusinessException("大包号不能为空");
            }
        });
        if (!errorList.isEmpty()) {
            return JsonResult.error("提交的批次明细存在运单号重复：" + String.join(",", errorList));
        }
        JsonResult<Object> jsonResult = validateCustomerCode(forecastDTO.getCustomerCode());
        if (!jsonResult.getSuccess()) return jsonResult;
        forecastDTO.getDetailsInfo().forEach(x -> {
            String waybillNumber = x.getWaybillNumber();
            OverseaForecastDTO.ParcelDTO parcelDTO = new OverseaForecastDTO.ParcelDTO();
            parcelDTO.setWaybillNumber(waybillNumber);
            ArrayList<OverseaForecastDTO.ParcelDTO> parcelDTOS = new ArrayList<>(2);
            parcelDTOS.add(parcelDTO);
            x.setParcels(parcelDTOS);
        });
        return JsonResult.success();
    }

    private JsonResult<Object> validateCustomerCode(String customerCode) {
        UserAgent user = getUser();
        List<OverseaAccountVO> overseaAccounts = getNewAccountList(user.getUserCode());
        if (overseaAccounts.isEmpty()) {
            return JsonResult.error(ResponseCode.PORTAL_415);
        }
        Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> customerCode.equals(x.getAccountCode())).findAny();
        if (!any.isPresent()) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/20 9:53
     * @description: 海外派导入预报
     */
    @Logger(name = "海外派导入预报数据接口", module = Module.OVERSEA_FORECAST)
    @RequestMapping("importForecast")
    public JsonResult importForecast(MultipartFile file) {
        if (file == null) {
            return JsonResult.error("文件为空");
        }
        try {
            List<Map<Integer, String>> list = EasyExcel.read(file.getInputStream())
                    .sheet(0)
                    .headRowNumber(0)
                    .doReadSync();
            OverseaForecastDTO forecastDTO = getForecastDTO(list);
            if (StringUtils.isNotBlank(forecastDTO.getChannelName())) {
                List<PlmProductVo.DataDTO> channelList = overseaService.getChannelList();
                Optional<PlmProductVo.DataDTO> any = channelList.stream().filter(x -> x.getProductCnName().equals(forecastDTO.getChannelName())).findAny();
                if (any.isPresent()) {
                    forecastDTO.setChannelId(any.get().getProductNumber());
                }
            }
            if (StringUtils.isNotBlank(forecastDTO.getCustomerCode())) {
                UserAgent user = getUser();
                List<OverseaAccountVO> overseaAccounts = getNewAccountList(user.getUserCode());
                if (overseaAccounts.isEmpty()) {
                    forecastDTO.setCustomerCode(null);
                } else {
                    Optional<OverseaAccountVO> any = overseaAccounts.stream().filter(x -> forecastDTO.getCustomerCode().equals(x.getAccountCode())).findAny();
                    if (!any.isPresent()) {
                        forecastDTO.setCustomerCode(null);
                    }
                }
            }
            return JsonResult.success(forecastDTO);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        } catch (Exception e) {
            return JsonResult.error("导入失败");
        }
    }

    private OverseaForecastDTO getForecastDTO(List<Map<Integer, String>> list) {
        OverseaForecastDTO forecastDTO = new OverseaForecastDTO();
        forecastDTO.setCustomerCode(getValue(list, 0, 1));
        forecastDTO.setChannelName(getValue(list, 0, 3));
        // 通过渠道名称获取渠道id
        // forecastDTO.setChannelId();
        forecastDTO.setBatchCode(getValue(list, 1, 1));
        forecastDTO.setPredictArriveDate(getValue(list, 1, 3));
        forecastDTO.setCompanyCode(getValue(list, 2, 1));
        forecastDTO.setTransportWay(getValue(list, 2, 3));
        forecastDTO.setMawbNo(getValue(list, 3, 1));
        forecastDTO.setPol(getValue(list, 3, 3));
        forecastDTO.setPod(getValue(list, 4, 1));
        forecastDTO.setTimeZone(getValue(list, 4, 3));
        forecastDTO.setEtd(getValue(list, 5, 1));
        forecastDTO.setEta(getValue(list, 5, 3));
        if (StringUtils.isNotBlank(forecastDTO.getMawbNo()) || StringUtils.isNotBlank(forecastDTO.getEta()) || StringUtils.isNotBlank(forecastDTO.getEtd()) || StringUtils.isNotBlank(forecastDTO.getPod()) || StringUtils.isNotBlank(forecastDTO.getPol()) || StringUtils.isNotBlank(forecastDTO.getTimeZone())) {
            forecastDTO.setHasFlightInformation("1");
        } else {
            forecastDTO.setHasFlightInformation("0");
        }
        List<OverseaForecastDTO.DetailsInfoDTO> detailsInfo = new ArrayList<>();
        Set<String> waybillNumbers = new HashSet<>();
        Set<String> errorList = new HashSet<>();
        for (int i = 7; i < list.size(); i++) {
            OverseaForecastDTO.DetailsInfoDTO detailsInfoDTO = new OverseaForecastDTO.DetailsInfoDTO();
            detailsInfoDTO.setWaybillNumber(getValue(list, i, 0));
            if (StringUtils.isNotBlank(detailsInfoDTO.getWaybillNumber())) {
                if (waybillNumbers.contains(detailsInfoDTO.getWaybillNumber())) {
                    errorList.add(detailsInfoDTO.getWaybillNumber());
                } else {
                    waybillNumbers.add(detailsInfoDTO.getWaybillNumber());
                }
            }
            detailsInfoDTO.setBagNo(getValue(list, i, 1));
            detailsInfo.add(detailsInfoDTO);
        }
        if (!errorList.isEmpty()) {
            throw new BusinessException("导入的批次明细存在运单重复：" + String.join(",", errorList));
        }
        forecastDTO.setDetailsInfo(detailsInfo);
        return forecastDTO;
    }


    private String getValue(List<Map<Integer, String>> list, int index, int key) {
        if (list.size() > index) {
            return list.get(index).get(key);
        } else {
            return null;
        }
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/20 10:17
     * @description: 下载预报模版
     */
    @GetMapping("getForecastTemplate")
    public void downloadForecastTemplate(HttpServletResponse response) throws Exception {
        ClassPathResource classPathResource = new ClassPathResource("data/海外派导入预报模版.xlsx");
        HttpUtil.setDownloadHeader(response, "海外派导入预报模版" + ".xlsx", HttpUtil.DOWNLOAD, "UTF-8", null);
        List<String> products = overseaService.getChannelList().stream().map(x -> x.getProductCnName()).collect(Collectors.toList());
        String[] productArray = products.toArray(new String[products.size()]);
        List<String[]> options = new ArrayList<>();
        options.add(productArray);
        ExcelDownloadUtil.downloadWithDropdowns(response, classPathResource.getInputStream(), options);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/20 10:23
     * @description: 查询列表
     */
    @RequestMapping("getForecastList")
    public JsonResult<?> getForecastList(@Valid @RequestBody OverseaQueryParamDTO param) {
        JsonResult<Object> jsonResult = validateCustomerCode(param.getCustomerCode());
        if (!jsonResult.getSuccess()) return jsonResult;
        OverseaForecastListVO overseaForecastListVO = overseaService.getForecastList(param);
        return JsonResult.success(overseaForecastListVO);
    }


    /**
     * @author: dinghy
     * @createTime: 2025/3/20 10:51
     * @description: 查询详情
     */
    @RequestMapping("getForecastDetail")
    public JsonResult getForecastDetail(@JsonParam String customerCode, @JsonParam String batchCode) {
        JsonResult<Object> jsonResult = validateCustomerCode(customerCode);
        if (!jsonResult.getSuccess()) return jsonResult;
        return JsonResult.success(overseaService.getForecastDetail(customerCode, batchCode));
    }

    /**
     * @author: dinghy
     * @createTime: 2025/3/21 17:13
     * @description: 查询海外派产品
     */
    @RequestMapping("getForecastChannelList")
    public JsonResult getChannelList() {
        return JsonResult.success(overseaService.getChannelList());
    }

    public Boolean validateDate(String dateStr) {
        try {
            if (StringUtils.isBlank(dateStr)) {
                return true;
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate.parse(dateStr, formatter);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public List<OverseaAccountVO> getNewAccountList(String userCode) {
        List<OverseaAccountVO> overseaAccounts = overseaService.getOverseaAccounts(userCode,BusinessTypeEnum.OVERSEA.getValue());
        YWEApplyInfoVo yweApplyAllInfoVo = yWEOverseaService.getYWEApplyAllInfoVo(userCode);
        if (yweApplyAllInfoVo != null) {
            OverseaAccountVO overseaAccountVO = new OverseaAccountVO();
            overseaAccountVO.setAccountCode(yweApplyAllInfoVo.getMerchantCode());
            overseaAccounts.add(overseaAccountVO);
        }
        return overseaAccounts;
    }

}


