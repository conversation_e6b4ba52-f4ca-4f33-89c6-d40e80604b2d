package com.ywwl.customer.center.api.common;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.excel.util.StringUtils;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.provider.domain.ClientReadRecord;
import com.ywwl.customer.center.modules.common.provider.dto.MessageProductDTO;
import com.ywwl.customer.center.modules.common.provider.enums.ClientReadTypeEnum;
import com.ywwl.customer.center.modules.common.provider.enums.MessageProductEnum;
import com.ywwl.customer.center.modules.common.provider.service.ClientReadRecordService;
import com.ywwl.customer.center.modules.common.provider.vo.PlmProductVo;
import com.ywwl.customer.center.modules.general.crm.dto.CrmUpdateAdminInfoParam;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.plm.service.PLMService;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import com.ywwl.customer.center.modules.international.dto.TrackSubscriptionCreateDTO;
import com.ywwl.customer.center.modules.international.dto.TrackSubscriptionDeleteDTO;
import com.ywwl.customer.center.modules.international.dto.TrackSubscriptionQueryDTO;
import com.ywwl.customer.center.modules.international.dto.TrackSubscriptionUpdateDTO;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.TrackSubscriptionService;
import com.ywwl.customer.center.modules.international.vo.TrackSubProgramVO;
import com.ywwl.customer.center.modules.international.vo.TrackSubscriptionListVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("trackSubscription")
public class TrackSubscriptionController extends BaseController {
    @Resource
    private TrackSubscriptionService trackSubscriptionService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private AccountService accountService;
    @Resource
    private PLMService plmService;
    @Resource
    private ClientReadRecordService clientReadRecordService;
    @Resource
    private CommonCrmService commonCrmService;

    @Idempotent
    @Logger(module = Module.TRACK_SUBSCRIPTION, name = "运单维度轨迹订阅提交")
    @PostMapping("submitWaybillNumberTrackSub")
    public JsonResult submitWaybillNumberTrackSub(@Valid @RequestBody TrackSubscriptionCreateDTO trackSubscriptionCreateDTO) {
        trackSubscriptionCreateDTO.setType(1);
        List<String> waybillNumberList = trackSubscriptionCreateDTO.getWaybillNumberList();
        if (waybillNumberList == null || waybillNumberList.isEmpty()) {
            return JsonResult.error("运单号不能为空");
        }
        UserAgent user = getUser();
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(user.getUserCode());
        trackSubscriptionCreateDTO.setMerchantCode(packetMerchantCode);
        trackSubscriptionCreateDTO.setCreateId(user.getLoginName());
        trackSubscriptionService.submitWaybillNumberSub(trackSubscriptionCreateDTO);
        return JsonResult.success();
    }

    @Logger(module = Module.TRACK_SUBSCRIPTION, name = "运单维度轨迹订阅修改")
    @PostMapping("updateWaybillNumberTrackSub")
    public JsonResult updateWaybillNumberTrackSub(@Valid @RequestBody TrackSubscriptionUpdateDTO trackSubscriptionUpdateDTO) {
        trackSubscriptionUpdateDTO.setType(1);
        trackSubscriptionUpdateDTO.setCreateId(getUser().getLoginName());
        trackSubscriptionService.updateTrackSub(trackSubscriptionUpdateDTO);
        return JsonResult.success();
    }


    @Idempotent
    @Logger(module = Module.TRACK_SUBSCRIPTION, name = "产品维度轨迹订阅提交")
    @PostMapping("submitProductTrackSub")
    public JsonResult submitProductTrackSub(@RequestBody TrackSubscriptionCreateDTO trackSubscriptionCreateDTO) {
        trackSubscriptionCreateDTO.setType(2);
        List<String> accountCodeList = trackSubscriptionCreateDTO.getAccountCodeList();
        if (accountCodeList == null || accountCodeList.isEmpty()) {
            return JsonResult.error("制单账号不能为空");
        }
        if (StringUtils.isBlank(trackSubscriptionCreateDTO.getProductCode())) {
            return JsonResult.error("产品不能为空");
        }
        if (trackSubscriptionCreateDTO.getCountryCodeList() == null || trackSubscriptionCreateDTO.getCountryCodeList().isEmpty()) {
            return JsonResult.error("国家不能为空");
        }
        // 校验制单账号
        if (!accountService.existAccount(accountCodeList, getUser(), 0)) {
            return JsonResult.error(ResponseCode.PORTAL_5060);
        }
        UserAgent user = getUser();
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(user.getUserCode());
        trackSubscriptionCreateDTO.setMerchantCode(packetMerchantCode);
        trackSubscriptionCreateDTO.setCreateId(user.getLoginName());
        trackSubscriptionService.submitWaybillNumberSub(trackSubscriptionCreateDTO);
        return JsonResult.success();
    }


    @Logger(module = Module.TRACK_SUBSCRIPTION, name = "产品维度轨迹订阅修改")
    @PostMapping("updateProductTrackSub")
    public JsonResult updateProductTrackSub(@Valid @RequestBody TrackSubscriptionUpdateDTO trackSubscriptionUpdateDTO) {
        trackSubscriptionUpdateDTO.setType(2);
        if (trackSubscriptionUpdateDTO.getCountryCodeList() == null || trackSubscriptionUpdateDTO.getCountryCodeList().isEmpty()) {
            return JsonResult.error("国家不能为空");
        }
        trackSubscriptionUpdateDTO.setCreateId(getUser().getLoginName());
        trackSubscriptionService.updateTrackSub(trackSubscriptionUpdateDTO);
        return JsonResult.success();
    }

    @Logger(module = Module.TRACK_SUBSCRIPTION, name = "轨迹订阅批量删除")
    @PostMapping("batchDeleteSub")
    public JsonResult batchDeleteSub(@RequestBody List<String> list) {
        if (list.isEmpty()) {
            return JsonResult.error("请勾选要删除的数据");
        }
        TrackSubscriptionDeleteDTO trackSubscriptionDeleteDTO = new TrackSubscriptionDeleteDTO();
        trackSubscriptionDeleteDTO.setIds(list);
        trackSubscriptionDeleteDTO.setUpdateId(getUser().getLoginName());
        trackSubscriptionService.batchDeleteSub(trackSubscriptionDeleteDTO);
        return JsonResult.success();
    }

    @PostMapping("listWaybillNumberTrackSub")
    public JsonResult listWaybillNumberTrackSub(@RequestBody TrackSubscriptionQueryDTO trackSubscriptionQueryDTO) {
        trackSubscriptionQueryDTO.setType(1);
        UserAgent user = getUser();
        List<Integer> auditStateList = trackSubscriptionQueryDTO.getAuditStateList();
        if (auditStateList == null || auditStateList.size() == 0) {
            return JsonResult.error("查询状态参数为空");
        }
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(user.getUserCode());
        trackSubscriptionQueryDTO.setMerchantCode(packetMerchantCode);
        TrackSubscriptionListVO trackSubscriptionListVO = trackSubscriptionService.listTrackSub(trackSubscriptionQueryDTO);
        return JsonResult.success(trackSubscriptionListVO);
    }

    @PostMapping("listProductTrackSub")
    public JsonResult listProductTrackSub(@RequestBody TrackSubscriptionQueryDTO trackSubscriptionQueryDTO) {
        trackSubscriptionQueryDTO.setType(2);
        String packetMerchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
        trackSubscriptionQueryDTO.setMerchantCode(packetMerchantCode);
        trackSubscriptionQueryDTO.setMerchantCode(packetMerchantCode);
        TrackSubscriptionListVO trackSubscriptionListVO = trackSubscriptionService.listTrackSub(trackSubscriptionQueryDTO);
        return JsonResult.success(trackSubscriptionListVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2025/1/13 14:41
     * @description: 查询轨迹订阅方案
     */
    @GetMapping("queryProgramList")
    public JsonResult queryProgramList() {
        List<TrackSubProgramVO> trackSubProgramVOS = trackSubscriptionService.selectSubProgram();
        return JsonResult.success(trackSubProgramVOS);
    }


    /**
     * @author: dinghy
     * @createTime: 2025/1/21 9:43
     * @description: 获取ejf产品
     */
    @GetMapping("getEjfProducts")
    public JsonResult getEjfProducts() {
        MessageProductDTO productDTO = new MessageProductDTO();
        productDTO.setType(0);
        productDTO.setProductType(AbnormalConstant.CODE_1);
        //查询EJF产品
        productDTO.setName(EnumUtil.getBy(MessageProductEnum::getCode, MessageProductEnum.EJF.getCode(), MessageProductEnum.ERROR).getName());
        productDTO.setPlatform(MessageProductEnum.EJF.getCode());
        productDTO.setIsValid(1);
        List<PlmProductVo.DataDTO> originalProducts = plmService.getOriginalProducts(productDTO);
        return JsonResult.success(originalProducts);
    }


    /**
     * @author: dinghy
     * @createTime: 2025/1/22 15:52
     * @description: 通知crm记录日志
     */
    @GetMapping("noticeCrmRecord")
    public JsonResult noticeCrmRecord() {
        Boolean flag = clientReadRecordService.checkIfReadByType(ClientReadTypeEnum.TRACK_SUBSCRIPTION_NOTICE.getValue(), getUser());
        if (!flag) {
            UserAgent user = getUser();
            String clientTypeDesc = ClientReadTypeEnum.TRACK_SUBSCRIPTION_NOTICE.getDesc();
            ClientReadRecord clientReadRecord = ClientReadRecord.builder().userId(user.getUserId()).typeId("0").type(clientTypeDesc).build();
            clientReadRecordService.save(clientReadRecord);
            final CrmUpdateAdminInfoParam param = CrmUpdateAdminInfoParam.builder()
                    .userCode(currentUser().getUserCode())
                    .note("客户点击同意轨迹订阅告知书")
                    .operator(currentUser().getLoginName())
                    .build();
            commonCrmService.updateAdminLog(param);
        }
        return JsonResult.success();
    }


}
