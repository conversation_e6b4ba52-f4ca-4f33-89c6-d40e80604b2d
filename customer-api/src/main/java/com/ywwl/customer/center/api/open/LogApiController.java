package com.ywwl.customer.center.api.open;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.IpUtils;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.framework.log.domain.SysUserActionLog;
import com.ywwl.customer.center.framework.log.service.SysUserActionLogService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;

@RequestMapping("/api/log")
@RestController
public class LogApiController {
    @Resource
    private SysUserActionLogService sysUserActionLogService;

    @RequestMapping("saveLogs")
    public JsonResult saveLogs(@RequestBody List<SysUserActionLog> logs, HttpServletRequest request){
        if(logs.isEmpty()){
            return JsonResult.error();
        }
        Subject subject = SecurityUtils.getSubject();
        UserAgent userAgent=new UserAgent();
        userAgent.setUserId(0L);
        userAgent.setUserCode("0");
        String ipAddress = IpUtils.getIpAddress(request);
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            userAgent = JsonUtils.parse((String) principal, UserAgent.class);
        }
        UserAgent finalUserAgent = userAgent;
        logs.forEach(x->{
            x.setCreateTime(LocalDateTime.now());
            x.setUserId(finalUserAgent.getUserId());
            x.setUserCode(finalUserAgent.getUserCode());
            x.setUserIp(ipAddress);
        });
        sysUserActionLogService.saveBatch(logs);
        return JsonResult.success();
    }
}
