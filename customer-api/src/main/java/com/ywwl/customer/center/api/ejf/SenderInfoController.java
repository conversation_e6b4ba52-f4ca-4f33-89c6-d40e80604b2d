package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.framework.annotation.ValidType.Delete;
import com.ywwl.customer.center.framework.annotation.ValidType.Update;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.dto.SenderInfoRequestDTO;
import com.ywwl.customer.center.modules.ejf.dto.SenderInfoResponseDTO;
import com.ywwl.customer.center.modules.ejf.entity.SenderInfoCRM;
import com.ywwl.customer.center.modules.ejf.service.SenderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.groups.Default;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EJF发件人信息
 *
 * <AUTHOR>
 * @data 2022/1/27
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/ejf/order/sender")
public class SenderInfoController extends BaseController {

	@Resource
	private SenderInfoService senderInfoService;

	@Resource
	AccountService accountService;

	/**
	 * 查询发件人信息
	 *
	 * @param param 参数
	 * @return 发件人
	 */
	@PostMapping("/getSender")
	public JsonResult<?> getSender(@Validated @RequestBody SenderInfoRequestDTO param) {
		// 只读权限
		final List<String> accountCodes = getAccount(2);
		// 为空则查询全部
		if (CollectionUtil.isEmpty(param.getAccountCodes())) {
			param.setAccountCodes(accountCodes);
		}else {
			// 仅查询有权限
			final List<String> haveAccessTo = param.getAccountCodes().stream()
					.filter(accountCodes::contains)
					.collect(Collectors.toList());
			param.setAccountCodes(haveAccessTo);
		}
		final SenderInfoResponseDTO sender = senderInfoService.getSender(param);
		if (sender.getResult()) {
			return JsonResult.success(sender);
		}
		return JsonResult.error(sender.getMessage());
	}

	/**
	 * 获取有权限的制单账号
	 *
	 * @param scene 类型
	 * @return 制单账号
	 */
	private List<String> getAccount(Integer scene) {
		UserAgent currentUser = getUser();
		AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
		accountGetReqDTO.setUserCode(currentUser.getUserCode());
		// 0 直发业务
		accountGetReqDTO.setAccountType(0);
		accountGetReqDTO.setScene(scene);
		List<AccountGetResVO> accounts = accountService.getAccounts(accountGetReqDTO);
		final List<String> accountCodes = accounts.stream()
				.map(AccountGetResVO::getAccountCode)
				.collect(Collectors.toList());
		return accountCodes;
	}

	/**
	 * 新增发件人信息
	 *
	 * @param senderInfo 发件人信息
	 * @return 响应
	 */
	@PostMapping("/addSender")
	public JsonResult<?> addSender(@Validated @RequestBody SenderInfoCRM senderInfo) {
		final List<String> account = getAccount(1);
		if (!account.contains(senderInfo.getAccountCode())) {
			return JsonResult.error(StrUtil.format("无权限操作此制单账号({})数据", senderInfo.getAccountCode()));
		}
		final UserAgent user = getUser();
		senderInfo.setOperator(user.getUsername());
		senderInfo.setSource(4);
		SenderInfoResponseDTO data = senderInfoService.addSender(senderInfo);
		if (data.getResult()) {
			return JsonResult.success(data);
		}
		return JsonResult.error(data.getMessage());
	}

	/**
	 * 修改发件人信息
	 *
	 * @param senderInfo 发件人信息
	 * @return 响应
	 */
	@PostMapping("/updateSender")
	public JsonResult<?> updateSender(@Validated({Update.class, Default.class}) @RequestBody SenderInfoCRM senderInfo) {
		final List<String> account = getAccount(1);
		if (!account.contains(senderInfo.getAccountCode())) {
			return JsonResult.error(StrUtil.format("无权限操作此制单账号({})数据", senderInfo.getAccountCode()));
		}
		final UserAgent user = getUser();
		senderInfo.setOperator(user.getUsername());
		senderInfo.setSource(4);
		SenderInfoResponseDTO data = senderInfoService.updateSender(senderInfo);
		if (data.getResult()) {
			return JsonResult.success(data);
		}
		return JsonResult.error(data.getMessage());
	}

	/**
	 * 删除发件人信息
	 *
	 * @param senderInfo 发件人信息
	 * @return 响应
	 */
	@PostMapping("/deleteSender")
	public JsonResult<?> deleteSender(@Validated({Delete.class}) @RequestBody SenderInfoCRM senderInfo) {
		final UserAgent user = getUser();
		senderInfo.setOperator(user.getUsername());
		senderInfo.setSource(4);
		SenderInfoResponseDTO data = senderInfoService.deleteSender(senderInfo);
		if (data.getResult()) {
			return JsonResult.success(data);
		}
		return JsonResult.error(data.getMessage());
	}

}
