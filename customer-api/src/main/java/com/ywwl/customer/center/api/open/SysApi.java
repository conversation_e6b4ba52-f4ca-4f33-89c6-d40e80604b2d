// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.api.open;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.api.common.CryptoController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.*;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.common.utils.MessageUtils;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.enums.SupplierPlatformEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.CityInfoVo;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.general.sale.dto.CreateClueDTO;
import com.ywwl.customer.center.modules.general.sale.service.SaleService;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.system.dto.UserRegisterDTO;
import com.ywwl.customer.center.system.enums.VerifyCodeTypeEnum;
import com.ywwl.customer.center.system.service.BaseIdStore;
import com.ywwl.customer.center.system.service.RegisterService;
import com.ywwl.customer.center.system.shiro.RemoteAuthcToken;
import com.ywwl.customer.center.system.utils.SessionCacheUtil;
import com.ywwl.customer.center.system.vo.RegisterUserVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.UnavailableSecurityManagerException;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.cache.Cache;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.springframework.core.io.ClassPathResource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/2/22 9:38
 * @ModifyDate 2023/2/22 9:38
 * @Version 1.0
 */
@RestController
@RequestMapping("/api/sys")
@Slf4j
public class SysApi {

    @Resource
    private SessionCacheUtil sessionCacheUtil;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private RegisterService registerService;
    @Resource
    private CmccService cmccService;
    @Resource
    private SaleService saleService;
    @Resource
    private SessionDAO sessionDAO;
    @Resource
    private CrmService crmService;
    @Resource
    private CommonCrmService commonCrmService;

    /**
     * 首页、登录等前端获取年份
     *
     * @return 下年
     */
    @GetMapping("/nowYear")
    public JsonResult<?> nowYear() {
        String nowYear = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy"));
        return JsonResult.success(nowYear);
    }


    /**
     * 获取用户的信息
     */
    @RequestMapping("/user/info")
    @ResponseBody
    public JsonResult<Map<String, Object>> getUserInfo() {
        //TODO 需要重新写
        Map<String, Object> dataS = new HashMap<>(8);
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            Object principal = subject.getPrincipal();
            UserAgent user = JsonUtils.parse((String) principal, UserAgent.class);

            String customerName = "";
            dataS.put("userCode", user.getUserCode());
            dataS.put("userId", user.getUserId());
            dataS.put("accessToken", user.getAccessToken());
            dataS.put("userName", user.getLoginName());
            dataS.put("customerName", customerName);
            dataS.put("merchantNo", user.getMerchantNo());
            dataS.put("mobile", PrivacyDimmer.maskMobile(user.getPhone()));
            dataS.put("isAdmin", user.isAdmin());
            dataS.put("email", PrivacyDimmer.maskEmail(user.getEmail()));
            return JsonResult.success("用户是登陆状态", dataS, ResponseCode.PORTAL_200.getCode());
        } else {
            return JsonResult.error("用户未登录", dataS, ResponseCode.PORTAL_401.getCode());
        }
    }

    /**
     * 获取随机数图形验证码
     *
     * @param out 输出流
     * @param req 响应
     */
    @GetMapping("/auth/captcha")
    public JsonResult<Object> getCaptcha(OutputStream out, HttpServletRequest req) {
        return registerService.getCaptCha(out, req, false);
    }


    /**
     * 获取滑动图片验证码
     *
     * <AUTHOR>
     * @date 2021/1/15 9:50
     */
    @GetMapping("/auth/getSliderCapatcha")
    public JsonResult<Object> getSliderCaptcha(HttpServletRequest request) {
        JsonResult<Object> jsonResult = registerService.getSliderCaptcha();
        if (jsonResult.getSuccess()) {
            Map<String, Object> data = (Map<String, Object>) jsonResult.getData();
            Object pointX = data.get("pointX");
            if (pointX == null) {
                return JsonResult.error(ResponseCode.PORTAL_5001);
            }
            String id = request.getSession().getId();
            Cache<Object, Object> sliderCaptchaCache = cacheManager.getCache("sliderCaptchaCache");
            log.info(JSONObject.toJSONString(ImmutableMap.of("id", id, "X", pointX)));
            sliderCaptchaCache.put(id, pointX);
            data.remove("pointX");
            jsonResult.setData(data);
        }
        return jsonResult;
    }

    /***
     * 发送短信验证码
     * <AUTHOR>
     * @date 2023/2/21 11:16
     * @param phone 手机号
     * @param typeCode 类型
     * @param verifyCode 类型id
     * @param req  响应头
     * @param plat 坐标
     * @param pointX 坐标
     */
    @GetMapping("/auth/sendVerifyCode")
    public JsonResult<Object> sendSmsVerifyCode(@RequestParam String phone, @RequestParam String typeCode, @RequestParam int verifyCode, HttpServletRequest req, String plat, String pointX) {
        String baseId = IdWorker.get32UUID();
        OPCodeEnum opCodeEnum;
        if (Integer.parseInt(VerifyCodeTypeEnum.REGISTER.value()) == verifyCode) {
            opCodeEnum = OPCodeEnum.GENERATE_SMS_VERIFY;
        } else if (Integer.parseInt(VerifyCodeTypeEnum.LOGGIN.value()) == verifyCode
                || Integer.parseInt(VerifyCodeTypeEnum.RESET_PASSWORD.value()) == verifyCode) {
            opCodeEnum = OPCodeEnum.GENERATE_SMS_AND_VERIFY_MOBILE;
        } else {
            return JsonResult.error(ResponseCode.PORTAL_5006);
        }

        // 如果是手机验证码登录的时候校验图形验证码
        if (StringUtils.isBlank(pointX)) {
            return JsonResult.error(ResponseCode.PORTAL_5001);
        }

        String id = req.getSession().getId();
        JsonResult<Object> jsonResult = registerService.validateSliderCaptcha(pointX, id);
        if (!jsonResult.getSuccess()) {
            return jsonResult;
        }

        JsonResult<Object> result = registerService.sendSmsVerifyCode(phone, baseId, opCodeEnum, typeCode, plat);
        if (!result.getSuccess()) {
            log.error(result.getMessage());
            return result;
        }
        //用于短信校验
        HttpSession session = req.getSession(true);
        sessionCacheUtil.setSessionValue(req.getSession().getId(), BaseIdStore.SMS_VERIFY_CODE_BASE_ID_PREFIX + typeCode, baseId);
        log.debug("发送短信验证码. phone={}, codeType={}, baseId={}, sessionId={}", phone, typeCode, baseId, session.getId());
        return result;
    }

    /***
     * //  用户注册
     * <AUTHOR>
     * @date 2023/2/20 14:43
     * @param registerUser 注册入参
     * @param req 浏览器
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @PostMapping("/auth/register/phoneRegister")
    public JsonResult<Object> registerByPhone(@Validated @RequestBody RegisterUserVo registerUser, HttpServletRequest req) {
        //从session获取baseId
        HttpSession session = req.getSession();
        String baseCaptchaId = Optional.ofNullable(sessionCacheUtil.getSessionValue(req.getSession().getId(), BaseIdStore.CAPTCHA_BASE_ID)).orElse("").toString();
        if (StringUtils.isBlank(baseCaptchaId)) {
            return JsonResult.error(ResponseCode.PORTAL_5001);
        }
        sessionCacheUtil.removeSessionValue(req.getSession().getId(), BaseIdStore.CAPTCHA_BASE_ID);
        log.debug("校验图片验证码. baseId={}, sessionId={}", baseCaptchaId, session.getId());
        registerUser.setBaseId(baseCaptchaId);

        JsonResult<Object> checkCaptchaCodeResult = registerService.checkCaptchaCode(registerUser);

        if (!checkCaptchaCodeResult.getSuccess()) {
            return checkCaptchaCodeResult;
        }
        if (!registerUser.getPassword().equals(registerUser.getPasswordConfirm())) {
            return JsonResult.error(ResponseCode.PORTAL_5004);
        }
        if (!MessageUtils.isPwComplex(registerUser.getPassword())) {
            log.warn("密码过于简单，请修改密码:{}", registerUser.getPassword());
            return JsonResult.error(ResponseCode.PORTAL_5002);
        }
        JsonResult<Object> validateResult = registerService.validateRegisterUser(registerUser);
        if (!validateResult.getSuccess()) {
            return validateResult;
        }
        String baseIdKey = BaseIdStore.SMS_VERIFY_CODE_BASE_ID_PREFIX + SMSTypeEnum.REGISTER.value();
        String baseId = Optional.ofNullable(sessionCacheUtil.getSessionValue(session.getId(), baseIdKey)).orElse("").toString();
        if (StringUtils.isBlank(baseId)) {
            return JsonResult.error(ResponseCode.PORTAL_5007);
        }
        //注册
        registerUser.setBaseId(baseId);
        Cache<String, String> cacheOpenId = cacheManager.getCache("cacheOpenId");
        if (StringUtils.isNotBlank(registerUser.getOpenId())) {
            String openid = cacheOpenId.get(registerUser.getOpenId());
            registerUser.setOpenId(openid);
        }
        log.info("手机号{}用户注册填写销售信息,销售empCode{},销售手机号:{}",registerUser.getPhone(),registerUser.getSalesEmpCode(),registerUser.getSalesPhone());
        // 优先empCode
        if (StringUtils.isNotBlank(registerUser.getTarget())) {
            try {
                final String empCode = CryptoController.DES_I.decryptStr(registerUser.getTarget(), StandardCharsets.UTF_8);
                registerUser.setSalesEmpCode(empCode);
            } catch (Throwable e) {
                throw new BusinessException(ResponseCode.PORTAL_9301);
            }
        } else if (StringUtils.isNotBlank(registerUser.getSalesPhone())) {
            //注册前先进行校验销售手机号
            String code = saleService.saleMobile(registerUser.getSalesPhone());
            registerUser.setSalesEmpCode(code);
        }
        clueCondition(registerUser);
        UserRegisterDTO regResBody = registerService.registerUser(registerUser);
        if (regResBody == null) {
            return JsonResult.error(ResponseCode.PORTAL_5008);
        } else if (!regResBody.getResult()) {
            return JsonResult.error("注册失败" + regResBody.getMessage());
        }
        //注册成功后新建线索
        userCreateClue(registerUser, regResBody.getData().getUserCode());
        //注册成功删除短信验证码对应baseId
        sessionCacheUtil.removeSessionValue(session.getId(), baseIdKey);
        //清除上一个存在HttpServletRequest  中的请求session
        //自动登录
        RemoteAuthcToken remoteAuthcToken = RemoteAuthcToken.builder()
                .loginType(LoginTypeEnum.PWD.value())
                .loginName(registerUser.getLoginName())
                .phone(registerUser.getPhone())
                .password(registerUser.getPassword())
                //传指纹
                .deviceNo(registerUser.getDeviceNo())
                .loginSource("1")
                .email(registerUser.getEmail())
                .build();
        try {
            SecurityUtils.getSubject().login(remoteAuthcToken);
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
            return JsonResult.error();
        }
        Map<String, Object> resultMap = new HashMap<>(4);
        //获取用户信息
        Subject subject = SecurityUtils.getSubject();
        Object principal = subject.getPrincipal();
        UserAgent user = JsonUtils.parse((String) principal, UserAgent.class);
        resultMap.put("user", user);
        resultMap.put("pageState", PageStateEnum.NEW_AUTH.getPageState());
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(user.getUserCode())
                        .phone(user.getPhone())
                        .mail(user.getEmail())
                        .desc("用户注册")
                        .changeType(StraightCrmConstant.ADD)
                        .build()
        );
        //登录成功
        if (SupplierPlatformEnum.DSERS.getValue().equals(registerUser.getPlatCode())) {
            try {
                commonCrmService.saveSupplierProperty(user.getUserCode(), user.getMerchantNo(), user.getLoginName());
            } catch (Exception e) {
                DingTalkClient.sendMessage("保存供应商引流操作异常,用户代码:" + user.getUserCode());
                log.error("保存供应商引流操作异常，原因：{}", e);
            }
        }
        return JsonResult.success(resultMap);
    }

    private void userCreateClue(RegisterUserVo registerUser, String userCode) {
        CreateClueDTO createClueDTO = new CreateClueDTO();
        createClueDTO.setUserCode(userCode);
        createClueDTO.setCityCode(registerUser.getCityCode());
        // 如果有销售表示销售链接，没有表示portal录入
        createClueDTO.setChannelCode(StringUtils.isBlank(registerUser.getSalesEmpCode()) ? "6" : "10");
        createClueDTO.setBusinessTypeCode(registerUser.getBusinessTypeCode());
        createClueDTO.setMobile(registerUser.getPhone());
        createClueDTO.setCustomerName(registerUser.getLoginName() + "_" + userCode);
        createClueDTO.setLinkman(registerUser.getLoginName());
        createClueDTO.setShipmentOfDayCode(registerUser.getShipmentOfDayCode());
        createClueDTO.setSalesEmpCode(registerUser.getSalesEmpCode());
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        createClueDTO.setTimeOfContract(formatter.format(localDateTime));
        createClueDTO.setCustomerNo(userCode);
        saleService.createClue(createClueDTO);
    }

    /***
     * //  创建线索条件
     * <AUTHOR>
     * @date 2023/3/30 16:54
     * @param registerUser 注册信息
     */
    public void clueCondition(RegisterUserVo registerUser) {
        if (registerUser.getSaleExist()) {
            if (StringUtils.isBlank(registerUser.getCityCode())) {
                throw new BusinessException(ResponseCode.PORTAL_5038);
            }
        }
        List<String> businessTypeCode = registerUser.getBusinessTypeCode();
        if (businessTypeCode.size() > 1) {
            if (StringUtils.isBlank(registerUser.getShipmentOfDayCode())) {
                throw new BusinessException(ResponseCode.PORTAL_5039);
            }
        } else {
            String string = AccountTypeEnum.FBA.getType().toString();
            if (!businessTypeCode.contains(string) && StringUtils.isBlank(registerUser.getShipmentOfDayCode())) {
                throw new BusinessException(ResponseCode.PORTAL_5039);
            }
        }
    }

    /**
     * 获取所有城市信息
     *
     * @return 城市信息
     */
    @RequestMapping("/province")
    public JsonResult<Collection<CityInfoVo>> province() {
        return JsonResult.success(cmccService.getProvince());
    }

    /**
     * 获取城市信息
     *
     * @param param 省code
     * @return 城市信息
     */
    @RequestMapping("/city")
    public JsonResult<Collection<CityInfoVo>> province(@RequestBody ProvinceParam param) {
        return JsonResult.success(cmccService.getProvince(param.getProvinceCode()));
    }

    @Data
    static class ProvinceParam {
        private String provinceCode;
    }

    /**
     * 城市redis缓存清理
     *
     * @return 缓存清理是否成功
     */
    @RequestMapping("/province/clean")
    public JsonResult<Object> clean() {
        cmccService.clean();
        return JsonResult.success();
    }

    /**
     * 获取日均票件量
     *
     * @return 票件量枚举
     */
    @RequestMapping("/shipmentOfDay")
    public JsonResult<?> shipmentOfMonth() {
        return JsonResult.success(saleService.shipmentOfMonth());
    }

    /***
     * //  校验客户推荐销售手机号是否有效
     * <AUTHOR>
     * @date 2023/3/28 18:15
     * @param mobile 手机号
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Validated
    @PostMapping("/mobile/verify")
    public JsonResult<Object> saleMobile(@NotBlank(message = "手机号不得为空") @JsonParam String mobile) {
        return JsonResult.success(crmService.getSaleList(mobile));
    }

    /**
     * <AUTHOR>
     * @description 强制客户退出登录
     * @date 2023/7/4 16:53
     **/
    @GetMapping("managerSessionDel")
    public JsonResult managerSessionDel(String merchantNo) {
        JsonResult jsonResult = new JsonResult();
        if (StringUtils.isBlank(merchantNo)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("参数缺失");
            return jsonResult;
        }
        singleUseLogin(merchantNo, jsonResult);
        return jsonResult;
    }

    public void singleUseLogin(String merchantNo, JsonResult result) {
        try {
            Collection<Session> sessions = sessionDAO.getActiveSessions();
            if (CollectionUtils.isEmpty(sessions)) {
                result.setMessage("用户未上线");
                return;
            }
            if (sessions.size() > 0) {
                for (Session onlineSession : sessions) {
                    if (onlineSession == null) {
                        continue;
                    }
                    Object principal = onlineSession.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
                    if (principal == null) {
                        continue;
                    }
                    Map<String, Object> map = null;
                    if (principal != null) {
                        map = JSONObject.parseObject(JSON.toJSONString(principal));
                    }
                    Object object = map.get("primaryPrincipal");
                    if (object == null) {
                        continue;
                    }
                    UserAgent user = JsonUtils.parse((String) object, UserAgent.class);
                    // 清除当前用户以前登录时保存的session会话
                    if (merchantNo.equals(user.getMerchantNo())) {
                        sessionDAO.delete(onlineSession);
                        log.info("清理用户[" + merchantNo + "],SessionId为[" + onlineSession.getId() + "]的Session信息!");
                        result.setSuccess(true);
                        result.setMessage("清理用户[" + merchantNo + "],SessionId为[" + onlineSession.getId() + "]的Session信息!");
                        break;
                    }
                }
            } else {
                log.info("无可清理用户信息!");
                result.setSuccess(false);
                result.setMessage("无可清理用户信息!");
            }
        } catch (UnavailableSecurityManagerException e) {
            result.setSuccess(false);
            result.setMessage("异常e:" + e.getMessage());
        }
    }

    /**
     * <AUTHOR>
     * @description 更新省市区编码缓存
     * @date 2023/7/12 15:45
     **/
    @GetMapping("updateCountryCache")
    public JsonResult updateCountryCache() {
        cmccService.cacheCountryCode();
        return JsonResult.success();
    }


    /**
     * @Description 发送短信重置密码
     * <AUTHOR>
     * @Date 2020/7/6 15:53
     */
    @PostMapping("pwdResetSendVerifyCode")
    @ResponseBody
    public JsonResult sendEmailVerifyCode(@RequestBody Map<String, String> param, HttpServletRequest req) {
        JsonResult result = new JsonResult();
        String userName = param.get("userName");
        if (StringUtils.isBlank(userName)) {
            result.setSuccess(false);
            result.setMessage("参数缺失");
            return result;
        }
        JSONObject jsonObject = registerService.getForgetPhone(userName);
        if (jsonObject == null) {
            result.setSuccess(false);
            result.setMessage("网络异常");
            return result;
        }
        if (jsonObject.getBoolean("success")) {
            String mobile = jsonObject.getString("mobile");
            String typeCode = SMSTypeEnum.CHANGE_PASSWORD.value();
            String plat = "pc";
            //发送验证码
            String baseId = IdWorker.get32UUID();
            result = registerService.sendSMSVerifyCode(mobile, baseId, OPCodeEnum.GENERATE_SMS_VERIFY, typeCode, plat);
            if (!result.getSuccess()) {
                log.error(result.getMessage());
                return result;
            }
            //用于短信校验
            HttpSession session = req.getSession(true);
            sessionCacheUtil.setSessionValue(session.getId(), BaseIdStore.SMS_VERIFY_CODE_BASE_ID_PREFIX + typeCode, baseId);
            // session.setAttribute(BaseIdStore.SMS_VERIFY_CODE_BASE_ID_PREFIX + typeCode, baseId);
            sessionCacheUtil.setSessionValue(session.getId(), userName, mobile);
            // session.setAttribute(userName, mobile);
            log.debug("发送短信验证码. phone={}, codeType={}, baseId={}, sessionId={}", mobile, typeCode, baseId, session.getId());
            result.setSuccess(true);
            String message = "验证码已发送您注册的手机号" + PrivacyDimmer.maskPhone(mobile) + ",请注意查收";
            result.setMessage(message);
            return result;
        } else {
            result.setSuccess(false);
            result.setMessage(jsonObject.getString("message"));
        }
        return result;
    }


    /**
     * @Description 修改密码
     * <AUTHOR>
     * @Date 2020/7/6 16:08
     */
    @PostMapping("pwdReset")
    @ResponseBody
    public JsonResult verifyAndChangePassword(@RequestBody Map<String, String> param, HttpServletRequest req) {
        JsonResult jsonResult = new JsonResult();
        String loginName = param.get("loginName");
        String password = param.get("password");
        String passwordConfirm = param.get("passwordConfirm");
        String code = param.get("code");
        if (StringUtils.isBlank(loginName) || StringUtils.isBlank(password) || StringUtils.isBlank(passwordConfirm) || StringUtils.isBlank(code)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("参数缺失");
            return jsonResult;
        }
        if (!password.equals(passwordConfirm)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("两次密码输入不一致");
            return jsonResult;
        }
        if (!MessageUtils.isPwComplex(password)) {
            jsonResult.setSuccess(false);
            jsonResult.setMessage("您的密码强度太低");
            return jsonResult;
        }
        //从session获取baseId
        HttpSession session = req.getSession();
        if (session == null) {
            throw new RuntimeException("窗口失效, 请刷新重试.");
        }
        String baseIdKey = BaseIdStore.SMS_VERIFY_CODE_BASE_ID_PREFIX + SMSTypeEnum.CHANGE_PASSWORD.value();
        String baseId = Optional.ofNullable(sessionCacheUtil.getSessionValue(session.getId(), baseIdKey)).orElse("").toString();
        String mobile = Optional.ofNullable(sessionCacheUtil.getSessionValue(session.getId(), loginName)).orElse("").toString();
        if (StringUtils.isBlank(baseId) || StringUtils.isBlank(mobile)) {
            JsonResult result = new JsonResult();
            result.setMessage("验证码已失效, 请重新发送.");
            result.setSuccess(false);
            return result;
        }
        //校验短信
        jsonResult = registerService.smsCheckReqBody(mobile, baseId, OPCodeEnum.VERIFY_SMS_CODE, code, PlatformTypeEnum.WEB.value());
        if (!jsonResult.getSuccess()) {
            log.info("修改密码校验短信结果:{}", jsonResult);
            return jsonResult;
        }
        // 修改密码
        jsonResult = registerService.changePassword(loginName, password, "", "3");
        log.info("修改密码结果:{}", jsonResult);
        return jsonResult;
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/19 9:53
     * @description: 从老系统迁移下载服务确认书
     */
    @RequestMapping("/agreement/download")
    public void downloadAgreement(HttpServletResponse response) {
        InputStream in = null;
        ClassPathResource classPathResource = new ClassPathResource("data/服务确认书.docx");
        try {
            in = classPathResource.getInputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode("欧盟税改服务确认书.docx", "UTF-8"));
            byte[] buffer = new byte[1024];
            int len;
            while ((len = in.read(buffer)) > 0) {
                response.getOutputStream().write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            try {
                response.getOutputStream().write("下载失败".getBytes());
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}
