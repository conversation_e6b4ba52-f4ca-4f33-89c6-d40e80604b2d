package com.ywwl.customer.center.api.system;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.common.utils.PdfUtil;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAreaEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerTypeEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerAuthVo;
import com.ywwl.customer.center.modules.common.provider.vo.EmployeeVo;
import com.ywwl.customer.center.modules.general.crm.dto.CrmUpdateAdminInfoParam;
import com.ywwl.customer.center.modules.general.crm.dto.UpdateAdminPhoneDTO;
import com.ywwl.customer.center.modules.general.crm.dto.UpdateMobileApplyDTO;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.general.crm.vo.UpdateMobileResultVo;
import com.ywwl.customer.center.modules.general.sale.dto.CreateClueDTO;
import com.ywwl.customer.center.modules.general.sale.service.SaleService;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.upload.service.FileUploadRecordService;
import com.ywwl.customer.center.system.dto.AdminInfoUpdateDTO;
import com.ywwl.customer.center.system.dto.EmailCheckDTO;
import com.ywwl.customer.center.system.dto.EnableSafeNoticeDTO;
import com.ywwl.customer.center.system.dto.SubUserDTO;
import com.ywwl.customer.center.system.request.SendEmailCodeReqBody;
import com.ywwl.customer.center.system.response.SubUserInfoResBody;
import com.ywwl.customer.center.system.service.*;
import com.ywwl.customer.center.system.service.impl.SubUserService;
import com.ywwl.customer.center.system.vo.EmailVo;
import com.ywwl.customer.center.system.vo.SetPassWordVo;
import com.ywwl.customer.center.system.vo.UserSafeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * // 用户登录
 *
 * <AUTHOR>
 * @date 2023/2/22
 */

@Validated
@Controller
@Slf4j
@RequestMapping("user")
public class UserController extends BaseController {
    @Resource
    private ResetPasswordService resetPasswordService;
    @Resource
    private SubUserService subUserService;
    @Resource
    private WechatService wechatService;
    @Resource
    private UserService userService;
    @Resource
    private SmsService smsService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private CrmService crmService;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private SaleService saleService;
    @Resource
    private FileService fileService;
    @Resource
    private FileUploadRecordService fileUploadRecordService;


    /**
     * 修改密码
     *
     * @param passWordDTO 密码参数
     * @param currentUser 登录信息
     */
    @Logger(module = Module.USER, name = "修改密码操作")
    @ResponseBody
    @PostMapping("changePassword")
    public JsonResult<Object> changePassword(@Valid @RequestBody SetPassWordVo passWordDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        if (!passWordDTO.getPassword().equals(passWordDTO.getPasswordConfirm())) {
            return JsonResult.error(ResponseCode.PORTAL_5004);
        }
        if (StringUtils.isBlank(passWordDTO.getOldPwd())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        passWordDTO.setLoginName(currentUser.getLoginName());
        // 1 自行修改密码
        JsonResult<Object> result = resetPasswordService.changePassword(passWordDTO.getLoginName(), passWordDTO.getPassword(), passWordDTO.getOldPwd(), "1");
        //修改成功后进行记录
        if (result.getSuccess()) {
            //TODO 重新写
        }

        return result;
    }


    /***
     * //用户信息
     * <AUTHOR>
     * @date 2023/3/2 10:16
     * @param userAgent 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.USER, name = "用户信息", recordReq = false, recordRsp = false)
    @ResponseBody
    @GetMapping("profile")
    public JsonResult<Object> setting(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent) {
        SubUserInfoResBody subUserInfoResBody = subUserService.requestSubUserInfo(userAgent.getUserId(), userAgent.getAccessToken());
        // 获取用户的openid,判断是否绑定微信
        String openid = wechatService.getOpenid(userAgent.getUserId());
        Boolean isBind = StringUtils.isNotBlank(openid);
        Map<String, Object> map = new HashMap<>(4);
        map.put("isBind", isBind);
        if (subUserInfoResBody.getEmailVerify()) {
            subUserInfoResBody.setEmail(PrivacyDimmer.maskEmail(subUserInfoResBody.getEmail()));
        }
        subUserInfoResBody.setMobile(PrivacyDimmer.maskMobile(subUserInfoResBody.getMobile()));
        map.put("subUser", subUserInfoResBody);
        map.put("isEditable", false);
        String customerTypeInfo = "";
        CustomerAuthVo customerAuthVo = customerAuthService.getCustomerAuthInfo(userAgent.getMerchantNo());
        if (Objects.nonNull(customerAuthVo) && Objects.nonNull(customerAuthVo.getCustomerType()) && Objects.nonNull(customerAuthVo.getCustomerArea())) {
            CustomerAreaEnum customerAreaEnum = CustomerAreaEnum.getCustomerAreaEnum(customerAuthVo.getCustomerArea());
            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getCustomerTypeEnum(customerAuthVo.getCustomerType());
            if (Objects.nonNull(customerAreaEnum)) {
                customerTypeInfo += customerAreaEnum.getDesc();
            }
            if (Objects.nonNull(customerTypeEnum)) {
                customerTypeInfo += customerTypeEnum.desc();
                if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerTypeEnum.value())) {
                    map.put("customerName", customerAuthVo.getPersonName());
                } else {
                    map.put("customerName", customerAuthVo.getCompanyName());
                }
            }
            map.put("customerType", customerTypeInfo);
        } else {
            map.put("customerType", "");
        }
        if (userAgent.isAdmin()) {
            String businessSale = saleService.checkBusinessSale(null, userAgent.getPhone(), userAgent.getUserCode());
            if (StringUtils.isNotBlank(businessSale)) {
                EmployeeVo employeeInfo = packetBusinessApplyService.getEmployeeInfo(businessSale);
                if (employeeInfo != null) {
                    map.put("saleName", employeeInfo.getUsername());
                    map.put("salePhone", employeeInfo.getPhone());
                    map.put("enterpriseWeChat", employeeInfo.getEnterpriseWeChat());
                }
            } else {
                // 从小包查询销售，如果小包数据不存在或者销售也不存在，且不为公司的话，到售前创建线索
                PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userAgent.getUserCode());
                // 公司的empCode
                String companyEmpCode = "LS0300031";
                if (packetApplyInfo != null && StringUtils.isNotBlank(packetApplyInfo.getIntroducerSaleId()) && !companyEmpCode.equals(packetApplyInfo.getIntroducerSaleId())) {
                    EmployeeVo employeeInfo = packetBusinessApplyService.getEmployeeInfo(businessSale);
                    if (employeeInfo != null) {
                        map.put("saleName", employeeInfo.getUsername());
                        map.put("salePhone", employeeInfo.getPhone());
                        map.put("enterpriseWeChat", employeeInfo.getEnterpriseWeChat());
                    }
                } else {
                    // 如果是admin用户登录,查不到主销售
                    if (userAgent.isAdmin()) {
                        map.put("noSale", true);
                    }
                }

            }
        }

        map.put("updateFlag", false);
        return JsonResult.success(ResponseCode.PORTAL_200, map);
    }

    /**
     * 邮箱验证、发送验证码
     *
     * @param userAgent
     * @param emailVo
     * @param request
     * @return
     */
    @Logger(module = Module.USER, name = "发送邮箱验证码")
    @PostMapping("/send/verifyEmail")
    @ResponseBody
    public JsonResult<Object> activeEmail(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @Valid @RequestBody EmailVo emailVo, HttpServletRequest request) {
        SendEmailCodeReqBody reqBody = new SendEmailCodeReqBody();
        reqBody.setUserId(userAgent.getUserId());
        reqBody.setToAddress(emailVo.getEmail());
        reqBody.setAction("activeEmail");
        reqBody.setBaseId(IdWorker.get32UUID());
        JsonResult result = userService.sendEmailCheckCode(reqBody, request);
        return result;
    }


    @Logger(module = Module.USER, name = "校验邮箱验证码")
    @PostMapping("/email/checkEmailCode")
    @ResponseBody
    public JsonResult<Object> checkMailCode(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @RequestBody EmailCheckDTO emailCheckDTO, HttpServletRequest request) {
        if (StringUtils.isEmpty(emailCheckDTO.getCheckCode())) {
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        emailCheckDTO.setUserId(userAgent.getUserId());
        emailCheckDTO.setAddress(userAgent.getEmail());
        return userService.checkMailCode(emailCheckDTO, request);
    }

    /***
     * //  修改邮箱
     * <AUTHOR>
     * @date 2023/3/3 15:29
     * @param adminPhoneUpdateVo 修改参数
     * @param request 相应
     * @return com.ywwl.customer.center.common.domain.JsonResult
     */
    @Logger(module = Module.USER, name = "修改邮箱")
    @RequestMapping("updateEmailInfo")
    @ResponseBody
    public JsonResult<?> updateEmailInfo(@Valid @RequestBody AdminInfoUpdateDTO adminPhoneUpdateVo, HttpServletRequest request) {
        UserAgent userAgent = getUser();
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(userAgent.getUserCode())
                        .mail(adminPhoneUpdateVo.getEmail())
                        .desc("修改邮箱")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        // 校验验证码
        String id = request.getSession().getId();
        if (StringUtils.isBlank(adminPhoneUpdateVo.getEmail())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        if (StringUtils.isNotBlank(userAgent.getEmail()) && userAgent.getEmail().equals(adminPhoneUpdateVo.getEmail())) {
            return JsonResult.error(ResponseCode.PORTAL_5016);
        }
        adminPhoneUpdateVo.setMerchantNo(userAgent.getMerchantNo());
        try {
            smsService.checkSmsCode(id, userAgent.getPhone(), adminPhoneUpdateVo.getCode());
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        JsonResult<?> result = JsonResult.success();
        if (result.getSuccess()) {
            // 调用CRM修改管理员邮箱
            // 调用CRM的更新管理员接口
            final CrmUpdateAdminInfoParam param = CrmUpdateAdminInfoParam.builder()
                    .userCode(currentUser().getUserCode())
                    .note("修改管理员信息")
                    .originalValue(currentUser().getEmail())
                    .targetValue(adminPhoneUpdateVo.getEmail())
                    .operator(currentUser().getLoginName())
                    .build();
            try {
                commonCrmService.updateAdminLog(param);
                result = userService.updateAdminInfo(adminPhoneUpdateVo);
            } catch (Throwable e) {
                result = userService.updateAdminInfo(adminPhoneUpdateVo);
                final String msg = StrUtil.format("CRM修改管理员邮箱异常：{} {}", param, e.getMessage());
                log.error(msg, e);
                DingTalkClient.sendMessage(msg);
            }
        }
        return result;
    }

    /***
     * //  修改用户名
     * <AUTHOR>
     * @date 2023/3/31 10:42
     * @param adminInfoUpdateDTO 用户名
     * @param userAgent 登录信息
     * @param request  响应
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "修改用户名操作")
    @RequestMapping("updateNameInfo")
    @ResponseBody
    public JsonResult<Object> updateNameInfo(@Valid @RequestBody AdminInfoUpdateDTO adminInfoUpdateDTO, @ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, HttpServletRequest request) {
        // 校验验证码
        String id = request.getSession().getId();
        if (StringUtils.isBlank(adminInfoUpdateDTO.getLoginName())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        if (userAgent.getLoginName().equals(adminInfoUpdateDTO.getLoginName())) {
            return JsonResult.error(ResponseCode.PORTAL_5043);
        }
        adminInfoUpdateDTO.setMerchantNo(currentUser().getMerchantNo());
        try {
            smsService.checkSmsCode(id, userAgent.getPhone(), adminInfoUpdateDTO.getCode());
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        return userService.updateAdminInfo(adminInfoUpdateDTO);
    }

    /***
     * //  修改手机号
     * <AUTHOR>
     * @date 2023/3/28 9:57
     * @param adminPhoneUpdateVo 信息
     * @param userAgent 登录信息
     * @param request 响应
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "修改手机号操作")
    @RequestMapping("updatePhoneInfo")
    @ResponseBody
    public JsonResult<?> updatePhoneInfo(@Valid @RequestBody AdminInfoUpdateDTO adminPhoneUpdateVo, @ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, HttpServletRequest request) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(userAgent.getUserCode())
                        .phone(adminPhoneUpdateVo.getPhone())
                        .desc("修改手机号")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        // 校验验证码
        String id = request.getSession().getId();
        adminPhoneUpdateVo.setMerchantNo(currentUser().getMerchantNo());
        if (userAgent.getPhone().equals(adminPhoneUpdateVo.getPhone())) {
            return JsonResult.error(ResponseCode.PORTAL_5034);
        }
        JsonResult<?> result = JsonResult.success();
        try {
            smsService.checkSmsCode(id, adminPhoneUpdateVo.getPhone(), adminPhoneUpdateVo.getCode());
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        //修改成功后通知公共CRM修改手机号
        UpdateAdminPhoneDTO updateAdminPhoneDTO = new UpdateAdminPhoneDTO();
        updateAdminPhoneDTO.setNo(userAgent.getMerchantNo());
        updateAdminPhoneDTO.setAdminPhone(adminPhoneUpdateVo.getPhone());
        try {
            commonCrmService.updateAdminPhone(updateAdminPhoneDTO);
        } catch (Exception e) {
            log.error("公共CRM修改手机号异常：{} {}", updateAdminPhoneDTO, e.getMessage());
        }
        // 调用CRM的更新管理员接口
        final CrmUpdateAdminInfoParam param = CrmUpdateAdminInfoParam.builder()
                .userCode(currentUser().getUserCode())
                .note("修改管理员信息")
                .originalValue(currentUser().getPhone())
                .targetValue(adminPhoneUpdateVo.getPhone())
                .operator(currentUser().getLoginName())
                .build();
        try {
            commonCrmService.updateAdminLog(param);
            result = userService.updateAdminInfo(adminPhoneUpdateVo);
        } catch (Exception e) {
            // 如果CRM接口出错，则主动修改
            result = userService.updateAdminInfo(adminPhoneUpdateVo);
            final String msg = StrUtil.format("CRM修改管理员手机号异常：{} {}", param, e.getMessage());
            log.error(msg, e);
            DingTalkClient.sendMessage(msg);
        }
        return result;
    }

    /***
     * //  新手机号发送短信
     * <AUTHOR>
     * @date 2023/3/28 15:39
     * @param phone
     * @param type
     * @param request
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "手机号发送短信操作")
    @PostMapping("sendNewPhoneSmsCode")
    @ResponseBody
    public JsonResult<Object> sendNewPhoneSmsCode(@NotBlank(message = "新手机号不得为空") @JsonParam String phone, @NotBlank(message = "短信类型不得为空") @JsonParam String type, HttpServletRequest request) {
        try {
            if (currentUser().getPhone().equals(phone)) {
                return JsonResult.error(ResponseCode.PORTAL_5034);
            }
            String id = request.getSession().getId();
            smsService.sendSmsCode(id, phone, type);
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 绑定微信
     * @date 2023/4/14 13:45
     **/
    @Logger(module = Module.USER, name = "绑定微信操作")
    @PostMapping("bindWechat")
    @ResponseBody
    public JsonResult<Object> bindWechat(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent, @JsonParam @NotBlank(message = "参数为空") String scanKey) {
        return wechatService.bindWeChat(userAgent.getUserId(), scanKey);
    }


    /**
     * @Description 解绑微信
     * <AUTHOR>
     * @Date 2020/1/9 13:39
     */
    @Logger(module = Module.USER, name = "解绑微信操作")
    @GetMapping("unBindWeChat")
    @ResponseBody
    public JsonResult<Object> unBindWeChat(@ModelAttribute(value = "currentUser", binding = false) UserAgent userAgent) {
        return wechatService.unbindWechat(userAgent.getUserId());

    }

    /***
     * // 子用户修改密码
     * <AUTHOR>
     * @date 2023/4/11 13:57
     * @param currentUser 登录信息
     * @param subUserDTO 参数
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.USER, name = "子用户修改密码操作")
    @PostMapping("/initializePassword")
    @ResponseBody
    public JsonResult<Object> sbUserChangePassword(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Validated @RequestBody SubUserDTO subUserDTO) {
        if (!currentUser.getUserId().equals(subUserDTO.getUserId())) {
            return JsonResult.error(ResponseCode.PORTAL_5050);
        }
        return resetPasswordService.sbUserChangePassword(subUserDTO, currentUser.getAccessToken());
    }

    /**
     * @author: dinghy
     * @createTime: 2024/5/9 17:28
     * @description: 没有销售首页填充销售
     */
    @Idempotent
    @PostMapping("createNoSaleClue")
    @ResponseBody
    public JsonResult createNoSaleClue(@JsonParam String shipmentOfDayCode) {
        UserAgent user = getUser();
        if (!user.isAdmin()) {
            return JsonResult.error(ResponseCode.PORTAL_5027);
        }
        if (StringUtils.isBlank(shipmentOfDayCode)) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        CustomerAuthVo customerAuthVo = customerAuthService.getCustomerAuthInfo(user.getMerchantNo());
        String customerName = user.getLoginName() + "_" + user.getUserCode();
        String linkMan = user.getLoginName();
        if (customerAuthVo != null) {
            if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthVo.getCustomerType())) {
                if (StringUtils.isNotBlank(customerAuthVo.getPersonName())) {
                    customerName = customerAuthVo.getPersonName();
                    linkMan = customerName;
                }
            } else {
                if (StringUtils.isNotBlank(customerAuthVo.getCompanyName())) {
                    customerName = customerAuthVo.getCompanyName();
                }
            }
        }
        CreateClueDTO createClueDTO = new CreateClueDTO();
        createClueDTO.setUserCode(user.getUserCode());
        createClueDTO.setShipmentOfDayCode(shipmentOfDayCode);
        createClueDTO.setCityCode(null);
        createClueDTO.setLinkman(linkMan);
        createClueDTO.setCustomerName(customerName);
        createClueDTO.setChannelCode("6");
        createClueDTO.setBusinessTypeCode(Arrays.asList("0"));
        createClueDTO.setMobile(user.getPhone());
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        createClueDTO.setTimeOfContract(formatter.format(localDateTime));
        createClueDTO.setCustomerNo(user.getUserCode());
        saleService.createClue(createClueDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/7/4 14:50
     * @description: 启用关闭安全登录
     */
    @RequestMapping("enableSafeNotice")
    @ResponseBody
    public JsonResult enableSafeLogin(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody EnableSafeNoticeDTO enableSafeNoticeDTO) {
        enableSafeNoticeDTO.setUserId(currentUser.getUserId());
        JsonResult jsonResult = userService.enableSafeLogin(enableSafeNoticeDTO);
        if (jsonResult.getSuccess()) {
            commonCrmService.addSafeNotice(currentUser.getUserCode(), enableSafeNoticeDTO.getEnable() == 0 ? "关闭" : "开启", currentUser.getLoginName());
        }
        return jsonResult;
    }

    /**
     * @author: dinghy
     * @createTime: 2024/7/4 15:29
     * @description: 查询安全提醒信息
     */
    @RequestMapping("querySafeInfo")
    @ResponseBody
    public JsonResult querySafeInfo(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        UserSafeVO userSafeVO = userService.querySafeInfo(currentUser.getUserId());
        return JsonResult.success(userSafeVO);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/12/6 14:55
     * @description: 校验手机号是否存在
     */
    @RequestMapping("validatePhoneOrEmailExist")
    @ResponseBody
    public JsonResult validatePhoneOrEmailExist(@JsonParam String phone) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(getUserCode())
                        .phone(phone)
                        .desc("特殊情况修改手机号申请")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        return userService.validatePhoneOrEmailExist(phone, null);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/12/16 10:03
     * @description: 修改手机号申请提交
     */
    @Idempotent
    @PostMapping("submitUpdatePhoneApply")
    @ResponseBody
    public JsonResult submitUpdatePhoneApply(@Validated @RequestBody UpdateMobileApplyDTO updateMobileApplyDTO,HttpServletRequest request){
        String userCode = getUserCode();
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(userCode)
                        .phone(updateMobileApplyDTO.getNewPhone())
                        .desc("修改手机号申请")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        updateMobileApplyDTO.setUserCode(userCode);
        String id = request.getSession().getId();
        try {
            smsService.checkSmsCode(id, updateMobileApplyDTO.getNewPhone(), updateMobileApplyDTO.getSmsCode());
        } catch (BusinessException e) {
            return JsonResult.error(e.getMessage());
        }
        commonCrmService.submitUpdatePhoneApply(updateMobileApplyDTO);
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/12/16 10:19
     * @description: 查询提交修改手机号结果
     */
    @GetMapping("getUpdatePhoneResult")
    @ResponseBody
    public JsonResult getUpdatePhoneResult(){
        try {
            UpdateMobileResultVo  updateMobileResultVo= commonCrmService.getUpdatePhoneResult(getUserCode());
            if(updateMobileResultVo!=null){
                updateMobileResultVo.setAttach1(fileService.appendSignature(updateMobileResultVo.getAttach1()));
                updateMobileResultVo.setAttach2(fileService.appendSignature(updateMobileResultVo.getAttach2()));
                return JsonResult.success(updateMobileResultVo);
            }
        } catch (Exception e) {

        }
        return JsonResult.success(new UpdateMobileResultVo());
    }
    /**
     * @author: dinghy
     * @createTime: 2024/12/16 18:01
     * @description: 下载企业
     */
    @GetMapping("downloadCompanyUpdateProof")
    public void downloadCompanyUpdateProof(HttpServletResponse response){
        Map<String,Object> fields=new HashMap<>(2);
        CustomerAuthVo customerAuthVo = customerAuthService.getCustomerAuthInfo(getMerchantNo());
        if(customerAuthVo==null){
            throw new BusinessException(ResponseCode.PORTAL_5029);
        }
        if(!CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthVo.getCustomerType())){
            throw new BusinessException("仅支持企业下载");
        }
        String companyName = customerAuthVo.getCompanyName();
        String registerNumber = customerAuthVo.getRegisterNumber();
        if(StringUtils.isBlank(companyName)||StringUtils.isBlank(registerNumber)){
            throw new BusinessException("信息缺失，请联系销售/客服");
        }
        fields.put("businessCode",registerNumber);
        fields.put("businessName",companyName);
        PdfUtil.generateCompanyUpdatePhoneProof(fields,response);
    }
}


