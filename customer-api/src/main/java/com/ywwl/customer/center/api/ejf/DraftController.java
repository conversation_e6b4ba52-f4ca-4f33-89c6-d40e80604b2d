package com.ywwl.customer.center.api.ejf;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.aspect.ParameterValidationAspect;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.business.dto.InnerBeChannelGetListDTO;
import com.ywwl.customer.center.modules.business.dto.InnerBeChannelGetListParamDTO;
import com.ywwl.customer.center.modules.business.service.BusinessService;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.provider.domain.ClientReadRecord;
import com.ywwl.customer.center.modules.common.provider.service.ClientReadRecordService;
import com.ywwl.customer.center.modules.ejf.annotation.Import;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import com.ywwl.customer.center.modules.ejf.component.excel.CommentWriteHandler;
import com.ywwl.customer.center.modules.ejf.dto.DraftPageDTO;
import com.ywwl.customer.center.modules.ejf.dto.OrderKey;
import com.ywwl.customer.center.modules.ejf.dto.OrderKeysDTO;
import com.ywwl.customer.center.modules.ejf.entity.*;
import com.ywwl.customer.center.modules.ejf.entity.ImportResultObject.DataDTO.RecordsDTO;
import com.ywwl.customer.center.modules.ejf.service.BaseInfoService;
import com.ywwl.customer.center.modules.ejf.service.DraftService;
import com.ywwl.customer.center.modules.ejf.util.EJFMsgCenter;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.ejf.util.ExcelImportUtil;
import com.ywwl.customer.center.modules.general.cmcc.dto.BusinessOrderEnumDTO;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.OverseasDeliveryLocationDTO;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.general.plm.entity.SysWarehouse;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.TaxManageService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.upload.entity.FileUploadRecord;
import com.ywwl.customer.center.modules.upload.enums.FileUploadRecordTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.groups.Default;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 草稿控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ejf/draft")
@Slf4j
public class DraftController extends BaseController {

	/**
	 * 草稿服务
	 */
	@Resource
	private DraftService draftService;
	@Resource
	private BaseInfoService baseInfoService;
	@Resource
	private AccountService accountService;
	@Resource
	private CrmService crmService;
	@Resource
	private BusinessService businessService;
	@Resource
	private CmccService cmccService;
	@Resource
	private ClientReadRecordService clientReadRecordService;
	@Resource
	private TaxManageService taxManageService;

	private static final String IMPORT_ERROR_MSG = "导入错误记录-%s.xlsx";

	@Value("${ejf.buss.import-max-size}")
	private Integer size;

	/**
	 * 订单生成运单号
	 *
	 * @param key 订单ID
	 * @return 基础信息
	 */
	@RequestMapping("/generateExpressCode")
	@Valid
	public JsonResult<?> generateExpressCode(@RequestBody OrderKeysDTO key) {
		final UserAgent user = getUser();
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		// 获取商户信息
		final PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(getUserCode());
		final Integer ejfStatus = packetApplyInfo.getEjfStatus();
		if (!Objects.equals(ejfStatus, 1)) {
			return JsonResult.error("制单账号处于金额冻结状态，请充值运费活动账号再制单，如有疑问请联系销售！");
		}
		// 验证商户状态
		if (!crmService.checkMerchantStatus(user.getMerchantNo(), user.getUserCode())) {
			return JsonResult.error("您的账号存在未清款项，请联系客服核实！");
		}
		if (CollectionUtils.isNotEmpty(key.getIds())) {
			if (key.getIds().size() == 1) {
				// 只处理一条
				List<OrderKey> orderKeyList = key.getOrderKeyList();
				Object result = draftService.generateExpressCode(orderKeyList.get(0));
				return JsonResult.success(Collections.singleton(result));
			}
			final String token = draftService.getToken(key.getUserId());
			draftService.generateExpressCode(key, token);
			int time = (int) Math.ceil(key.getIds().size() / 1000d * 90);
			return JsonResult.success("制单任务已提交，总订单数量 " + key.getIds().size() + " 条，预计处理时间 " + time + " 秒，请稍后查询", null);
		}
		return JsonResult.error("无任务");
	}

	/**
	 * 更新订单
	 *
	 * @param order 订单信息
	 * @return 基础信息
	 */
	@RequestMapping("/updateOrder")
	@Validated(value = {Default.class, Input.class})
	@Logger(module = Module.EJF, name = "更新订单")
	public JsonResult<?> updateOrder(@RequestBody Order order) {
		// 验证权限
		accountService.existAccountThrow(order.getUserId());
		return draftService.updateOrder(order);
	}

	/**
	 * 删除订单
	 *
	 * @param keys 订单id集合
	 * @return 基础信息
	 */
	@RequestMapping("/deleteOrder")
	@Valid
	@Logger(module = Module.EJF, name = "删除订单")
	public JsonResult<?> deleteOrder(@RequestBody OrderKeysDTO keys) {
		// 验证权限
		accountService.existAccountThrow(keys.getUserId());
		return draftService.deleteOrder(keys);
	}

	/**
	 * 展示草稿列表
	 *
	 * @param param 条件
	 * @return 基础信息
	 */
	@RequestMapping("/listOrder")
	@Valid
	@Logger(module = Module.EJF, name = "展示草稿列表")
	public JsonResult<?> listOrder(@RequestBody DraftPageDTO param) {
		// 验证权限
		accountService.existAccountThrow(param.getUserId());
		return JsonResult.success(EJFMsgCenter.SUCCESS_001, draftService.listOrder(param));
	}

	/**
	 * 展示草稿详情
	 *
	 * @param key
	 * @return 基础信息
	 */
	@RequestMapping("/orderDetails")
	@Valid
	@Logger(module = Module.EJF, name = "展示草稿详情")
	public JsonResult<?> orderDetails(@RequestBody OrderKey key) {
		// 验证权限
		accountService.existAccountThrow(key.getUserId());
		return draftService.orderDetails(key);
	}

	/**
	 * 批量创建草稿
	 *
	 * @param file   excel
	 * @param userId 发货账号
	 * @return 基础信息
	 */
	@RequestMapping("/createOrder/{userId}")
	public JsonResult<?> createOrder(@RequestParam(value = "files") MultipartFile file, @PathVariable("userId") String userId) {
		try {
			// 验证权限
			accountService.existAccountThrow(userId);
			long start = System.currentTimeMillis();
			List<Map> sourceData = new LinkedList<>();
			boolean[] isItMoreThan = {false};
			EasyExcel.read(file.getInputStream(), new ReadListener<Map>() {
				@Override
				public void onException(Exception exception, AnalysisContext context) throws Exception {
				}

				@Override
				public void invoke(Map data, AnalysisContext context) {
					sourceData.add(data);
				}

				@Override
				public void extra(CellExtra extra, AnalysisContext context) {
				}

				@Override
				public void doAfterAllAnalysed(AnalysisContext context) {
				}

				@Override
				public boolean hasNext(AnalysisContext context) {
					if (sourceData.size() > size + 1) {
						isItMoreThan[0] = true;
						return false;
					}
					;
					return true;
				}

				@Override
				public void invokeHead(Map headMap, AnalysisContext context) {
				}
			}).sheet(0).headRowNumber(0).doRead();
			if (isItMoreThan[0]) {
				throw new BusinessException("一次导入最多" + size + "条数据！");
			}
			log.info("读取Excel耗时" + (System.currentTimeMillis() - start));
			start = System.currentTimeMillis();
			Collection<String> headValues = sourceData.get(0).values();
			List<Map> source = sourceData.stream().skip(1).collect(Collectors.toList());

			// 最新模板判断
			boolean poPStation = headValues.stream().filter(Objects::nonNull).anyMatch(x -> x.contains("自提点ID"));
			if (!poPStation) {
				return JsonResult.error("当前模板非最新燕文模板，请下载最新模板对应填写信息重新导入");
			}

			// 解析Excel
			List<Order> orders = ExcelImportUtil.readExcel(headValues, source);
			List<Order> orderSuccess = new LinkedList<>();
			final String token = draftService.getToken(EJFUrl.CUSTOMER_CODE);
			// 进行数据验证
			List<ImportMessage> verifyResult = IntStream.range(0, orders.size()).mapToObj(i -> {
				Order order = orders.get(i);
				// 设置序列号
				order.setUserId(userId);
				order.setSequenceNo(String.valueOf(i + 1));
				order.setOrderSource(EJFUrl.SOURCE);
				order.setToken(token);
				long innerStart = System.currentTimeMillis();
				List<String> constraintViolation = ParameterValidationAspect.getConstraintViolation(order, Default.class, Import.class);
				log.info("验证单条数据耗时" + (System.currentTimeMillis() - innerStart));
				if (CollectionUtils.isNotEmpty(constraintViolation)) {
					return ImportMessage.builder()
							.row(Integer.valueOf(order.getSequenceNo()))
							.status("导入失败")
							.orderNumber(order.getOrderNumber())
							.message(String.join("，", constraintViolation)).build();
				}
				orderSuccess.add(order);
				return null;
			}).filter(Objects::nonNull).collect(Collectors.toList());
			log.info("验证数据耗时" + (System.currentTimeMillis() - start));
			start = System.currentTimeMillis();
			if (CollectionUtils.isNotEmpty(orderSuccess)) {
				// 倒序
				Collections.reverse(orderSuccess);
				// 获取EJF的导入信息
				ImportResultObject resultObject = draftService.createOrder(new Orders(userId, orderSuccess));
				resultObject.getData().getRecords().stream()
						.filter(rec -> !rec.getSuccess())
						.map(fail -> ImportMessage.builder()
								.row(Integer.valueOf(fail.getSequenceNo()))
								.status("导入失败")
								.orderNumber(fail.getOrderNumber())
								.message(fail.getErrorMessage()).build())
						.forEach(verifyResult::add);
			}
			log.info("接口创建耗时" + (System.currentTimeMillis() - start));
			JsonResult<?> result = JsonResult.success("全部导入成功", null);
			// 设置错误信息文件
			if (CollectionUtils.isNotEmpty(verifyResult)) {
				result.put(JsonResult.CODE_TAG, 500);
				result.put(JsonResult.MSG_TAG, "导入失败信息已下载，请查看");
				result.put(JsonResult.SUCCESS_TAG, false);
				result.put("totalCount", orders.size());
				result.put("successCount", orders.size() - verifyResult.size());
				result.put("failuresCount", verifyResult.size());
				try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
					 ExcelWriter writer = EasyExcel.write(outputStream).build()) {
					// 进行序号排序
					verifyResult.sort(Comparator.comparing(ImportMessage::getRow));
					List<Map> errorOrderData = verifyResult.stream()
							.map(ImportMessage::getRow)
							.map(i -> i - 1)
							.map(source::get)
							.collect(Collectors.toList());
					List<List<Object>> dataList = new ArrayList<>(orders.size());
					for (Map errorOrderDatum : errorOrderData) {
						List<Object> data = new ArrayList<>(50);
						for (int j = 0; j < errorOrderDatum.values().size(); j++) {
							data.add(errorOrderDatum.get(j));
						}
						dataList.add(data);
					}
					String sheetName = "错误运单数据";
					final List<CommentModel> comments = IntStream.range(0, verifyResult.size()).mapToObj(i -> {
						ImportMessage v = verifyResult.get(i);
						v.setRow(i + 1);
						return CommentModel.createCommentModel(sheetName, i, 0, v.toString());
					}).collect(Collectors.toList());
					final List<List<String>> head = headValues.stream().filter(Objects::nonNull)
							.map(Arrays::asList).collect(Collectors.toList());
					start = System.currentTimeMillis();
					WriteSheet dataSheet = EasyExcel.writerSheet()
							.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
							.registerWriteHandler(new CommentWriteHandler(comments))
							.registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 34.3, (short) 30.5))
							.sheetName(sheetName)
							.head(head)
							.build();
					WriteSheet errorSheet = EasyExcel.writerSheet()
							.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
							.registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 34.3, (short) 30.5))
							.sheetName("错误信息")
							.head(ImportMessage.class)
							.build();
					writer.write(dataList, dataSheet);
					writer.write(verifyResult, errorSheet);
					log.error("错误数据日志备份: {}, {}", JSON.toJSONString(dataList), JSON.toJSONString(verifyResult));
					writer.finish();
					log.info("生成Excel耗时" + (System.currentTimeMillis() - start));
					String base64 = EJFUtil.byteToString(outputStream);
					result.put("errorMsg", base64);
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}
				result.put("fileName", String.format(IMPORT_ERROR_MSG, DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd")));
				result.put(JsonResult.CODE_TAG, 502);
			}
			return result;
		} catch (BusinessException e) {
			throw e;
		} catch (Exception e) {
			log.info(userId.concat(" " + e.getMessage()), e);
			return JsonResult.error(EJFMsgCenter.ERROR_005);
		}
	}

	/**
	 * 批量创建草稿
	 *
	 * @param order 订单信息
	 * @return 基础信息
	 */
	@RequestMapping("/createOrderSimple")
	@Logger(module = Module.EJF, name = "批量创建草稿")
	public JsonResult<?> createOrderSimple(@RequestBody Order order) {
		// 验证权限
		accountService.existAccountThrow(order.getUserId());
		List<String> constraintViolation = new ArrayList<>(2);
		if (StringUtils.isBlank(order.getUserId())) {
			constraintViolation.add("发货账号不能为空");
		}
		if (StringUtils.isBlank(order.getOrderNumber())) {
			constraintViolation.add("订单号不能为空");
		}
		if (CollectionUtil.isNotEmpty(constraintViolation)) {
			return JsonResult.error(String.join(",", constraintViolation));
		}
		ImportResultObject result = draftService.createOrder(order);
		RecordsDTO record = Optional.ofNullable(result.getData())
				.map(ImportResultObject.DataDTO::getRecords)
				.filter(CollectionUtil::isNotEmpty)
				.map(recordsDTOS -> recordsDTOS.get(0))
				.orElse(RecordsDTO.build(false, "接口错误，无法获取到制单数据"));
		if (record.getSuccess()) {
			return JsonResult.success();
		}
		return JsonResult.error(record.getErrorMessage());
	}


	/**
	 * 获取EJF模板
	 *
	 * @return 基础信息
	 */
	@RequestMapping("/getEJFGenericTemplate")
	@Logger(module = Module.EJF, name = "获取EJF模板")
	public JsonResult<?> getEJFGenericTemplate() {
		final UserAgent user = getUser();
		final List<FileUploadRecord> template = draftService.getEJFGenericTemplate();
		for (FileUploadRecord record : template) {
			if (Objects.nonNull(user)) {
				final LambdaQueryWrapper<ClientReadRecord> choose =
						Wrappers.<ClientReadRecord>lambdaQuery()
								.eq(ClientReadRecord::getUserId, user.getUserId())
								.eq(ClientReadRecord::getTypeId, record.getId())
								.eq(ClientReadRecord::getType, "下载EJF模板");
				final ClientReadRecord clientReadRecord = clientReadRecordService.getOne(choose, false);
				if (Objects.isNull(clientReadRecord)) {
					record.setChange(true);
				}
			}
		}
		return JsonResult.success(template);
	}

	@Resource
	private PacketBusinessApplyService packetBusinessApplyService;

	/**
	 * 下载EJF模板
	 */
	@RequestMapping("/downloadEJFGenericTemplate/{id}")
	public void downloadEJFGenericTemplate(@PathVariable("id") int id, HttpServletResponse response) throws IOException {
		FileUploadRecord fileUploadRecord = draftService.downloadEJFGenericTemplate(id);
		EJFUtil.setDownloadResponse(response, fileUploadRecord.getOriginalName(), EJFUtil.DOWNLOAD);
		try (okhttp3.ResponseBody responseBody = HttpUtil.doGet(fileUploadRecord.getUrl(), okhttp3.ResponseBody.class);
			 ExcelWriter writer = EasyExcel.write(response.getOutputStream()).withTemplate(responseBody.byteStream()).build()) {
			// 设置文件已下载
			saveClientRead(id);
//			final String merchantCode = packetBusinessApplyService.getPacketMerchantCode(getUserCode());
//			WriteSheet ioss = EasyExcel.writerSheet("IOSS").head(TaxManageVo.Detail.class).build();
//			WriteSheet vat = EasyExcel.writerSheet("税号").head(TaxManageVo.Detail.class).build();
			WriteSheet product = EasyExcel.writerSheet("燕文产品名称").head(Channel.class).build();
			WriteSheet country = EasyExcel.writerSheet("国家中英文&二字代码").head(Country.class).build();
			WriteSheet warehouse = EasyExcel.writerSheet("交货仓").head(SysWarehouse.class).build();
			WriteSheet deliveryLocation = EasyExcel.writerSheet("海外交货地").head(OverseasDeliveryLocationDTO.class).build();
			List<Country> cacheCountry = new ArrayList<>(baseInfoService.getCacheCountry());
			Collections.sort(cacheCountry);
			writer.fill(cacheCountry, country);
			List<SysWarehouse> cacheWarehouse = baseInfoService.getSysWarehouses();
			writer.fill(cacheWarehouse, warehouse);
			List<OverseasDeliveryLocationDTO> overseasDeliveryLocation = cmccService.getOverseasDeliveryLocation();
			overseasDeliveryLocation = overseasDeliveryLocation.stream()
					.filter(x -> Objects.equals(x.getIsEnable(), 1))
					.collect(Collectors.toList());
			writer.fill(overseasDeliveryLocation, deliveryLocation);
//			List<TaxManageVo.Detail> iossList = taxManageService.queryValidityTaxCode(1, merchantCode);
//			writer.fill(iossList, ioss);
//			List<TaxManageVo.Detail> vatList = taxManageService.queryValidityTaxCode(2, merchantCode);
//			writer.fill(vatList, vat);
			// 商业快递
			if (fileUploadRecord.getType().equals(FileUploadRecordTypeEnum.BUSINESS.getId())) {
				final List<BusinessOrderEnumDTO.DataDTO.ValueDTO> taxType =
						cmccService.getBusinessOrderTypeEnum(CmccItemEnum.TAX_TYPE);
				final List<BusinessOrderEnumDTO.DataDTO.ValueDTO> packType =
						cmccService.getBusinessOrderTypeEnum(CmccItemEnum.PACK_TYPE);
				final List<BusinessOrderEnumDTO.DataDTO.ValueDTO> commodityType =
						cmccService.getBusinessOrderTypeEnum(CmccItemEnum.COMMODITY_TYPE);
				final List<BusinessOrderEnumDTO.DataDTO.ValueDTO> batteryType =
						cmccService.getBusinessOrderTypeEnum(CmccItemEnum.BATTERY_TYPE);
				WriteSheet taxTypeSheet = EasyExcel.writerSheet("税号类型")
						.head(BusinessOrderEnumDTO.DataDTO.ValueDTO.class).build();
				WriteSheet packTypeSheet = EasyExcel.writerSheet("包裹类型")
						.head(BusinessOrderEnumDTO.DataDTO.ValueDTO.class).build();
				WriteSheet commodityTypeSheet = EasyExcel.writerSheet("商品类型")
						.head(BusinessOrderEnumDTO.DataDTO.ValueDTO.class).build();
				WriteSheet batteryTypeSheet = EasyExcel.writerSheet("电池类型")
						.head(BusinessOrderEnumDTO.DataDTO.ValueDTO.class).build();
				writer.fill(taxType, taxTypeSheet);
				writer.fill(packType, packTypeSheet);
				writer.fill(commodityType, commodityTypeSheet);
				writer.fill(batteryType, batteryTypeSheet);
				final InnerBeChannelGetListDTO result = businessService.getBaseChannel(new InnerBeChannelGetListParamDTO());
				if (result.getSuccess()) {
					List<Channel> cacheChannel = result.getData();
					cacheChannel.removeIf(channel -> Objects.equals(channel.getStatus(), "0"));
					Collections.sort(cacheChannel);
					writer.fill(cacheChannel, product);
				}
			} else{
				List<Channel> cacheChannel = new ArrayList<>(baseInfoService.getCacheChannel());
				cacheChannel.removeIf(channel -> Objects.equals(channel.getStatus(), "0"));
				Collections.sort(cacheChannel);
				writer.fill(cacheChannel, product);
			}
			writer.finish();
		}
	}

	/**
	 * 设置文件已下载
	 *
	 * @param id 模板ID
	 */
	private void saveClientRead(int id) {
		final UserAgent user = getUser();
		if (Objects.nonNull(user)) {
			final LambdaQueryWrapper<ClientReadRecord> choose =
					Wrappers.<ClientReadRecord>lambdaQuery()
							.eq(ClientReadRecord::getUserId, user.getUserId())
							.eq(ClientReadRecord::getTypeId, String.valueOf(id))
							.eq(ClientReadRecord::getType, "下载EJF模板");
			final ClientReadRecord clientReadRecord = clientReadRecordService.getOne(choose, false);
			if (Objects.isNull(clientReadRecord)) {
				final ClientReadRecord record = ClientReadRecord.builder().userId(user.getUserId())
						.typeId(String.valueOf(id)).type("下载EJF模板").build();
				clientReadRecordService.save(record);
			}
		}
	}
}
