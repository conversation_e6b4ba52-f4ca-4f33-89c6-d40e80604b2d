package com.ywwl.customer.center.api.common;

import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.common.utils.StringHandleUtils;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.modules.common.auth.dto.*;
import com.ywwl.customer.center.modules.common.auth.enums.*;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.*;
import com.ywwl.customer.center.modules.common.provider.enums.ContractTypeEnum;
import com.ywwl.customer.center.modules.general.crm.dto.AttachOcrDTO;
import com.ywwl.customer.center.modules.general.crm.dto.ValidateCustomerDTO;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.service.CrmService;
import com.ywwl.customer.center.modules.general.crm.vo.CardAttachOcrVo;
import com.ywwl.customer.center.modules.general.crm.vo.SignContractVO;
import com.ywwl.customer.center.modules.international.constant.StraightCrmConstant;
import com.ywwl.customer.center.system.service.SmsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/3/2 9:50
 */
@RequestMapping("customerAuth")
@RestController
public class CustomerAuthController extends BaseController {
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private SmsService smsService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private CrmService crmService;

    /**
     * @author: dinghy
     * @createTime: 2024/4/3 10:36
     * @description: 查询客户进入首页展示弹框的认证状态
     */
    @GetMapping("getCustomerIndexAuthState")
    public JsonResult<CustomerAuthStateVo> getCustomerIndexAuthState() {
        CustomerAuthStateVo authState = customerAuthService.getAuthState(getUserCode());
        return JsonResult.success(authState);
    }

    /**
     * <AUTHOR>
     * @description 查询认证状态
     * @date 2023/3/2 10:52
     **/
    @GetMapping("getCustomerAuthStatus")
    public JsonResult<String> getCustomerAuthStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        String status = customerAuthService.getCustomerAuthStatus(currentUser.getMerchantNo());
        JsonResult<String> success = JsonResult.success();
        success.setData(status);
        return success;
    }

    /**
     * <AUTHOR>
     * @description
     * @date 2023/3/3 16:23
     **/
    @GetMapping("getCustomerAuthInfo")
    public JsonResult<CustomerAuthVo> getCustomerAuthInfo(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        CustomerAuthVo customerAuthVo = customerAuthService.getCustomerAuthInfo(currentUser.getMerchantNo());
        privacyDimmerCustomer(customerAuthVo);
        return JsonResult.success(customerAuthVo);
    }

    /**
     * <AUTHOR>
     * @description 对数据进行脱敏, 防止客户数据泄露
     * @date 2023/6/29 10:13
     **/
    public void privacyDimmerCustomer(CustomerAuthVo customerAuthVo) {
        if (CustomerAuthStatusEnum.AUTH_SUCCESS.value().equals(customerAuthVo.getAuthStatus())) {
            customerAuthVo.setCorporateCard(null);
            customerAuthVo.setCorporatePhone(null);
            customerAuthVo.setMobile(null);
            customerAuthVo.setCorporateName(PrivacyDimmer.maskName(customerAuthVo.getCorporateName()));
            customerAuthVo.setPersonCard(PrivacyDimmer.maskIdCard(customerAuthVo.getPersonCard()));
            if (!ApplyTypeEnum.CHANGE_ID_CARD_ATTACH.getValue().equals(customerAuthVo.getApplyType())) {
                customerAuthVo.setRegisterNumber(PrivacyDimmer.maskIdCard(customerAuthVo.getRegisterNumber()));
            }
            customerAuthVo.setPersonBankCard(null);
            customerAuthVo.setBankBranchName(null);
            customerAuthVo.setCompanyBankCard(null);
            if (ApplyTypeEnum.APPLY_SUCCESS.getValue().equals(customerAuthVo.getApplyType())) {
                customerAuthVo.setCardAttach(null);
                customerAuthVo.setCardBackAttach(null);
                customerAuthVo.setCardHoldAttach(null);
            }
        }

    }

    /**
     * <AUTHOR>
     * @description 保存客户类型
     * @date 2023/3/3 17:18
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交客户认证类型")
    @PostMapping("saveCustomerType")
    public JsonResult<Object> saveCustomerType(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid SaveCustomerTypeDTO saveCustomerTypeDTO) {
        needAdmin(currentUser);
        saveCustomerTypeDTO.setNo(currentUser.getMerchantNo());
        saveCustomerTypeDTO.setUserCode(currentUser.getUserCode());
        customerAuthService.saveCustomerType(saveCustomerTypeDTO);
        return JsonResult.success();
    }

    private void needAdmin(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Assert.isTrue(currentUser.isAdmin(), ResponseCode.PORTAL_5027.getMessage());
    }

    /**
     * <AUTHOR>
     * @description 提交个人认证
     * @date 2023/3/3 17:43
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交个人认证")
    @PostMapping("savePersonApply")
    public JsonResult<Object> savePersonApply(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid SavePersonApplyDTO savePersonApplyDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .idCard(savePersonApplyDTO.getIdCard())
                        .bankCard(savePersonApplyDTO.getBankCard())
                        .desc("提交个人认证")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        needAdmin(currentUser);
        savePersonApplyDTO.setNo(currentUser.getMerchantNo());
        savePersonApplyDTO.setUserCode(currentUser.getUserCode());
        savePersonApplyDTO.setAdminMobile(currentUser.getPhone());
        StringHandleUtils.stripTrim(savePersonApplyDTO);
        String idCard = handleIdCard(savePersonApplyDTO.getCertificateType(), savePersonApplyDTO.getIdCard());
        savePersonApplyDTO.setIdCard(idCard);
        PersonAuthVo personAuthVo = customerAuthService.savePersonApply(savePersonApplyDTO);
        if (Objects.nonNull(personAuthVo)) {
            if (PersonAuthResultEnum.SUCCESS.getValue().equals(personAuthVo.getAuthResult())) {
                return JsonResult.success(PersonAuthResultEnum.SUCCESS.getValue());
            }
        }
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 获取人脸认证地址
     * @date 2023/3/6 9:51
     **/
    @PostMapping("getFaceAuthUrl")
    public JsonResult<String> getFaceAuthUrl(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid QueryFaceAuthDTO queryFaceAuthDTO) {
        queryFaceAuthDTO.setNo(currentUser.getMerchantNo());
        queryFaceAuthDTO.setUserCode(currentUser.getUserCode());
        String url = customerAuthService.getFaceAuthUrl(queryFaceAuthDTO);
        return JsonResult.success(url);
    }

    /**
     * <AUTHOR>
     * @description 查询人脸识别结果
     * @date 2023/3/6 11:01
     **/
    @PostMapping("getFaceAuthResult")
    public JsonResult<Integer> getFaceAuthResult(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid QueryFaceAuthDTO queryFaceAuthDTO) {
        queryFaceAuthDTO.setNo(currentUser.getMerchantNo());
        queryFaceAuthDTO.setUserCode(currentUser.getUserCode());
        Integer authResult = customerAuthService.getFaceAuthResult(queryFaceAuthDTO);
        return JsonResult.success(authResult);
    }

    /**
     * <AUTHOR>
     * @description 发起个人银行卡认证
     * @date 2023/3/6 11:21
     **/
    @Logger(module = Module.CUSTOMER_AUTH, name = "发起个人银行卡认证")
    @GetMapping("createPersonBankAuth")
    public JsonResult<Object> createPersonBankAuth(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        needAdmin(currentUser);
        customerAuthService.createPersonBankAuth(currentUser.getMerchantNo());
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 校验个人银行预留手机号验证码
     * @date 2023/3/6 13:41
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "发起个人银行卡认证")
    @PostMapping("checkPersonBankMobile")
    public JsonResult<Object> checkPersonBankMobile(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid CheckBankMobileDTO checkBankMobileDTO) {
        needAdmin(currentUser);
        checkBankMobileDTO.setNo(currentUser.getMerchantNo());
        checkBankMobileDTO.setUserCode(currentUser.getUserCode());
        customerAuthService.checkPersonBankMobile(checkBankMobileDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 提交企业代办人认证
     * @date 2023/3/6 14:04
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交企业代办人认证")
    @PostMapping("saveCompanyAgentAuth")
    public JsonResult<Object> saveCompanyAgentAuth(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid SaveCompanyAgentAuthDTO saveCompanyAgentAuthDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .idCard(saveCompanyAgentAuthDTO.getIdCard())
                        .bankCard(saveCompanyAgentAuthDTO.getBankCard())
                        .desc("提交企业代办人认证")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        needAdmin(currentUser);
        saveCompanyAgentAuthDTO.setNo(currentUser.getMerchantNo());
        saveCompanyAgentAuthDTO.setAdminMobile(currentUser.getPhone().trim());
        saveCompanyAgentAuthDTO.setUserCode(currentUser.getUserCode());
        SaveCompanyAgentAuthDTO.checkPersonAuthDTO(saveCompanyAgentAuthDTO);
        String idCard = handleIdCard(saveCompanyAgentAuthDTO.getCertificateType(), saveCompanyAgentAuthDTO.getIdCard());
        saveCompanyAgentAuthDTO.setIdCard(idCard);
        PersonAuthVo personAuthVo = customerAuthService.saveCompanyAgentAuth(saveCompanyAgentAuthDTO);
        if (Objects.nonNull(personAuthVo)) {
            if (PersonAuthResultEnum.SUCCESS.getValue().equals(personAuthVo.getAuthResult())) {
                return JsonResult.success(PersonAuthResultEnum.SUCCESS.getValue());
            }
        }
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 保存企业认证
     * @date 2023/3/6 14:38
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交企业信息认证")
    @PostMapping("saveCompanyApply")
    public JsonResult<Object> saveCompanyApply(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid SaveCompanyApplyDTO companyAuthDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .idCard(companyAuthDTO.getCorporateCard())
                        .bankCard(companyAuthDTO.getBankCard())
                        .desc("提交企业信息认证")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        needAdmin(currentUser);
        companyAuthDTO.setNo(currentUser.getMerchantNo());
        companyAuthDTO.setUserCode(currentUser.getUserCode());
        StringHandleUtils.stripTrim(companyAuthDTO);
        // 如果是需要审核
        if(companyAuthDTO.getAudit()){
            if(companyAuthDTO.getPersonIsCorporate()==0){
                if(StringUtils.isBlank(companyAuthDTO.getBankCode())||StringUtils.isBlank(companyAuthDTO.getBankCard())||StringUtils.isBlank(companyAuthDTO.getBankAccountProof())||StringUtils.isBlank(companyAuthDTO.getBankName())){
                    return JsonResult.error(ResponseCode.PORTAL_5077);
                }
            }
        }
        CompanyAuthVo companyAuthVo = customerAuthService.saveCompanyApply(companyAuthDTO);
        if (Objects.nonNull(companyAuthVo)) {
            if (CompanyAuthStatusEnum.SUCCESS.getValue().equals(companyAuthVo.getAuthStatus())) {
                // 认证成功
                return JsonResult.success(CompanyAuthStatusEnum.SUCCESS.getValue());
            }
        }
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 根据关键字查询支行列表
     * @date 2023/3/6 15:33
     **/
    @PostMapping("getBranchBankList")
    public JsonResult<Object> getBranchBankList(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid QueryBranchBankDTO queryBranchBankDTO) {
        queryBranchBankDTO.setNo(currentUser.getMerchantNo());
        queryBranchBankDTO.setUserCode(currentUser.getUserCode());
        List<String> list = customerAuthService.getBranchBankList(queryBranchBankDTO);
        return JsonResult.success(list);
    }

    /**
     * <AUTHOR>
     * @description
     * @date 2023/3/6 16:15
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "提交企业银行卡认证")
    @PostMapping("saveCompanyBank")
    public JsonResult<Object> saveCompanyBank(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid SaveCompanyBankDTO saveCompanyBankDTO) {
        // 调用crm操作
        crmService.noticeCrmValidateCustomerInfo(
                ValidateCustomerDTO.builder()
                        .userCode(currentUser.getUserCode())
                        .bankCard(saveCompanyBankDTO.getBankCard())
                        .desc("提交企业银行卡认证")
                        .changeType(StraightCrmConstant.UPDATE)
                        .build()
        );
        needAdmin(currentUser);
        saveCompanyBankDTO.setNo(currentUser.getMerchantNo());
        saveCompanyBankDTO.setUserCode(currentUser.getUserCode());
        StringHandleUtils.stripTrim(saveCompanyBankDTO);
        customerAuthService.saveCompanyBank(saveCompanyBankDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 企业银行卡金额校验
     * @date 2023/3/6 16:42
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "企业打款金额校验认证")
    @PostMapping("verifyCompanyBankAmount")
    public JsonResult<Object> verifyCompanyBankAmount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody @Valid VerifyCompanyBankAmountDTO verifyCompanyBankAmountDTO) {
        needAdmin(currentUser);
        verifyCompanyBankAmountDTO.setNo(currentUser.getMerchantNo());
        verifyCompanyBankAmountDTO.setUserCode(currentUser.getUserCode());
        customerAuthService.verifyCompanyBankAmount(verifyCompanyBankAmountDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 查询企业金额打款状态
     * @date 2023/3/7 11:26
     **/
    @GetMapping("getCompanyPayState")
    public JsonResult<Integer> getCompanyPayState(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Integer state = customerAuthService.getCompanyPayState(currentUser.getMerchantNo());
        return JsonResult.success(state);
    }

    /**
     * <AUTHOR>
     * @description 重新发起企业认证
     * @date 2023/3/13 17:41
     **/
    @Logger(module = Module.CUSTOMER_AUTH, name = "重新发起客户认证")
    @GetMapping("reAuth")
    public JsonResult<Object> reCompanyAuth(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        needAdmin(currentUser);
        customerAuthService.reCompanyAuth(currentUser.getMerchantNo());
        return JsonResult.success();
    }


    /**
     * <AUTHOR>
     * @description 如果身份证信息是小写x转换为大写
     * @date 2023/4/13 17:02
     **/
    public String handleIdCard(Integer type, String idCard) {
        if (CertificateTypeEnum.ID_CARD.getCode().equals(type)) {
            return idCard.replace("x", "X");
        }
        return idCard;
    }

    /**
     * <AUTHOR>
     * @description 校验客户证件号唯一性
     * @date 2023/5/13 9:27
     **/
    @PostMapping("validateCustomerUnique")
    public JsonResult<Object> validateCustomerUnique(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ValidateCustomerUniqueDTO validateCustomerUniqueDTO) {
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(validateCustomerUniqueDTO.getCustomerType())) {
            Assert.isTrue(StringUtils.isNotBlank(validateCustomerUniqueDTO.getPersonCard()), "个人身份证不能为空");
        }
        if (CustomerTypeEnum.ENTERPRISE.value().equals(validateCustomerUniqueDTO.getCustomerType())) {
            Assert.isTrue(StringUtils.isNotBlank(validateCustomerUniqueDTO.getRegisterNumber()), "企业营业执照号不能为空");
            Assert.isTrue(StringUtils.isNotBlank(validateCustomerUniqueDTO.getCompanyName()), "企业名称不能为空");
        }
        validateCustomerUniqueDTO.setUserCode(currentUser.getUserCode());
        customerAuthService.validateCustomerUnique(validateCustomerUniqueDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 校验是否客户已经实名认证
     * @date 2023/8/4 14:42
     **/
    @RequestMapping("checkCustomerRealAuth")
    public JsonResult<Object> checkCustomerRealAuth(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Integer status = customerAuthService.checkCustomerRealAuth(currentUser.getMerchantNo());
        return JsonResult.success(status);
    }

    /**
     * <AUTHOR>
     * @description 发起补齐资料
     * @date 2023/8/7 18:06
     **/
    @RequestMapping("completeData")
    public JsonResult<Object> completeData(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        customerAuthService.completeData(currentUser.getUserCode(), currentUser.getLoginName());
        return JsonResult.success();
    }

    /**
     * @author: dinghy
     * @createTime: 2024/6/5 15:01
     * @description: 发起补齐证件照
     */
    @RequestMapping("completeAttach")
    public JsonResult<Object> completeAttach(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Integer res = customerAuthService.completeAttach(currentUser.getUserCode(), currentUser.getLoginName());
        return JsonResult.success(res);
    }

    /**
     * <AUTHOR>
     * @description 变更客户名
     * @date 2023/9/5 15:39
     **/
    @Idempotent
    @Logger(module = Module.CUSTOMER_AUTH, name = "自主发起变更客户名")
    @RequestMapping("changeCustomerName")
    public JsonResult<Object> changeCustomerName(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @Valid @RequestBody ChangeCustomerNameDTO changeCustomerNameDTO, HttpServletRequest request) {
        needAdmin(currentUser);
        String relationShip = changeCustomerNameDTO.getRelationShip();
        if("控股股东或实际控制人,实际控制人控制的其他公司".contains(relationShip)){
            boolean realAuth = customerAuthService.checkRealAuth(currentUser.getUserCode());
            if(!realAuth){
                return JsonResult.error(ResponseCode.PORTAL_5078);
            }
        }
        String id = request.getSession().getId();
        smsService.checkSmsCode(id, currentUser.getPhone(), changeCustomerNameDTO.getSmsCode());
        changeCustomerNameDTO.setUserCode(currentUser.getUserCode());
        customerAuthService.changeCustomerName(changeCustomerNameDTO);
        return JsonResult.success();
    }

    /**
     * <AUTHOR>
     * @description 查询变更客户名信息
     * @date 2023/9/6 10:38
     **/
    @GetMapping("getCustomerChangeInfo")
    public JsonResult<ChangeCustomerNameVo> getCustomerChangeInfo(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        if (!currentUser.isAdmin()) {
            ChangeCustomerNameVo changeCustomerNameVo = new ChangeCustomerNameVo();
            changeCustomerNameVo.setChangeStatus(ChangeCustomerEnum.NOT_ALLOW_CHANGE.getValue());
            return JsonResult.success(changeCustomerNameVo);
        }
        ChangeCustomerNameVo changeCustomerNameVo = customerAuthService.getCustomerChangeInfo(currentUser.getUserCode());
        String maskMobile = PrivacyDimmer.maskMobile(currentUser.getPhone());
        changeCustomerNameVo.setAdminPhone(maskMobile);
        return JsonResult.success(changeCustomerNameVo);
    }

    /**
     * <AUTHOR>
     * @date 2023/9/11 10:20
     * @description 获取转让协议
     */
    @Logger(module = Module.CUSTOMER_AUTH, name = "获取转让协议")
    @GetMapping("getTransferAgreement")
    public JsonResult<Object> getTransferAgreement(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        QueryContractDTO queryContractDTO = new QueryContractDTO();
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        queryContractDTO.setContractType(ContractTypeEnum.ACTIVATE_CONTRACT.value());
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        try {
            Thread.currentThread().sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return JsonResult.success(signContractVO.getUrl());
    }

    /**
     * <AUTHOR>
     * @date 2023/10/13 9:27
     * @description 证件信息ocr识别
     */
    @PostMapping("attachOcr")
    public JsonResult<CardAttachOcrVo> attachOcr(@RequestBody AttachOcrDTO attachOcrDTO) {
        if (attachOcrDTO.getType() == 0) {
            if (StringUtils.isBlank(attachOcrDTO.getPersonBackPic()) && StringUtils.isBlank(attachOcrDTO.getPersonFrontPic())) {
                return JsonResult.error("图片信息缺失");
            }
        } else {
            if (StringUtils.isBlank(attachOcrDTO.getCompanyPic())) {
                return JsonResult.error("营业执照不能为空");
            }
        }
        CardAttachOcrVo attachOcr = commonCrmService.getAttachOcr(attachOcrDTO);
        return JsonResult.success(attachOcr);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/7/10 16:03
     * @description: 校验是否实名认证
     */
    @GetMapping("validateRealAuth")
    public JsonResult validateRealAuth(){
        String userCode = getUserCode();
        boolean realAuth = customerAuthService.checkRealAuth(userCode);
        return JsonResult.success(realAuth);
    }


}
