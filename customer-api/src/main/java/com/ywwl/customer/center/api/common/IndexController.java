package com.ywwl.customer.center.api.common;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ywwl.customer.center.api.BaseController;
import com.ywwl.customer.center.common.constant.SysUserConstants;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessApplyStatusEnum;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.enums.PlatformTypeEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAreaEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerAuthStateEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerStatusEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerTypeEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.BusinessAndCustomerVo;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerAuthStateVo;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerAuthVo;
import com.ywwl.customer.center.modules.common.provider.domain.SysContentUser;
import com.ywwl.customer.center.modules.common.provider.dto.AutowiredDTO;
import com.ywwl.customer.center.modules.common.provider.service.IndexService;
import com.ywwl.customer.center.modules.common.provider.service.PaymentAccountService;
import com.ywwl.customer.center.modules.common.provider.service.SysContentUserService;
import com.ywwl.customer.center.modules.common.provider.vo.ReminderVo;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.vo.FbaApplyInfoVO;
import com.ywwl.customer.center.modules.international.constant.InternationalConstant;
import com.ywwl.customer.center.modules.international.dto.TailStrokeDTO;
import com.ywwl.customer.center.modules.international.enums.FrozenStatusEnum;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.vo.BusinessStatusVO;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.overseas.service.OverseaService;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.YWEApplyInfoVo;
import com.ywwl.customer.center.system.dto.MerchantDTO;
import com.ywwl.customer.center.system.entity.SysMenuVO;
import com.ywwl.customer.center.system.entity.TbMenuAndButton;
import com.ywwl.customer.center.system.service.CrmUserService;
import com.ywwl.customer.center.system.service.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * @author: dinghy
 * @date: 2023/3/20 14:31
 */
@Slf4j
@RequestMapping("/index")
@RestController
@Validated
public class IndexController extends BaseController {

    /**
     * 未开通权限状态
     */
    public static final ArrayList<Integer> STATUS_ENUMS =
            CollectionUtil.newArrayList(
                    BusinessApplyStatusEnum.NOT_SUPPORT.getValue(),
                    BusinessApplyStatusEnum.WAIT_DEVELOP.getValue(),
                    BusinessApplyStatusEnum.NOT_OPEN.getValue(),
                    BusinessApplyStatusEnum.NOT_AUTH.getValue(),
                    BusinessApplyStatusEnum.WAIT_AUDIT.getValue(),
                    BusinessApplyStatusEnum.AUDIT_FAIL.getValue()
            );

    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PaymentAccountService paymentAccountService;
    @Resource
    private FbaApplyService fbaApplyService;
    @Resource
    private IndexService indexService;
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private SysContentUserService sysContentUserService;

    @Resource
    private CrmUserService crmUserService;
    @Value("${hwp.property}")
    private String hwpProperty;
    @Resource
    private OverseaService overseaService;
    @Resource
    private YWEOverseaService ywEOverseaService;

    /**
     * <AUTHOR>
     * @description 获取业务申请状态
     * @date 2023/3/20 16:21
     **/
    @PostMapping("getBusinessApplyStatus")
    public JsonResult<Object> getBusinessApplyStatus(@JsonParam @NotNull(message = "[类型]不能为空") Integer type, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult<Object> jsonResult = JsonResult.success();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(type);
        BusinessStatusVO businessStatusVO = null;
        switch (businessTypeEnum) {
            case STRAIGHT:
                businessStatusVO = packetBusinessApplyService.getPacketStatusAndFronzenStatus(currentUser.getUserCode());
                break;
            case FBA:
                businessStatusVO = fbaApplyService.getFbaStatusAndFrozen(currentUser.getUserCode());
                break;
            case CHINA_WAREHOUSE:
                businessStatusVO = new BusinessStatusVO();
                businessStatusVO.setApplyStatus(BusinessApplyStatusEnum.NOT_OPEN.getValue());
                break;
            case OVERSEA:
                businessStatusVO = new BusinessStatusVO();
                businessStatusVO.setApplyStatus(BusinessApplyStatusEnum.WAIT_DEVELOP.getValue());
                businessStatusVO.setFrozenStatus(FrozenStatusEnum.FROZEN.value());
                break;
        }
        jsonResult.setData(businessStatusVO);
        return jsonResult;
    }

    /**
     * <AUTHOR>
     * @description 查询多个业务类型状态
     * @date 2023/4/19 13:40
     **/
    @PostMapping("getBusinessStatusByTypes")
    public JsonResult<Object> getBusinessStatusByTypes(@JsonParam @NotNull(message = "[类型]不能为空") List<Integer> types, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        Map<String, Integer> res = new HashMap<>(4);
        for (Integer type : types) {
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(type);
            switch (businessTypeEnum) {
                case STRAIGHT:
                    Integer status = packetBusinessApplyService.getNotLimitState(currentUser.getUserCode());
                    res.put(String.valueOf(type), status);
                    break;
                case FBA:
                    Integer fbaApplyStatus = fbaApplyService.getFbaApplyStatus(currentUser.getUserCode());
                    res.put(String.valueOf(type), fbaApplyStatus);
                    break;
                case CHINA_WAREHOUSE:
                    res.put(String.valueOf(type), BusinessApplyStatusEnum.NOT_OPEN.getValue());
                    break;
                case OVERSEA:
                    res.put(String.valueOf(type), getOverseaApplyStatus(currentUser.getUserCode()));
                    break;
            }
        }
        return JsonResult.success(res);
    }

    public Integer getOverseaApplyStatus(String userCode) {
        // 以前海外派数据存在
        boolean checkOverseaMerchant = overseaService.checkOverseaMerchant(currentUser().getUserCode());
        if(checkOverseaMerchant){
            return BusinessApplyStatusEnum.SIGNED.getValue();
        }
        // 判断是否为YWE
        // 国外不支持YWE
        CustomerAuthVo customerAuthInfo = customerAuthService.getCustomerInfoByUserCode(userCode);
        if(customerAuthInfo==null){
            return BusinessApplyStatusEnum.NOT_AUTH.getValue();
        }
        // 认证状态
        Integer authStatus = customerAuthInfo.getAuthStatus();
        Integer applyType = customerAuthInfo.getApplyType();
        // 新客户未认证，不展示YWE
        if(1!=authStatus&&applyType ==0){
            return BusinessApplyStatusEnum.NOT_AUTH.getValue();
        }
        YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(userCode);
        if (yweApplyInfoVo == null) {
            return BusinessApplyStatusEnum.NOT_OPEN.getValue();
        }
        Integer applyStatus = ywEOverseaService.getApplyStatus(yweApplyInfoVo);
        return applyStatus;
    }

    /***
     * //下载文件服务文件
     * <AUTHOR>
     * @date 2023/3/30 16:05
     * @param url  地址
     * @param name 生成文件名
     * @param res  响应头
     */
    @Logger(module = Module.COMMON, name = "下载文件服务器文件")
    @RequestMapping(value = "/getImage")
    public void getImage(@RequestParam String url, @RequestParam String name, HttpServletResponse res) {
        String fileFormat = url.substring(url.lastIndexOf("."));
        HttpUtil.download(url, res, name + fileFormat, true);
    }


    /***
     * //  获取有权限的菜单信息
     * <AUTHOR>
     * @date 2023/3/1 17:59
     * @param currentUser 用户信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<com.ywwl.customer.center.modules.crm.domain.TbMenuAndButton>
     */
    @Logger(module = Module.COMMON, name = "获取权限菜单", recordRsp = false)
    @RequestMapping("qryMyMenuInfos")
    @ResponseBody
    public JsonResult<TbMenuAndButton> qryMyMenuInfos(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        log.info("开始获取菜单！" + currentUser.getAccessToken() + currentUser.getUserId());
        // 获取菜单信息
        JSONObject menuResult = permissionService.requestSubUserPermissionToMenu(currentUser, PlatformTypeEnum.WEB.value());
        permissionService.checkMerchantAttributes(currentUser, menuResult);
        TbMenuAndButton menuInfo = menuResult.getObject("returnlist", TbMenuAndButton.class);
        final List<String> permsList = menuInfo.getPermsList();
        try {
            // 是否开通FBA
            final Integer applyStatus = fbaApplyService.getFbaApplyStatus(currentUser.getUserCode());
            if (!STATUS_ENUMS.contains(applyStatus)) {
                permsList.add("fbaOrder");
            }
        } catch (Throwable ignored) {
        }
        boolean overseaSettleCompany=false;
        try {
            // 是否开通小包
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
            if(packetApplyInfo!=null){
                overseaSettleCompany= InternationalConstant.HONG_KONG_COMPANY.equals(packetApplyInfo.getSettleCompanyCode());
            }
            Integer applyStatus = packetBusinessApplyService.getApplyStatus(packetApplyInfo);
            if (!STATUS_ENUMS.contains(applyStatus)) {
                permsList.add("smallPackage");
            }
        } catch (Throwable ignored) {
        }
        try {
            // 是否开通海外派
            Integer overseaApplyStatus = getOverseaApplyStatus(currentUser.getUserCode());
            if (!STATUS_ENUMS.contains(overseaApplyStatus)||BusinessApplyStatusEnum.AUDIT_FAIL.getValue().equals(overseaApplyStatus)||BusinessApplyStatusEnum.WAIT_AUDIT.getValue().equals(overseaApplyStatus)) {
                permsList.add("overseas");
            }

        } catch (Throwable ignored) {
        }
        List<SysMenuVO> menus = menuInfo.getMenus();
        CustomerAuthVo customerAuthInfo = customerAuthService.getCustomerAuthInfo(currentUser.getMerchantNo());
        if(customerAuthInfo!=null){
            Integer customerArea = customerAuthInfo.getCustomerArea();
            boolean  overseaCompany = (CustomerAreaEnum.OVERSEA.getCode().equals(customerArea) || CustomerAreaEnum.GANG_AO_TAI.getCode().equals(customerArea))&&CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthInfo.getCustomerType());
            // 香港结算公司或者境外-港澳台企业，不展示线下充值
            if(overseaCompany||overseaSettleCompany){
                if(menus!=null){
                    menus.forEach(sysMenuVO -> {
                        String menuName = sysMenuVO.getName();
                        if("财务管理".equals(menuName)){
                            List<SysMenuVO> routes = sysMenuVO.getRoutes();
                            routes.removeIf(x->"充值管理".equals(x.getName()));
                        }
                    });
                }
            }
            if (permsList.contains("mymessage:merchant:addPayment")) {
                // 境外企业或者个人人工审核客户
                boolean isOversea = CustomerAreaEnum.OVERSEA.getCode().equals(customerArea) || CustomerAreaEnum.GANG_AO_TAI.getCode().equals(customerArea);
                boolean isAuth = CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthInfo.getCustomerType()) || customerAuthInfo.getAuditType() == null || customerAuthInfo.getAuditType().equals(1);
                if (isOversea && isAuth) {
                    permsList.remove("mymessage:merchant:addPayment");
                }

            }
        }
        // 设置响应
        return JsonResult.success(ResponseCode.PORTAL_201, menuInfo);
    }

    /***
     * //TODO  查询可用高德key
     * <AUTHOR>
     * @date 2023/2/3 14:48
     * @return com.cmhb.common.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "高德所用key信息")
    @RequestMapping("/list/autoNavi")
    public JsonResult<Object> list() {
        List<AutowiredDTO> autoNavi = CacheUtil.redis().getValue(CacheKeyEnum.GD_NUMBER, "AutoNavi");
        for (AutowiredDTO autowiredDTO : autoNavi) {
            Boolean status = autowiredDTO.getStatus();
            if (status) {
                return JsonResult.success(autowiredDTO);
            }
        }
        //全部用完返回无用即可
        DingTalkClient.sendMessage("高德次数用完啦！");
        return JsonResult.success(autoNavi.get(0));
    }

    /***
     * //TODO  更新高德次数限制
     * <AUTHOR>
     * @date 2023/2/3 14:48
     * @param autowired key
     * @return com.cmhb.common.JsonResult<java.lang.Object>
     */
    @PostMapping("/update/autoNavi")
    public JsonResult<Object> updateLabel(@RequestBody AutowiredDTO autowired) {
        String key = autowired.getKey();
        boolean succeed = false;
        //查询所有的高德信息
        List<AutowiredDTO> autoNavi = CacheUtil.redis().getValue(CacheKeyEnum.GD_NUMBER, "AutoNavi");
        for (AutowiredDTO autowiredDTO : autoNavi) {
            String key1 = autowiredDTO.getKey();
            Boolean status = autowiredDTO.getStatus();
            if (key.equals(key1) && status) {
                //标记为已用完
                autowiredDTO.setStatus(false);
                succeed = true;
            }
        }
        //更新信息
        if (succeed) {
            CacheUtil.redis().setValue(CacheKeyEnum.GD_NUMBER, "AutoNavi", autoNavi);
            log.info("高德key更新成功==========!");
        }
        return JsonResult.success();
    }

    /***
     * //查询是否有需要签订的委托书
     * <AUTHOR>
     * @date 2023/5/16 13:43
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "查询需签订的委托书信息")
    @PostMapping("/paymentAccountStatus")
    public JsonResult<Object> getPaymentAccountStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam String businessType) {
        try {
            return paymentAccountService.getPaymentAccountStatus(currentUser.getUserCode(), businessType);
        } catch (Exception e) {
            log.error("查询是否有需要签订的委托书异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }

    /***
     * //  首页弹窗
     * <AUTHOR>
     * @date 2023/5/22 14:05
     * @param currentUser 用户信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "首页弹窗")
    @GetMapping("/popup/message")
    public JsonResult<Object> popupMessage(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return indexService.popupMessage(currentUser);
        } catch (Exception e) {
            log.error("首页弹窗异常", e);
            return JsonResult.success();
        }
    }


    /**
     * @description 判断是不是海外派商户
     * <AUTHOR>
     * @date 2021/7/16 9:19
     */
    @GetMapping("validateHwpMerchant")
    public JsonResult validateHwpMerchant(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        JsonResult jsonResult = new JsonResult();
        Boolean isHwp = false;
        MerchantDTO merchant = null;
        try {
            merchant = crmUserService.getMerchant(currentUser.getMerchantNo());
        } catch (Exception e) {
            log.error("判断是否海外派商户，查询商户信息异常:", e);
        }
        if (merchant != null && StringUtils.isNotBlank(merchant.getMerchantProperty())) {
            String merchantProperty = merchant.getMerchantProperty();
            String[] split = hwpProperty.split(",");
            for (String str : split) {
                if (merchantProperty.contains(str)) {
                    isHwp = true;
                    break;
                }

            }
        }
        if (isHwp) {
            jsonResult.setSuccess(true);
            jsonResult.setData("1");
            return jsonResult;
        }
        jsonResult.setSuccess(true);
        jsonResult.setData("0");
        return jsonResult;
    }

    /**
     * <AUTHOR>
     * @description 查询客户进入业务线的权限状态
     * @date 2023/5/29 16:22
     **/
    @PostMapping("getAuthorityStatus")
    public JsonResult getAuthorityStatus(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam @NotNull(message = "[类型]不能为空") Integer type) {
        JsonResult<Object> jsonResult = JsonResult.success();
        CustomerAuthStateVo authState = customerAuthService.getAuthState(currentUser.getUserCode());
        if (CustomerAuthStateEnum.COMPLETE_ATTACH.getValue().equals(authState.getAuthState())) {
            jsonResult.setData(BusinessApplyStatusEnum.COMPLETE_CARD_ATTACH.getValue());
        } else if (CustomerAuthStateEnum.SUCCESS.getValue().equals(authState.getAuthState()) || CustomerAuthStateEnum.COMPLETE_UN_LIMIT.getValue().equals(authState.getAuthState())) {
            // 认证完成或者补齐资料不受限
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(type);
            switch (businessTypeEnum) {
                case STRAIGHT:
                    Integer status = packetBusinessApplyService.getNotLimitState(currentUser.getUserCode());
                    jsonResult.setData(status);
                    break;
                case FBA:
                    Integer fbaApplyStatus = fbaApplyService.getFbaApplyStatus(currentUser.getUserCode());
                    jsonResult.setData(fbaApplyStatus);
                    break;
                case OVERSEA:
                    Integer overseaApplyStatus = getOverseaApplyStatus(currentUser.getUserCode());
                    jsonResult.setData(overseaApplyStatus);
                    break;
                default:
                    Assert.isTrue(false, "未知业务类型");
                    break;
            }
        } else {
            jsonResult.setData(BusinessApplyStatusEnum.NOT_AUTH.getValue());
        }
        return jsonResult;
    }

    /***
     * //查询包裹数量
     * <AUTHOR>
     * @date 2023/5/29 15:06
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @Logger(module = Module.COMMON, name = "首页小包待处理数量")
    @PostMapping("/reminder/message")
    public JsonResult<ReminderVo> reminderMessage(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @RequestBody TailStrokeDTO tailStrokeDTO) {
        try {
            return indexService.reminderMessage(currentUser, tailStrokeDTO);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询包裹数量异常：{}", e.getMessage());
            return JsonResult.success();
        }
    }

    /***
     * //查询解冻所需付款账号
     * <AUTHOR>
     * @date 2023/7/10 13:38
     * @param currentUser 登录信息
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/unfreezeAccount")
    public JsonResult<Object> getUnfreezeAccount(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            return indexService.getUnfreezeAccount(currentUser);
        } catch (BusinessException e) {
            log.error("查询解冻付款账号异常：{}", e.getMessage());
            return JsonResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询解冻付款账号异常：{}", e.getMessage());
            return JsonResult.error();
        }
    }

    @RequestMapping("/MenuSearch")
    public JsonResult<Object> MeusSearch(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            // 获取菜单信息
            JSONObject jsonObject = permissionService.requestSubUserPermissionToMenu(currentUser, PlatformTypeEnum.WEB.value());
            checkMerchantAttributes(currentUser, jsonObject);
            TbMenuAndButton menuInfo = jsonObject.getObject("returnlist", TbMenuAndButton.class);
            List<SysMenuVO> sysMenuVOS = permissionService.filtrationMenu(menuInfo);
            return JsonResult.success(sysMenuVOS);
        } catch (Exception e) {
            log.error("菜单搜索栏异常:{}", e.getMessage());
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }
    }

    private void checkMerchantAttributes(UserAgent currentUser, JSONObject menuResult) {
        // 未配置的商户属性
        JSONArray jsonArray = menuResult.getJSONArray(SysUserConstants.NO_MERCHANT_ATTRIBUTES);
        if (Objects.nonNull(jsonArray) && jsonArray.size() > 0) {
            String info = String.format(SysUserConstants.PROMPT_INFORMATION,
                    currentUser.getUserId(),
                    currentUser.getMerchantNo(),
                    jsonArray);
           log.info(info);
        }
    }

    /***
     * //查询冻结状态
     * <AUTHOR>
     * @date 2023/11/2 14:22
     * @param currentUser 用户代码
     * @return com.ywwl.customer.center.common.domain.JsonResult<java.lang.Object>
     */
    @GetMapping("/selectUnfreezeReasons")
    public JsonResult<Object> selectUnfreezeReasons(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        return indexService.selectFreezeReasons(currentUser);
    }

    /***
     * 记录用户点击添加企业微信操作
     * <AUTHOR>
     * @date 2022/2/18 14:02
     * @param currentUser 登录信息
     * @return com.cmhb.common.JsonResult
     */
    @GetMapping("/add/enterpriseWeChat")
    public JsonResult<Object> userEnterpriseWeChat(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            indexService.userEnterpriseWeChat(currentUser.getUserId());
            return JsonResult.success();
        } catch (Exception e) {
            log.info("记录用户点击添加企业微信操作异常:{}", e.getMessage(), e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }

    }

    /***
     * //记录阅读的公告
     * <AUTHOR>
     * @date 2021/8/4 15:47
     * @param sysContentUser 公告
     * @param currentUser
     * @return com.cmhb.common.JsonResult
     */
    @PostMapping("/record/readAnnouncement")
    public JsonResult<Object> readAnnouncement(@RequestBody SysContentUser sysContentUser, @ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        log.info("记录阅读的公告:{},{}", sysContentUser, currentUser.getUserId());
        sysContentUser.setUserId(currentUser.getUserId());
        QueryWrapper<SysContentUser> wrapper = new QueryWrapper<>(sysContentUser);
        SysContentUser one = sysContentUserService.getOne(wrapper);
        if (null == one) {
            sysContentUserService.save(sysContentUser);
        } else {
            sysContentUserService.updateById(one);
        }
        return JsonResult.success(ResponseCode.PORTAL_5029);
    }

    /***
     * 记录用户点击关闭新闻关闭
     * <AUTHOR>
     * @date 2022/2/18 14:02
     * @param currentUser 登录信息
     * @return com.cmhb.common.JsonResult
     */
    @PostMapping("/close/journalism")
    public JsonResult<Object> closeJournalism(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser, @JsonParam @NotNull(message = "公告id不能为空") String contentId) {
        try {
            indexService.closeJournalism(currentUser.getUserId(), contentId);
            return JsonResult.success();
        } catch (Exception e) {
            log.info("记录用户点击关闭新闻关闭操作异常:{}", e.getMessage(), e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }

    }

    /***
     * 记录用户修改密码
     * <AUTHOR>
     * @date 2022/2/18 14:02
     * @param currentUser 登录信息
     * @return com.cmhb.common.JsonResult
     */
    @GetMapping("/record/changePassword")
    public JsonResult<Object> changePassword(@ModelAttribute(value = "currentUser", binding = false) UserAgent currentUser) {
        try {
            indexService.changePassword(currentUser.getUserId());
            return JsonResult.success();
        } catch (Exception e) {
            log.info("记录用户修改密码:{}", e.getMessage(), e);
            return JsonResult.error(ResponseCode.PORTAL_5029);
        }

    }

    /**
     * @author: dinghy
     * @createTime: 2024/4/10 16:17
     * @description: 查询客户认证状态和业务线开通状态
     */
    @RequestMapping("queryAuthAndBusinessState")
    public JsonResult queryAuthAndBusinessState(@JsonParam @NotNull(message = "[类型]不能为空") Integer type) {
        UserAgent user = getUser();
        String userCode = user.getUserCode();
        BusinessAndCustomerVo businessAndCustomerVo = new BusinessAndCustomerVo();
        CustomerAuthVo customerAuthInfo = customerAuthService.getCustomerAuthInfo(user.getMerchantNo());
        if (customerAuthInfo == null) {
            businessAndCustomerVo.setBusinessAuditStatus(BusinessApplyStatusEnum.NOT_OPEN.getValue());
            businessAndCustomerVo.setCustomerAuditStatus(CustomerStatusEnum.UN_COMMIT.status());
            return JsonResult.success(businessAndCustomerVo);
        } else {
            businessAndCustomerVo.setCustomerAuditStatus(customerAuthInfo.getAuditStatus());
            BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(type);
            BusinessStatusVO businessStatusVO = null;
            switch (businessTypeEnum) {
                case STRAIGHT:
                    businessStatusVO = packetBusinessApplyService.getPacketStatusAndFronzenStatus(userCode);
                    break;
                case FBA:
                    businessStatusVO = fbaApplyService.getFbaStatusAndFrozen(userCode);
                    break;
                case OVERSEA:
                    boolean checkOverseaMerchant = overseaService.checkOverseaMerchant(currentUser().getUserCode());
                    businessStatusVO=new BusinessStatusVO();
                    if(checkOverseaMerchant){
                        businessStatusVO.setApplyStatus(BusinessApplyStatusEnum.SIGNED.getValue());
                    }else{
                        Integer applyStatus = ywEOverseaService.getApplyStatus(userCode);
                        businessStatusVO.setApplyStatus(applyStatus);
                    }
                    break;
                default:
                    Assert.isTrue(false, "未知业务类型");
                    break;
            }
            businessAndCustomerVo.setBusinessAuditStatus(businessStatusVO.getApplyStatus());
            businessAndCustomerVo.setFrozenStatusName(businessStatusVO.getFrozenStatusName());
            businessAndCustomerVo.setFrozenStatus(businessStatusVO.getFrozenStatus());
        }
        businessAndCustomerVo.handleBusinessProcessState();
        return JsonResult.success(businessAndCustomerVo);
    }

    /**
     * @author: dinghy
     * @createTime: 2024/5/20 15:11
     * @description: 判断是否可以查看账单
     */
    @RequestMapping("checkIfShowBill")
    public JsonResult checkIfLockBill( @JsonParam @NotNull(message = "[类型]不能为空") Integer type) {
        UserAgent currentUser = getUser();
        JsonResult<Object> jsonResult = JsonResult.success();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(type);
        switch (businessTypeEnum) {
            case STRAIGHT:
                PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
                if(packetApplyInfo!=null&&StringUtils.isNotBlank(packetApplyInfo.getMerchantCode())){
                    jsonResult.setData(BusinessApplyStatusEnum.SIGNED.getValue());
                }else{
                    jsonResult.setData(BusinessApplyStatusEnum.NOT_OPEN.getValue());
                }
                break;
            case FBA:
                FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(currentUser.getUserCode());
                if(fbaApplyInfo!=null&&StringUtils.isNotBlank(fbaApplyInfo.getAccountCode())){
                    jsonResult.setData(BusinessApplyStatusEnum.SIGNED.getValue());
                 }else{
                    jsonResult.setData(BusinessApplyStatusEnum.NOT_OPEN.getValue());
                 }
                break;
            case OVERSEA:
                Integer overseaApplyStatus = getOverseaApplyStatus(getUserCode());
                if(BusinessApplyStatusEnum.SIGNED.getValue() == overseaApplyStatus){
                    jsonResult.setData(BusinessApplyStatusEnum.SIGNED.getValue());
                }else{
                    jsonResult.setData(BusinessApplyStatusEnum.NOT_OPEN.getValue());
                }
                break;
            default:
                break;
        }
        return jsonResult;
    }
}
