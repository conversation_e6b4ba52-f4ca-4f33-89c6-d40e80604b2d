<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ywwl.customer.center</groupId>
        <artifactId>customer-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>customer-api</artifactId>
    <description>
        对外暴漏接口的模块，所有的Controller存放的地方，服务器启动类也在于此
    </description>
    <dependencies>
        <!-- 系统模块 -->
        <dependency>
            <groupId>com.ywwl.customer.center</groupId>
            <artifactId>customer-modules</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
