oms:
  host: https://oms-openapi.yanwentech.com
  authorization: eXd3bDo2OUNITkxMZEhkY3YyMWRTNG5UbWViMklGak84ZmtRUQ==
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: **********************************************************************************************************************
          username: portal
          password: QbR3LOYU3GnoKZWV
          driver-class-name: com.mysql.cj.jdbc.Driver
        old:
          url: *****************************************************************************************************************
          username: portal
          password: QbR3LOYU3GnoKZWV
          driver-class-name: com.mysql.cj.jdbc.Driver
        time:
          url: ************************************************************************************************************************
          username: portal
          password: QbR3LOYU3GnoKZWV
          driver-class-name: com.mysql.cj.jdbc.Driver
      lazy: true #是否启用懒加载,默认false
    hikari:
      connection-timeout: 10000
      validation-timeout: 3000
      idle-timeout: 60000
      minimum-idle: 5
      login-timeout: 5
      max-lifetime: 60000
      # 最大链接数
      maximum-pool-size: 20
      keepalive-time: 30000
  devtools:
    restart:
      enabled: true  #关闭热部署
  mvc:
    servlet:
      load-on-startup: 1
    async:
      request-timeout: 90000
  aop:
    proxy-target-class: true
  shiro:
    anno-resources:
      - /free/**
      - /forget/**
      - /register
      - /register/**
      - /email/activate
      - /email/resetPassWord
      - /current/user
      - /oauth2/login
      - /api/**
      - /view/**
      - /test/**
      - /customer/contract/notify
      - /validate/**
      - /actuator/health
      - /maintenance/**
      - /warn/**
      - /user/info
      - /site/**
      - /front/**
      - /wechat/**
      - /my-scanCode/**
      - /webjars/**
      - /my-ws/**
      - /file/**
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      resolve-lazily: true
  redis:
    database: 1
    host: *************
    port: 6000
    password: vM*QMvWYLT7Snf5e
    timeout: 3000
    lettuce:
      pool:
        max-active: 10
        max-idle: 200
        max-wait: -1
        min-idle: 0
ok:
  http:
    connect-timeout: 8
    read-timeout: 8
    write-timeout: 8
    max-idle-connections: 200
    keep-alive-duration: 300
oauth:
  devModel: false
  localLoginUrl: /login
  successUrl: /index
  unauthorizedUrl: /unauthorized

# 认证中心
api-request:
  api-url: http://*************:9092/api/
  auth-url: http://*************:9092/jsonreq.action
  picverify: http://*************:9092/picverify
  token: F89B3E7D4C9A8458731
  tokenreq-url: http://*************:9092/tokenreq.action

# crm和工单相关地址
crm:
  oldApi: https://crm.yanwentech.com/crmapi/api/
  host: https://crm.yanwentech.com/crmapi
  new-host: https://customerapi.yanwentech.com/customer-api
  workOrder: https://workorder.yanwentech.com/api/
  secret-token: YanWenPortal1564974464994APITOKEN


# 钉钉发送地址
ding-talk:
  url: https://oapi.dingtalk.com/robot/send?access_token=3ad8b861eae05adb2ade9b2f7940d7dd3aa5c85aca6d310fc63310a397f398be
  sign: SEC336cc7cd1b85047fae401c39bc269cbeafa4a09d27c52a14d94ddea798cf332b

# 文件服务器
attachment-file-cloud:
  down-url: https://fs2.yanwentech.com/files
  tenant-id: ywwl
  token: F89B3E7D4C9A8458731
  upload-url: https://fs2.yanwentech.com/jsonreq.action


# 公共crm
common-crm:
  host: https://crm-common.yanwentech.com

portal-tracking:
  url: http://**********:8866
  apiUrl: http://track.yanwentech.com

trackApi:
  Authorization: 461C53DF-3BDA-432A-85A7-20BE84584D72
  url: http://trackapi.yanwentech.com
# 主数据
cmcc:
  host: https://ycs.yanwentech.com/cis
#问题件
abnormal:
  url: https://mes-ams.yanwentech.com/api/
# MES
mes:
  order-url: http://*************:9933/api/
  url: http://*************:9953/api/
  weight-forecast: https://mes.yw56.com.cn/api/

# 售前PSM地址
sale:
  url: https://psm.yanwentech.com/server
  token: ZdingUn0siEfrgOqwCAJxQ==

#PLM地址
plm:
  url: https://plm.yanwentech.com
#商业快递
business:
  dictionary: https://occ.yanwentech.com/gateway/router
  api: https://be.yanwentech.com
  import-max-size: 200
#Portal版本试算
calculate-engine:
  productDetail: https://cmcccalc.yanwentech.com/api/productQuotation/quotationExplain
  batch: https://cmcccalc.yanwentech.com/api/calculate/calcAccs
  fba: https://cmcccalc.yanwentech.com/api/calculate/calcFbaAcc
  business: https://cmcccalc.yanwentech.com/api/calculate/manyPiece
  single: https://cmcccalc.yanwentech.com/api/calculate/calcAcc
  # 计算中心地址
  url: https://cmcccalc.yanwentech.com
# EJF
ejf:
  buss:
    import-max-size: 2000
  host: https://ejf.yanwentech.com
  port-url: https://ejf-v2.yanwentech.com

# TMS
tms:
  eventUrl: http://*************:9938/api/transmitDataSetting/GetDataSource
  orderUrl: http://*************:9953/api/wishExpressSetting/getDataOrder/{WarehouseId}

# 计费
charged-weight:
  url: http://accdrelease.yanwentech.com:663/jsonreq.action
#  url: http://accdrelease.yanwentech.com:663/jsonreq.action

# 海外派导入预报
forecast:
  confirmUrl: http://online.yw56.com.cn/api/portal/CODE/portalconfirm
  url: http://online.yw56.com.cn/api/portal/CODE/portalforecast

# 获取微信扫码地址
wxgl:
  qrCodeUrl: https://portal.yw56.com.cn/wxgl/api/qrCode/getQrCode
  appid: wxd600324e849792af

# 调度
dispatcher:
  url: https://dispatch.yanwentech.com/ywwl-front/

# PP
pp:
  host: https://pp.yanwentech.com

# 账务
bill:
  # 导出账单地址
  export-url: http://accreport.yanwentech.com:665/exportbill.action
  # 查询账单余额,账单等
  query-url: http://accreport.yanwentech.com:665/extInterface.action
  token: F89B3E7D4C9A8458731
  # 查询运单明细地址
  waybill-url: http://**********:20010/

# 管理后台
cps-manager:
  token: CPS18039501855MANAGERPRO

# 亚马逊打印标签
part-net:
  api: http://partner.yw56.com.cn:5006
  template-code:
    amazon: YW202209010001_V001

#账务中心地址
acc:
  # 非商户主体支付宝扫码记账
  alipay-record-url: http://accbdinter.yanwentech.com:666/alipay-acc/jsonreq.action

#企业微信地址
wXCrm:
  url:  https://wxcrm.yw56.com.cn/api/


# 支付宝扫码相关
alipay:
  cert-location: /home/<USER>/portal/cert/
  notify-url: https://portal-v2.yw56.com.cn/csc/api/alipay/notify/callback
  payment-amount: 1.00
  product-name: 收钱码收款
  time-out-period: 120m

# 数据库加密
encrypt-config:
  url: https://infrastructure-service.yanwentech.com/api/security
  appid: 373271
  secret: VUAWH7cX4TQiChw5jJ5satxlIc9ubfCn

# fba地址
fba:
  url: http://**********:5000
  old-url: https://fba.yanwentech.com

ffs:
  url: http://**********:5000
# 临时文件路径
temp-path:
mybatis-plus:
  lazy-initialization: true # 延迟加载
  configuration:
    lazy-loading-enabled: true # 延迟加载
    aggressive-lazy-loading: true # 延迟加载
# 老portal地址
oldPortal:
  url: https://portal.yw56.com.cn
  font: https://portal-v2.yw56.com.cn
register:
  url: https://portal.yw56.com.cn/register
# 跟踪接口地址
track:
  url: http://api.track.yw56.com.cn/api/tracking?nums=
  Authorization: 81449893-7012-4D8B-B5AB-17108DADA4AF
  temu: http://api.track.yw56.com.cn/temu/tracking/firstmile?nums=
#帮助中心
help-center:
  url: https://www.yw56.com.cn/cn/helpCenter-newPortal.html
# 临时文件路径
tmpdir: /portal

# 海外派属性
hwp:
  property: 28,29,30

download:
  url: http://10.11.132.206:8081/api/downloadTask/download

# 品名匹配
match:
  url: https://fms-pnm.yanwentech.com/api/searchMatchGoods
  accessKey: 3pF5sAID14zEhvPR
  secretKey: vEfqZTidnW4NU5S1

# 发送钉钉通知地址
send-ding-notice:
  url: http://10.10.98.88/jsonreq.action
  token: F89B3E7D4C9A8458731

# 从吕总接口查询海外派获取秘钥
tool:
  getPublicKey: https://tools.yanwentech.com/rsa/public-key/

# 美国税率查询，从丁戈的接口取
usaTaxRate:
  url : http://10.10.132.118:5002/query_hts_data/
  pdfOcr: http://10.10.132.143:5005/extract_number/

# YWE
ywe:
  url: https://dds.yanwentech.com
  token: eyJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiIiwidXNlcktpbmQiOjQsInRlcm1pbmFsIjoiUnpTbUVRIiwidXNlcklkIjoxMjEwMywidXNlcm5hbWUiOiJZd0NSTSIsImp0aSI6IjhjMDEyYTRmLTA1MmUtNDk1ZC1hMGVmLTI3ZGNiMTU4MDU1NiIsImlhdCI6MTc0NDY4NjM0NiwiaXNzIjoiWXdDUk0iLCJleHAiOjQxMDI0MTU5OTl9.y75b-DslrdAIhzNabFhM6P41zCPeYYfvNmf2i52hH8g
