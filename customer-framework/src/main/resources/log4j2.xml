<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别从低到高优先级为：ALL < TRACE < DEBUG < INFO < WARN < ERROR < FATAL < OFF。-->
<!--status：设置 log4j2 自身内部的信息输出，可以不设置-->
<!--monitorInterval：监视配置文件变化间隔时间，单位秒，Log4j2 能够自动检测配置文件是否修改，同时更新配置-->
<configuration status="WARN" monitorInterval="30">
    <!--先定义所有的 appender 附加器-->
    <appenders>
        <!--控制台输出配置-->
        <console name="CONSOLE-LOG" target="SYSTEM_OUT">
            <!--输出日志的格式
                %d表示日期时间，
                %thread表示线程名，
                %-5level：级别从左显示5个字符宽度
                %logger{50} 表示logger名字最长50个字符，否则按照句点分割。
                %line：表示行号
                %msg：日志消息
                %n是换行符-->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} =>[%thread] =>%-5level %logger{50}:%line - %msg%n"/>
        </console>
        <ThreadLogAppender name="ThreadLogAppender"/>
        <!--文件存储文件设置-->
        <!--fileName：文件存储路径及名称，可以是绝对路径或者相对路径; 存储的永远是最新的日志信息-->
        <!--filePattern：当 fileName 指定的文件大小超过限制，就会根据此文件名规则新建存档目录与文件，同时将 fileName 文件中的
         内容剪切到存档文件中，如下配置，会新建存档路径 logs/2021-06/2021-06-01-1.log -->
        <RollingFile name="FILE-LOG" fileName="logs/customer-all.log"
                     filePattern="logs/$${date:yyyy-MM}/all-%d{yyyy-MM-dd}-%i.log">
            <!--日志文件中日志信息的格式-->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} =>[%thread] == %-5level %logger{50}:%line - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--日志文件大小超过多少时进行存档-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5"/>
        </RollingFile>
        <!-- 后端请求外部系统的日志信息 -->
        <RollingFile name="API" fileName="logs/customer-api.log"
                     filePattern="logs/$${date:yyyy-MM}/api-%d{yyyy-MM-dd}-%i.log">
            <!--日志文件中日志信息的格式-->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} =>[%thread] == %-5level %logger{50}:%line - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--日志文件大小超过多少时进行存档-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5"/>
        </RollingFile>
        <!-- 前端请求后端的运单日志信息 -->
        <RollingFile name="KEY" fileName="logs/customer-key.log"
                     filePattern="logs/$${date:yyyy-MM}/key-%d{yyyy-MM-dd}-%i.log">
            <!--日志文件中日志信息的格式-->
            <PatternLayout pattern="%msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--日志文件大小超过多少时进行存档-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5"/>
        </RollingFile>
        <!-- 前端请求后端的日志信息 -->
        <RollingFile name="CONTROLLER" fileName="logs/customer-controller.log"
                     filePattern="logs/$${date:yyyy-MM}/controller-%d{yyyy-MM-dd}-%i.log">
            <!--日志文件中日志信息的格式-->
            <PatternLayout pattern="%msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <!--日志文件大小超过多少时进行存档-->
                <SizeBasedTriggeringPolicy size="50 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5"/>
        </RollingFile>
    </appenders>
    <!--
    1、root与logger是父子关系，没有特别定义logger的都默认为root。
    2、任何一个类只会和一个logger对应，要么是定义的logger，要么是root，判断的关键在于找到这个logger，然后判断这个logger的appender和level。
    3、appender-ref 用于引用上面定义好的 appender 日志追加器，只有引用了，上面的 appender 才能生效. -->
    <loggers>
        <logger name="com.ywwl" level="DEBUG"/>

        <logger name="API" level="INFO">
            <appender-ref ref="API"/>
        </logger>

        <logger name="CONTROLLER" level="INFO">
            <appender-ref ref="CONTROLLER"/>
        </logger>

        <logger name="KEY" level="INFO">
            <appender-ref ref="KEY"/>
        </logger>

        <!-- 去除shiro日志打印 -->
        <logger name="org.apache.shiro" level="ERROR"/>

        <!--root 日志记录器-->
        <root level="WARN">
            <appender-ref ref="FILE-LOG"/>
            <appender-ref ref="CONSOLE-LOG"/>
            <appender-ref ref="ThreadLogAppender"/>
        </root>

    </loggers>
</configuration>