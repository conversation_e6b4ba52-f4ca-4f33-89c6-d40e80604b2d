oms:
  host: https://oms-openapi-fat.yanwentech.com
  authorization: eXd3bDo2OUNITkxMZEhkY3YyMWRTNG5UbWViMklGak84ZmtRUQ==
spring:
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ************************************************************************************************************
          username: portal_test
          password: ^u@*szl#rtuiz9ip
          driver-class-name: com.mysql.cj.jdbc.Driver
        old:
          url: *******************************************************************************************************
          username: portal_test
          password: ^u@*szl#rtuiz9ip
          driver-class-name: com.mysql.cj.jdbc.Driver
        time:
          url: ****************************************
          username: ywwl
          password: QvE8E4ZmjRuE^TyF
          driver-class-name: com.mysql.cj.jdbc.Driver
      lazy: true #是否启用懒加载,默认false
    hikari:
      connection-timeout: 10000
      validation-timeout: 3000
      idle-timeout: 60000
      minimum-idle: 5
      login-timeout: 5
      max-lifetime: 60000
      # 最大链接数
      maximum-pool-size: 20
      keepalive-time: 30000
  devtools:
    restart:
      enabled: true  #关闭热部署
  mvc:
    servlet:
      load-on-startup: 1
    static-path-pattern: /static/**
    async:
      request-timeout: 90000
  aop:
    proxy-target-class: true
  shiro:
    anno-resources:
      - /free/**
      - /forget/**
      - /register
      - /register/**
      - /email/activate
      - /email/resetPassWord
      - /current/user
      - /oauth2/login
      - /api/**
      - /view/**
      - /test/**
      - /customer/contract/notify
      - /validate/**
      - /actuator/health
      - /maintenance/**
      - /warn/**
      - /user/info
      - /site/**
      - /front/**
      - /wechat/**
      - /my-scanCode/**
      - /webjars/**
      - /my-ws/**
      - /file/**
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      resolve-lazily: true
  redis:
    database: 0
    host: **********
    port: 6379
    lettuce:
      pool:
        max-active: 10
        max-idle: 200
        max-wait: -1
        min-idle: 0
    timeout: 30000
ok:
  http:
    connect-timeout: 30
    read-timeout: 30
    write-timeout: 30
    max-idle-connections: 200
    keep-alive-duration: 300
oauth:
  devModel: false
  localLoginUrl: /login
  successUrl: /index
  unauthorizedUrl: /unauthorized
api-request:
  api-url: http://**********:9092/api/
  auth-url: http://**********:9092/jsonreq.action
  picverify: http://**********:9092/picverify
  token: F89B3E7D4C9A8458731
  tokenreq-url: http://**********:9092/tokenreq.action
crm:
  oldApi: https://crm-test.yanwentech.com/crmapitest/api/
  host: https://crm-test.yanwentech.com/crmapitest
  new-host: https://customerapi-test.yanwentech.com/customer-api
  workOrder: https://workorder-test.yanwentech.com/api/
  secret-token: YanWenPortal1564974464994APITOKEN
ding-talk:
  sign: SEC615eeff6928b912fc62aa5eef72f5f2fe3c96e5e4bf4b8a5e1b5f1ac4d22faa7
  url: https://oapi.dingtalk.com/robot/send?access_token=1a77a78512be350f469eee25962185827599d95ee1d0790b4d4d193b1a1a7fe4
attachment-file-cloud:
  down-url: https://fs2.yanwentech.com/files
  tenant-id: crmsys
  token: F89B3E7D4C9A8458731
  upload-url: https://fs2.yanwentech.com/jsonreq.action
api:
  sms: http://*************:9908
# 公共crm
common-crm:
  host: https://crm-common-test.yanwentech.com

portal-tracking:
  url: http://************:8866
  apiUrl: http://track.yanwentech.com
trackApi:
  Authorization: 461C53DF-3BDA-432A-85A7-20BE84584D72
  url: http://trackapi.yanwentech.com
# 主数据
cmcc:
  host: https://ycs.yanwentech.com/cis
#问题件
abnormal:
  url: http://**********:5000/api/
mes:
  order-url: http://*************:9933/api/
  url: http://*************:9953/api/
  weight-forecast: http://**********:9001/api/
sale:
  url: http://**********:8080/server
  token: ZdingUn0siEfrgOqwCAJxQ==
plm:
  url: https://plm-pre.yanwentech.com
business:
  dictionary: https://occ-pre.yanwentech.com/gateway/router
  api: https://be-fat.yanwentech.com
  import-max-size: 200
calculate-engine:
  productDetail: http://************:8085/api/productQuotation/quotationExplain
  batch: http://************:8085/api/calculate/calcAccs
  business: http://************:8085/api/calculate/manyPiece
  fba: http://************:8085/api/calculate/calcFbaAcc
  single: http://************:8085/api/calculate/calcAcc
  # 计算中心地址
  url: http://************:8085
ejf:
  Authorization: basic OTk5MDAxOjEyMzQ1Njc4
  buss:
    import-max-size: 2000
  host: http://*************:802
  old-port-url: http://*************:802
  port-url: https://ejf-fat.yw56.com.cn
tms:
  eventUrl: http://*************:9973/api/SysEventSource/GetDataSource
  orderUrl: http://*************:9953/api/wishExpressSetting/getDataOrder/{WarehouseId}

charged-weight:
  url: http://accdrelease-test.yanwentech.com:663/jsonreq.action
#  url: http://accdrelease.yanwentech.com:663/jsonreq.action

# 海外派导入预报
forecast:
  confirmUrl: http://*************:15005/api/portal/CODE/portalconfirm
  url: http://*************:15005/api/portal/CODE/portalforecast

# 获取微信扫码地址
wxgl:
  qrCodeUrl: https://portal.yw56.com.cn/wxgltest/api/qrCode/getQrCode
  appid: wx5d50494b49928eb5
dispatcher:
  url: https://dispatch-front.yanwentech.com/ywwl-front/

pp:
  host: https://pptest.yanwentech.com

bill:
  # 导出账单地址
  export-url: http://accreport-test.yanwentech.com:665/exportbill.action
  #  export-url: http://accreport.yanwentech.com:665/extInterface.action
  # 查询账单余额,账单等
  query-url: http://accreport-test.yanwentech.com:665/extInterface.action
  #  query-url: http://accreport.yanwentech.com:665/extInterface.action
  token: F89B3E7D4C9A8458731
  # 查询运单明细地址
  waybill-url: http://**********:20010/
  # 扫码支付
  alipayChargeUrl: http://************:7013/paycenter/pay
  alipayChargeKey: 888
  # api接口展示
  billApiUrl: http://accdatainter-test.yanwentech.com:662/jsonreq.action

cps-manager:
  token: CPS18039501855MANAGERTEST
part-net:
  api: http://partner.yw56.com.cn:15005
  template-code:
    amazon: YW202209010001_V001
#账务中心地址
acc:
  payment-record-url: http://accbdinter-test.yanwentech.com:666/syndata-acc/jsonreq.action
  json-req-url: http://accreport-test.yanwentech.com:665/jsonreq.action
  # 非商户主体支付宝扫码记账
  alipay-record-url: http://accbdinter-test.yanwentech.com:666/alipay-acc/jsonreq.action
#企业微信地址
wXCrm:
  url: https://wxcrm-test.yw56.com.cn/api/


# 支付宝扫码相关
alipay:
  cert-location: /home/<USER>/portal/cert/
  notify-url: https://portal-test.yw56.com.cn/csc/api/alipay/notify/callback
  payment-amount: 0.01
  product-name: 收钱码收款test
  time-out-period: 120m

# 数据库加密
encrypt-config:
  appid: 377172
  secret: CiPiMHnKhfdNkxFx52ORoyOQhPi3KFgK
  url: https://infrastructure-service-fat.yanwentech.com/api/security

# fba地址
fba:
  url: http://************:5001
  old-url: http://fba-test.yanwentech.com
ffs:
  url: http://**********:5000
# 临时文件路径
temp-path:
mybatis-plus:
  lazy-initialization: true # 延迟加载
  configuration:
    lazy-loading-enabled: true # 延迟加载
    aggressive-lazy-loading: true # 延迟加载
# 老portal地址
oldPortal:
  url: https://portal-test.yanwentech.com
  font: https://csc-fat.yanwentech.com
#帮助中心
help-center:
  url: https://www.yw56.com.cn/cn/helpCenter-newPortal.html
register:
  url: https://portal-test.yanwentech.com/register
# 跟踪接口地址
track:
  url: http://api.track.yw56.com.cn/api/tracking?nums=
  Authorization: 81449893-7012-4D8B-B5AB-17108DADA4AF
  temu: https://tracking-fat.yw56.com.cn/temu/tracking/firstmile?nums=
#  temu: http://api.track.yw56.com.cn/api/tracking?nums=
# 临时文件路径
tmpdir: /portal

# 海外派属性
hwp:
  property: 40,41,42

download:
  url: http://**********:8081/api/downloadTask/download

# 品名匹配
match:
  url: https://fms-pnm.yanwentech.com
  accessKey: 3pF5sAID14zEhvPR
  secretKey: vEfqZTidnW4NU5S1

# 查询轨迹展示方案
trackSub:
  url: http://10.10.144.188:5003/api/int/checkpoint-display/program/paged?api-version=1.0&api-key=portal

# 发送钉钉通知地址
send-ding-notice:
  url: http://10.10.98.88/jsonreq.action
  token: F89B3E7D4C9A8458731

# 从吕总接口查询海外派获取秘钥
tool:
  getPublicKey: http://tools-fat.yanwentech.com/rsa/public-key/

# 美国税率查询，从丁戈的接口取
usaTaxRate:
  url : http://10.10.144.30:5002/query_hts_data/
  pdfOcr: http://10.10.144.30:5005/extract_number/

# YWE
ywe:
  url: https://dds-fat.yanwentech.com
  token: eyJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiIiwidXNlcktpbmQiOjQsInRlcm1pbmFsIjoiVEJYMWhxIiwidXNlcklkIjo0NTgzLCJ1c2VybmFtZSI6Ill3Q1JNIiwianRpIjoiYTI4MjhlNTctYTJjNy00OGZiLWFjZWQtNmJlYWQzM2U0ODUwIiwiaWF0IjoxNzQ0Njg2MTc2LCJpc3MiOiJZd0NSTSIsImV4cCI6NDEwMjQxNTk5OX0.FueI8JyOM480ZBClzX8f4lD2lmbcqKeZqborTc7S8VI

server:
  compression:
    enabled: true
    mime-types: application/json