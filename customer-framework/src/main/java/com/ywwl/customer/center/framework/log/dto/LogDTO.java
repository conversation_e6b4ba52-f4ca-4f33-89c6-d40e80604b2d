package com.ywwl.customer.center.framework.log.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.framework.enums.RType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 日志信息
 *
 * <AUTHOR>
 * @date 2023/06/26 16:49
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogDTO {

    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 操作类型
     */
    private String type;
    /**
     * key
     */
    private String key;
    /**
     * 操作模块
     */
    private String operationModule;
    /**
     * 操作名称
     */
    private String operationName;
    /**
     * 请求参数
     */
    private String requestContent;
    /**
     * 响应内容
     */
    private String responseContent;
    /**
     * 耗时(ms)
     */
    private Long elapsedTime;
    /**
     * 操作人ID
     */
    private Long operatorId;
    /**
     * 请求IP
     */
    private String ip;
    /**
     * 操作时间
     */
    private Date time;
    /**
     * 操作日志
     */
    private List<Object> logs;
    /**
     * 请求类型
     */
    @JSONField(name = "rType")
    private RType rType;
}
