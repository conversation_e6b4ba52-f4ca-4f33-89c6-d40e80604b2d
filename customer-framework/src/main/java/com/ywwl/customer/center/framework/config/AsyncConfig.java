package com.ywwl.customer.center.framework.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步方法配置
 *
 * <AUTHOR>
 * @date 2022/12/07 17:36
 **/
@Configuration
public class AsyncConfig implements AsyncConfigurer {

	@Bean(name = "async_pool", destroyMethod = "shutdown")
	public ThreadPoolTaskExecutor defaultAsyncPool() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 设置线程池前缀：方便排查
		executor.setThreadNamePrefix("async-");
		// 设置线程池的大小
		executor.setCorePoolSize(20);
		// 设置线程池的最大值
		executor.setMaxPoolSize(20);
		// 设置线程池的队列大小
		executor.setQueueCapacity(150);
		// 设置线程最大空闲时间，单位：秒
		executor.setKeepAliveSeconds(3000);
		// 饱和策略
		// AbortPolicy:直接抛出java.util.concurrent.RejectedExecutionException异常
		// CallerRunsPolicy:若已达到待处理队列长度，将由主线程直接处理请求
		// DiscardOldestPolicy:抛弃旧的任务；会导致被丢弃的任务无法再次被执行
		// DiscardPolicy:抛弃当前任务；会导致被丢弃的任务无法再次被执行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		return executor;
	}

	/**
	 * 自定义异步线程池，若不重写，则使用默认的
	 */
	@Override
	public Executor getAsyncExecutor() {
		return defaultAsyncPool();
	}

}
