package com.ywwl.customer.center.framework.mybatis;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentMap;

/**
 * 解密拦截器：查询数据库之后对敏感数据解密
 * 场景：查询时生效
 * 策略：
 * - 在敏感字段所在实体类上添加@SensitiveData注解
 * - 在敏感字段上添加@SensitiveField注解
 */
@Slf4j
@Component
@Intercepts({
        @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = Statement.class)
})
public class DecryptInterceptor implements Interceptor {

    @Resource
    private ConcurrentMap<String, List<Field>> encryptCache;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object resultObject = invocation.proceed();
        try {
            if (!Objects.isNull(resultObject)) {
                if (resultObject instanceof ArrayList) {
                    // 基于selectList
                    List resultList = (ArrayList) resultObject;
                    if (!resultList.isEmpty()) {
                        for (int i = 0; i < resultList.size(); i++) {
                            //逐一解密
                            Object o = resultList.get(i);
                            if (o == null) {
                                continue;
                            }
                            if (!checkSensitive(o)) {
                                break;
                            }
                            Object object = declaredFields(o);
                            resultList.set(i, object);
                        }
                    }
                    return resultList;
                } else {
                    // 基于selectOne
                    return declaredFields(resultObject);
                }
            }
        } catch (Exception e) {
            log.error("解密失败", e);
            throw new RuntimeException("解密数据失败");
        }
        return null;
    }

    /**
     * 解密字段
     */
    private Object declaredFields(Object object) throws Throwable {
        try {
            Class<?> sensitiveObjectClass = object.getClass();
            String name = sensitiveObjectClass.getName();
            List<Field> fieldList = encryptCache.get(name);
            if (Objects.nonNull(fieldList)) {
                List<Field> decryptFieldList = new LinkedList<>();
                // 需要加密的字段名和值
                Map<String, String> needDecrypt = new HashMap<>(8);
                String keyVersion = null;
                for (Field field : fieldList) {
                    Object targetProperty = field.get(object);
                    if (Objects.nonNull(targetProperty)) {
                        String value = (String) targetProperty;
                        String decryptValue = YwEncryptUtils.decryptHandle(value);
                        if (YwEncryptUtils.check(value, decryptValue)) {
                            keyVersion = YwEncryptUtils.getDecryptKeyVersion(value);
                            needDecrypt.put(field.getName(), decryptValue);
                            decryptFieldList.add(field);
                        }
                    }
                }
                if (decryptFieldList.size() > 0 && Objects.nonNull(keyVersion)) {
                    Map<String, String> decrypt = YwEncryptUtils.decrypt(keyVersion, needDecrypt);
                    for (Field field : decryptFieldList) {
                        field.set(object, decrypt.get(field.getName()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("调用解密异常,原因:{}", e.getMessage());
            throw new RuntimeException("调用解密异常");
        }
        return object;
    }

    @Override
    public Object plugin(Object target) {
        // 将这个拦截器接入拦截器链
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    /**
     * @description 判断是否为需要脱敏的类
     * <AUTHOR>
     * @date 2022/3/3 13:37
     */
    public Boolean checkSensitive(Object object) {
        Class<?> sensitiveObjectClass = object.getClass();
        String name = sensitiveObjectClass.getName();
        List<Field> fieldList = encryptCache.get(name);
        return Objects.nonNull(fieldList);
    }
}