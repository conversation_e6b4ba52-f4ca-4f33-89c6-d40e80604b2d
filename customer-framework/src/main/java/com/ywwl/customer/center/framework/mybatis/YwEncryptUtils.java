package com.ywwl.customer.center.framework.mybatis;


import com.yanwentech.security.client.SecurityHelper;
import com.yanwentech.security.client.model.Response;
import com.ywwl.customer.center.framework.annotation.SensitiveData;
import com.ywwl.customer.center.framework.annotation.SensitiveField;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.core.type.classreading.MetadataReaderFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 参考文档
 * http://112.124.122.136:7890/security.html
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class YwEncryptUtils {
    public static final String VERSION = "key_version==";
    public static final String KEY_VERSION = "$$$_key_version";
    public static final String TEMPLATE = "%skey_version==%s";

    public static String url;

    public static String appId;

    public static String secret;

    public static SecurityHelper securityHelper;

    @Value("${encrypt-config.url}")
    public void setUrl(String encryptConfigUrl) {
        YwEncryptUtils.url = encryptConfigUrl;
    }

    @Value("${encrypt-config.appid}")
    public void setAppId(String encryptConfigAppid) {
        YwEncryptUtils.appId = encryptConfigAppid;
    }

    @Value("${encrypt-config.secret}")
    public void setSecret(String encryptConfigSecret) {
        YwEncryptUtils.secret = encryptConfigSecret;
    }

    /**
     * @description 检查是否是加密过的数据
     * <AUTHOR>
     * @date 2022/2/24 13:58
     */
    public static Boolean check(String value) {
        String[] strings = value.split(VERSION);
		return strings.length <= 1;
	}

    public static String getEncryptKeyVersion(Map<String, String> map) {
        return map.get(KEY_VERSION);
    }

    public static String encryptHandle(String value, String keyVersion) {
        return String.format(TEMPLATE, value, keyVersion);
    }

    /**
     * @description 对从数据库取出来的数据进行处理
     * <AUTHOR>
     * @date 2022/2/24 14:25
     */
    public static String decryptHandle(String value) {
        String[] strings = value.split(VERSION);
        if (strings.length > 1) {
            return strings[0];
        }
        return value;
    }

    /**
     * @description 如果对从数据库取出来的数据处理之后和处理之前值一样, 不做解密操作
     * <AUTHOR>
     * @date 2022/2/24 14:27
     */
    public static Boolean check(String value, String afterValue) {
		return !value.equals(afterValue);
	}

    public static String getDecryptKeyVersion(String value) {
        String[] strings = value.split(VERSION);
        if (strings.length > 1) {
            return strings[1];
        }
        return null;
    }

    /**
     * @description 对数据加密
     * <AUTHOR>
     * @date 2022/2/24 14:34
     */
    public static Map<String, String> encrypt(Map<String, String> param) throws Exception {
        // 定义要加密的字段集合
        SecurityHelper securityHelper = getSingleton();
        Response encryptRsp = securityHelper.encrypt(param);
        if (encryptRsp != null && encryptRsp.isSuccess()) {
            return encryptRsp.getData();
        }
        throw new RuntimeException("调用加密服务异常");

    }

    /**
     * @description 对数据解密
     * <AUTHOR>
     * @date 2022/2/24 14:34
     */
    public static Map<String, String> decrypt(String keyVersion, Map<String, String> param) throws Exception {
        SecurityHelper securityHelper = getSingleton();
        Response response = securityHelper.decrypt(keyVersion, param);
        if (response != null && response.isSuccess()) {
            return response.getData();
        }
        throw new RuntimeException("调用解密服务异常");
    }


    /**
     * @description 对单个字段数据加
     * <AUTHOR>
     * @date 2022/2/24 14:34
     */
    public static String encrypt(String value) {
        // 定义要加密的字段集合
        Map<String, String> param = new HashMap<>(2);
        param.put("name", value);
        try {
            SecurityHelper securityHelper = getSingleton();
            Response encryptRsp = securityHelper.encrypt(param);
            if (encryptRsp != null && encryptRsp.isSuccess()) {
                Map<String, String> data = encryptRsp.getData();
                String result = data.get("name");
                String version = data.get(KEY_VERSION);
                return encryptHandle(result, version);
            }
        } catch (Exception e) {
            log.error("调用加密服务异常,异常原因:{}", e.getMessage());
        }
        throw new RuntimeException("调用加密服务异常");
    }

    /**
     * @description 单例模式获取
     * <AUTHOR>
     * @date 2022/3/7 11:08
     */
    public static SecurityHelper getSingleton() {
        if (securityHelper == null) {
            // 双重检测
            synchronized (YwEncryptUtils.class) {
                if (securityHelper == null) {
                    securityHelper = new SecurityHelper(url, appId, secret);
                }
            }
        }
        return securityHelper;
    }


    /**
     * <AUTHOR>
     * @description 需要加密和解密的类, 字段
     * @date 2023/5/9 10:09
     **/
    @Bean(name = "encryptCache")
    public ConcurrentMap<String, List<Field>> createEncryptConcurrentMap() {
        ConcurrentHashMap<String, List<Field>> handlerMap = new ConcurrentHashMap(16);
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        try {
            String pattern = ResourcePatternResolver.CLASSPATH_ALL_URL_PREFIX
                    .concat(ClassUtils.convertClassNameToResourcePath("com.ywwl.customer.center.modules.common.provider.domain")
                            .concat("/**/*.class"));
            org.springframework.core.io.Resource[] resources = resourcePatternResolver.getResources(pattern);
            //MetadataReader 的工厂类
            MetadataReaderFactory readerfactory = new CachingMetadataReaderFactory(resourcePatternResolver);
            for (org.springframework.core.io.Resource resource : resources) {
                //用于读取类信息
                MetadataReader reader = readerfactory.getMetadataReader(resource);
                //扫描到的class
                String classname = reader.getClassMetadata().getClassName();
                Class<?> clazz = Class.forName(classname);
                //判断是否有指定主解
                SensitiveData anno = clazz.getAnnotation(SensitiveData.class);
                if (Objects.nonNull(anno)) {
                    //将注解中的类型值作为key，对应的类作为 value
                    Field[] fields = clazz.getDeclaredFields();
                    List<Field> fieldList = new LinkedList<>();
                    for (Field field : fields) {
                        SensitiveField sensitiveField = field.getAnnotation(SensitiveField.class);
                        if (Objects.nonNull(sensitiveField)) {
                            field.setAccessible(true);
                            fieldList.add(field);
                        }
                    }
                    if (fieldList.size() > 0) {
                        handlerMap.put(classname, fieldList);
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException("初始化敏感数据字段失败");
        }
        return handlerMap;
    }

}
