package com.ywwl.customer.center.framework.config;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

/**
 * 去除空白字符过滤器
 *
 * <AUTHOR>
 * @date 2023/03/17 17:55
 **/
public class StripHandlerInterceptor implements Filter {

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest)servletRequest;
		HttpServletResponse response = (HttpServletResponse)servletResponse;

		// 因为 HttpServletRequest 不能直接操作 parmeterMap.所以构建 Mapper 对象来进行操作
		HttpServletRequestWrapper wrapper = new HttpServletRequestWrapper(request) {
			// 重写 wrapper 对象的 getParameter 方法. 替换掉脏字符串
			@Override
			public String getParameter(String name) {
				String value = super.getParameter(name);
				return StringUtils.isNotBlank(value) ? value.replaceAll("fuck", "***") : "";
			}

			@Override
			public String[] getParameterValues(String name) {
				String[] values = super.getParameterValues(name);
				if (values == null) {
					return new String[]{};
				}
				return Arrays.asList(values)
						.stream()
						.map(item -> item.replaceAll("fuck", "***"))
						.toArray(String[]::new);
			}
		};

		filterChain.doFilter(wrapper,servletResponse);
	}

}
