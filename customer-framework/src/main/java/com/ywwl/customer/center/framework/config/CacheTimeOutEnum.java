package com.ywwl.customer.center.framework.config;

import java.util.Arrays;
import java.util.Objects;

/**
 * 缓存超时时间配置枚举
 * <AUTHOR>
 */
public enum CacheTimeOutEnum {

	/**
	 * 授权缓存
	 */
	AUTHORIZATION_CACHE("authorizationCache",86400),

	/**
	 * 认证缓存
	 */
	AUTHENTICATION_CACHE("authenticationCache",86400),

	/**
	 * Session缓存
	 */
	ACTIVE_SESSION_CACHE("activeSessionCache",10800),

	/**
	 * 数字验证码缓存
	 */
	CAPTCHA_CACHE("captchaCache",3600),

	/**
	 * CSRF缓存
	 */
	CSRF_CACHE("csrfCache", 86400),

	/**
	 * 微信OpenID缓存
	 */
	CACHE_OPENID("cacheOpenId", 300),

	/**
	 * 滑动窗口缓存
	 */
	SLIDER_CAPTCHA_CACHE("sliderCaptchaCache", 120),

	/**
	 * 用户权限变更缓存
	 */
	PERMISSIONS_UPDATE_STATUS("permissions_update_status", -1),

	/**
	 * 城市缓存 暂不生效 在ehCache.xml中设置
	 */
	COUNTRY_CACHE("countryCache", -1),

	/**
	 * 发货账号选择项缓存
	 */
	SHIPER_ID_CACHE("shiperIdCache", 86400),

	/**
	 * 商户号缓存
	 */
	MERCHANT_CODE("merchantCode", 86400),

	/**
	 * 默认选项
	 */
	DEFAULT("default", 86400),
	/**
	 * <AUTHOR>
	 * @description 忘记密码
	 * @date 2022/8/15
	 **/
	FORGET_PASSWORD_CACHE("forget_password", 86400),

	SMS_CODE_CACHE("SMS_CODE", 300);

	/**
	 * KEY
	 */
	private String key;
	/**
	 * 超时时间 单位秒
	 */
	private int timeout;

	CacheTimeOutEnum(String key, int timeout) {
		this.key = key;
		this.timeout = timeout;
	}

	public String getKey() {
		return key;
	}

	public int getTimeout() {
		return timeout;
	}

	/**
	 * 获取枚举超时类
	 * @param key	key
	 * @return
	 */
	public static CacheTimeOutEnum getCacheTimeOut(String key){
		return Arrays.stream(CacheTimeOutEnum.values())
				.filter(cache -> Objects.equals(cache.getKey(),key))
				.findFirst()
				.orElse(DEFAULT);
	}

}
