package com.ywwl.customer.center.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @author: dinghy
 * @date: 2023/4/18 9:42
 */
@Configuration
@Data
@ConfigurationProperties("bill")
public class BillConfig {
    private String exportUrl;
    private String queryUrl;
    private String token;
    private String waybillUrl;
    private String alipayChargeUrl;
    private String alipayChargeKey;
    private String billApiUrl;
}
