package com.ywwl.customer.center.framework.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 幂等性注解
 *
 * <AUTHOR>
 * @date 2023/03/28 11:35
 **/
@Target({ElementType.METHOD, ElementType.CONSTRUCTOR})
@Retention(RetentionPolicy.RUNTIME)
public @interface Idempotent {

	/**
	 * 超时时间(s)
	 */
	long timeout() default 2;

}
