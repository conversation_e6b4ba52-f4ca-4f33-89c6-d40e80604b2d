package com.ywwl.customer.center.framework.annotation;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 去除空格切面
 *
 * <AUTHOR>
 * @date 2023/03/17 17:15
 **/
@Slf4j
@Aspect
@Component
public class StripAspect {


	@Pointcut(value = "@annotation(com.ywwl.customer.center.framework.annotation.Strip)")
	public void stripAspect() {
	}

	/**
	 * 分页切面方法
	 */
	@Around("stripAspect()")
	public Object verificationAspect(ProceedingJoinPoint pointcut) throws Throwable {

		return pointcut.proceed();
	}

}
