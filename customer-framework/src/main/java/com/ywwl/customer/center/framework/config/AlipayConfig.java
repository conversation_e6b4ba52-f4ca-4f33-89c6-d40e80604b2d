package com.ywwl.customer.center.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/6/15 15:26
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "alipay")
public class AlipayConfig {
    public static final String protocol = "https";
    public static final String gatewayHost = "openapi.alipay.com";
    public static final String signType = "RSA2";
    /**
     * @description 支付宝证书位置
     * <AUTHOR>
     * @date 2021/6/15 15:42
     */
    private String certLocation;
    /**
     * @description 支付回调地址
     * <AUTHOR>
     * @date 2021/6/15 15:42
     */
    private String notifyUrl;
    /**
     * @description 超时时间
     * <AUTHOR>
     * @date 2021/6/15 15:43
     */
    private String timeOutPeriod;
    /**
     * @description 支付商品名字
     * <AUTHOR>
     * @date 2021/6/16 10:52
     */
    private String productName;
    /**
     * @description 付款码支付的金额
     * <AUTHOR>
     * @date 2021/6/16 10:54
     */
    private String paymentAmount;
}
