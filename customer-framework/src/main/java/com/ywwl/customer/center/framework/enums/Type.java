package com.ywwl.customer.center.framework.enums;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 日志Key枚举
 *
 * <AUTHOR>
 * @date 2023/06/19 11:05
 **/

@Slf4j
public enum Type {

    WAYBILL_NUMBER(0, "运单号", "KEY"),
    NONE(-1, "默认","CONTROLLER"),
    ;

    Type(Integer code, String name, String topic) {
        this.code = code;
        this.name = name;
        this.logger = LoggerFactory.getLogger(topic);
    }

    Type(Integer code, String name) {
        this.code = code;
        this.name = name;
        this.logger = LoggerFactory.getLogger(Object.class);
    }

    /**
     * 枚举Code
     */
    private final Integer code;
    /**
     * 枚举名称
     */
    private final String name;

    /**
     * 日志对象
     * @return  日志对象
     */
    private final Logger logger;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Logger getLogger() {
        return logger;
    }

    @Override
    public String toString() {
        return getName();
    }
}
