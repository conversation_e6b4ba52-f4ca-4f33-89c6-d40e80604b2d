package com.ywwl.customer.center.framework.aspect;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.framework.annotation.Idempotent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 幂等性判断切面
 *
 * <AUTHOR>
 * @date 2023/03/28 13:34
 **/
@Slf4j
@Aspect
@Component
public class IdempotentAspect {

	@Pointcut(value = "@annotation(com.ywwl.customer.center.framework.annotation.Idempotent)")
	public void idempotentAspect() {}

	/**
	 * 分页切面方法
	 */
	@Around("idempotentAspect()&&@annotation(idempotent)")
	public Object verificationAspect(ProceedingJoinPoint pointcut, Idempotent idempotent) throws Throwable {
		List<Object> argsList = Arrays.stream(pointcut.getArgs())
				.filter(x -> !(x instanceof UserAgent))
				.collect(Collectors.toList()
			);
		// 根据方法名称和参数来限定调用次数
		String method = pointcut.getSignature().getName();
		String source = StringUtils.join(method, argsList);
		final String key = SecureUtil.sha1(source);
		// 尝试获取锁
		if (CacheUtil.redis().setIfAbsent(StrUtil.join(":",method,key), Duration.ofSeconds(idempotent.timeout()))) {
			// 获取到锁
			return pointcut.proceed();
		}
		// 如果有直接异常
		throw ResponseCode.PORTAL_505.getError();
	}

}
