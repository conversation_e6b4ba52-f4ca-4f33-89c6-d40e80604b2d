package com.ywwl.customer.center.framework.config;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @date 2023/02/16 18:16
 **/
@RestControllerAdvice
@Slf4j
public class ExceptionConfig {

	/**
	 * 接口参数缺失异常
	 */
	@ExceptionHandler(BindException.class)
	public JsonResult<String> handleRejectedBindException(BindException e) {
		log.warn(e.getMessage());
		List<ObjectError> errors = e.getAllErrors();
		String msg = errors.stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(","));
		return JsonResult.error(msg);
	}

	/**
	 * 接口参数缺失异常
	 */
	@ExceptionHandler(ConstraintViolationException.class)
	public JsonResult<String> handleRejectedConstraintViolationException(ConstraintViolationException e) {
		log.warn(e.getMessage());
		String collect = e.getConstraintViolations().stream()
				.map(x -> (ConstraintViolation<?>) x)
				.map(ConstraintViolation::getMessage)
				.collect(Collectors.joining(","));
		return JsonResult.error(collect);
	}

	/**
	 * 全局异常
	 */
	@ExceptionHandler(value = {Throwable.class,RuntimeException.class,Exception.class})
	public JsonResult<String> handleRejectedThrowable(Throwable e) {
		log.error(e.getMessage(),e);
		return JsonResult.error("服务器响应异常，请联系客服处理");
	}

	/**
	 * 全局 BusinessException 异常
	 */
	@ExceptionHandler(value = {BusinessException.class})
	public JsonResult<String> handleRejectedThrowable(BusinessException e) {
		log.error(e.getMessage(),e);
		if (Objects.nonNull(e.getResponseCode())) {
			return JsonResult.error(e.getResponseCode());
		}
		return JsonResult.error(e.getMessage());
	}

	/**
	 * 全局 IllegalAccessException 异常
	 */
	@ExceptionHandler(value = {IllegalArgumentException.class})
	public JsonResult<String> handleRejectedThrowable(IllegalArgumentException e) {
		log.error(e.getMessage(),e);
		return JsonResult.error(e.getMessage());
	}
	/**
	 * 全局 BusinessException 异常
	 */
	@ExceptionHandler(value = {ResponseCode.ResponseException.class})
	public JsonResult<String> handleRejectedThrowable(ResponseCode.ResponseException e) {
		log.error(e.getMessage(),e);
		return JsonResult.error(e.getResponseCode());
	}
}
