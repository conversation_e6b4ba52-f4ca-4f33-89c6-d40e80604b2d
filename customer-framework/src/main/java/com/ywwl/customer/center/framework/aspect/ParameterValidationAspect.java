package com.ywwl.customer.center.framework.aspect;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson2.JSON;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;

/**
 * 统一参数验证切面
 *
 * <AUTHOR>
 * @date 2023/03/28 13:34
 */
@Order(value = 0)
@Slf4j
@Aspect
@Component
public class ParameterValidationAspect {

	/**
	 * Hibernate Validator 检验类
	 */
	private static Validator validator;

	@Autowired
	public ParameterValidationAspect(Validator validator) {
		ParameterValidationAspect.validator = validator;
	}

	@Pointcut(value = "@annotation(org.springframework.validation.annotation.Validated)")
	public void validated() {
	}

	/**
	 *
	 * @param pointcut		切点
	 * @param valid			校验类
	 * @return				原响应
	 * @throws Throwable	原异常
	 */
	@Around("validated()&&@annotation(valid)")
	public Object verificationAspect(ProceedingJoinPoint pointcut, Validated valid) throws Throwable {
		Object[] args = pointcut.getArgs();
		List<String> infoMsg = new ArrayList<>(15);
		for (Object arg : args) {
			infoMsg.addAll(getConstraintViolation(arg, valid.value()));
		}
		// 非空则验证有问题
		if (!CollectionUtils.isEmpty(infoMsg)) {
			log.error(JSON.toJSONString(args));
			throw new BusinessException(String.join("，", infoMsg));
		}
		return pointcut.proceed();
	}

	/**
	 * 获取验证信息
	 *
	 * @param arg       对象
	 * @param group     校验组
	 * @return			错误信息
	 */
	public static List<String> getConstraintViolation(Object arg, Class<?>... group) {
		// 如果对象为空则不校验
		if (Objects.isNull(arg)) {
			return Collections.emptyList();
		}
		List<ConstraintViolation<?>> constraintViolation = new LinkedList<>();
		// 如果直接是List或Array
		if (arg instanceof Collection) {
			for (Object child : (Collection<?>)arg) {
				constraintViolation.addAll(ParameterValidationAspect.validator.validate(child, group));
			}
		}else if (ArrayUtil.isArray(arg)) {
			for (Object child : (Object[]) arg) {
				constraintViolation.addAll(ParameterValidationAspect.validator.validate(child, group));
			}
		}else {
			// 进行第一次验证
			constraintViolation.addAll(ParameterValidationAspect.validator.validate(arg, group));
		}
		// 错误信息列表
		List<String> infoMsg = new ArrayList<>(constraintViolation.size() * 2);
		// 转换为栈
		Stack<ConstraintViolation<?>> constraintViolations = new Stack<>();
		constraintViolations.addAll(constraintViolation);
		// 进行栈遍历
		while (!constraintViolations.empty()) {
			// 弹出一个
			ConstraintViolation<?> pop = constraintViolations.pop();
			// 获取并判断类型
			Object invalidValue = pop.getInvalidValue();
			if (invalidValue instanceof List) {
				final List<?> invalidValueList = (List<?>)pop.getInvalidValue();
				infoMsg.add(String.format(pop.getMessage(), invalidValueList.size()));
			} else if (ArrayUtil.isArray(invalidValue)) {
				final Object[] invalidValueList = (Object[])pop.getInvalidValue();
				infoMsg.add(String.format(pop.getMessage(), invalidValueList.length));
			} else {
				if (invalidValue instanceof String && ((String) invalidValue).length() > 100) {
					invalidValue = StringUtils.substring((String) invalidValue, 0, 100).concat("...");
				}
				infoMsg.add(String.format(pop.getMessage(),invalidValue));
			}
		}
		return infoMsg;
	}

}
