package com.ywwl.customer.center.framework.config;

import com.ywwl.customer.center.common.utils.MD5Util;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "match")
public class MatchNameConfig {
    private String url;
    private String accessKey;
    private String secretKey;

    public String getSign(String param){
        return MD5Util.getMD5String(param + secretKey).toUpperCase();
    }
}
