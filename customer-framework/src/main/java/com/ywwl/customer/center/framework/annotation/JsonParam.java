package com.ywwl.customer.center.framework.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 单独取Json参数注解类
 *
 * <AUTHOR>
 * @date 2023/03/28 13:34
 **/
@Target({ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface JsonParam {

	/**
	 * JsonKey
	 * @return	JsonKey
	 */
	String value() default "";

	/**
	 * 提示信息
	 * @return	提示信息
	 */
	String message() default "不能为空";

	/**
	 * 是否必输(默认非必输)
	 * @return	是否必输
	 */
	boolean required() default false;

}
