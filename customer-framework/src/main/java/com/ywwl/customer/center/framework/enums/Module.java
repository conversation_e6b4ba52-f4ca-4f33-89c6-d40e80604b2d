package com.ywwl.customer.center.framework.enums;

/**
 * 日志模块枚举
 *
 * <AUTHOR>
 * @date 2023/06/19 11:05
 **/
public enum Module {

    EJF(0, "EJF"),
    COMMON(1,"公共"),
    CALC_ACC(2,"运价试算"),
    SMALL_PACKAGE(3,"小包"),
    CUSTOMER_AUTH(4,"客户认证模块"),
    CONTRACT(5,"合同模块"),
    FBA(6,"fba"),
    USER(7,"用户模块"),
    BILL(8,"账单"),
    HWP(9,"海外派"),
    BUSINESS(10,"商业快递"),
    AMAZON(11,"亚马逊"),
    TAX_CONFIG(12,"税号配置"),
    TAX_RECORD(13,"税号备案"),
    OPEN(14,"开放"),
    SELF_SEND_FORECAST(15,"自寄自送预报"),
    PRODUCT_NAME_CHANGE(16,"品名工单修改"),
    TRACK_SUBSCRIPTION(17,"轨迹订阅"),
    OVERSEA_FORECAST(18,"海外派预报搜导入"),
    YWE_OVERSEA(19,"YWE业务"),

    ;

    Module(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 枚举Code
     */
    private final Integer code;
    /**
     * 枚举名称
     */
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    @Override
    public String toString() {
        return getName();
    }
}
