package com.ywwl.customer.center.framework.config;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.framework.annotation.JsonParam;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * Json数据解析器
 *
 * <AUTHOR>
 * @date 2023/03/09 17:09
 **/
public class JsonPathArgumentResolver implements HandlerMethodArgumentResolver {

	private static final String JSON_REQUEST_BODY = "JSON_REQUEST_BODY";

	@Override
	public boolean supportsParameter(MethodParameter methodParameter) {
		return methodParameter.hasParameterAnnotation(JsonParam.class);
	}

	@Override
	public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest webRequest, WebDataBinderFactory webDataBinderFactory) {
		JSONObject body = getRequestBody(webRequest);
		JsonParam annotation = parameter.getParameterAnnotation(JsonParam.class);
		// 断言annotation不为空
		if (Objects.isNull(annotation)) {
			return null;
		}
		Object val = null;
		try {
			if (Objects.nonNull(body)) {
				Type type = parameter.getGenericParameterType();
				String parameterName = annotation.value();
				if (StringUtils.isBlank(parameterName)) {
					parameterName = parameter.getParameterName();
				}
				val = body.getObject(parameterName, type);
			}
			if (annotation.required() && val == null) {
				throw new RuntimeException(annotation.message());
			}
		} catch (Exception exception) {
			if (annotation.required()) {
				throw exception;
			}
		}
		return val;
	}

	/**
	 * 获取Json字符串
	 * @param webRequest 请求
	 * @return Json字符串
	 */
	private JSONObject getRequestBody(NativeWebRequest webRequest) {
		HttpServletRequest servletRequest = webRequest.getNativeRequest(HttpServletRequest.class);
		JSONObject jsonObject = (JSONObject) servletRequest.getAttribute(JSON_REQUEST_BODY);
		if (Objects.isNull(jsonObject)) {
			try {
				String jsonBody = IOUtils.toString(servletRequest.getInputStream(), StandardCharsets.UTF_8);
				jsonObject = JSONObject.parseObject(jsonBody);
				servletRequest.setAttribute(JSON_REQUEST_BODY, jsonObject);
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
		}
		return jsonObject;
	}

}
