package com.ywwl.customer.center.framework.annotation;

import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.Type;
import org.apache.commons.lang3.StringUtils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.ywwl.customer.center.framework.enums.Type.NONE;

/**
 * 日志记录注解
 *
 * <AUTHOR>
 * @date 2023/03/30 16:30
 **/
@Target({ElementType.METHOD, ElementType.CONSTRUCTOR})
@Retention(RetentionPolicy.RUNTIME)
public @interface Logger {

	/**
	 * 描述一下当前方法的作用
	 */
	String name();

	/**
	 * 当前方法的模块
	 */
	Module module();

	/**
	 * 业务Key类型
	 * @return	业务Key路径
	 */
	Type type() default NONE;

	/**
	 * 请求参数JsonPath
	 * @return jsonPath
	 */
	String req() default StringUtils.EMPTY;

	/**
	 * 响应JsonPath
	 * @return jsonPath
	 */
	String rsp() default StringUtils.EMPTY;

	/**
	 * 忽略Json
	 * @return	忽略Json
	 */
	String ignoreRspKey() default StringUtils.EMPTY;

	/**
	 * 是否记录响应日志
	 * @return 是否记录响应日志
	 */
	boolean recordRsp() default true;
	/**
	 * 是否记录请求日志
	 * @return 是否记录请求日志
	 */
	boolean recordReq() default true;

	/**
	 * 是否持久化
	 * @return 是否持久化
	 */
	boolean persistence() default false;
}
