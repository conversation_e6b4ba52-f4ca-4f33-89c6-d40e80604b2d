package com.ywwl.customer.center.framework.log.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("sys_user_action_log")
@Builder
@Data
@NoArgsConstructor // 添加无参构造函数
@AllArgsConstructor // 可选：如果需要全参构造函数
public class SysUserActionLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Integer type;
    private String userCode;
    private Long userId;
    private String userIp;
    // 操作名称
    private String actionName;
    private LocalDateTime createTime;
    private String actionData;
}
