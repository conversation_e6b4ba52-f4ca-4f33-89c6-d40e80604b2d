package com.ywwl.customer.center.framework.aspect;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.ThreadContext;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.Filter;
import org.apache.logging.log4j.core.Layout;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.appender.AbstractAppender;
import org.apache.logging.log4j.core.config.Property;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.config.plugins.PluginAttribute;
import org.apache.logging.log4j.core.config.plugins.PluginElement;
import org.apache.logging.log4j.core.config.plugins.PluginFactory;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 获取线程日志
 *
 * <AUTHOR>
 * @date 2023/03/28 13:34
 */
@Plugin(name = "ThreadLogAppender", category = "Core", elementType = "appender", printObject = true)
public class ThreadLogAppender extends AbstractAppender {
    private static final ThreadLocal<LinkedList<Object>> threadLogs = new ThreadLocal<>();

    /**
     * 创建线程日志
     * @param name 名称
     * @param filter 过滤器
     * @param layout 布局
     * @param ignoreExceptions 是否忽略异常
     * @param properties 属性
     */
    public ThreadLogAppender(String name,
                             Filter filter,
                             Layout<? extends Serializable> layout,
                             boolean ignoreExceptions,
                             Property[] properties) {
        super(name, filter, layout, ignoreExceptions, properties);
    }

    /**
     * 创建线程日志
     * @param name 名称
     * @param filter 过滤器
     * @param layout 布局
     * @param ignoreExceptions 是否忽略异常
     * @param properties 属性
     * @return 线程日志追加器
     */
    @PluginFactory
    public static Appender createAppender(@PluginAttribute("name") String name,
                                          @PluginElement("filter")Filter filter,
                                          @PluginElement("layout") Layout<? extends Serializable> layout,
                                          @PluginAttribute("ignoreExceptions") boolean ignoreExceptions,
                                          @PluginElement("properties") Property[] properties) {
        return new ThreadLogAppender(name, filter, layout, ignoreExceptions, properties);
    }

    /**
     * 日志追加
     * @param event 日志事件
     */
    @Override
    public void append(LogEvent event) {
        if (ThreadContext.get("key") != null) {
            LinkedList<Object> logs = threadLogs.get();
            if (CollectionUtils.isEmpty(logs)) {
                logs = new LinkedList<>();
                threadLogs.set(logs);
            }
            String logMessage = event.getMessage().getFormattedMessage();
            logs.add(logMessage);
        }
    }

    /**
     * 获取日志信息
     * @return 日志信息
     */
    public static List<Object> getThreadLogs() {
        return threadLogs.get();
    }
}
