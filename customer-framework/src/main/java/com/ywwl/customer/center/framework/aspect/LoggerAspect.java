package com.ywwl.customer.center.framework.aspect;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.*;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.utils.IpUtils;
import com.ywwl.customer.center.common.utils.JsonUtils;
import com.ywwl.customer.center.framework.annotation.Logger;
import com.ywwl.customer.center.framework.enums.Module;
import com.ywwl.customer.center.framework.enums.RType;
import com.ywwl.customer.center.framework.enums.SysLogTypeEnum;
import com.ywwl.customer.center.framework.enums.Type;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.framework.log.domain.SysUserActionLog;
import com.ywwl.customer.center.framework.log.dto.LogDTO;
import com.ywwl.customer.center.framework.log.service.SysUserActionLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ywwl.customer.center.common.utils.HttpUtil.getStackTrace;
import static org.apache.commons.lang3.StringUtils.*;


/**
 * 日志记录切面
 *
 * <AUTHOR>
 * @date 2023/03/28 13:34
 **/
@Slf4j
@Aspect
@Component
public class LoggerAspect {

    public static final String IGNORE = "ignore";
    @Resource
    private SysUserActionLogService sysUserActionLogService;

    @Pointcut(value = "@annotation(com.ywwl.customer.center.framework.annotation.Logger)")
    public void reqAspect() {
    }

    /**
     * 打印操作信息的日志
     *
     * @param requestId       请求ID
     * @param operationModule 模块名称
     * @param operationName   操作名称
     * @param key             key
     * @param type            Key类型
     * @param requestContent  请求参数
     * @param operatorId      操作人ID
     */
    public void logOperationInfoRequest(String requestId,
                                        Type type,
                                        String key,
                                        Module operationModule,
                                        String operationName,
                                        String requestContent,
                                        String ip,
                                        long operatorId,
                                        boolean persistence) {
        // 判断Key是否存在
        log.info("请求ID: {}, 操作模块: {}, {}: {}, 操作名称: {}, 请求参数: {}, 操作人: {}, 请求IP: {}",
                requestId,
                operationModule.getName(),
                type.getName(),
                key,
                operationName,
                requestContent,
                operatorId,
                ip);
        // 存入es
        final LogDTO dto = LogDTO.builder()
                .operatorId(operatorId)
                .requestId(requestId)
                .operationModule(operationModule.getName())
                .operationName(operationName)
                .requestContent(requestContent)
                .ip(ip)
                .key(key)
                .type(type.getName())
                .time(new Date())
                .rType(RType.REQUEST)
                .build();
        type.getLogger().info(JSON.toJSONString(dto, JSONWriter.Feature.WriteEnumsUsingName));
        if (persistence) {
            // 存库...
            SysUserActionLog log = SysUserActionLog.builder()
                    .type(SysLogTypeEnum.ACTION.getValue())
                    .userId(operatorId)
                    .userIp(ip)
                    .actionName(operationModule.getName()+"-"+operationName)
                    .createTime(LocalDateTime.now())
                    .build();
            sysUserActionLogService.insertLog(log);
        }
    }

    /**
     * 打印操作信息的日志
     *
     * @param requestId       请求ID
     * @param operationModule 模块名称
     * @param operationName   操作名称
     * @param responseContent 响应内容
     * @param elapsedTime     耗时，单位为毫秒
     * @param operatorId      操作人ID
     */
    public void logOperationInfoResponse(String requestId,
                                         Type type,
                                         String key,
                                         Module operationModule,
                                         String operationName,
                                         String requestContent,
                                         String responseContent,
                                         String ip,
                                         long elapsedTime,
                                         long operatorId) {
        // 判断Key是否存在
        log.info("请求ID: {}, 操作模块: {}, {}: {}, 操作名称: {}, 请求参数: {}, 响应内容: {}, 耗时: {}ms, 操作人: {}, 请求IP: {}",
                requestId,
                operationModule.getName(),
                type.getName(),
                key,
                operationName,
                requestContent,
                responseContent,
                elapsedTime,
                operatorId,
                ip);
        final List<Object> logs = ThreadLogAppender.getThreadLogs();
        try {
            // 存入es
            final LogDTO dto = LogDTO.builder()
                    .operatorId(operatorId)
                    .requestId(requestId)
                    .elapsedTime(elapsedTime)
                    .operationModule(operationModule.getName())
                    .operationName(operationName)
                    .requestContent(requestContent)
                    .responseContent(responseContent)
                    .ip(ip)
                    .logs(logs)
                    .key(key)
                    .type(type.getName())
                    .time(new Date())
                    .rType(RType.RESPONSE)
                    .build();
            type.getLogger().info(JSON.toJSONString(dto, JSONWriter.Feature.WriteEnumsUsingName));
        }finally {
            // 清空日志
            if (CollectionUtils.isNotEmpty(logs)) {
                logs.clear();
            }
        }
    }


    /**
     * 日志切面方法
     *
     * @param pointcut 切点
     * @param logBean  日志类
     * @return 原响应
     * @throws Throwable 执行方法可能出现的异常
     */
    @Around("reqAspect()&&@annotation(logBean)")
    public Object verificationAspect(ProceedingJoinPoint pointcut, Logger logBean) throws Throwable {
        // 获取模块名称
        Module module = logBean.module();
        Type type = logBean.type();
        String name = logBean.name();
        long start = System.currentTimeMillis();
        // 生成请求ID
        String uuid = IdUtil.fastSimpleUUID();
        // 获取操作人ID
        Long userId = getUserId();
        final String ip = getIP();
        Object[] args = pointcut.getArgs();
        List<?> argsList = Optional.ofNullable(args)
                .map(param -> Arrays.stream(param)
                        .map(LoggerAspect::nonSerializedContent)
                        .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        // 获取请求参数Json
        final String param = JSON.toJSONString(
                argsList,
                JSONWriter.Feature.WriteByteArrayAsBase64);
        // 打印请求日志
        final String reqKey = getKeyValue(logBean.req(), param);
        // 是否记录请求日志
        if (logBean.recordReq()) {
            logOperationInfoRequest(
                    uuid,
                    type,
                    reqKey,
                    module,
                    name,
                    param,
                    ip,
                    userId,
                    logBean.persistence());
        }else {
            logOperationInfoRequest(
                    uuid,
                    type,
                    reqKey,
                    module,
                    name,
                    IGNORE,
                    ip,
                    userId,
                    logBean.persistence());
        }
        try {
            Object proceed;
            try {
                ThreadContext.put("key", uuid);
                // 在这里可以对threadLogs进行处理，比如打印或存储到文件等
                proceed = pointcut.proceed();
            } finally {
                // 操作完成后停止记录
                ThreadContext.remove("key");
            }
            String result = EMPTY;
            String rspKey = EMPTY;
            // 判断是否打印响应日志
            if (logBean.recordRsp()) {
                // 打印响应日志
                result = Optional.ofNullable(proceed)
                        .map(LoggerAspect::nonSerializedContent)
                        .map(p -> JSON.toJSONString(
                                p,
                                JSONWriter.Feature.WriteByteArrayAsBase64))
                        .orElse(EMPTY);
                rspKey = getKeyValue(logBean.rsp(), result);
                // 进行响应结果的忽略
                if (StringUtils.isNotBlank(logBean.ignoreRspKey())) {
                    final Object parse = JSON.parse(result);
                    ignore(logBean.ignoreRspKey(), parse);
                    result = JSON.toJSONString(parse);
                }
            } else{
                result = IGNORE;
            }
            logOperationInfoResponse(
                    uuid,
                    type,
                    Stream.of(reqKey, rspKey)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(",")),
                    module,
                    name,
                    param,
                    result,
                    ip,
                    System.currentTimeMillis() - start,
                    userId);
            return proceed;
        } catch (ResponseCode.ResponseException | BusinessException ie) {
            logOperationInfoResponse(
                    uuid,
                    type,
                    reqKey,
                    module,
                    name,
                    param,
                    ie.getMessage(),
                    ip,
                    System.currentTimeMillis() - start,
                    userId);
            throw ie;
        } catch (Throwable t) {
            logOperationInfoResponse(
                    uuid,
                    type,
                    reqKey,
                    module,
                    name,
                    param,
                    getStackTrace(t),
                    ip,
                    System.currentTimeMillis() - start,
                    userId);
            throw t;
        }
    }

    /**
     * 获取请求IP
     *
     * @return 请求IP
     */
    private String getIP() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(requestAttributes)) {
            HttpServletRequest request = requestAttributes.getRequest();
            return IpUtils.getIpAddress(request);
        }
        return null;
    }

    /**
     * 非序列化内容
     *
     * @param arg 参数
     * @return 是否非序列化内容
     */
    private static Object nonSerializedContent(Object arg) {
        if (arg instanceof File ||
                arg instanceof File[] ||
                arg instanceof InputStream ||
                arg instanceof InputStream[] ||
                arg instanceof OutputStream ||
                arg instanceof OutputStream[] ||
                arg instanceof ServletRequest ||
                arg instanceof ServletResponse ||
                arg instanceof MultipartFile ||
                arg instanceof MultipartFile[] ||
                arg instanceof UserAgent) {
            return null;
        }
        return arg;
    }

    /**
     * 获取请求KeyValue
     *
     * @param key    请求Key
     * @param result 响应结果
     * @return 请求KeyValue
     */
    private String getKeyValue(String key, String result) {
        // 获取请求Key
        String value = null;
        Object keyObj = null;
        if (isNotBlank(key)) {
            final Object json = JSON.parse(result);
            // 如果是JsonObject
            if (json instanceof JSONObject) {
                final JSONObject resultObj = (JSONObject) json;
                keyObj = resultObj.getByPath(key);
            }
            // 如果是JsonArray
            if (json instanceof JSONArray) {
                final JSONArray resultAry = (JSONArray) json;
                if (CollectionUtil.isEmpty(resultAry)) {
                    return value;
                }
                JSONObject resultObj = new JSONObject();
                if (resultAry.size() == 1) {
                    // 如果只有一个元素，直接取第一个元素
                    final Object o = resultAry.get(0);
                    if (o instanceof JSONObject) {
                        resultObj = (JSONObject) o;
                    } else {
                        resultObj.put("v", o);
                    }
                } else {
                    // 如果有多个元素，则设置为v
                    resultObj.put("v", resultAry);
                }
                keyObj = resultObj.getByPath(key);
            }
            if (Objects.nonNull(keyObj)) {
                value = Objects.toString(keyObj);
                if (keyObj instanceof Iterable) {
                    Iterable<?> iterable = (Iterable<?>) keyObj;
                    value = join(iterable, ",");
                }
            }
        }
        return value;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    public Long getUserId() {
        try {
            Subject subject = SecurityUtils.getSubject();
            Long userId = 0L;
            if (subject.isAuthenticated()) {
                Object principal = subject.getPrincipal();
                UserAgent user = JsonUtils.parse((String) principal, UserAgent.class);
                userId = user.getUserId();
            }
            return userId;
        } catch (Throwable e) {
            log.error("日志无法获取到操作用户信息 {}",e.getMessage());
        }
        return 0L;
    }

    /**
     * 排除字段
     * @param ignore 排除字段
     * @param parse 响应结果
     */
    private static void ignore(String ignore, Object parse) {
        // 忽略响应中的某些字段
        if (StringUtils.isNotBlank(ignore)) {
            // 如果是JSONObject
            if (parse instanceof JSONObject) {
                final JSONObject jsonObject = (JSONObject) parse;
                JSONPath.remove(jsonObject, ignore);
                // 如果是JSONArray
            }else if (parse instanceof JSONArray) {
                final JSONObject jsonObject = new JSONObject();
                jsonObject.put("v", parse);
                JSONPath.remove(jsonObject, ignore);
            }
        }
    }



}
