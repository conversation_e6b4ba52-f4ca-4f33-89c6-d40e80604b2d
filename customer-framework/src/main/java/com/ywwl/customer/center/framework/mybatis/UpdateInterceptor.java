package com.ywwl.customer.center.framework.mybatis;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2022/3/4 15:19
 */
@Slf4j
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class UpdateInterceptor implements Interceptor {
    private static final String crypt = "et";
    @Resource
    private ConcurrentMap<String, List<Field>> encryptCache;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];
        if (Objects.isNull(parameter)) {
            //无参数，直接放行
            return invocation.proceed();
        }
        // 如果是多个参数或使用param注解（param注解会将参数放置在parammap中）
        if (parameter instanceof MapperMethod.ParamMap) {
// 更新操作被拦截
            Map paramMap = (Map) parameter;
            if (paramMap.containsKey(crypt)) {
                Object updateParameter = paramMap.get(crypt);
                // 假如需要加密
                if (Objects.nonNull(updateParameter) && checkSensitive(updateParameter)) {
                    return proceed(invocation, mappedStatement, updateParameter);
                }
            }
        } else if (checkSensitive(parameter)) {
            return proceed(invocation, mappedStatement, parameter);
        }
        return invocation.proceed();
    }

    public Object proceed(Invocation invocation, MappedStatement mappedstatement, Object parameter) throws Exception {
        //调用加解密服务
        Class<?> sensitiveObjectClass = parameter.getClass();
        String name = sensitiveObjectClass.getName();
        List<Field> fieldList = encryptCache.get(name);
        try {
            if (Objects.nonNull(fieldList)) {
                List<Field> encryptFieldList = new LinkedList<>();
                // 需要加密的字段名和值
                Map<String, String> needEncrypt = new HashMap<>(8);
                for (Field field : fieldList) {
                    field.setAccessible(true);
                    Object targetProperty = field.get(parameter);
                    // 如果这个字段值为空,跳过
                    if (Objects.nonNull(targetProperty)) {
                        String value = String.valueOf(targetProperty);
                        if (YwEncryptUtils.check(value)) {
                            needEncrypt.put(field.getName(), value.trim());
                            encryptFieldList.add(field);
                        }
                    }
                }
                if (encryptFieldList.size() > 0) {
                    Map<String, String> encryptMap = YwEncryptUtils.encrypt(needEncrypt);
                    String keyVersion = YwEncryptUtils.getEncryptKeyVersion(encryptMap);
                    for (Field field : encryptFieldList) {
                        field.set(parameter, YwEncryptUtils.encryptHandle(encryptMap.get(field.getName()), keyVersion));
                    }
                    Object result = invocation.proceed();
                    // 还原加密之后的字段
                    for (Field field : encryptFieldList) {
                        field.set(parameter, needEncrypt.get(field.getName()));
                    }
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("调用加密异常,原因:{}", e.getMessage());
        }
        return invocation.proceed();
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    /**
     * @description 判断是否为需要脱敏的类
     * <AUTHOR>
     * @date 2022/3/3 13:37
     */
    public Boolean checkSensitive(Object object) {
        Class<?> sensitiveObjectClass = object.getClass();
        String name = sensitiveObjectClass.getName();
        List<Field> fieldList = encryptCache.get(name);
        return Objects.nonNull(fieldList);
    }
}
