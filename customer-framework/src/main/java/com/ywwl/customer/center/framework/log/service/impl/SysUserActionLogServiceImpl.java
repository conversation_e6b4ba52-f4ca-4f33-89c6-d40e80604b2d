package com.ywwl.customer.center.framework.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.framework.log.dao.SysUserActionLogDao;
import com.ywwl.customer.center.framework.log.domain.SysUserActionLog;
import com.ywwl.customer.center.framework.log.service.SysUserActionLogService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public class SysUserActionLogServiceImpl extends ServiceImpl<SysUserActionLogDao, SysUserActionLog> implements SysUserActionLogService {
    @Override
    public void insertLog(SysUserActionLog log) {
      CompletableFuture.runAsync(() -> {
            try {
                save(log);
            } catch (Exception e) {
                DingTalkClient.sendDingNoticeToUser("1150045","日志","记录操作日志插入异常，原因:"+e.getMessage());
            }
        });
    }

    @Override
    public void asyncBatchSave(List<SysUserActionLog> logs) {
        CompletableFuture.runAsync(() -> {
            try {
               saveBatch(logs);
            } catch (Exception e) {
                DingTalkClient.sendDingNoticeToUser("1150045","日志","记录操作日志插入异常，原因:"+e.getMessage());
            }
        });
    }
}
