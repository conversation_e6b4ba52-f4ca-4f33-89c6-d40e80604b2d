package com.ywwl.customer.center.framework.config.util;

/**
 * 请求参数传递辅助类
 */
public class DynamicTableNameHelper {
    /**
     * 请求参数存取
     */
    private static final ThreadLocal<Object> REQUEST_DATA = new ThreadLocal<>();

    /**
     * 设置请求参数
     *
     * @param requestData 请求参数 MAP 对象
     */
    public static void setRequestData(Object requestData) {
        REQUEST_DATA.set(requestData);
    }

    /**
     * 获取请求参数
     *
     * @return 请求参数 MAP 对象
     */
    public static Object getRequestData() {
        return REQUEST_DATA.get();
    }

    /**
     * 清理请求参数
     **/
    public static void removeRequestData() {
        REQUEST_DATA.remove();
    }
}
