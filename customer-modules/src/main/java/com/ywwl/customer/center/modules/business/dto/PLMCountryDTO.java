package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PLMCountryDTO extends GeneralDTO {
    private List<DataDTO> data;
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private String countryId;
        private String countryCode;
        private String countryName;
        private Boolean allowOrder;
        private Boolean allowEnter;
        private Boolean hasRecommend;
        private Boolean orderPretreatment;
        private Boolean addressVerification;
        private Boolean supportReassignment;
        private Boolean recordTax;
    }
}
