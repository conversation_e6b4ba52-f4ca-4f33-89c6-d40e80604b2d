package com.ywwl.customer.center.modules.common.auth.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import com.ywwl.customer.center.modules.common.auth.dto.*;
import com.ywwl.customer.center.modules.common.auth.enums.*;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.*;
import com.ywwl.customer.center.modules.common.provider.dto.PersonalCertificateDTO;
import com.ywwl.customer.center.modules.common.provider.reponse.VerificationJsonBody;
import com.ywwl.customer.center.modules.common.provider.service.VerificationService;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.system.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * @author: dinghy
 * @date: 2023/3/2 10:54
 * <p>
 * 客户认证
 * </p>
 */
@Slf4j
@Service
public class CustomerAuthServiceImpl implements CustomerAuthService {
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private FileService fileService;
    @Resource
    private VerificationService verificationService;


    @Override
    public String getCustomerAuthStatus(String no) {
        JSONObject data = commonCrmService.getCustomerAuthInfo(no);
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        if (Objects.isNull(customer)) {
            return CustomerAuthStepEnum.AUTH_INIT.value();
        }
        if (ApplyTypeEnum.CHANGE_CUSTOMER_NAME.getValue().equals(customer.getApplyType())) {
            customer.setCustomerType(customer.getCustomerTypeNew());
            customer.setCustomerArea(customer.getCustomerAreaNew());
        }
        CustomerAuthStatusEnum customerAuthStatusEnum = CustomerAuthStatusEnum.getCustomerAuthStatusEnum(customer.getAuthStatus());
        String merchantProperty = customer.getMerchantProperty();
        switch (customerAuthStatusEnum) {
            case WAIT_AUTH: {
                CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getCustomerTypeEnum(customer.getCustomerType());
                CustomerStatusEnum customerStatusEnum = CustomerStatusEnum.getCustomerStatusEnum(customer.getAuditStatus());
                Integer customerArea = customer.getCustomerArea();
                switch (customerTypeEnum) {
                    case INDIVIDUAL:
                        switch (customerStatusEnum) {
                            case UN_COMMIT:
                            case STAGING:
                                if (!CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea)) {
                                    return CustomerAuthStepEnum.PERSON_AUDITING.value();
                                }
                                return CustomerAuthStepEnum.PERSON_AUTH.value();
                            case WAIT_AUDIT:
                            case AUDIT_FAIL:
                                return CustomerAuthStepEnum.PERSON_AUDITING.value();
                            case AUDIT_SUCCESS:
                                return CustomerAuthStepEnum.DATA_ERROR.value(); // added return statement
                        }
                    case ENTERPRISE:
                        switch (customerStatusEnum) {
                            case WAIT_AUDIT:
                            case AUDIT_FAIL:
                                // 如果是大陆，并且有线下实名属性
                                if (CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea) && merchantProperty != null &&  merchantProperty.contains(MerchantPropertyEnum.OFF_LINE_REAL_NAME.getValue())) {
                                    return CustomerAuthStepEnum.MAINLAND_COMPANY_AUDIT.value();
                                }
                                return CustomerAuthStepEnum.COMPANY_AUDITING.value();
                            case AUDIT_SUCCESS:
                                return CustomerAuthStepEnum.DATA_ERROR.value(); // added return statement
                            case UN_COMMIT:
                            case STAGING: {
                                // 如果是大陆，并且有线下实名属性
                                if (CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea) && merchantProperty != null && merchantProperty.contains(MerchantPropertyEnum.OFF_LINE_REAL_NAME.getValue())) {
                                    return CustomerAuthStepEnum.MAINLAND_COMPANY_AUDIT.value();
                                }
                                if (!CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea)) {
                                    return CustomerAuthStepEnum.COMPANY_AUDITING.value();
                                }
                                // 是否个人 
                                PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                                if (Objects.isNull(personAuthVo)) {
                                    return CustomerAuthStepEnum.COMPANY_AGENT_AUTH.value();
                                } else {
                                    if (!CommonCrmConstant.PERSON_AUTHED.equals(personAuthVo.getAuthResult())) {
                                        return CustomerAuthStepEnum.COMPANY_AGENT_AUTH.value();
                                    } else {
                                        CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
                                        if (Objects.isNull(companyAuthVo)) {
                                            return CustomerAuthStepEnum.COMPANY_AUTH_SUBMIT.value();
                                        } else {
                                            CompanyAuthStatusEnum companyAuthStatusEnum = CompanyAuthStatusEnum.getCompanyAuthStatusEnum(companyAuthVo.getAuthStatus());
                                            switch (companyAuthStatusEnum) {
                                                case INIT:
                                                case FAIL:
                                                    return CustomerAuthStepEnum.COMPANY_AUTH_SUBMIT.value();
                                                case SUBMIT_BANK:
                                                    return CustomerAuthStepEnum.COMPANY_BANK_SUBMIT.value();
                                                case AMOUNT_VERIFY:
                                                    return CustomerAuthStepEnum.COMPANY_VERIFY_SUBMIT.value();
                                                case SUCCESS:
                                                    return CustomerAuthStepEnum.DATA_ERROR.value(); // added return statement
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                }
                            }
                            default:
                                break;
                        }
                    default:
                        break;
                }
            }
            break;
            case AUTH_SUCCESS: {
                ApplyTypeEnum customerApplyTyeEnum = ApplyTypeEnum.getCustomerApplyTyeEnum(customer.getApplyType());
                switch (customerApplyTyeEnum) {
                    case NEW:
                    case APPLY_SUCCESS:
                    case AUTONOMY_CHANGE:
                    case CHANGE_CUSTOMER_NAME:
                        return CustomerAuthStepEnum.AUTH_SUCCESS.value();
                    case CHANGE_ID_CARD_ATTACH:
                        // 是否为待审核
                        CustomerStatusEnum customerStatusEnum = CustomerStatusEnum.getCustomerStatusEnum(customer.getAuditStatus());
                        switch (customerStatusEnum) {
                            case STAGING:
                            case UN_COMMIT:
                            case AUDIT_FAIL:
                                return CustomerAuthStepEnum.COMPLETE_CARD_ATTACH.value();
                            case WAIT_AUDIT:
                                return CustomerAuthStepEnum.AUTH_SUCCESS.value();
                        }
                }
            }
            break;
        }
        return CustomerAuthStepEnum.DATA_ERROR.value();
    }


    @Override
    public CustomerAuthVo getCustomerAuthInfo(String merchantNo) {
        JSONObject data = commonCrmService.getCustomerAuthInfo(merchantNo);
        return getCustomerAuthVo(data);
    }

    @Override
    public CustomerAuthVo getCustomerInfoByUserCode(String userCode) {
        JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
        return getCustomerAuthVo(data);
    }

    private CustomerAuthVo getCustomerAuthVo(JSONObject data) {
        CustomerAuthVo customerAuthVo = new CustomerAuthVo();
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        if (Objects.isNull(customer)) {
            return customerAuthVo;
        }
        if (StringUtils.isNotBlank(customer.getBankAccountProof())) {
            customer.setBankAccountProof(fileService.appendSignature(customer.getBankAccountProof()));
        }
        customerAuthVo.setAuditType(customer.getAuditType());
        // 如果是变更客户名时候
        if (ApplyTypeEnum.CHANGE_CUSTOMER_NAME.getValue().equals(customer.getApplyType()) && (CustomerStatusEnum.UN_COMMIT.status().equals(customer.getAuditStatus())) && !CustomerChangeType.MERCHANT_INFO.getCode().equals(customer.getChangeType())) {
            customerAuthVo.setApplyType(ApplyTypeEnum.CHANGE_CUSTOMER_NAME.getValue());
            customerAuthVo.setCustomerType(customer.getCustomerTypeNew());
            customerAuthVo.setCustomerArea(customer.getCustomerAreaNew());
            if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthVo.getCustomerType())) {
                customerAuthVo.setPersonName(customer.getMerchantNameNew());
            }
            if (CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthVo.getCustomerType())) {
                customerAuthVo.setCompanyName(customer.getMerchantNameNew());
                PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                if (Objects.nonNull(personAuthVo)) {
                    customerAuthVo.setPersonBankCard(personAuthVo.getBankCard());
                    customerAuthVo.setMobile(personAuthVo.getMobile());
                    customerAuthVo.setAuthType(personAuthVo.getAuthType());
                    customerAuthVo.setPersonName(personAuthVo.getName());
                    customerAuthVo.setPersonCard(personAuthVo.getIdNumber());
                }
            }
            return customerAuthVo;
        }
        BeanUtils.copyProperties(customer, customerAuthVo);
        // 图片增加验签
        customerAuthVo.setCardAttach(fileService.appendSignature(customerAuthVo.getCardAttach()));
        customerAuthVo.setCardBackAttach(fileService.appendSignature(customerAuthVo.getCardBackAttach()));
        customerAuthVo.setCardHoldAttach(fileService.appendSignature(customerAuthVo.getCardHoldAttach()));
        customerAuthVo.setPassAttach(fileService.appendSignature(customerAuthVo.getPassAttach()));
        customerAuthVo.setPassBackAttach(fileService.appendSignature(customerAuthVo.getPassBackAttach()));
        customerAuthVo.setCorporateCompanyProve(fileService.appendSignature(customerAuthVo.getCorporateCompanyProve()));
        customerAuthVo.setUserProve(fileService.appendSignature(customerAuthVo.getUserProve()));
        customerAuthVo.setUserMandate(fileService.appendSignature(customerAuthVo.getUserMandate()));
        customerAuthVo.setPersonCompanyProve(fileService.appendSignature(customerAuthVo.getPersonCompanyProve()));
        customerAuthVo.setHomeAttach(fileService.appendSignature(customerAuthVo.getHomeAttach()));
        customerAuthVo.setHomeBackAttach(fileService.appendSignature(customerAuthVo.getHomeBackAttach()));
        // 审核状态
        if (CommonCrmConstant.PERSONA_AUDIT_TYPE.equals(customer.getAuditType())) {
            customerAuthVo.setAuthType(PersonalAuthTypeEnum.AUDIT.getCode());
            return customerAuthVo;
        }
        PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
        if (Objects.nonNull(personAuthVo)) {
            customerAuthVo.setPersonBankCard(personAuthVo.getBankCard());
            customerAuthVo.setMobile(personAuthVo.getMobile());
            customerAuthVo.setAuthType(personAuthVo.getAuthType());
            customerAuthVo.setPersonName(personAuthVo.getName());
            customerAuthVo.setPersonCard(personAuthVo.getIdNumber());
            customerAuthVo.setBankCode(personAuthVo.getBankCode());
        }
        CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
        if (Objects.nonNull(companyAuthVo)) {
            customerAuthVo.setCompanyBankCard(companyAuthVo.getBankCard());
            customerAuthVo.setBankBranchName(companyAuthVo.getBankBranchName());
            customerAuthVo.setBankCode(companyAuthVo.getBankCode());
            if (Objects.nonNull(companyAuthVo.getCounter())) {
                customerAuthVo.setCounter(CommonCrmConstant.MAX_COUNTER - companyAuthVo.getCounter());
            }
            if (StringUtils.isNotBlank(companyAuthVo.getLegalIdCard()) && personAuthVo != null && StringUtils.isNotBlank(personAuthVo.getIdNumber()) && companyAuthVo.getLegalIdCard().equals(personAuthVo.getIdNumber())) {
                customerAuthVo.setAgentIsLegal(true);
            }

        }
        return customerAuthVo;
    }

    @Override
    public void saveCustomerType(SaveCustomerTypeDTO saveCustomerTypeDTO) {
        commonCrmService.saveCustomerType(saveCustomerTypeDTO);
    }

    @Override
    public PersonAuthVo savePersonApply(SavePersonApplyDTO savePersonApplyDTO) {
        ValidateCustomerUniqueDTO validateCustomerUniqueDTO = new ValidateCustomerUniqueDTO();
        validateCustomerUniqueDTO.setUserCode(savePersonApplyDTO.getUserCode());
        validateCustomerUniqueDTO.setCustomerType(CustomerTypeEnum.INDIVIDUAL.value());
        validateCustomerUniqueDTO.setPersonCard(savePersonApplyDTO.getIdCard());
        validateCustomerUnique(validateCustomerUniqueDTO);
        return commonCrmService.savePersonApply(savePersonApplyDTO);
    }

    @Override
    public String getFaceAuthUrl(QueryFaceAuthDTO queryFaceAuthUrlDTO) {
        return commonCrmService.getFaceAuthUrl(queryFaceAuthUrlDTO);
    }

    @Override
    public Integer getFaceAuthResult(QueryFaceAuthDTO queryFaceAuthDTO) {
        return commonCrmService.getFaceAuthResult(queryFaceAuthDTO);
    }

    @Override
    public void createPersonBankAuth(String merchantNo) {
        commonCrmService.createPersonBankAuth(merchantNo);
    }

    @Override
    public void checkPersonBankMobile(CheckBankMobileDTO checkBankMobileDTO) {
        commonCrmService.checkPersonBankMobile(checkBankMobileDTO);
    }

    @Override
    public PersonAuthVo saveCompanyAgentAuth(SaveCompanyAgentAuthDTO saveCompanyAgentAuthDTO) {
        return commonCrmService.saveCompanyAgentAuth(saveCompanyAgentAuthDTO);
    }

    @Override
    public CompanyAuthVo saveCompanyApply(SaveCompanyApplyDTO companyAuthDTO) {
        ValidateCustomerUniqueDTO validateCustomerUniqueDTO = new ValidateCustomerUniqueDTO();
        validateCustomerUniqueDTO.setUserCode(companyAuthDTO.getUserCode());
        validateCustomerUniqueDTO.setCustomerType(CustomerTypeEnum.ENTERPRISE.value());
        validateCustomerUniqueDTO.setRegisterNumber(companyAuthDTO.getRegisterNumber());
        validateCustomerUniqueDTO.setCompanyName(companyAuthDTO.getCompanyName());
        validateCustomerUnique(validateCustomerUniqueDTO);
        return commonCrmService.saveCompanyApply(companyAuthDTO);
    }

    @Override
    public List getBranchBankList(QueryBranchBankDTO queryBranchBankDTO) {
        return commonCrmService.getBranchBankList(queryBranchBankDTO);
    }

    @Override
    public void saveCompanyBank(SaveCompanyBankDTO saveCompanyBankDTO) {
        commonCrmService.saveCompanyBank(saveCompanyBankDTO);
    }

    @Override
    public void verifyCompanyBankAmount(VerifyCompanyBankAmountDTO verifyCompanyBankAmountDTO) {
        commonCrmService.verifyCompanyBankAmount(verifyCompanyBankAmountDTO);
    }

    @Override
    public Integer getCompanyPayState(String merchantNo) {
        return commonCrmService.getCompanyPayState(merchantNo);
    }

    @Override
    public void pushFaceAuthNotifyMessage(JSONObject notifyMessage) {
        commonCrmService.pushFaceAuthNotifyMessage(notifyMessage);
    }

    @Override
    public void pushCompanyBankMoneyNotify(JSONObject notifyMessage) {
        commonCrmService.pushCompanyBankMoneyNotify(notifyMessage);
    }

    @Override
    public void reCompanyAuth(String merchantNo) {
        commonCrmService.reCompanyAuth(merchantNo);
    }


    @Override
    public void pushContractNotify(JSONObject jsonObject) {
        commonCrmService.pushContractNotify(jsonObject);
    }

    @Override
    public void submitCustomerAttach(CardAttachDTO cardAttachDTO) {
        if (StringUtils.isNotBlank(cardAttachDTO.getRegisterNumber())) {
            // 校验注册号码是否唯一
            ValidateCustomerUniqueDTO validateCustomerUniqueDTO = new ValidateCustomerUniqueDTO();
            validateCustomerUniqueDTO.setCustomerType(CustomerTypeEnum.ENTERPRISE.value());
            validateCustomerUniqueDTO.setUserCode(cardAttachDTO.getUserCode());
            CustomerAuthVo customerAuthVo = getCustomerInfoByUserCode(cardAttachDTO.getUserCode());
            validateCustomerUniqueDTO.setCompanyName(customerAuthVo.getCompanyName());
            validateCustomerUniqueDTO.setRegisterNumber(cardAttachDTO.getRegisterNumber());
            validateCustomerUnique(validateCustomerUniqueDTO);
        }
        commonCrmService.submitCustomerAttach(cardAttachDTO);
    }

    @Override
    public void validateCustomerUnique(ValidateCustomerUniqueDTO validateCustomerUniqueDTO) {
        commonCrmService.validateCustomerUnique(validateCustomerUniqueDTO);
    }

    @Override
    public String getCustomerName(String merchantNo) {
        CustomerAuthVo customerAuthInfo = getCustomerAuthInfo(merchantNo);
        Assert.isTrue(Objects.nonNull(customerAuthInfo), "网络异常,未查询到客户信息");
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthInfo.getCustomerType())) {
            return customerAuthInfo.getPersonName();
        }
        return customerAuthInfo.getCompanyName();
    }

    @Override
    public Integer checkCustomerRealAuth(String no) {
        JSONObject data = commonCrmService.getCustomerAuthInfo(no);
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        if (Objects.isNull(customer)) {
            throw new BusinessException(ResponseCode.PORTAL_415);
        }
        Integer auditType = customer.getAuditType();
        if (auditType == 1) {
            throw new BusinessException(ResponseCode.PORTAL_7007);
        }
        // 判断是否支持e签宝认证
        Integer customerType = customer.getCustomerType();
        Integer customerArea = customer.getCustomerArea();
        Integer idCardType = customer.getIdCardType();
        Integer auditStatus = customer.getAuditStatus();
        Integer applyType = customer.getApplyType();
        // 如果数据有为空的
        if (Objects.isNull(customerType) || Objects.isNull(customerArea) || Objects.isNull(idCardType) || Objects.isNull(auditStatus) || Objects.isNull(applyType)) {
            throw new BusinessException(ResponseCode.PORTAL_415);
        }
        // 如果是在审核中的话
        if (CustomerStatusEnum.WAIT_AUDIT.status().equals(auditStatus)) {
            throw new BusinessException(ResponseCode.PORTAL_7009);
        }
        // 补齐证件照,审核失败,弹出补齐证件照弹框
        if (ApplyTypeEnum.CHANGE_ID_CARD_ATTACH.getValue().equals(applyType) && CustomerStatusEnum.AUDIT_FAIL.status().equals(auditStatus)) {
            return 2;
        }
        if (!ApplyTypeEnum.APPLY_SUCCESS.getValue().equals(applyType) || !CustomerStatusEnum.AUDIT_SUCCESS.status().equals(auditStatus)) {
            throw new BusinessException(ResponseCode.PORTAL_7010);
        }

        // 如果为0,不需要发起认证
        int res = 0;
        // 如果是个人
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
            // 个人存在认证信息
            PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
            if (Objects.nonNull(personAuthVo)) {
                return res;
            }
            // 证件类型是港澳台身份证,不支持认证,到时候找客服
            if (CertificateTypeEnum.AO_MEN_ID_CARD.getCode().equals(idCardType) || CertificateTypeEnum.HONG_KONG_ID_CARD.getCode().equals(idCardType) || CertificateTypeEnum.TAI_WAN_ID_CARD.getCode().equals(idCardType)) {
                return res;
            }
        } else {
            // 如果是企业
            CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
            if (Objects.nonNull(companyAuthVo)) {
                return res;
            }
            // 非大陆客户,不支持认证,到时候找客服
            if (!CustomerAreaEnum.MAIN_LAND.getCode().equals(customerArea)) {
                return res;
            }
        }
        return 1;
    }

    @Override
    public void completeData(String userCode, String userName) {
        commonCrmService.completeInfo(userCode, userName, 0);
    }

    @Override
    public void changeCustomerName(ChangeCustomerNameDTO changeCustomerNameDTO) {
        // 校验注册号码是否唯一
        ValidateCustomerUniqueDTO validateCustomerUniqueDTO = new ValidateCustomerUniqueDTO();
        validateCustomerUniqueDTO.setUserCode(changeCustomerNameDTO.getUserCode());
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(changeCustomerNameDTO.getCustomerTypeNew())) {
            validateCustomerUniqueDTO.setPersonCard(changeCustomerNameDTO.getCardNumber());
            validateCustomerUniqueDTO.setCustomerType(CustomerTypeEnum.INDIVIDUAL.value());
        } else {
            validateCustomerUniqueDTO.setRegisterNumber(changeCustomerNameDTO.getCardNumber());
            validateCustomerUniqueDTO.setCustomerType(CustomerTypeEnum.ENTERPRISE.value());
            validateCustomerUniqueDTO.setCompanyName(changeCustomerNameDTO.getMerchantNameNew());
        }
        validateCustomerUnique(validateCustomerUniqueDTO);
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(changeCustomerNameDTO.getCustomerTypeNew()) && CustomerAreaEnum.MAIN_LAND.getCode().equals(changeCustomerNameDTO.getCustomerAreaNew())) {
            PersonalCertificateDTO personalCertificateDTO = new PersonalCertificateDTO();
            personalCertificateDTO.setName(changeCustomerNameDTO.getMerchantNameNew());
            personalCertificateDTO.setIdNo(changeCustomerNameDTO.getCardNumber());
            VerificationJsonBody verificationJsonBody = verificationService.personalCertificate(personalCertificateDTO);
            if (Boolean.FALSE.toString().equals(verificationJsonBody.getResult())) {
                throw new BusinessException("姓名与证件号不匹配，请检查！");
            }
        }
        commonCrmService.changeCustomerName(changeCustomerNameDTO);
    }

    @Override
    public ChangeCustomerNameVo getCustomerChangeInfo(String userCode) {
        CustomerFlowVo customerFlowVo = commonCrmService.getCustomerChangeInfo(userCode);
        if (Objects.isNull(customerFlowVo)) {
            throw new BusinessException(ResponseCode.PORTAL_7008);
        }
        Integer applyType = customerFlowVo.getApplyType();
        Integer auditStatus = customerFlowVo.getAuditStatus();
        Integer changeState = customerFlowVo.getChangeStatus();
        ChangeCustomerNameVo changeCustomerNameVo = new ChangeCustomerNameVo();
        Integer changeStatus = ChangeCustomerEnum.NOT_ALLOW_CHANGE.getValue();
        if (ApplyTypeEnum.APPLY_SUCCESS.getValue().equals(applyType) && CustomerStatusEnum.AUDIT_SUCCESS.status().equals(auditStatus)) {
            changeStatus = ChangeCustomerEnum.ALLOW_CHANGE.getValue();
        }
        if (ApplyTypeEnum.AUTONOMY_CHANGE.getValue().equals(applyType)) {
            changeCustomerNameVo.setCustomerTypeNew(customerFlowVo.getCustomerTypeNew());
            changeCustomerNameVo.setCustomerAreaNew(customerFlowVo.getCustomerAreaNew());
            changeCustomerNameVo.setMerchantNameNew(customerFlowVo.getMerchantNameNew());
            changeCustomerNameVo.setRelationShip(customerFlowVo.getRelationShip());
            changeCustomerNameVo.setTransferAttach(fileService.appendSignature(customerFlowVo.getTransferAttach()));
            changeCustomerNameVo.setTransferReason(customerFlowVo.getTransferReason());
            if (changeState == 2) {
                changeStatus = ChangeCustomerEnum.AUDITING.getValue();
            }
            if (changeState == 4) {
                changeStatus = ChangeCustomerEnum.AUDIT_FAIL.getValue();
                changeCustomerNameVo.setRefuseReason(customerFlowVo.getRefuseReason());
            }
        }
        changeCustomerNameVo.setChangeStatus(changeStatus);
        return changeCustomerNameVo;
    }

    @Override
    public CustomerAuthStateVo getAuthState(String userCode) {
        CustomerAuthStateVo customerAuthStateVo = new CustomerAuthStateVo();
        JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        // 新客户
        if (customer == null) {
            customerAuthStateVo.setAuthState(CustomerAuthStateEnum.NEW.getValue());
            return customerAuthStateVo;
        }
        Integer applyType = customer.getApplyType();
        ApplyTypeEnum customerApplyTyeEnum = ApplyTypeEnum.getCustomerApplyTyeEnum(applyType);
        // 如果是待审核状态或者审核完成状态
        if (CustomerStatusEnum.AUDIT_SUCCESS.status().equals(customer.getAuditStatus()) || CustomerStatusEnum.WAIT_AUDIT.status().equals(customer.getAuditStatus())) {
            customerAuthStateVo.setAuthState(CustomerAuthStateEnum.SUCCESS.getValue());
            return customerAuthStateVo;
        }
        String desc = CustomerChangeType.getDescByCode(customer.getChangeType());
        customerAuthStateVo.setChangTypeName(desc);
        switch (customerApplyTyeEnum) {
            case NEW:
                customerAuthStateVo.setAuthState(CustomerAuthStateEnum.NEW.getValue());
                return customerAuthStateVo;
            case APPLY_SUCCESS:
                customerAuthStateVo.setAuthState(CustomerAuthStateEnum.SUCCESS.getValue());
                return customerAuthStateVo;
            case CHANGE_ID_CARD_ATTACH:
                customerAuthStateVo.setAuthState(CustomerAuthStateEnum.COMPLETE_ATTACH.getValue());
                customerAuthStateVo.setCompleteAttachMessage(customer.getRefuseReason());
                return customerAuthStateVo;
            case AUTONOMY_CHANGE:
            case CHANGE_CUSTOMER_NAME:
                LocalDateTime unLimitTime = customer.getUnLimitTime();
                if (unLimitTime != null && LocalDateTime.now().isBefore(unLimitTime)) {
                    customerAuthStateVo.setAuthState(CustomerAuthStateEnum.COMPLETE_UN_LIMIT.getValue());
                    customerAuthStateVo.setUnLimitTime(unLimitTime.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")));
                } else {
                    customerAuthStateVo.setAuthState(CustomerAuthStateEnum.COMPLETE_DATA.getValue());
                }
                return customerAuthStateVo;
            default:
                throw new BusinessException(ResponseCode.PORTAL_415);
        }
    }

    @Override
    public Integer completeAttach(String userCode, String loginName) {
        return commonCrmService.completeInfo(userCode, loginName, 1);
    }

    @Override
    public boolean checkRealAuth(String userCode) {
        JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
        Assert.isTrue(Objects.nonNull(data), "网络异常,未查询到客户信息");
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        Integer customerType = customer.getCustomerType();
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
            PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
            if (personAuthVo == null) {
                return false;
            }
            Integer authResult = personAuthVo.getAuthResult();
            if (Integer.valueOf(1).equals(authResult)) {
                return true;
            }
        } else {
            CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
            if (companyAuthVo == null) {
                return false;
            }
            Integer verifyStatus = companyAuthVo.getAuthStatus();
            if (Integer.valueOf(3).equals(verifyStatus)) {
                return true;
            }
        }
        return false;
    }
}
