package com.ywwl.customer.center.modules.business.annotation.entrance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

import static com.ywwl.customer.center.modules.ejf.util.EJFUtil.cmccService;

/**
 * 揽收仓导入校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class CompanyImportValidator implements ConstraintValidator<CompanyImportVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<WareHouseVo> warehouses = cmccService.getWarehouses(StringUtils.strip(value), WareHouseVo::getCode, WareHouseVo::getName);
        // 导入校验
        if (CollectionUtil.isEmpty(warehouses)) {
            return false;
        }
        if (warehouses.size() > 1) {
            setError(StrUtil.format("揽收仓有多个匹配项 {}", warehouses), constraintValidatorContext);
            return false;
        }
        // 获取揽收仓类型
        return true;
    }

    /**
     * 设置错误信息
     *
     * @param msg 错误信息
     */
    private void setError(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg)
                .addConstraintViolation();
    }

}
