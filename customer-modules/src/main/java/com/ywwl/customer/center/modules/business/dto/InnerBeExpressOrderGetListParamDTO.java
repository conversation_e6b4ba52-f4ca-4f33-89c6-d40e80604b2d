package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运单详情-批量参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:41
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class InnerBeExpressOrderGetListParamDTO {
    /**
     * 制单账号
     */
    private String userId;
    /**
     * 运单号
     */
    private List<String> listNumber;

}
