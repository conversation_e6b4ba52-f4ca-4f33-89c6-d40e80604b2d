package com.ywwl.customer.center.modules.common.auth.vo;

import lombok.Data;

/**
 * @author: dinghy
 * @date: 2023/3/3 14:04
 * <p>
 *     个人客户认证或企业代办人认证信息
 * </p>
 */
@Data
public class PersonAuthVo {
    /**
     * 姓名
     */
    private String name;

    /**
     * 个人或企业代办人身份证类型：0是身份证,1是香港来往大陆通行证,2是澳门通行证,3是台湾通行证,4是护照
     */
    private Integer certificateType;

    /**
     * 证件编号
     */
    private String idNumber;


    /**
     * 认证类型:1是人脸识别,2是银行卡
     */
    private Integer authType;

    /**
     * 0是发起校验流程未认证,1是成功,2是失败
     */
    private Integer authResult;

    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * 银行预留手机号
     */
    private String mobile;

    private String bankCode;

}
