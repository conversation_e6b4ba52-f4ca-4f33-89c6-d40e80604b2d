// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.dto;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 11:00
 * @ModifyDate 2023/3/14 11:00
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
public class AccountGetReqDTO {
    /**
     * 用户代码
     * 制单账号为空必填
     */
    private String userCode;
    /**
     * 制单类型
     * 制单账号为空必填
     */
    private Integer accountType;
    /**
     * 制单账号
     * 用户代码为空、制单类型为空必填
     */
    private String accountCode;
    /**
     * 使用场景
     */
    private Integer scene;
    /**
     * 获取所有的业务类型
     */
    private List<Integer> business;
}
