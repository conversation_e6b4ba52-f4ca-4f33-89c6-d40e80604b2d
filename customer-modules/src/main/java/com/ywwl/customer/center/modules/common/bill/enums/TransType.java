package com.ywwl.customer.center.modules.common.bill.enums;

/**
 * 账单类型汇总
 * 增加TC15类型账单
 * 路鹏飞
 * 2019年12月14日
 */
public enum TransType {
	TC01("TC01", "应收快件"),
	BTC01("BTC01", "应收快件"),
	CTC01("CTC01", "应收快件调整"),
	TC02("TC02", "应收退件"),
	BTC02("BTC02", "应收退件"),
	CTC02("CTC02", "应收退件调整"),
	TC03("TC03", "应收赔偿"),
	BTC03("BTC03", "应收赔偿"),
	CTC03("CTC03", "应收赔偿调整"),
	TC04("TC04", "二次费用"),
	BTC04("BTC04", "二次费用"),
	CTC04("CTC04", "二次费用调整"),
	TC05("TC05", "应收返点"),
	BTC05("BTC05", "应收返点"),
	TC06("TC06", "支付宝或者银行收款"),
	CTC05("CTC05", "应收返点调整"),
	TC08("TC08", "应收国内快递退件"),
	BTC08("BTC08", "应收国内快递退件"),
	CTC08("CTC08", "应收国内快递退件调整"),
	TC15("TC15", "应收优惠"),
	BTC15("BTC15", "应收优惠"),
	CTC15("CTC15", "应收优惠调整"),
	BTC35("BTC35", "应收税费"),
	CTC35("CTC35", "应收税费调整"),
	BTC39("BTC39", "应收重派"),
	TC38("TC38", "首公里揽收"),
	BTC38("BTC38", "首公里揽收"),
	TC61("TC61", "支付宝线上充值"),
	;
	private String value;
	private String desc;

	TransType(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	public static String getDesc(String value) {
		for (TransType transType : TransType.values()) {
			if (transType.value.equals(value)) {
				return transType.desc;
			}
		}
		throw new RuntimeException(value+"不支持类型");
	}

	public String getValue() {
		return value;
	}

	public String getDesc() {
		return desc;
	}
}