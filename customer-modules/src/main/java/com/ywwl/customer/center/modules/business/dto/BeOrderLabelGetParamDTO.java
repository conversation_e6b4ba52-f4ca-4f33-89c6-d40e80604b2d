package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 打印标签参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:23
 **/
@NoArgsConstructor
@Data
public class BeOrderLabelGetParamDTO {

    /**
     * 制单账号
     */
    @NotBlank(message = "制单账号不能为空")
    private String userId;
    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String waybillNumber;
    /**
     * 是否打印拣货单
     */
    private Integer printRemark;

}
