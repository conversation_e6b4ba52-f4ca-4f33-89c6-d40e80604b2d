package com.ywwl.customer.center.modules.business.annotation.entrance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.plm.dto.ProductResultDTO;
import com.ywwl.customer.center.modules.general.plm.enums.PLMPlatformEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class ProductImportValidator implements ConstraintValidator<ProductImportVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<ProductResultDTO.DataDTO> products = EJFUtil.plmService.getProducts(
                PLMPlatformEnum.EJF,
                StringUtils.strip(value),
                ProductResultDTO.DataDTO::getProductNumber,
                ProductResultDTO.DataDTO::getProductCnName);
        // 导入校验
        if (CollectionUtil.isEmpty(products)) {
            return false;
        }
        if (products.size() > 1) {
            setError(StrUtil.format("国家有多个匹配项 {}", products), constraintValidatorContext);
            return false;
        }
        return true;
    }

    /**
     * 设置错误信息
     *
     * @param msg 错误信息
     */
    private void setError(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg)
                .addConstraintViolation();
    }

}
