package com.ywwl.customer.center.modules.business.enums;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 产品类型枚举
 *
 * <AUTHOR>
 * @since 2023/10/23 16:33
 **/
public enum ProductTypeEnum {

    COLLECTION_AGENT("27", "代揽收", 91),
    CHINA_POST_PRODUCTS("18", "中邮产品", 92),
    CHINA_POST_ECONOMICS("25", "中邮经济", 93),
    INTERNATIONAL_FREIGHT_FORWARDER_BIG_PACKAGE("24", "国际货代大包", 94),
    OVERSEAS_SCHOOL("12", "海外派", 95),
    INTERNATIONAL_FREIGHT_FORWARDER("11", "国际货代", 96),
    INTERNATIONAL_AIR_FORCE("20", "国际空派", 97),
    INTERNATIONAL_SCHOOL("21", "国际海派", 98),
    INTERNATIONAL_CARD_PARTY("22", "国际卡派", 99),
    INTERNATIONAL_IRON_SCHOOL("23", "国际铁派", 89),
    FIRST_KILOMETER("9", "首公里", 88),
    WAREHOUSE_ALLOCATION("10", "集运仓配", 87),
    YAN_WEN_PROFESSIONAL_TRANSPORT_LINES("3", "燕文专线", 2),
    YAN_WEN_EXTERNAL_WAYBILL_NUMBER("2", "燕文挂号", 3),
    YAN_WEN_ECONOMY("1", "燕文经济", 86),
    REGISTERED_CHINESE_MAIL("26", "中邮挂号", 85),
    OUTWARD_MAIL_PRODUCTS("19", "外邮产品", 84),
    COMMERCIAL_EXPRESS_DELIVERY("17", "商业快递", 4),
    RECOMMEND_PRODUCTS("-1", "推荐产品", 1),
    OTHER("999", "其他产品", 83),
    ;
    /**
     * 产品类型CODE
     */
    private final String code;
    /**
     * 产品类型名称
     */
    private final String name;
    /**
     * 排序
     */
    private final Integer sort;

    /**
     * 产品类型枚举
     *
     * @param code 产品类型CODE
     * @param name 产品类型名称
     */
    ProductTypeEnum(String code, String name, Integer sort) {
        this.code = code;
        this.name = name;
        this.sort = sort;
    }

    /**
     * 产品类型CODE
     *
     * @return String
     */
    public String getCode() {
        return code;
    }

    /**
     * 排序
     *
     * @return Integer
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * 产品类型名称
     *
     * @return String
     */
    @JsonValue
    public String getName() {
        return name;
    }

}
