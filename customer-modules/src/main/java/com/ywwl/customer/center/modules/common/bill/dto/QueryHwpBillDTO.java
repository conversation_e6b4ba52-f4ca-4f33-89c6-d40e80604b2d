package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;

import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/27 17:23
 */
@Data
public class QueryHwpBillDTO {
    private String opCode = BillOpCodeEnum.SEND_WING_WITHOUT_LIT.value();
    private String userCode;
    private String merchantCode;
    private Integer pageSize = 10;
    private Integer currentPage = 1;
    /**
     * 运单号
     */
    private List<String> waybillNumbers;

    public void toExport() {
        setOpCode(BillOpCodeEnum.EXPORT_SEND_WING_BILL.value());
    }

    public String build() {
        StringBuffer sb = new StringBuffer();
        sb.append("?")
                .append("merchantCode=").append(merchantCode).append("&")
                .append("opCode=").append(opCode);
        return sb.toString();
    }

}
