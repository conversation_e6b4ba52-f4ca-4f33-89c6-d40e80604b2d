package com.ywwl.customer.center.modules.amazon.result;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 结果类
 * <AUTHOR>
 * @date 2022/09/05 15:57
 **/
@NoArgsConstructor
@Data
public class QueryPlatformAccountResult {

	private Boolean result;
	private String code;
	private String message;
	private List<DataDTO> data;

	@NoArgsConstructor
	@Data
	public static class DataDTO {
		/**
		 * 平台账号
		 */
		private String plamCode;
		/**
		 * 平台类型
		 */
		private String platformId;
		/**
		 * 发货账号
		 */
		private String accountCode;

		@JSONField(name = "platformCode")
		public void setPlamCode(String plamCode) {
			this.plamCode = plamCode;
		}
	}

}
