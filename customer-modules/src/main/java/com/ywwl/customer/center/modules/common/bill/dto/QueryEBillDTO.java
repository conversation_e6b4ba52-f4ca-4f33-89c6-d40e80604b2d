package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;

import java.time.LocalDate;

@Data
public class QueryEBillDTO {
    private Integer businessType;
    private String opCode= BillOpCodeEnum.E_BILL.value();
    private String userCode;
    /**
     * <AUTHOR>
     * @description 业务账号
     * @date 2023/4/19 15:56
     **/
    private String merchantCode;
    /**
     * <AUTHOR>
     * @description 开始时间
     * @date 2023/4/19 15:56
     **/
    private String startTime;
    /**
     * <AUTHOR>
     * @description 结束时间
     * @date 2023/4/19 15:56
     **/
    private String endTime;
    private Integer currentPage;
    private Integer pageSize;

    public static final String code="30038494";
//    public static final String code="10003791";
    public static final LocalDate date=LocalDate.parse("2024-05-20");
    // JIRA-55124 客户中心-30038494历史账单屏蔽
    public void handleSpecialCustomer(){
      if(code.equals(merchantCode)){
          if(LocalDate.parse(startTime).isBefore(date)){
               startTime=date.toString();
          }
      }
    }
}
