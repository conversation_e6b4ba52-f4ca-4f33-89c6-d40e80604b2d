package com.ywwl.customer.center.modules.amazon.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询平台订单数量统计
 *
 * @author: dinghy
 * @date: 2022/9/6 10:30
 */
@NoArgsConstructor
@Data
public class QueryOrderCountResultDTO {

    /**
     * 成功标志
     */
    @JsonProperty("Success")
    private Boolean success;
    /**
     * 数据
     */
    @JsonProperty("Data")
    private List<DataDTO> data;

    /**
     * 数据Item
     */
    @NoArgsConstructor
    public static class DataDTO {
        /**
         * 数量
         */
        private Integer count;
        /**
         * 子运输状态
         */
        private String subStatus;

        @JsonProperty("count")
        public Integer getCount() {
            return count;
        }

        @JsonProperty("Count")
        public void setCount(Integer count) {
            this.count = count;
        }

        @JsonProperty("subStatus")
        public String getSubStatus() {
            return subStatus;
        }

        @JsonProperty("SubStatus")
        public void setSubStatus(String subStatus) {
            this.subStatus = subStatus;
        }
    }

}
