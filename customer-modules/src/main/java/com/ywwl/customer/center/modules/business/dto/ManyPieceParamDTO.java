package com.ywwl.customer.center.modules.business.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.ywwl.customer.center.modules.business.annotation.CountryVerify;
import com.ywwl.customer.center.modules.business.annotation.ProductVerify;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商业快递费用试算参数
 *
 * <AUTHOR>
 * @since 2023/10/12 09:59
 **/
@NoArgsConstructor
@Data
public class ManyPieceParamDTO {
    /**
     * 计费类型(1.应收2.应付)
     */
    private Integer calcType = 1;
    /**
     * 产品号（应收必填）
     */
    @NotBlank(message = "产品号不能为空")
    @ProductVerify(message = "产品不存在", groups = Input.class)
    private String productCode;
    /**
     * 揽收仓
     */
    @JsonAlias("companyCode")
    private String companyCodeFrom;
    /**
     * 发货账号
     */
    @JsonAlias("userId")
    @NotBlank(message = "制单账号不能为空")
    private String customerCode;
    /**
     * 目的国id
     */
    @NotBlank(message = "目的国家不能为空")
    @CountryVerify(message = "目的国家不存在", groups = Input.class)
    private String countryId;
    /**
     * 计费时间
     */
    private String chargingTime;
    /**
     * 目的国城市
     */
    private String city;
    /**
     * 目的国邮编
     */
    private String postCode;
    /**
     * 申报价值
     */
    private BigDecimal declaredValue;
    /**
     * 申报价值币种id
     */
    private Integer declaredCurrencyId;
    /**
     * 地址
     */
    private String address;
    /**
     * 品名数量
     */
    private Integer specialNameCount;
    /**
     * 小件参数
     */
    @NotNull(message = "至少填写一件包裹信息")
    @Size(min = 1, message = "至少填写一件包裹信息")
    private List<ItemsDTO> items;

    /**
     * 小件参数
     */
    @NoArgsConstructor
    @Data
    public static class ItemsDTO {
        /**
         * 长（cm 单件）
         */
        @NotNull(message = "长不能为空")
        private BigDecimal length;
        /**
         * 宽（cm 单件）
         */
        @NotNull(message = "宽不能为空")
        private BigDecimal width;
        /**
         * 高（cm 单件）
         */
        @JsonAlias("height")
        @NotNull(message = "高不能为空")
        private BigDecimal high;
        /**
         * 计费实重(单件)
         */
        @NotNull(message = "实重不能为空")
        private BigDecimal weight;
        /**
         * 重量单位（g、kg、oz、lb、ct）
         */
        private String unit;
    }
}
