package com.ywwl.customer.center.modules.business.annotation;

import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class CountryValidator implements ConstraintValidator<CountryVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<CountryResultDTO.RegionListDTO> country = EJFUtil.plmService.getCountry(value);
        // 其他校验，仅Code校验
        return country.stream()
                .anyMatch(v -> Objects.equals(v.getId(), value));
    }

}
