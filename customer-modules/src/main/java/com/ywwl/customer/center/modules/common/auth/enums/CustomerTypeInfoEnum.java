// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.auth.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/29 15:24
 * @ModifyDate 2023/3/29 15:24
 * @Version 1.0
 */
@Getter
public enum CustomerTypeInfoEnum {
    CHINA_LAND_PERSON("00", "中国大陆个人客户"),
    CHINA_LAND_COMPANY("01", "中国大陆企业客户"),
    CHINA_GAT_PERSON("10", "中国港澳台个人客户"),
    CHINA_GAT_COMPANY("11", "中国港澳台企业客户"),
    OVERSEAS_PERSON("20", "海外个人客户"),
    OVERSEAS_COMPANY("21", "海外企业客户");


    private String code;
    private String desc;

    CustomerTypeInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getDescValue(String code) {
        for (CustomerTypeInfoEnum info : CustomerTypeInfoEnum.values()) {
            if (info.getCode().equals(code)) {
                return info.getDesc();
            }
        }

        return null;
    }
}
