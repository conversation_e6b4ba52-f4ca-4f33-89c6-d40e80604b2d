package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/18 17:50
 */
@Data
public class QueryBillWaybillDTO {
    // 制单账号,海外派客户这个是商户号
    private String customerCode;
    @JSONField(serialize = false)
    private String userCode;
    // 直发业务账号
    private String merchantCode;
    /**
     * <AUTHOR>
     * @description 开始时间
     * @date 2023/4/18 17:53
     **/
    @NotBlank(message = "开始时间不能为空")
    private String dateOfBillBegin;
    /**
     * <AUTHOR>
     * @description 结束时间
     * @date 2023/4/18 17:53
     **/
    @NotBlank(message = "结束时间不能为空")
    private String dateOfBillEnd;
    /**
     * <AUTHOR>
     * @description 运单列表
     * @date 2023/4/18 17:53
     **/
    private List<String> waybillNumbers;
    /**
     * <AUTHOR>
     * @description 账单类型
     * @date 2023/4/18 17:53
     **/
    private String transType;
    /**
     * <AUTHOR>
     * @description 业务类型
     * @date 2023/6/15 16:03
     **/
    private Integer businessType;

    private Integer page = 1;
    private Integer size = 10;
    public String build() {
        StringBuffer sb = new StringBuffer();
        sb.append("merchantCode=").append(merchantCode).append("&")
                .append("dateOfBillBegin=").append(dateOfBillBegin).append("&")
                .append("dateOfBillEnd=").append(dateOfBillEnd);
        if (!CollectionUtils.isEmpty(waybillNumbers)){
            for (String waybillNumber : waybillNumbers) {
                sb.append("&").append("waybillNumbers=").append(waybillNumber);
            }
        }
        if(StringUtils.isNotBlank(customerCode)){
            sb.append("&customerCode=").append(customerCode);
        }
        return sb.toString();
    }

}
