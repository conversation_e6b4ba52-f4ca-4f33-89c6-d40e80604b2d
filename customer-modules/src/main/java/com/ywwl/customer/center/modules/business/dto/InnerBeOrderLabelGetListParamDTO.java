package com.ywwl.customer.center.modules.business.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打印标签-批量参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:47
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@ToString
public class InnerBeOrderLabelGetListParamDTO {

    /**
     * 运单号
     */
    @Size(min = 1, message = "运单号不能为空")
    private List<String> waybillNumbers;
    /**
     * 运单号
     */
    private String waybillNumber;
    /**
     * 制单账号
     */
    @NotBlank(message = "制单账号不能为空")
    private String userId;
    /**
     * 是否打印拣货单
     */
    private Integer printRemark;

    /**
     * 转换List
     *
     * @return 转换结果
     */
    @JsonIgnore
    @JSONField(serialize = false)
    public List<InnerBeOrderLabelGetListParamDTO> toList() {
        if (CollectionUtil.isNotEmpty(getWaybillNumbers())) {
            return getWaybillNumbers().stream()
                    .map(v -> InnerBeOrderLabelGetListParamDTO.builder()
                            .waybillNumber(v)
                            .printRemark(getPrintRemark())
                            .userId(getUserId())
                            .build()
                    )
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
