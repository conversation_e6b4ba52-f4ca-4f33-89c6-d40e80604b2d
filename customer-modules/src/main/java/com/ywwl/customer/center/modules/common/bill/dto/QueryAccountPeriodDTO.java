package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;

/**
 * @author: dinghy
 * @date: 2023/4/19 14:33
 */
@Data
public class QueryAccountPeriodDTO {
    private Integer businessType;
    private String userCode;
    private String opCode = BillOpCodeEnum.ACCOUNT_PERIOD.value();
    /**
     * <AUTHOR>
     * @description 业务账号
     * @date 2023/4/19 15:56
     **/
    private String merchantCode;
    /**
     * <AUTHOR>
     * @description 开始时间
     * @date 2023/4/19 15:56
     **/
    private String startRefDate;
    /**
     * <AUTHOR>
     * @description 结束时间
     * @date 2023/4/19 15:56
     **/
    private String endRefDate;
    /**
     * <AUTHOR>
     * @description 账期状态:1全部核销,2部分核销,3全部
     * @date 2023/4/19 15:57
     **/
    private String chkStatus;
}
