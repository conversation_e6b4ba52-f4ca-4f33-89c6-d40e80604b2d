package com.ywwl.customer.center.modules.common.auth.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/3/17 13:40
 */
@Data
public class QueryContractDTO {
    private String userCode;
    private String no;
    @NotNull(message = "[合同类型]不能为空")
    private Integer contractType;
    /**
     * <AUTHOR>
     * @description 管理员手机号
     * @date 2023/3/21 16:52
     **/
    private String adminPhone;
    /**
     * <AUTHOR>
     * @description 其他的数据
     * @date 2023/5/10 14:22
     **/
    private String extraData;

    private Boolean viewContract=false;

}
