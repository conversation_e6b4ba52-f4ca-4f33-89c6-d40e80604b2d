package com.ywwl.customer.center.modules.amazon.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2022/9/6 10:30
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryOrderParam {

    /**
     * userId
     */
    String userId;

    /**
     * 平台账号
     */
    @NotEmpty(message = "平台账号不能为空")
    private List<String> platfromCustomCodes;
    /**
     * 页码
     */
    private Integer pageIndex;
    /**
     * 页大小
     */
    private Integer pageSize;
    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 运输子状态
     */
    private Collection<Integer> subStatus;
    /**
     * 运输状态
     */
    private String status;
    /**
     * 目的地
     */
    private String destinationCountry;
    /**
     * 运单号或订单号
     */
    private List<String> numbers;
    /**
     * 收件人名称
     */
    private String receiverName;
    /**
     * 打印状态：0 未打印,1 已打印（为空时查询全部状态）
     */
    private Integer printStatus;
    /**
     * 平台名称：AMAZON、DHGate
     */
    private String platformName;

    @JsonProperty("PlatformName")
    public String getPlatformName() {
        return platformName;
    }

    @JsonProperty("platformName")
    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    @JsonProperty("PrintStatus")
    public Integer getPrintStatus() {
        return printStatus;
    }

    @JsonProperty("printStatus")
    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    @JsonProperty("SubStatus")
    public Collection<Integer> getSubStatus() {
        return subStatus;
    }

    @JsonProperty("subStatus")
    public void setSubStatus(Collection<Integer> subStatus) {
        this.subStatus = subStatus;
    }

    @JsonIgnore
    public String getStatus() {
        return status;
    }
    @JsonProperty
    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("pageIndex")
    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    @JsonProperty("pageSize")
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @JsonProperty("beginTime")
    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    @JsonProperty("endTime")
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    @JsonProperty("destinationCountry")
    public void setDestinationCountry(String destinationCountry) {
        this.destinationCountry = destinationCountry;
    }

    @JsonProperty("numbers")
    public void setNumbers(List<String> numbers) {
        this.numbers = numbers;
    }

    @JsonProperty("receiverName")
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    @JsonProperty("platfromCustomCodes")
    public void setPlatfromCustomCodes(List<String> platfromCustomCodes) {
        this.platfromCustomCodes = platfromCustomCodes;
    }

    @JsonProperty("PlatfromCustomCodes")
    public List<String> getPlatfromCustomCodes() {
        return platfromCustomCodes;
    }

    @JsonProperty("platformCustomCodes")
    public void setPlatformCustomCodes(List<String> platformCustomCodes) {
        this.platfromCustomCodes = platformCustomCodes;
    }

    @JsonProperty("PageIndex")
    public Integer getPageIndex() {
        return pageIndex;
    }

    @JsonProperty("PageSize")
    public Integer getPageSize() {
        return pageSize;
    }

    @JsonProperty("BeginTime")
    public String getBeginTime() {
        if (!StringUtils.isBlank(beginTime)) {
            return StringUtils.join(beginTime," 00:00:00");
        }
        return beginTime;
    }

    @JsonProperty("EndTime")
    public String getEndTime() {
        if (!StringUtils.isBlank(endTime)) {
            return StringUtils.join(endTime," 23:59:59");
        }
        return endTime;
    }

    @JsonProperty("DestinationCountry")
    public String getDestinationCountry() {
        return destinationCountry;
    }

    @JsonProperty("Numbers")
    public List<String> getNumbers() {
        return numbers;
    }

    @JsonProperty("ReceiverName")
    public String getReceiverName() {
        return receiverName;
    }
}
