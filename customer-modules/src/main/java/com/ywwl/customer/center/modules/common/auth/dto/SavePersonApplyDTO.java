package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @author: dinghy
 * @date: 2023/2/21 15:52
 */
@Data
public class SavePersonApplyDTO {
    private String no;
    /**
     * <AUTHOR>
     * @description 个人认证类型
     * @date 2023/2/21 16:25
     **/
    @NotNull(message = "[认证类型]不能为空")
    private Integer personalAuthType;
    /**
     * <AUTHOR>
     * @description 证件类型
     * @date 2023/2/21 16:25
     **/
    @NotNull(message = "[证件类型]不能为空")
    private Integer certificateType;
    @NotBlank(message = "[客户姓名]不能为空")
    private String name;
    @NotBlank(message = "[客户证件号]不能为空")
    private String idCard;
    /*** 证件正面照 */
    @NotBlank(message = "[证件正面照]不能为空")
    private String cardAttach;

    /*** 证件反面照 */
    @NotBlank(message = "[证件反面照]不能为空")
    private String cardBackAttach;

    /*** 证件手持照 */
    private String cardHoldAttach;
    // 证件有效期
    @NotNull(message = "[证件有效期]不能为空")
    private LocalDateTime certificateValidity;

    private String bankCard;
    /**
     * <AUTHOR>
     * @description 银行卡预留手机号
     * @date 2023/2/21 16:33
     **/
    private String reservedMobile;
    /**
     * <AUTHOR>
     * @description 管理员手机号
     * @date 2023/2/22 9:57
     **/
    private String adminMobile;

    private String userCode;
    /**
     * <AUTHOR>
     * @date 2023/10/12 10:41
     * @description 银行code
     */
    private String bankCode;

    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;

    /**
     * @author: dinghy
     * @createTime: 2024/2/28 9:34
     * @description: 境外客户付款账号证明
     */
    private String bankAccountProof;

    /**
     * @author: dinghy
     * @createTime: 2024/2/28 9:36
     * @description: 银行名称
     */
    private String bankName;

    // 通行证号码
    private String passCard;
    // 通行证件正面
    private String passAttach;
    // 通行证件反面
    private String passBackAttach;
    // 通行证件有效期
    private LocalDateTime passValidityPeriod;

    /*** 境外个人-本国身份证号码 */
    private String homeCard;

    /*** 境外个人-本国身份证正面照 */
    private String homeAttach;

    /*** 境外个人-本国身份证反面照 */
    private String homeBackAttach;

    /*** 境外个人-本国身份证有效期 */
    private LocalDateTime homeValidityPeriod;
}
