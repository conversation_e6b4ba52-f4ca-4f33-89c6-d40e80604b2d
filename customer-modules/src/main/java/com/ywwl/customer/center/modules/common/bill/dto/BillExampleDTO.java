package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.modules.common.bill.enums.TransType;
import com.ywwl.customer.center.modules.common.bill.enums.WaybillApiEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BillExampleDTO {
    private String code = WaybillApiEnum.CREATE_TEST_DATA.value();
    @JSONField(name = "WaybillNumber")
    private String waybillNumber = "";
    @JSONField(name = "CompanyCodeOfFrom")
    private String companyCodeOfFrom = "";
    @JSONField(name = "CompanyCodeOfTo")
    private String companyCodeOfTo = "";
    @JSONField(name = "CompanyCodeOfSettlement")
    private String companyCodeOfSettlement;
    @JSONField(name = "OutWarehouse")
    private String outWarehouse = "";
    @JSONField(name = "MerchantCode")
    private String merchantCode;
    @JSONField(name = "CustomerCode")
    private String customerCode;
    @J<PERSON>NField(name = "ProductCode")
    private String productCode;
    @JSONField(name = "ServiceCode")
    private String serviceCode = "";
    @JSONField(name = "Weight")
    private BigDecimal weight = BigDecimal.ZERO;
    @JSONField(name = "CalcWeight")
    private BigDecimal calcWeight = BigDecimal.ZERO;
    @JSONField(name = "DeclaredValue")
    private BigDecimal declaredValue = BigDecimal.ZERO;
    @JSONField(name = "DeclaredCurrencyName")
    private String declaredCurrencyName = "CNY";
    @JSONField(name = "ExpressLength")
    private BigDecimal expressLength = BigDecimal.ZERO;
    @JSONField(name = "ExpressWidth")
    private BigDecimal expressWidth = BigDecimal.ZERO;
    @JSONField(name = "ExpressHeight")
    private BigDecimal expressHeight = BigDecimal.ZERO;
    @JSONField(name = "CountryId")
    private String countryId;
    @JSONField(name = "TimeOfExpress")
    private LocalDateTime timeOfExpress;
    @JSONField(name = "TimeOfCreate")
    private LocalDateTime timeOfCreate;
    @JSONField(name = "TimeOfExpense")
    private LocalDateTime timeOfExpense;
    @JSONField(name = "TimeOfCalc")
    private LocalDateTime timeOfCalc;
    @JSONField(name = "ExpenseType")
    private String expenseType = TransType.TC01.getValue();
    @JSONField(name = "Money1OfOriginal")
    private BigDecimal money1OfOriginal = BigDecimal.ZERO;
    @JSONField(name = "Money1")
    private BigDecimal money1 = BigDecimal.ZERO;
    @JSONField(name = "Money2")
    private BigDecimal money2 = BigDecimal.ZERO;
    @JSONField(name = "Money3")
    private BigDecimal money3 = BigDecimal.ZERO;
    @JSONField(name = "MoneyTax")
    private BigDecimal moneyTax = BigDecimal.ZERO;
    @JSONField(name = "Currency")
    private String currency;
    @JSONField(name = "CostType")
    private String costType = "B";
    @JSONField(name = "RelationId")
    private String relationId;

    @JSONField(name = "YanwenNumber")
    private String yanwenNumber;
    @JSONField(name = "OrderNumber")
    private String orderNumber;
    @JSONField(name = "VersionCode")
    private Integer versionCode;
    @JSONField(name = "TimeOfOrder")
    private LocalDateTime timeOfOrder;
    @JSONField(name = "OrderStatus")
    private String orderStatus;
    @JSONField(name = "ExchangeNumber")
    private String exchangeNumber;
    private String userCode;
}
