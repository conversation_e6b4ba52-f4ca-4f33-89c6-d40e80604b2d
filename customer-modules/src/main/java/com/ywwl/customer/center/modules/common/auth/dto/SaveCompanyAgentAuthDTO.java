package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import com.ywwl.customer.center.modules.common.auth.enums.PersonalAuthTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/2/24 10:55
 */
@Data
public class SaveCompanyAgentAuthDTO {
    private String no;
    @NotNull(message = "[认证类型]不能为空")
    private Integer personalAuthType;
    @NotNull(message = "[证件类型]不能为空")
    private Integer certificateType;
    @NotBlank(message = "[客户姓名]不能为空")
    private String name;
    @NotBlank(message = "[客户证件号]不能为空")
    private String idCard;
    private String bankCard;
    /**
     * <AUTHOR>
     * @description 银行卡预留手机号
     * @date 2023/2/21 16:33
     **/
    private String reservedMobile;
    /**
     * <AUTHOR>
     * @description 管理员手机号
     * @date 2023/2/22 9:57
     **/
    private String adminMobile;

    private String userCode;

    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;

    public static void checkPersonAuthDTO(SaveCompanyAgentAuthDTO personAuthDTO) {
        // 如果是银行卡认证校验是否为空
        if (PersonalAuthTypeEnum.BANK.equals(personAuthDTO.getPersonalAuthType())) {
            if (StringUtils.isBlank(personAuthDTO.getBankCard())) {
                throw new BusinessException("银行卡不能为空");
            }
            if (StringUtils.isBlank(personAuthDTO.getReservedMobile())) {
                throw new BusinessException("银行卡预留手机号不能为空");
            }
        }
    }
}
