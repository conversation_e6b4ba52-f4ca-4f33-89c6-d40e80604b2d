package com.ywwl.customer.center.modules.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.modules.business.dto.*;
import com.ywwl.customer.center.modules.business.service.BusinessService;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.ywwl.customer.center.modules.business.util.BusinessUtil.*;

/**
 * 商业快递服务类
 *
 * <AUTHOR>
 * @since 2023/10/10 15:03
 **/
@Service
@Slf4j
public class BusinessServiceImpl implements BusinessService {

    @Value("${calculate-engine.business}")
    private String businessManyPieceUrl;

    @Value("${business.dictionary}")
    private String dictionary;

    /**
     * 账号服务
     */
    @Resource
    private AccountService accountService;

    /**
     * 商业快递列表查询
     *
     * @param param 参数
     * @return 商业快递列表
     */
    @Override
    public InnerBeOrderPortalGetListFilterDTO getBusiness(InnerBeOrderPortalGetListFilterParamDTO param) {
        return innerBeOrderPortalGetListFilter().customerCode(param.getUserId()).body(
                MapUtil.ofEntries(
                        entry("channelId", param.getChannelId()),
                        entry("countryId", param.getCountryId()),
                        entry("listStatus", param.getListStatus()),
                        entry("isPrint", param.getIsPrint()),
                        entry("listNumber", param.getListNumber()),
                        entry("receiverName", param.getReceiverName()),
                        entry("pageNum", param.getPageNum()),
                        entry("pageSize", param.getPageSize()),
                        entry("startTime", param.getStartTime()),
                        entry("endTime", param.getEndTime())
                )
        ).post();
    }

    /**
     * 获取产品列表
     *
     * @param warehouseCode 仓库号
     * @param countryId     国家id
     * @return 国家列表
     */
    @Override
    public BusinessChannelDTO getBaseChannel(String userId, String warehouseCode, String countryId) {
        return HttpUtil.clazz(BusinessChannelDTO.class)
                .url(dictionary)
                .header(
                        MapUtil.ofEntries(
                                entry("method", "product.country_warehouse.getlist")
                        )
                )
                .body(
                        MapUtil.ofEntries(
                                entry("customerCode", userId),
                                entry("warehouseNumber", warehouseCode),
                                entry("countryId", countryId)
                        )
                )
                .logRsp(false)
                .post();
    }

    /**
     * 创建运单
     *
     * @param param 参数
     * @return 运单号
     */
    @Override
    public BeOrderCreateDTO createBusinessOrder(BusinessOrderDTO param) {
        setTotalParam(param);
        return beOrderCreate().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 查询产品列表-根据燕文仓库编码、国家Id
     *
     * @param param 参数
     * @return 产品
     */
    @Override
    public InnerBeChannelGetListDTO getBaseChannel(InnerBeChannelGetListParamDTO param) {
        return innerBeChannelGetList()
                .customerCode(param.getUserId())
                .body(param).post();
    }

    /**
     * 取消运单
     *
     * @param param 参数
     * @return 取消运单结果
     */
    @Override
    public GeneralDTO cancelBusinessOrder(BeOrderCancelParamDTO param) {
        try {
            return beOrderCancel().customerCode(param.getUserId()).body(param).post();
        } catch (Throwable e) {
            log.error("取消运单失败 {}", param.getWaybillNumber(), e);
            return GeneralDTO.builder()
                    .success(false)
                    .code("500")
                    .message(e.getMessage())
                    .build();
        }
    }

    /**
     * 运单详细
     *
     * @param waybillNumber 参数
     * @return 运单详细结果
     */
    @Override
    public BeOrderGetDTO getBusinessOrderDetail(String userId, String waybillNumber) {
        return beOrderGet().customerCode(userId).body(MapUtil.ofEntries(entry("waybillNumber", waybillNumber))).post();
    }

    /**
     * 打印标签
     *
     * @param param 参数
     * @return 打印标签结果
     */
    @Override
    public BeOrderLabelGetDTO businessOrderPrintLabel(BeOrderLabelGetParamDTO param) {
        return beOrderLabelGet().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 运单信息修改
     *
     * @param param 参数
     * @return 运单信息修改结果
     */
    @Override
    public GeneralDTO editBusinessOrder(BusinessOrderDTO param) {
        setTotalParam(param);
        return innerBeOrderEdit().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 运单状态统计
     *
     * @param param 参数
     * @return 运单状态统计结果
     */
    @Override
    public InnerBeOrderStatusGetListFilterDTO getBusinessOrderStatistics(InnerBeOrderPortalGetListFilterParamDTO param) {
        return innerBeOrderStatusGetListFilter().customerCode(param.getUserId()).body(
                MapUtil.ofEntries(
                        entry("channelId", param.getChannelId()),
                        entry("countryId", param.getCountryId()),
                        entry("isPrint", param.getIsPrint()),
                        entry("listNumber", param.getListNumber()),
                        entry("receiverName", param.getReceiverName()),
                        entry("startTime", param.getStartTime()),
                        entry("endTime", param.getEndTime())
                )
        ).post();
    }

    /**
     * 确认发货
     *
     * @param waybillNumber 参数
     * @return 确认发货结果
     */
    @Override
    public GeneralDTO businessOrderConfirmDelivery(String userId, String waybillNumber) {
        return innerBeOrderAcknowledge().customerCode(userId).body(
                MapUtil.ofEntries(entry("waybillNumber", waybillNumber))
        ).post();
    }

    /**
     * 取消确认发货
     *
     * @param param 参数
     * @return 取消确认发货结果
     */
    @Override
    public GeneralDTO businessOrderCancelDelivery(InnerBeOrderAcknowledgeCancelParamDTO param) {
        return innerBeOrderAcknowledgeCancel().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 运单详细-批量
     *
     * @param param 参数
     * @return 批量结果
     */
    @Override
    public BeOrderGetListDTO getBusinessOrderDetailBatch(InnerBeExpressOrderGetListParamDTO param) {
        return innerBeOrderGetList().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 打印标签
     *
     * @param param 参数
     * @return 批量结果
     */
    @Override
    public InnerBeOrderLabelGetListDTO businessOrderPrintLabelBatch(InnerBeOrderLabelGetListParamDTO param) {
        try {
            return innerBeOrderLabelGetList().customerCode(param.getUserId()).body(param).post();
        } catch (Throwable e) {
            log.error("打印标签失败 {}", param, e);
            return InnerBeOrderLabelGetListDTO.vBuilder().success(false).message(e.getMessage()).code("500").build();
        }
    }

    /**
     * 查询运单取消记录
     *
     * @param userId         制单账号
     * @param waybillNumbers 运单号
     * @return 查询运单取消记录结果
     */
    @Override
    public InnerBeOrderCancelRecordGetListDTO cancelBusinessOrderInfo(String userId, List<String> waybillNumbers) {
        return innerBeOrderCancelRecordGetList()
                .customerCode(userId)
                .body(ImmutableMap.of("waybillNumbers", waybillNumbers))
                .post();
    }

    /**
     * 导入订单
     *
     * @param param 参数
     * @return 导入订单结果
     */
    @Override
    public InnerBeDraftOrderImportDTO importBusinessOrder(String userId, List<BusinessOrderDTO> param) {
        param.forEach(this::setTotalParam);
        return innerBeDraftOrderImport().customerCode(userId).body(ImmutableMap.of("draftExpresses", param)).post();
    }

    /**
     * 草稿订单修改
     *
     * @param param 参数
     * @return 草稿订单修改结果
     */
    @Override
    public GeneralDTO editBusinessDraft(BusinessOrderDTO param) {
        setTotalParam(param);
        return innerBeDraftOrderEdit().customerCode(param.getUserId()).body(param).post();
    }

    /**
     * 设置商品总货值
     *
     * @param param 参数
     */
    private void setTotalParam(BusinessOrderDTO param) {
        param.setOrderSource(EJFUrl.SOURCE);
       
        // 设置总货值
        if (CollectionUtil.isNotEmpty(param.getProductInfo())) {
            final BigDecimal totalProductPrice = param.getProductInfo().stream()
                    .map(p -> NumberUtil.mul(
                            EJFUtil.getBigDecimal(p.getPrice()),
                            EJFUtil.getBigDecimal(p.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            param.getCustomsInfo().setTotalProductPrice(totalProductPrice);
        }
        final String price = NumberUtil.add(EJFUtil.getBigDecimal(param.getCustomsInfo().getOtherFee()), EJFUtil.getBigDecimal(param.getCustomsInfo().getPackFee()),EJFUtil.getBigDecimal(param.getCustomsInfo().getInsurance()),param.getCustomsInfo().getTotalProductPrice()).toString();
        param.getCustomsInfo().setPrice(price);
        // 设置揽收仓编号
        if (StringUtils.isBlank(param.getCompanyCode())) {
            final AccountGetResVO account = accountService.getAccount(param.getUserId());
            param.setCompanyCode(account.getWarehouseCode());
        }
        // 设置发件人国家为中国
        param.getSenderInfo().setCountryId("8");
        // 设置总数量
        param.getCustomsInfo().setTotalQuantity(CollectionUtil.size(param.getParcelInfo()));
    }

    /**
     * 订单状态统计
     *
     * @param param 参数
     * @return 订单状态统计结果
     */
    @Override
    public InnerBeDraftOrderStatusGetListDTO getBusinessDraftStatistics(InnerBeDraftOrderGetListFilterParamDTO param) {
        return innerBeDraftOrderStatusGetList().customerCode(param.getUserId()).body(
                MapUtil.ofEntries(
                        entry("orderNumbers", CollectionUtil.addAll(ListUtil.toList(param.getOrderNumbers()), param.getListNumber())),
                        entry("channelId", param.getChannelId()),
                        entry("countryId", param.getCountryId()),
                        entry("receiverName", param.getReceiverName()),
                        entry("startTime", param.getStartTime()),
                        entry("endTime", param.getEndTime())
                )
        ).post();
    }

    /**
     * 订单生成运单
     *
     * @param userId 参数
     * @param id     参数
     * @return 订单生成运单结果
     */
    @Override
    public InnerBeDraftOrderGenerateDTO businessDraftToOrder(String userId, String id) {
        try {
            return innerBeDraftOrderGenerate().customerCode(userId)
                    .body(MapUtil.ofEntries(entry("id", id))).post();
        } catch (Throwable e) {
            log.error("订单生成运单失败 {}", id, e);
            return InnerBeDraftOrderGenerateDTO.vBuilder()
                    .success(false)
                    .code("500")
                    .message(e.getMessage())
                    .build();
        }
    }

    /**
     * 删除订单
     *
     * @param userId 参数
     * @param ids    参数
     * @return 删除订单结果
     */
    @Override
    public GeneralDTO deleteBusinessDraft(String userId, List<String> ids) {
        return innerBeDraftOrderDelete().customerCode(userId).body(MapUtil.ofEntries(entry("ids", ids))).post();
    }

    /**
     * 订单列表查询
     *
     * @param param 参数
     * @return 订单列表查询结果
     */
    @Override
    public InnerBeDraftOrderGetListFilterDTO getBusinessDraft(InnerBeDraftOrderGetListFilterParamDTO param) {
        return innerBeDraftOrderGetListFilter().customerCode(param.getUserId()).body(
                MapUtil.ofEntries(
                        entry("orderNumbers", param.getOrderNumbers()),
                        entry("channelId", param.getChannelId()),
                        entry("listStatus", param.getListStatus()),
                        entry("countryId", param.getCountryId()),
                        entry("receiverName", param.getReceiverName()),
                        entry("pageNum", param.getPageNum()),
                        entry("pageSize", param.getPageSize()),
                        entry("startTime", param.getStartTime()),
                        entry("endTime", param.getEndTime())
                )
        ).post();
    }

    /**
     * 订单详细
     *
     * @param userId 参数
     * @param id     参数
     * @return 订单详细结果
     */
    @Override
    public BeOrderGetDTO getBusinessDraftDetail(String userId, String id) {
        return innerBeDraftOrderGet().customerCode(userId).body(MapUtil.ofEntries(entry("id", id))).post();
    }

    /**
     * 已存在订单号查询
     *
     * @param param 参数
     * @return 已存在订单号查询结果
     */
    @Override
    public InnerBeOrderNumberGetListDTO getExistBusinessDraftNumber(InnerBeOrderNumberGetListParamDTO param) {
        return innerBeOrderNumberGetList().customerCode(param.getUserId()).body(param).post();
    }


    /**
     * 运价试算
     *
     * @param param 参数
     * @return 运价试算结果
     */
    @Override
    public ManyPieceDTO manyPiece(ManyPieceParamDTO param) {
        return HttpUtil
                .clazz(ManyPieceDTO.class)
                .url(businessManyPieceUrl)
                .body(param).post();
    }


    @Override
    public List<PLMCountryDTO.DataDTO> getCountryByProductCode(String code) {
        PLMCountryDTO plmCountryDTO = HttpUtil.clazz(PLMCountryDTO.class)
                .url(dictionary)
                .header(
                        MapUtil.ofEntries(
                                entry("method", "product.country.querylist")
                        )
                )
                .body(
                        MapUtil.ofEntries(
                                entry("productNumber", code)
                        )
                )
                .logRsp(false)
                .post();
        if (plmCountryDTO.getSuccess()) {
            return plmCountryDTO.getData();
        }
        return new ArrayList<>();
    }
}