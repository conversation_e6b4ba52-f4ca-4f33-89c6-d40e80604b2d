package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * @author: dinghy
 * @date: 2023/2/24 14:16
 * <p>
 *     企业客户申请
 * </p>
 */
@Data
public class SaveCompanyApplyDTO {
    private String no;
    @NotBlank(message = "[企业名称]不能为空")
    private String companyName;
    @NotBlank(message = "[营业执照]不能为空")
    private String registerNumber;
    @NotBlank(message = "[法人名字]不能为空")
    private String legalName;
    @NotBlank(message = "[法人身份证号]不能为空")
    private String corporateCard;
    @NotBlank(message = "[法人手机号]不能为空")
    private String corporatePhone;
    @NotBlank(message = "[证件照]不能为空")
    private String cardAttach;
    @NotNull(message = "[证件有效期]不能为空")
    private LocalDateTime certificateValidity;
    /**
     * <AUTHOR>
     * @description 是否需要审核,true是需要,false不需要
     * @date 2023/2/24 15:35
     **/
    private Boolean audit;
    /**
     * <AUTHOR>
     * @description 是否代办人是法人,true是
     * @date 2023/2/27 10:17
     **/
    private Boolean agentIsLegal;

    private String userCode;
    /**
     * <AUTHOR>
     * @description 来源类型,提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType= CommonCrmConstant.PORTAL_SOURCE_TYPE;

    private String bankCard;

    /**
     * @author: dinghy
     * @createTime: 2024/2/28 9:34
     * @description: 境外客户付款账号证明
     */
    private String bankAccountProof;
    /**
     * @author: dinghy
     * @createTime: 2024/2/28 9:36
     * @description: 银行名称
     */
    private String bankName;
    /**
     * @author: dinghy
     * @createTime: 2024/2/28 10:07
     * @description: 银行编码
     */
    private String bankCode;

    /*** 经办人企业证明 */
    private String personCompanyProve;
    /*** 经办人是否为法人 0:否,1:是 */
    private Integer personIsCorporate;

    /*** 法人企业证明 */
    private String corporateCompanyProve;

    /*** 燕文后台使用人是否为法人 0:否,1:是 */
    private Integer userIsCorporate;

    /*** 燕文后台使用人证明 */
    private String userProve;

    /*** 燕文后台使用人授权委托书 */
    private String userMandate;
    // 代办人名字
    private String personName;
    // 代办人证件号
    private String personCard;

    /*** 办理人区域类型 0: 大陆经办人、1: 境外经办人、3: 港澳台经办人 */
    private Integer personArea;

    /*** 法人区域类型 0: 大陆法人、1: 境外法人、3: 港澳台法人 */
    private Integer corporateArea;
}
