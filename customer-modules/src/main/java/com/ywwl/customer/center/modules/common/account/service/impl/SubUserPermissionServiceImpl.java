// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.service.impl;

import cn.hutool.crypto.SecureUtil;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.enums.OPCodeEnum;
import com.ywwl.customer.center.common.enums.SystemTypeEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.modules.common.account.dto.SubUserAccountReqDTO;
import com.ywwl.customer.center.modules.common.account.service.SubUserPermissionService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/15 10:11
 * @ModifyDate 2023/3/15 10:11
 * @Version 1.0
 */
@Service
public class SubUserPermissionServiceImpl implements SubUserPermissionService {

    @Value("${api-request.api-url}")
    private String userCenterApi;

    @Override
    public JsonResult<?> getAccountByChildUserId(SubUserAccountReqDTO subUserAccountReqDTO) {
        // 独一无二key
        String key = SecureUtil.sha1(String.valueOf(subUserAccountReqDTO));
        return CacheUtil.redis().getValueAndCache(CacheKeyEnum.CHILD_ACCOUNT, key, () -> {
            // 获取子用户有权限的制单账号
            subUserAccountReqDTO.setBusinessType(SystemTypeEnum.CUSTOMER_CENTER.getType());
            return HttpUtil.doPost(userCenterApi + OPCodeEnum.GET_ACCOUNT_BY_USERID.value(), subUserAccountReqDTO, JsonResult.class);
        },false);
    }
}
