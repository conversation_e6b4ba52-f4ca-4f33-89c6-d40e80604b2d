package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运单状态统计
 *
 * <AUTHOR>
 * @since 2023/10/11 15:33
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeOrderStatusGetListFilterDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {

        /**
         * 总条数
         */
        private Integer totalCount;
        /**
         * 状态集
         */
        private List<ExpressDTO> express;

    }
}
