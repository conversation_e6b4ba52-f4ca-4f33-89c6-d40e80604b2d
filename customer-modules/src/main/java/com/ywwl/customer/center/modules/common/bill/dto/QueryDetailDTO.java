package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: dinghy
 * @date: 2023/4/21 13:48
 */
@Data
public class QueryDetailDTO {
    private String opCode = BillOpCodeEnum.BILL_DETAIL.value();
    private Integer businessType;
    @JSONField(serialize = false)
    private String userCode;
    /**
     * 主单号
     */
    private String mainNo;
    /**
     * 运单开始日期
     */
    private String startCalcTime;
    /**
     * 运单结束日期
     */
    private String endCalcTime;
    /**
     * 结账类型
     */
    private String transType;
    /**
     * 产品类型
     */
    private String productCode;
    /**
     * 运单号
     */
    private String waybillNumber;

    private Integer pageSize = 10;
    private Integer currentPage = 1;

    public void toExport() {
        setOpCode(BillOpCodeEnum.DETAIL_EXPORT.value());
    }


    public String build() {
        StringBuilder stringBuilder = new StringBuilder("?")
                .append("opCode=").append(opCode)
                .append("&transType=").append(transType)
                .append("&mainNo=").append(mainNo);
        if (StringUtils.isNotBlank(startCalcTime) && StringUtils.isNotBlank(endCalcTime)) {
            stringBuilder.append("&startCalcTime=").append(startCalcTime)
                    .append("&endCalcTime=").append(endCalcTime);
        }
        if (StringUtils.isNotBlank(waybillNumber)) {
            stringBuilder.append("&waybillNumber=").append(waybillNumber);
        }
        return stringBuilder.toString();
    }
}
