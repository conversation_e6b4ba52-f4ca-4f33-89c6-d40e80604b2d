package com.ywwl.customer.center.modules.common.auth.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.common.enums.BusinessApplyStatusEnum;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.enums.BusinessProcessStateEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerStatusEnum;
import lombok.Data;

@Data
public class BusinessAndCustomerVo {
    /**
     * 业务先开通状态 {@link com.ywwl.customer.center.common.enums.BusinessApplyStatusEnum}
     */
    @JSONField(serialize = false)
    private Integer businessAuditStatus;
    /**
     * 客户认证状态 {@link }
     */
    @JSONField(serialize = false)
    private Integer customerAuditStatus;
    /**
     * 业务先展示进度条 {@link com.ywwl.customer.center.modules.common.auth.enums.BusinessProcessStateEnum}
     */
    private Integer businessProcessState;

    private String frozenStatusName;
    private Integer frozenStatus;

    public void handleBusinessProcessState() {
        BusinessApplyStatusEnum businessApplyStatusEnum = BusinessApplyStatusEnum.getStatus(businessAuditStatus);
        switch (businessApplyStatusEnum) {
            case NOT_OPEN:
                businessProcessState = BusinessProcessStateEnum.UN_SUBMIT_INFO.value();
                break;
            case WAIT_AUDIT:
                businessProcessState = BusinessProcessStateEnum.SUBMIT_INFO.value();
                break;
            case AUDIT_FAIL:
                businessProcessState=BusinessProcessStateEnum.AUDIT_FAIL.value();
                break;
            case WAIT_SIGN:
                if(CustomerStatusEnum.AUDIT_SUCCESS.status().equals(customerAuditStatus)){
                    businessProcessState=BusinessProcessStateEnum.AUDIT_SUCCESS.value();
                }else{
                    businessProcessState=BusinessProcessStateEnum.SUBMIT_INFO.value();
                }
                break;
            case SIGNED:
                businessProcessState=BusinessProcessStateEnum.SIGN_SUCCESS.value();
                break;
            default:
                throw new BusinessException("客户状态异常");
        }
    }

}
