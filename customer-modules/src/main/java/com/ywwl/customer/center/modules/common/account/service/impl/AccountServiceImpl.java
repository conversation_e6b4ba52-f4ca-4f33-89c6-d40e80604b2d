// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.common.enums.JudgeTypeEnum;
import com.ywwl.customer.center.common.enums.SceneTypeEnum;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.account.dto.AccountEditReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountInsertReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.SubUserAccountReqDTO;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.service.SubUserPermissionService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import com.ywwl.customer.center.modules.general.cmcc.enums.WarehouseClassifyTypeEnum;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 13:34
 * @ModifyDate 2023/3/14 13:34
 * @Version 1.0
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {
    @Value("${common-crm.host}")
    private String commonCrmHost;

    @Resource
    private UserService userService;

    @Resource
    private CommonCrmService commonCrmService;

    @Resource
    private SubUserPermissionService subUserPermissionService;

    /**
     * 主数据服务类
     */
    @Resource
    CmccService cmccService;


    @Override
    public JsonResult<?> saveAccount(AccountInsertReqDTO accountInsertReqDTO) {
        accountInsertReqDTO.setSourceType("Portal");
        String account = commonCrmService.saveAccount(accountInsertReqDTO);
        return JsonResult.success(account);
    }

    /**
     * 获取制单账号
     *
     * @param accountGetReqDTO 参数
     * @return 制单账号
     */
    @Override
    public JsonResult<List<AccountGetResVO>> getAccountResults(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent) {
        //需要做部分逻辑判断
        if (StringUtils.isEmpty(accountGetReqDTO.getAccountCode()) && Objects.isNull(accountGetReqDTO.getAccountType())) {
            return JsonResult.error(ResponseCode.PORTAL_5010.getMessage());
        }

        accountGetReqDTO.setUserCode(userAgent.getUserCode());

        List<String> accountNosChild = new ArrayList<>();
        //获取所有的子用户制单账号
        if (!userAgent.isAdmin()) {
            accountNosChild = listAccountNoForChild(userAgent.getUserId());
        }
        accountNosChild.add(EJFUrl.CUSTOMER_CODE);
        List<AccountGetResVO> vos = filterAccountByScene(userAgent, accountNosChild, accountGetReqDTO);

        if (Objects.nonNull(vos)) {
            return JsonResult.success(vos);
        } else {
            return JsonResult.error(ResponseCode.PORTAL_3001.getMessage());
        }
    }

    /**
     * 获取制单账号
     *
     * @param accountGetReqDTO 参数
     * @return 制单账号
     */
    @Override
    public List<AccountGetResVO> getAccounts(AccountGetReqDTO accountGetReqDTO) {
        UserAgent userAgent = userService.currentUser();
        return getAccounts(accountGetReqDTO, userAgent);
    }

    /**
     * 获取制单账号
     *
     * @param accountGetReqDTO 参数
     * @param userAgent        用户信息
     * @return 制单账号
     */
    @Override
    public List<AccountGetResVO> getAccounts(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent) {
        final JsonResult<List<AccountGetResVO>> accountResults = getAccountResults(accountGetReqDTO, userAgent);
        if (accountResults.getSuccess()) {
            return accountResults.getData();
        }
        throw new BusinessException(accountResults.getMessage());
    }

    /**
     * 获取单个制单账号
     *
     * @param accountGetReqDTO 获取参数
     * @return 制单账号信息
     */
    @Override
    public AccountGetResVO getAccount(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent) {
        List<AccountGetResVO> accounts = Objects.nonNull(userAgent) ?
                getAccounts(accountGetReqDTO, userAgent) :
                getAccounts(accountGetReqDTO);
        if (!CollectionUtils.isEmpty(accounts)) {
            return accounts.get(0);
        }
        throw new BusinessException(ResponseCode.PORTAL_5033);
    }

    /**
     * 获取单个制单账号
     *
     * @param accountGetReqDTO 获取参数
     * @return 制单账号信息
     */
    @Override
    public AccountGetResVO getAccount(AccountGetReqDTO accountGetReqDTO) {
        return getAccount(accountGetReqDTO, null);
    }

    /**
     * 获取单个制单账号
     *
     * @param account 获取参数
     * @return 制单账号信息
     */
    @Override
    public AccountGetResVO getAccount(String account) {
        return getAccount(AccountGetReqDTO.builder().accountCode(account).build());
    }

    /**
     * 获取单个制单账号
     *
     * @param account 获取参数
     * @param user    用户
     * @return 制单账号信息
     */
    @Override
    public AccountGetResVO getAccount(String account, UserAgent user) {
        return getAccount(AccountGetReqDTO.builder().accountCode(account).build(), user);
    }

    @Override
    public JsonResult<?> editAccount(AccountEditReqDTO accountEditReqDTO) {
        commonCrmService.editAccount(accountEditReqDTO);
        return JsonResult.success();
    }

    @Override
    public boolean existAccount(List<String> accounts, UserAgent userAgent, Integer accountType) {
        return existAccount(accounts, userAgent.getUserCode(), accountType);
    }

    @Override
    public boolean existAccount(List<String> accounts, String userCode, Integer accountType) {
        // 过滤空
        if (CollectionUtils.isEmpty(accounts)) {
            return true;
        }
        // 获取制单账号
        AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
        accountGetReqDTO.setUserCode(userCode);
        accountGetReqDTO.setAccountType(accountType);
        List<AccountGetResVO> allAccount = getAccounts(accountGetReqDTO);
        // 验证是否存在
        List<String> accountNos = allAccount.stream()
                .map(AccountGetResVO::getAccountCode)
                .collect(Collectors.toList());
        return new HashSet<>(accountNos).containsAll(accounts);
    }

    @Override
    public boolean existAccount(List<String> accounts, String userCode) {
        return existAccount(accounts, userCode, 0);
    }

    @Override
    public boolean existAccount(List<String> accounts) {
        final UserAgent userAgent = userService.currentUser();
        return existAccount(accounts, userAgent.getUserCode(), 0);
    }

    @Override
    public void existAccountThrow(List<String> accounts, Integer accountType) {
        final UserAgent userAgent = userService.currentUser();
        final boolean existAccount = existAccount(accounts, userAgent.getUserCode(), accountType);
        if (!existAccount) {
            throw new BusinessException(ResponseCode.PORTAL_5060);
        }
    }

    @Override
    public void existAccountThrow(List<String> accounts) {
        existAccountThrow(accounts, 0);
    }

    @Override
    public void existAccountThrow(String account) {
        if (StringUtils.isBlank(account)) {
            return;
        }
        existAccountThrow(Lists.list(account), 0);
    }

    @Override
    public void existAccountThrow(String account, Integer accountType) {
        if (StringUtils.isBlank(account)) {
            return;
        }
        existAccountThrow(Lists.list(account), accountType);
    }

    @Override
    public List<String> getAccountNosByPermission(Integer accountType, Integer scene) {
        UserAgent userAgent = userService.currentUser();

        AccountGetReqDTO accountGetReqDTO = AccountGetReqDTO
                .builder()
                .accountType(accountType)
                .scene(scene)
                .build();
        JsonResult<List<AccountGetResVO>> datas = getAccountResults(accountGetReqDTO, userAgent);
        if (datas.getSuccess()) {
            return datas.getData()
                    .stream()
                    .map(account -> account.getAccountCode())
                    .collect(Collectors.toList());
        } else {
            throw new BusinessException(datas.getMessage());
        }
    }

    @Override
    public Map<Integer, List<AccountGetResVO>> getAccountForAll(List<Integer> businessType, UserAgent userAgent, Integer scene) {
        Map<Integer, List<AccountGetResVO>> accountForGroup = new HashMap<>();
        List<String> accountNosChild = new ArrayList<>();
        //获取所有的子用户制单账号
        if (!userAgent.isAdmin()) {
            accountNosChild = listAccountNoForChild(userAgent.getUserId());
        }

        List<Integer> business = new ArrayList<>();
        if (!CollectionUtils.isEmpty(businessType)) {
            business.addAll(businessType);
        } else {
            EnumSet.allOf(BusinessTypeEnum.class)
                    .forEach(b -> business.add(b.getValue()));
        }

        List<String> finalAccountNosChild = accountNosChild;
        business.forEach(b -> {
            AccountGetReqDTO accountGetReqDTO = AccountGetReqDTO
                    .builder()
                    .userCode(userAgent.getUserCode())
                    .scene(scene)
                    .accountType(b)
                    .build();
            List<AccountGetResVO> vos = filterAccountByScene(userAgent, finalAccountNosChild, accountGetReqDTO);
            // 前端返回不显示ApiToken
            vos.forEach(v -> v.setApiToken(null));
            accountForGroup.put(b, vos);
        });

        return accountForGroup;
    }


    /**
     * 根据用户ID获取当前用户下所有的制单账号
     *
     * @param userId
     * @return
     */
    @Override
    public List<String> listAccountNoForChild(Long userId) {
        SubUserAccountReqDTO subUserAccountReqDTO = new SubUserAccountReqDTO();
        subUserAccountReqDTO.setUserId(userId);
        JsonResult<?> subAccountResult = subUserPermissionService.getAccountByChildUserId(subUserAccountReqDTO);

        if (Objects.isNull(subAccountResult)) {
            throw new BusinessException(ResponseCode.PORTAL_500.getMessage());
        } else if (!subAccountResult.getSuccess()) {
            throw new BusinessException(subAccountResult.getMessage());
        }

        return (List<String>) subAccountResult.getData();
    }


    private List<AccountGetResVO> filterAccountByScene(UserAgent userAgent, List<String> subAccountNo, AccountGetReqDTO accountGetReqDTO) {
        List<AccountGetResVO> filterAccounts = new ArrayList<>();
        final List<WareHouseVo> warehouses = cmccService.getWarehouseByType(WarehouseClassifyTypeEnum.PACKET_WAREHOUSE.getType());
        Map<String, String> wareHourseMap = warehouses.stream().collect(Collectors.toMap(WareHouseVo::getCode, WareHouseVo::getName));

        if (StringUtils.isNotEmpty(accountGetReqDTO.getAccountCode())) {
            JSONArray crmAccount;
            try {
                crmAccount = commonCrmService.getAccounts(accountGetReqDTO);
            } catch (BusinessException e) {
                return filterAccounts;
            }

            List<AccountGetResVO> singleAccount = crmAccount.toJavaList(AccountGetResVO.class);

            singleAccount.stream().forEach(account -> {
                if (Objects.nonNull(account.getWarehouseCode())) {
                    account.setWarehouseName(wareHourseMap.get(account.getWarehouseCode()));
                }
            });

            if (userAgent.isAdmin()) {
                return singleAccount;
            } else {
                return singleAccount
                        .stream()
                        .filter(account -> subAccountNo.contains(account.getAccountCode()))
                        .collect(Collectors.toList());
            }
        }

        JSONArray allAccount;
        try {
            allAccount = commonCrmService.getAccounts(accountGetReqDTO);
        } catch (BusinessException e) {
            return filterAccounts;
        }

        List<AccountGetResVO> vos = allAccount.toJavaList(AccountGetResVO.class);


        vos.stream().forEach(account -> {
            if (Objects.nonNull(account.getWarehouseCode())) {
                account.setWarehouseName(wareHourseMap.get(account.getWarehouseCode()));
            }
        });

        Integer scene = accountGetReqDTO.getScene();
        if (Objects.isNull(scene)) {
            filterAccounts.addAll(vos);
        } else if (SceneTypeEnum.INSERT_ORDER.getType().equals(scene)) {
            filterAccounts = vos.stream()
                    .filter(account -> JudgeTypeEnum.FAKE.getType().equals(account.getEnableStatus()))
                    .collect(Collectors.toList());
        } else if (SceneTypeEnum.QUERY_ORDER.getType().equals(scene)) {
            filterAccounts = vos.stream()
                    .filter(account -> JudgeTypeEnum.FAKE.getType().equals(account.getSearchStatus()))
                    .collect(Collectors.toList());
        }

        if (!userAgent.isAdmin()) {
            return filterAccounts.stream()
                    .filter(account -> subAccountNo.contains(account.getAccountCode()))
                    .collect(Collectors.toList());
        }

        return filterAccounts;
    }


    @Override
    public void checkAllAccountCode(List<String> accounts, String userCode) {
        // 过滤空
        if (CollectionUtils.isEmpty(accounts)) {
            return;
        }
        AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
        accountGetReqDTO.setUserCode(userCode);
        JSONArray jsonArray = commonCrmService.getAccounts(accountGetReqDTO);
        List<AccountGetResVO> allAccount = jsonArray.toJavaList(AccountGetResVO.class);
        // 验证是否存在
        List<String> accountNos = allAccount.stream()
                .map(AccountGetResVO::getAccountCode)
                .collect(Collectors.toList());
        boolean containsAll = new HashSet<>(accountNos).containsAll(accounts);
        if (!containsAll) {
            throw ResponseCode.PORTAL_5060.getError();
        }
    }

    @Override
    public String getDefaultWarehouse(String accountCode) {
        AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
        accountGetReqDTO.setAccountType(0);
        List<AccountGetResVO> accounts = getAccounts(accountGetReqDTO);
        Optional<AccountGetResVO> any = accounts.stream().filter(x -> x.getAccountCode().equals(accountCode)).findAny();
        if (any.isPresent()) {
            return any.get().getWarehouseCode();
        }
        return null;
    }

    @Override
    public AccountGetResVO getAccountByCustomerCode(String accountCode) {
        AccountGetReqDTO accountGetReqDTO = new AccountGetReqDTO();
        // accountGetReqDTO.setAccountType(0);
        accountGetReqDTO.setAccountCode(accountCode);
        JSONArray accounts = commonCrmService.getAccounts(accountGetReqDTO);
        if (accounts == null || accounts.isEmpty()) {
            throw new BusinessException("制单账号异常");
        }
        List<AccountGetResVO> allAccount = accounts.toJavaList(AccountGetResVO.class);
        return  allAccount.get(0);
    }
}
