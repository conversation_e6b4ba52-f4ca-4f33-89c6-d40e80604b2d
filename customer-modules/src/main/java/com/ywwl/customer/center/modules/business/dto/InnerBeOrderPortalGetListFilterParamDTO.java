package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 列表查询参数Bean
 *
 * <AUTHOR>
 * @since 2023/10/10 15:25
 **/
@NoArgsConstructor
@Data
public class InnerBeOrderPortalGetListFilterParamDTO {
    /**
     * 制单账号
     */
    private String userId;
    /**
     * 产品
     */
    private String channelId;
    /**
     * 国家
     */
    private String countryId;
    /**
     * 状态
     */
    private List<String> listStatus;
    /**
     * 是否打印
     */
    private Integer isPrint;
    /**
     * 订单号/运单号
     */
    private List<String> listNumber;
    /**
     * 收件人名称
     */
    private String receiverName;
    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空")
    private String startTime;
    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空")
    private String endTime;
    /**
     * 页大小
     */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;
    /**
     * 页码
     */
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

}
