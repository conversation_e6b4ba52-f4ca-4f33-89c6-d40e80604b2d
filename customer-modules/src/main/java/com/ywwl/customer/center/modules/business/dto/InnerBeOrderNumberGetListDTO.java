package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 已经存在订单号
 *
 * <AUTHOR>
 * @since 2023/10/11 16:21
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeOrderNumberGetListDTO extends GeneralDTO {


    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 订单号
         */
        private List<String> listOrderNumber;
    }
}
