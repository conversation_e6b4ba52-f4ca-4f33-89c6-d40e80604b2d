package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import com.ywwl.customer.center.modules.overseas.enums.SelectTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class QueryOverseaRecordDTO {
    private String opCode = BillOpCodeEnum.OVERSEA_RECORD.value();
    @NotBlank(message = "业务账号不能为空")
    @JSONField(name = "merchantCode")
    private String accountCode;
    private String transStartDate;
    private String transEndDate;
    private String transNo;
    private Integer currentPage;
    private Integer pageSize;
    private String numbers;
    /**
     * @author: dinghy 
     * @createTime: 2024/4/8 11:14
     * @description: 0是账单详情，1是对账单 {@link SelectTypeEnum}
     */
    private Integer selectType;
    // TC01 消费， TC02 退费， TC03 赔偿， TC04 二次费用， TC20 付款 ，TC41 清关处理费 ，TC39 重派处理费
    private String expenseType;
}
