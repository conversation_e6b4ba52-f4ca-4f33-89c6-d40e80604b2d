package com.ywwl.customer.center.modules.amazon.result;

import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 打印标签结果类
 *
 * <AUTHOR>
 * @date 2022/09/05 16:38
 **/
@XmlRootElement(name = "LabelTemplatePrintRequest")
@NoArgsConstructor
public class PrintLabelsResult {

	private String epcode;
	private Boolean isSuccess;
	private String errorMsg;
	private String base64String;
	private String labelType;

	@XmlElement(name = "Epcode")
	public String getEpcode() {
		return epcode;
	}

	public void setEpcode(String epcode) {
		this.epcode = epcode;
	}

	@XmlElement(name = "IsSuccess")
	public Boolean getIsSuccess() {
		return isSuccess;
	}

	public void setIsSuccess(Boolean success) {
		isSuccess = success;
	}

	@XmlElement(name = "ErrorMsg")
	public String getErrorMsg() {
		return errorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}

	@XmlElement(name = "Base64String")
	public String getBase64String() {
		return base64String;
	}

	public void setBase64String(String base64String) {
		this.base64String = base64String;
	}

	@XmlElement(name = "LabelType")
	public String getLabelType() {
		return labelType;
	}

	public void setLabelType(String labelType) {
		this.labelType = labelType;
	}
}
