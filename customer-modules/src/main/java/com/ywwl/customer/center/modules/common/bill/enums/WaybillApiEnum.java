package com.ywwl.customer.center.modules.common.bill.enums;

public enum WaybillApiEnum {
    BILL_DETAIL("acc-ar/bill-detail/search/", "客户账单查询"),
    BILL_EXPORT("acc-ar/bill-detail/export?", "客户账单导出"),
    MONTH_SHIPMENT("/acc-ar/bill-detail/delAvgAmount","月发货值"),
    CREATE_SCAN_ORDER("payOrderCreate","创建支付宝二维码订单"),
    QUERY_ALIPAY_ORDER("payOrderQuery","查询支付宝扫码订单支付状况"),
    CREATE_TEST_DATA("arReleaseData","创建账单测试数据"),
    MERCHANT_ACCEPT_BILL_APPLY("merchantreleaseconfig","客户接受账单自主配置申请接口"),
    QUERY_BILL_CONFIG("qrymereleaseconfig","查询配置接口"),

    ;
    private String value;
    private String desc;

    WaybillApiEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String value() {
        return value;
    }
}
