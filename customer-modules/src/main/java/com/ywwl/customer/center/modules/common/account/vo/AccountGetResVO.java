// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 11:15
 * @ModifyDate 2023/3/14 11:15
 * @Version 1.0
 */
@Data
public class AccountGetResVO{
    /**
     * 账号id
     */
    private Long id;
    /**
     * 用户代码
     */
    private String userCode;
    /**
     * 账号类型
     */
    private Integer accountType;
    /**
     * 制单账号
     */
    private String accountCode;
    /**
     * 业务账号
     */
    private String merchantCode;
    /**
     * 是否启用:0启用
     */
    private Integer enableStatus;
    /**
     * 是否能够查询订单权限:0启用
     */
    private Integer searchStatus;
    /**
     * 秘钥
     */
    private String apiToken;
    /**
     * 账号来源
     */
    private String sourceType;
    /**
     * 是否删除
     */
    private Boolean deleteFlag;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 交货仓编码
     */
    private String warehouseCode;


    /**
     * 交货仓编码
     */
    private String warehouseName;
}
