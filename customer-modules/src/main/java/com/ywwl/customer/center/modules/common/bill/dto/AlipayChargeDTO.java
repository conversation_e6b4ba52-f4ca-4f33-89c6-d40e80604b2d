package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.common.utils.BillMD5Util;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.Data;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Data
public class AlipayChargeDTO {
    private String code;
    private String sign;
    private String data;

    public void setMDSign(String key){
        try {
            String sign = BillMD5Util.sign(data, key);
            this.sign=sign;
            this.data= URLEncoder.encode(data,"UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException("加签异常");
        }
    }
}
