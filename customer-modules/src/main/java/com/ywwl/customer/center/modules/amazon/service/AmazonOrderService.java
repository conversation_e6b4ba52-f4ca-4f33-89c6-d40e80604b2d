package com.ywwl.customer.center.modules.amazon.service;

import com.ywwl.customer.center.modules.amazon.result.*;
import com.ywwl.customer.center.modules.amazon.vo.AmazonOrderDetailVO;
import com.ywwl.customer.center.modules.amazon.vo.PageResultVO;
import com.ywwl.customer.center.modules.ejf.service.ProcessResult;

import java.io.IOException;
import java.util.List;

public interface AmazonOrderService extends ProcessResult {

    /**
     * 获取平台账号
     *
     * @param param 参数
     * @return 获取的平台账号
     */
    List<QueryPlatformAccountResult.DataDTO> queryPlatformAccount(QueryPlatformAccountParam param) throws IOException;

	/**
	 * 打印标签
	 *
	 * @param customerCode      发货账号
	 * @param amazonOrderDetail 详情
	 * @return 打印标签
	 */
	String getLabelBase64String(String customerCode, AmazonOrderDetailVO amazonOrderDetail);

    /**
     * 更新打印状态
     *
     * @param trackNumbers 运单号
     * @return 结果
     */
    UpdatePrintStatusResultDTO updatePrintStatus(List<String> trackNumbers);

	/**
	 * 获取敦煌标签
	 *
	 * @param customerCode 制单账号
	 * @param trackNumber  运单号
	 * @param orderNumber  订单号
	 * @return  敦煌标签
	 */
	String getDhLabelVase64String(String customerCode, String trackNumber, String orderNumber);

	/**
     * <AUTHOR>
     * @description 打印标签
     * @date 2022/9/7 14:45
     **/
    String getLabelBase64String(String customerCode, String trackNumber);

    /**
     * <AUTHOR>
     * @description 从oms查询订单
     * @date 2022/9/6 10:37
     **/
    PageResultVO queryOrders(QueryOrderParam queryOrderParam);

    /**
     * 转换为本地亚马逊对象
     *
     * @param o 远程亚马逊对象
     */
    AmazonOrder amazonInterfaceToLocal(QueryOrderResultDTO.DataDTO o);

    /**
     * 列表查询
     * @param queryOrderParam  查询参数
     * @return 查询结果
     */
    QueryOrderResultDTO getAmazonList(QueryOrderParam queryOrderParam);

    /**
     * <AUTHOR>
     * @description 获取订单详情
     * @date 2022/9/6 14:26
     **/
    AmazonOrderDetail queryOrderDetail(String trackNumber);

	/**
	 * 获取详情信息，不做处理
	 * @param trackNumber   运单号
	 * @return 详情信息
	 */
	AmazonOrderDetail getAmazonOrderDetail(String trackNumber);
}
