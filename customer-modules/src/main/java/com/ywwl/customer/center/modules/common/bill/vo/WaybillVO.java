package com.ywwl.customer.center.modules.common.bill.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/19 9:36
 */
@Data
public class WaybillVO {

    private String pageNum;
    private String pageSize;
    private Integer totalRecord=0;
    private Integer totalPages=0;
    /**
     * // 总金额
     *
     * @date 2022/12/8
     */
    private String arPriceSum;
    /**
     * // 运单集合
     *
     * @date 2022/11/16
     */
    private List<Detail> records;

    @Data
    public static class Detail {

        /**
         * //
         *
         * @date 2022/11/16
         */
        private Integer id;
        /**
         * // 主单号
         *
         * @date 2022/11/16
         */
        private String mainNo;
        /**
         * // 运单号
         *
         * @date 2022/11/16
         */
        private String waybillNumber;
        /**
         * //
         * 转单号
         *
         * @date 2022/11/16
         */
        private String exchangeNumber;
        /**
         * // 订单号
         *
         * @date 2022/11/16
         */
        private String orderNumber;
        /**
         * // 结算公司
         *
         * @date 2022/11/16
         */
        private String companyCodeOfSettlement;
        /**
         * // 商户号
         *
         * @date 2022/11/16
         */
        private String merchantCode;
        /**
         * // 发发货账号
         *
         * @date 2022/11/16
         */
        private String customerCode;
        /**
         * // 产品号
         *
         * @date 2022/11/16
         */
        private String productCode;
        /**
         * // 产品名称
         *
         * @date 2022/11/16
         */
        private String productName;
        /**
         * // 目的国id
         *
         * @date 2022/11/16
         */
        private Integer regionId;
        /**
         * // 目的国
         *
         * @date 2022/11/16
         */
        private String regionName;
        /**
         * // 实重
         *
         * @date 2022/11/16
         */
        private String weight;
        /**
         * // 计费重
         *
         * @date 2022/11/16
         */
        private String calcWeight;
        /**
         * // 重量单位
         *
         * @date 2022/11/16
         */
        private String weightUnit;
        /**
         * //  长
         *
         * @date 2022/11/16
         */
        private String expressLength;
        /**
         * // 宽
         *
         * @date 2022/11/16
         */
        private String expressWidth;
        /**
         * // 高
         *
         * @date 2022/11/16
         */
        private String expressHeight;
        /**
         * // 计费时间
         *
         * @date 2022/11/16
         */
        private LocalDateTime timeOfCalc;
        /**
         * // 账单日期
         *
         * @date 2022/11/16
         */
        private LocalDate dateOfBill;
        /**
         * // 本币金额
         *
         * @date 2022/11/16
         */
        private BigDecimal arPrice;
        /**
         * // 外币金额
         *
         * @date 2022/11/16
         */
        private String arPriceToFc;
        /**
         * // 币种
         *
         * @date 2022/11/16
         */
        private String currency;
        /**
         * // 交易类型
         *
         * @date 2022/11/16
         */
        private String transType;
        /**
         * // 交易类型名称
         *
         * @date 2022/11/16
         */
        private String transTypeName;
        /**
         * //
         *
         * @date 2022/11/16
         */
        private String businessType;
        /**
         * // 添加时间
         *
         * @date 2022/11/16
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime addTime;
        /**
         * // 总金额
         *
         * @date 2022/11/16
         */
        private BigDecimal money;

        /**
         * EJF流水号
         */
        private String yanwenOrderNumber;

    }
}
