package com.ywwl.customer.center.modules.common.account.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ywwl.customer.center.modules.common.account.domain.SysOperationController;
import com.ywwl.customer.center.modules.common.account.mapper.SysOperationControllerMapper;
import com.ywwl.customer.center.modules.common.account.service.SysOperationControllerService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_operation_controller(用户操作控制)】的数据库操作Service实现
 * @createDate 2023-12-22 16:07:58
 */
@Service
public class SysOperationControllerServiceImpl extends ServiceImpl<SysOperationControllerMapper, SysOperationController>
        implements SysOperationControllerService {

    /**
     * 是否需要展示
     *
     * @param userId userId
     * @return 展示
     */
    public Boolean doYouNeedToShow(Long userId, String type) {
        final LambdaQueryWrapper<SysOperationController> query =
                Wrappers.<SysOperationController>lambdaQuery()
                        .eq(SysOperationController::getUserId, userId)
                        .eq(SysOperationController::getType, type);
        return !baseMapper.exists(query);
    }

    /**
     * 查看完结
     *
     * @param userId userId
     */
    public void viewTheEnd(Long userId, String type) {
        // 检测是否存在，不存在则插入
        if (doYouNeedToShow(userId, type)) {
            final SysOperationController operation = SysOperationController
                    .builder()
                    .type(type)
                    .userId(userId)
                    .build();
            save(operation);
        }
    }

}




