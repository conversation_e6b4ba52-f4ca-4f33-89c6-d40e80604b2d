package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 取消记录DTO
 *
 * <AUTHOR>
 * @since 2023/10/26 10:09
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeOrderCancelRecordGetListDTO extends GeneralDTO {

    /**
     * data
     */
    private List<DataDTO> data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运单号
         */
        private String waybillNumber;
        /**
         * 取消原因
         */
        private String note;
        /**
         * 类型
         */
        private Integer type;
        /**
         * 时间
         */
        private String createTime;
    }
}
