package com.ywwl.customer.center.modules.amazon.result;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: dinghy
 * @date: 2022/9/7 13:48
 */
@NoArgsConstructor
@Data
public class AmazonOrderDetail {


    /**
     * 接口是否异常
     */
    @JsonProperty("HasError")
    private Boolean hasError;
    /**
     * 运单号
     */
    private String trackNumber;
    /**
     * 异常信息
     */
    @JsonProperty("Message")
    @JsonAlias({"ErrorMsg"})
    private String message;
    /**
     * 返回数据集合
     */
    @JsonProperty("Data")
    private List<DataDTO> data;

    /**
     * 返回数据集合Item
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 创建时间
         */
        @JsonProperty("CreateTime")
        private Integer createTime;

        /**
         * 创建时间
         */
        public String getCreateTime() {
            return DateUtil.format(DateUtil.date(createTime), DatePattern.NORM_DATETIME_PATTERN);
        }

        /**
         * isEncryt
         */
        @JsonProperty("IsEncryt")
        private Boolean isEncryt;
        /**
         * parcelHasbattery
         */
        @JsonProperty("Parcel_HasBattery")
        private Boolean parcelHasBattery;
        /**
         * isFillTaxNumber
         */
        @JsonProperty("IsFillTaxNumber")
        private Boolean isFillTaxNumber;
        /**
         * paidWithWish
         */
        @JsonProperty("PaidWithWish")
        private Boolean paidWithWish;
        /**
         * 固定false
         */
        @JsonProperty("RequireLabel")
        private Boolean requireLabel;
        /**
         * 固定false
         */
        @JsonProperty("IsTest")
        private Boolean isTest;
        /**
         * returninfoReturnactionoutcountry
         */
        @JsonProperty("ReturnInfo_ReturnActionOutCountry")
        private Integer returnInfoReturnActionOutCountry;
        /**
         * returninfoReturnactionincountry
         */
        @JsonProperty("ReturnInfo_ReturnActionInCountry")
        private Integer returnInfoReturnActionInCountry;
        /**
         * 产品号
         */
        @JsonProperty("Stype")
        private Integer stype;
        /**
         * 产品号
         */
        private String productName;
        /**
         * 揽收类型：1、上门揽收，2、快递送货，3、卖家自送
         */
        @JsonProperty("PickupType")
        private Integer pickupType;
        /**
         * carryType
         */
        @JsonProperty("CarryType")
        private Integer carryType;
        /**
         * carrierCode
         */
        @JsonProperty("CarrierCode")
        private Integer carrierCode;
        /**
         * status
         */
        @JsonProperty("Status")
        private Integer status;
        /**
         * 申报价值
         */
        @JsonProperty("Parcel_DeclareValue")
        private String parcelDeclareValue;
        /**
         * 重量g
         */
        @JsonProperty("Parcel_Weight")
        private String parcelWeight;
        /**
         * 唯一值
         */
        @JsonProperty("OrderId")
        private Long orderId;
        /**
         * productList
         */
        @JsonProperty("ProductList")
        private List<ProductListDTO> productList;
        /**
         * parcelCategorylocal
         */
        @JsonProperty("Parcel_CategoryLocal")
        private String parcelCategoryLocal;
        /**
         * parcelCategoryen
         */
        @JsonProperty("Parcel_CategoryEn")
        private String parcelCategoryEn;
        /**
         * 币种
         */
        @JsonProperty("Parcel_PriceCurrency")
        private String parcelPriceCurrency;
        /**
         * parcelPriceunit
         */
        @JsonProperty("Parcel_PriceUnit")
        private String parcelPriceUnit;
        /**
         * 重量
         */
        @JsonProperty("Parcel_WeightUnit")
        private String parcelWeightUnit;
        /**
         * parcelDescriptionlocal
         */
        @JsonProperty("Parcel_DescriptionLocal")
        private String parcelDescriptionLocal;
        /**
         * parcelDescriptionen
         */
        @JsonProperty("Parcel_DescriptionEn")
        private String parcelDescriptionEn;
        /**
         * 揽收地址中文信息：地址2
         */
        @JsonProperty("Pickup_AddressLocal_StreetAddress2")
        private String pickupAddressLocalStreetAddress2;
        /**
         * 揽收地址中文信息：地址1
         */
        @JsonProperty("Pickup_AddressLocal_StreetAddress1")
        private String pickupAddressLocalStreetAddress1;
        /**
         * 揽收地址中文信息：名称
         */
        @JsonProperty("Pickup_AddressLocal_Name")
        private String pickupAddressLocalName;
        /**
         * 揽收地址中文信息：城市
         */
        @JsonProperty("Pickup_AddressLocal_City")
        private String pickupAddressLocalCity;
        /**
         * 揽收地址中文信息：区/州
         */
        @JsonProperty("Pickup_AddressLocal_Province")
        private String pickupAddressLocalProvince;
        /**
         * 揽收地址英文信息：地址1
         */
        @JsonProperty("Pickup_AddressEn_StreetAddress1")
        private String pickupAddressEnStreetAddress1;
        /**
         * 揽收地址英文信息：名称
         */
        @JsonProperty("Pickup_AddressEn_Name")
        private String pickupAddressEnName;
        /**
         * 揽收地址英文信息：城市
         */
        @JsonProperty("Pickup_AddressEn_City")
        private String pickupAddressEnCity;
        /**
         * 揽收地址英文信息：区/州
         */
        @JsonProperty("Pickup_AddressEn_Province")
        private String pickupAddressEnProvince;
        /**
         * 揽收地址信息：国家编码
         */
        @JsonProperty("Pickup_CountryCode")
        private String pickupCountryCode;
        /**
         * 揽收地址信息：邮编
         */
        @JsonProperty("Pickup_Zipcode")
        private String pickupZipcode;
        /**
         * 揽收地址信息：电子邮箱
         */
        @JsonProperty("Pickup_Email")
        private String pickupEmail;
        /**
         * 揽收地址信息：电话
         */
        @JsonProperty("Pickup_Mobile")
        private String pickupMobile;
        /**
         * 揽收地址信息：手机
         */
        @JsonProperty("Pickup_Phone")
        private String pickupPhone;
        /**
         * 收件人英文信息：地址2
         */
        @JsonProperty("Receiver_AddressEn_StreetAddress2")
        private String receiverAddressEnStreetAddress2;
        /**
         * 收件人英文信息：地址1
         */
        @JsonProperty("Receiver_AddressEn_StreetAddress1")
        private String receiverAddressEnStreetAddress1;
        /**
         * 收件人英文信息：名称
         */
        @JsonProperty("Receiver_AddressEn_Name")
        private String receiverAddressEnName;
        /**
         * 收件人英文信息：城市
         */
        @JsonProperty("Receiver_AddressEn_City")
        private String receiverAddressEnCity;
        /**
         * 收件人英文信息：区/洲
         */
        @JsonProperty("Receiver_AddressEn_Province")
        private String receiverAddressEnProvince;
        /**
         * 收件人英文信息：税号
         */
        @JsonProperty("Receiver_Tax_Number")
        private String receiverTaxNumber;
        /**
         * 收件人国家编码
         */
        @JsonProperty("Receiver_CountryCode")
        private String receiverCountryCode;
        /**
         * 收件人国家
         */
        private String countryName;
        /**
         * 收件人邮编
         */
        @JsonProperty("Receiver_Zipcode")
        private String receiverZipcode;
        /**
         * 收件人邮编
         */
        @JsonProperty("Receiver_Email")
        private String receiverEmail;
        /**
         * 收件人电话
         */
        @JsonProperty("Receiver_Mobile")
        private String receiverMobile;
        /**
         * 收件人手机
         */
        @JsonProperty("Receiver_Phone")
        private String receiverPhone;
        /**
         * 收件人公司名称
         */
        @JsonProperty("Receiver_Company")
        private String receiverCompany;
        /**
         * 发件人中文信息：地址2
         */
        @JsonProperty("Sender_AddressLocal_StreetAddress2")
        private String senderAddressLocalStreetAddress2;
        /**
         * 发件人中文信息：地址1
         */
        @JsonProperty("Sender_AddressLocal_StreetAddress1")
        private String senderAddressLocalStreetAddress1;
        /**
         * 发件人中文信息：名称
         */
        @JsonProperty("Sender_AddressLocal_Name")
        private String senderAddressLocalName;
        /**
         * 发件人中文信息：城市
         */
        @JsonProperty("Sender_AddressLocal_City")
        private String senderAddressLocalCity;
        /**
         * 发件人中文信息：区/州
         */
        @JsonProperty("Sender_AddressLocal_Province")
        private String senderAddressLocalProvince;
        /**
         * 发件人英文信息：地址2
         */
        @JsonProperty("Sender_AddressEn_StreetAddress2")
        private String senderAddressEnStreetAddress2;
        /**
         * 发件人英文信息：地址1
         */
        @JsonProperty("Sender_AddressEn_StreetAddress1")
        private String senderAddressEnStreetAddress1;
        /**
         * 发件人英文信息：名称
         */
        @JsonProperty("Sender_AddressEn_Name")
        private String senderAddressEnName;
        /**
         * 发件人英文信息：城市
         */
        @JsonProperty("Sender_AddressEn_City")
        private String senderAddressEnCity;
        /**
         * 发件人英文信息：区/州
         */
        @JsonProperty("Sender_AddressEn_Province")
        private String senderAddressEnProvince;
        /**
         * 发件人英文信息：税号
         */
        @JsonProperty("Sender_Tax_Number")
        private String senderTaxNumber;
        /**
         * 发件人国家编码
         */
        @JsonProperty("Sender_CountryCode")
        private String senderCountryCode;
        /**
         * 发件人邮编
         */
        @JsonProperty("Sender_Zipcode")
        private String senderZipcode;
        /**
         * 发件人电子邮箱
         */
        @JsonProperty("Sender_Email")
        private String senderEmail;
        /**
         * 发件人电话
         */
        @JsonProperty("Sender_Mobile")
        private String senderMobile;
        /**
         * 发件人手机
         */
        @JsonProperty("Sender_Phone")
        private String senderPhone;
        /**
         * 平台客户名称
         */
        @JsonProperty("PaymentAccount_ContactName")
        private String paymentAccountContactName;
        /**
         * 平台客户电话
         */
        @JsonProperty("PaymentAccount_PhoneNumber")
        private String paymentAccountPhoneNumber;
        /**
         * 平台客户电子邮箱
         */
        @JsonProperty("PaymentAccount_Email")
        private String paymentAccountEmail;
        /**
         * 平台客户名称
         */
        @JsonProperty("PaymentAccount_Username")
        private String paymentAccountUsername;
        /**
         * 平台客户号
         */
        @JsonProperty("PaymentAccount_UserId")
        private String paymentAccountUserid;
        /**
         * 订单号
         */
        @JsonProperty("LogisticsOrderCode")
        private String logisticsOrderCode;
        /**
         * sname
         */
        @JsonProperty("Sname")
        private String sName;
        /**
         * 平台订单时间
         */
        @JsonProperty("OrderTime")
        private String orderTime;
        /**
         * 运单号
         */
        @JsonProperty("TrackingId")
        private String trackingId;
        /**
         * apiKey
         */
        @JsonProperty("ApiKey")
        private String apiKey;
        /**
         * omsUrl
         */
        @JsonProperty("OmsUrl")
        private String omsUrl;
        /**
         * 平台名称
         */
        @JsonProperty("PlatformName")
        private String platformName;
        // 敦煌线下客户号
        @JsonProperty("CustomCode")
        private String customerCode;

        /**
         * Item
         */
        @NoArgsConstructor
        @Data
        public static class ProductListDTO {
            /**
             * 是否含电
             */
            @JsonProperty("HasBattery")
            private Boolean hasBattery;
            /**
             * 数量
             */
            @JsonProperty("Quantity")
            private Integer quantity;
            /**
             * 数量
             */
            @JsonProperty("HsCode")
            private String hsCode;
            /**
             * productId
             */
            @JsonProperty("ProductId")
            private Integer productId;
            /**
             * 重量
             */
            @JsonProperty("Weight")
            private String weight;
            /**
             * 申报单价
             */
            @JsonProperty("Value")
            private String value;
            /**
             * 产品数量
             */
            @JsonProperty("Piece")
            private Integer piece;
            /**
             * 产品链接
             */
            @JsonProperty("ProductUrl")
            private String productUrl;
            /**
             * 海关编码
             */
            @JsonProperty("Sku")
            private String sku;
            /**
             * categoryLocal
             */
            @JsonProperty("CategoryLocal")
            private String categoryLocal;
            /**
             * categoryEn
             */
            @JsonProperty("CategoryEn")
            private String categoryEn;
            /**
             * 产品名称（英文）
             */
            @JsonProperty("DescriptionLocal")
            private String descriptionLocal;
            /**
             * 产品名称（中文）
             */
            @JsonProperty("DescriptionEn")
            private String descriptionEn;
        }
    }
}
