package com.ywwl.customer.center.modules.business.annotation;

import cn.hutool.core.collection.CollectionUtil;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.dto.BusinessOrderEnumDTO.DataDTO.ValueDTO;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class BusinessTypeValidator implements ConstraintValidator<BusinessTypeVerify, String> {

    /**
     * 商业快递枚举类型
     */
    private CmccItemEnum item;

    @Override
    public void initialize(BusinessTypeVerify constraintAnnotation) {
        item = constraintAnnotation.item();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<ValueDTO> orderTypeList =
                EJFUtil.cmccService.getBusinessOrderTypeEnum(item, value, ValueDTO::getCode);
        // 仅Code校验
        return CollectionUtil.isNotEmpty(orderTypeList);
    }

}
