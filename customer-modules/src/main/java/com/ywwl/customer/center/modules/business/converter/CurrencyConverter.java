package com.ywwl.customer.center.modules.business.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.ywwl.customer.center.modules.ejf.entity.Currency;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;

import java.util.Objects;

/**
 * 包裹类型转换器
 *
 * <AUTHOR>
 * @since 2023/10/20 15:17
 **/
public class CurrencyConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {

        final Currency currency = EJFUtil.baseInfoService.getCurrencyById(value);
        if (Objects.nonNull(currency)) {
            return new WriteCellData<>(currency.getName());
        }
        return new WriteCellData<>(value);
    }


}
