package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/3/3 17:04
 */
@Data
public class SaveCustomerTypeDTO {
    private String no;
    @NotNull(message = "[客户类型]不能为空")
    private Integer customerType;
    @NotNull(message = "[客户地域]不能为空")
    private Integer customerArea;
    private String userCode;
    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;
}
