package com.ywwl.customer.center.modules.amazon.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新打印状态结果
 *
 * <AUTHOR>
 * @since 2024/01/18 11:07
 **/
@NoArgsConstructor
@Data
public class UpdatePrintStatusResultDTO {

    /**
     * 是否有错误
     */
    @JSONField(name = "HasError")
    private Boolean hasError;
    /**
     * 结果
     */
    @JSONField(name = "Data")
    private String data;

}
