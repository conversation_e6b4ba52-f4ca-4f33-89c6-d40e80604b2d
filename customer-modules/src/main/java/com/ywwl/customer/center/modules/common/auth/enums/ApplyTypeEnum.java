package com.ywwl.customer.center.modules.common.auth.enums;

import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.Getter;

/**
 * @author: dinghy
 * @date: 2023/3/30 15:46
 */
@Getter
public enum ApplyTypeEnum {
    NEW(0,"新客户申请"),
    /** 1, "补齐证件照" */
    CHANGE_ID_CARD_ATTACH(1, "补齐证件照"),

    /** 2, "申请完成" */
    APPLY_SUCCESS(2, "申请完成"),

    /** 3, "变更客户名" */
    CHANGE_CUSTOMER_NAME(3, "变更客户名"),

    /** 4, "自主变更" */
    AUTONOMY_CHANGE(4, "自主变更");
    ;
    private Integer value;
    private String desc;

    ApplyTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ApplyTypeEnum getCustomerApplyTyeEnum(Integer value){
        for (ApplyTypeEnum applyTypeEnum : ApplyTypeEnum.values()) {
            if (applyTypeEnum.getValue().equals(value)) {
                return applyTypeEnum;
            }
        }
        throw new BusinessException(ResponseCode.PORTAL_7002);
    }
}
