package com.ywwl.customer.center.modules.business.dto;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.modules.international.dto.PLMCalcPriceResultDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商业快递费用试算
 *
 * <AUTHOR>
 * @since 2023/10/12 09:59
 **/
@NoArgsConstructor
@Data
public class ManyPieceDTO {

    /**
     * 结果集
     */
    private Boolean result;
    /**
     * 试算状态（200：成功，其他：失败）
     */
    private String code;
    /**
     * 试算状态描述
     */
    private String message;
    /**
     * 唯一版本号
     */
    private String version;
    /**
     * 返回数据
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 参考时效
         */
        private String referAging;
        /**
         * 详细产品信息
         */
        private JSONArray topcRemarks;
        /**
         * 详细产品信息
         */
        private JSONObject remark;
        /**
         * 产品信息
         */
        private PLMCalcPriceResultDTO.DataDTO productDetail;
        /**
         * 产品code
         */
        private String productCode;
        /**
         * 产品名称
         */
        private String productName;
        /**
         * 报价单模板
         */
        private String priceUdo;
        /**
         * 报价单编号
         */
        private String priceCode;
        /**
         * 报价级别编号
         */
        private String priceLevelCode;
        /**
         * 报价级别名称
         */
        private String priceLevelName;
        /**
         * 干线报价编号
         */
        private String transferPrice;
        /**
         * 报价单重量单位
         */
        private String calcWeightUnit;
        /**
         * 报价单币种
         */
        private String calcCurrency;
        /**
         * 是否计泡
         */
        private Boolean isThrowWeight;
        /**
         * 是否免泡
         */
        private Boolean isFreeThrowWeight;
        /**
         * 泡比系数
         */
        private BigDecimal throwWeightFactor;
        /**
         * 计泡系数
         */
        private Integer throwFactor;
        /**
         * 汇率
         */
        private Integer fxRate;
        /**
         * 燃油费率
         */
        private BigDecimal fuelRate;
        /**
         * 干线计费重量
         */
        private BigDecimal trunkMergeWeight;
        /**
         * 计费重量（不包含最小计重：实重或泡重）
         */
        private BigDecimal totalWeight;
        /**
         * 计费重量（包含最小计重：实重或泡重）
         */
        private BigDecimal calcWeight;
        /**
         * 计费重量G（包含最小计重：实重或泡重）
         */
        private Integer calcWeightG;
        /**
         * 计费重量G（不包含最小计重：实重或泡重）
         */
        private Integer totalWeightG;
        /**
         * 干线计费重量G
         */
        private Integer trunkMergeWeightG;
        /**
         * 折后总金额（报价单币种）
         */
        private BigDecimal totalMoney;
        /**
         * 折后总金额（人民币）
         */
        private BigDecimal cnyTotalMoney;
        /**
         * 分区编号（分区编号不存在为null）
         */
        private String zone;
        /**
         * 计费项详情
         */
        private List<ExpenseItemsDTO> expenseItems;
        /**
         * 服务费详情
         */
        private ServiceFeeDTO serviceFee;
        /**
         * 返点金额（报价单币种）
         */
        private RebateDTO rebate;

        /**
         * ServiceFeeDTO
         */
        @NoArgsConstructor
        @Data
        public static class ServiceFeeDTO {
            /**
             * 服务费金额（报价单币种）
             */
            private BigDecimal serviceFee;
            /**
             * 服务费金额（人民币）
             */
            private BigDecimal cnyServiceFee;
            /**
             * 服务费明细
             */
            private List<ServiceFeeDetailsDTO> serviceFeeDetails;

            /**
             * ServiceFeeDetailsDTO
             */
            @NoArgsConstructor
            @Data
            public static class ServiceFeeDetailsDTO {
                /**
                 * 服务号(应付必填)
                 */
                private String serviceCode;
                /**
                 * 服务费金额（报价单币种）
                 */
                private BigDecimal serviceFee;
                /**
                 * 服务费金额（人民币）
                 */
                private BigDecimal cnyServiceFee;
            }
        }

        /**
         * RebateDTO
         */
        @NoArgsConstructor
        @Data
        public static class RebateDTO {
            /**
             * 返点金额（报价单币种）
             */
            private BigDecimal rebate;
            /**
             * 返点金额（人民币）
             */
            private BigDecimal cnyRebate;
        }

        /**
         * ExpenseItemsDTO
         */
        @NoArgsConstructor
        @Data
        public static class ExpenseItemsDTO {
            /**
             * 计费项编号
             */
            private String itemCode;
            /**
             * 计费项名称
             */
            private String itemName;
            /**
             * 公式编号（仅附加费显示）
             */
            private String uFormula;
            /**
             * 折扣值
             */
            private Object discountRate;
            /**
             * 是否折扣
             */
            private Boolean isDiscount;
            /**
             * 折前金额（报价币种）
             */
            private BigDecimal beforeDiscountMoney;
            /**
             * 是否返点
             */
            private Boolean isRebate;
            /**
             * 折后金额（报价币种）
             */
            private BigDecimal money;
            /**
             * 折后金额（人民币）
             */
            private BigDecimal cnyMoney;
            /**
             * 申报价值汇率
             */
            private Object declaredRate;
            /**
             * 计费项详情
             */
            private List<SubMoneyDTO> subMoney;

            /**
             * 计费项详情
             */
            @NoArgsConstructor
            @Data
            public static class SubMoneyDTO {
                /**
                 * 子运单业务单号
                 */
                private String subWaybillNumber;
                /**
                 * 金额
                 */
                private Integer money;
            }
        }
    }

}
