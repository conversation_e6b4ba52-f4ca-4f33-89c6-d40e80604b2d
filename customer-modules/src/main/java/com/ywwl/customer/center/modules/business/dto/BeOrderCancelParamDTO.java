package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 取消运单参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:18
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class BeOrderCancelParamDTO {

    /**
     * 制单账号
     */
    @NotBlank(message = "制单账号不能为空")
    private String userId;
    /**
     * 运单号
     */
    private String waybillNumber;
    /**
     * 备注
     */
    private String note;
    /**
     * 平台
     */
    private String platform;

}
