package com.ywwl.customer.center.modules.common.auth.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 客户地域类型
 * @date 2023/2/21 15:00
 **/
@Getter
public enum CustomerAreaEnum {
    MAIN_LAND(0, "大陆"),
    OVERSEA(1, "境外"),
    GANG_AO_TAI(3, "港澳台"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    CustomerAreaEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomerAreaEnum getCustomerAreaEnum(Integer code) {
        for (CustomerAreaEnum value : CustomerAreaEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new BusinessException(ResponseCode.PORTAL_7005);
    }
}
