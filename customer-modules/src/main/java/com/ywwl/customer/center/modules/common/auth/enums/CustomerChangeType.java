package com.ywwl.customer.center.modules.common.auth.enums;

/**
 * 客户变更类型枚举类
 */
public enum CustomerChangeType {

	/**
	 * 0, "补齐证件照"
	 */
	CHANGE_ID_CARD_ATTACH(0, "补齐证件照"),

	/**
	 * 1, "个人转企业"
	 */
	PERSON_COMPANY(1, "个人转企业"),

	/**
	 * 2, "境内转境外"
	 */
	DOMESTIC_TO_OVERSEAS(2, "境内转境外"),

	/**
	 * 3, "变更客户名"
	 */
	CHANGE_CUSTOMER_NAME(3, "变更客户名"),

	/**
	 * 4, "补齐资料"
	 */
	MERCHANT_INFO(4, "补齐资料");

	private  Integer code;
	private String desc;

	CustomerChangeType(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String getDesc() {
		return desc;
	}

	public Integer getCode() {
		return code;
	}

	public static String getDescByCode(Integer code){
		for (CustomerChangeType value : CustomerChangeType.values()) {
			if(value.getCode().equals(code)){
				return value.getDesc();
			}
		}
		return null;
	}

}
