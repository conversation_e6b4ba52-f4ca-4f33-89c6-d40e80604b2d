package com.ywwl.customer.center.modules.common.auth.vo;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: dinghy
 * @date: 2023/3/2 11:25
 */
@Data
public class CustomerVo {
    /*** 用户代码 */
    private String userCode;

    /*** 客户流水号 */
    private String no;

    /*** 客户区域 */
    private Integer customerArea;

    /*** 客户类型 */
    private Integer customerType;

    /*** 证件类型 */
    private Integer idCardType;

    /*** 个人/办理人姓名 */
    private String personName;

    /*** 个人/办理人证件号 */
    private String personCard;

    /*** 企业名称 */
    private String companyName;

    /*** 营业执照号/注册号 */
    private String registerNumber;

    /*** 法人姓名 */
    private String corporateName;

    /*** 证件正面照 */
    private String cardAttach;

    /*** 证件反面照 */
    private String cardBackAttach;

    /*** 证件手持照 */
    private String cardHoldAttach;

    /*** 证件照有效期 */
    private LocalDateTime cardValidityPeriod;

    /*** 审核状态 */
    private Integer auditStatus;

    /*** 申请类型 */
    private Integer applyType;

    /*** 拒绝原因 */
    private String refuseReason;

    /*** 审核类型 0:在线认证, 1: 人工审核 */
    private Integer auditType;
    /**
     * <AUTHOR>
     * @description 是否认证：0未认证,1已认证
     * @date 2023/3/30 16:03
     **/
    private Integer authStatus;

    /**
     * <AUTHOR>
     * @description 新客户名
     * @date 2023/5/18 14:33
     **/
    private String merchantNameNew;
    /**
     * <AUTHOR>
     * @description 新客户类型
     * @date 2023/5/18 14:33
     **/
    private Integer customerTypeNew;
    /**
     * <AUTHOR>
     * @description 新客户区域
     * @date 2023/5/18 14:36
     **/
    private Integer customerAreaNew;
    // 类型变更
    private Integer changeType;

    /*** 法人手机号 */
    private String corporatePhone;
    /*** 法人身份证号 */
    private String corporateCard;

    private String bankCode;


    private String bankName;
    /**
     * 银行卡号
     */
    private String bankCard;

    /**
     * @author: dinghy
     * @createTime: 2024/2/28 9:34
     * @description: 境外客户付款账号证明
     */
    private String bankAccountProof;
    /**
     * @author: dinghy
     * @createTime: 2024/4/2 17:16
     * @description: 处于流程中限制客户操作截止日
     */
    private LocalDateTime unLimitTime;

    // 通行证号码
    private String passCard;
    // 通行证件正面
    private String passAttach;
    // 通行证件反面
    private String passBackAttach;
    // 通行证件有效期
    private LocalDateTime passValidityPeriod;

    /*** 经办人企业证明 */
    private String personCompanyProve;
    /*** 经办人是否为法人 0:否,1:是 */
    private Integer personIsCorporate;

    /*** 法人企业证明 */
    private String corporateCompanyProve;

    /*** 燕文后台使用人是否为法人 0:否,1:是 */
    private Integer userIsCorporate;

    /*** 燕文后台使用人证明 */
    private String userProve;

    /*** 燕文后台使用人授权委托书 */
    private String userMandate;
    /** 商户属性 **/
    private String merchantProperty;

    /*** 境外个人-本国身份证号码 */
    private String homeCard;

    /*** 境外个人-本国身份证正面照 */
    private String homeAttach;

    /*** 境外个人-本国身份证反面照 */
    private String homeBackAttach;

    /*** 境外个人-本国身份证有效期 */
    private LocalDateTime homeValidityPeriod;


    /*** 办理人区域类型 0: 大陆经办人、1: 境外经办人、3: 港澳台经办人 */
    private Integer personArea;

    /*** 法人区域类型 0: 大陆法人、1: 境外法人、3: 港澳台法人 */
    private Integer corporateArea;

}
