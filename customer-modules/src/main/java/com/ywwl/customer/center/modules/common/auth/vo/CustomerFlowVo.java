package com.ywwl.customer.center.modules.common.auth.vo;

import lombok.Data;

/**
 * @author: dinghy
 * @date: 2023/9/6 10:28
 */
@Data
public class CustomerFlowVo {
    private String no;
    private String userCode;
    private Integer customerType;
    private Integer customerArea;
    private Integer idCardType;
    private String personName;
    private String personCard;
    private String cardAttach;
    private String cardBackAttach;
    private String cardHoldAttach;
    private String cardValidityPeriod;
    private String companyName;
    private String registerNumber;
    private String corporateName;
    private String corporatePhone;
    private String corporateCard;
    private String registeredProvinceCode;
    private String registeredProvinceName;
    private String registeredCityCode;
    private String registeredCityName;
    private String registeredAreaCode;
    private String registeredAreaName;
    private String registeredAddress;
    private Integer authStatus;
    private Integer auditType;
    private Integer auditStatus;
    private Integer applyType;
    private Integer changeType;
    private String refuseReason;
    // 2 待审核 3 审核通过 4 审核拒绝
    private Integer changeStatus;
    private Integer customerTypeNew;
    private Integer customerAreaNew;
    private String merchantNameNew;
    private String formerName;
    private String relationShip;
    private String transferAttach;
    private String transferReason;
    /**
     * 商户属性
     */
    private String merchantProperty;
    /**
     * 商户属性名称
     */
    private String merchantPropertyName;
}
