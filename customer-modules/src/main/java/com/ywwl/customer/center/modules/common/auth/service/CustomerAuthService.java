package com.ywwl.customer.center.modules.common.auth.service;


import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.modules.common.auth.dto.*;
import com.ywwl.customer.center.modules.common.auth.vo.*;

import java.util.List;

public interface CustomerAuthService {
    /**
     * <AUTHOR>
     * @description 根据客户流水号查询客户认证信息
     * @date 2023/3/3 10:54
     **/
    String getCustomerAuthStatus(String no);

    /**
     * <AUTHOR>
     * @description 获取客户认证信息
     * @date 2023/3/3 17:18
     **/
    CustomerAuthVo getCustomerAuthInfo(String merchantNo);
     /**
      * <AUTHOR>
      * @description 根据用户代码查询客户认证信息
      * @date 2023/7/5 16:03
      **/
    CustomerAuthVo getCustomerInfoByUserCode(String userCode);

    /**
     * <AUTHOR>
     * @description 保存客户类型
     * @date 2023/3/3 17:17
     **/
    void saveCustomerType(SaveCustomerTypeDTO saveCustomerTypeDTO);

    /**
     * <AUTHOR>
     * @description 保存个人申请
     * @date 2023/3/6 10:12
     **/
    PersonAuthVo savePersonApply(SavePersonApplyDTO savePersonApplyDTO);

    /**
     * <AUTHOR>
     * @description 获取人脸识别认证地址
     * @date 2023/3/6 10:12
     **/
    String getFaceAuthUrl(QueryFaceAuthDTO queryFaceAuthDTO);

    /**
     * <AUTHOR>
     * @description 获取人脸认证结果:0是发起校验流程未认证,1是成功,2是失败
     * @date 2023/3/6 11:03
     **/
    Integer getFaceAuthResult(QueryFaceAuthDTO queryFaceAuthDTO);

    /**
     * <AUTHOR>
     * @description 发起银行卡认证
     * @date 2023/3/6 11:22
     **/
    void createPersonBankAuth(String merchantNo);

    /**
     * <AUTHOR>
     * @description 校验个人银行预留手机号验证码
     * @date 2023/3/6 13:45
     **/
    void checkPersonBankMobile(CheckBankMobileDTO checkBankMobileDTO);

    /**
     * <AUTHOR>
     * @description 提交企业代办人认证
     * @date 2023/3/6 14:14
     **/
    PersonAuthVo saveCompanyAgentAuth(SaveCompanyAgentAuthDTO saveCompanyAgentAuthDTO);

    /**
     * <AUTHOR>
     * @description 保存企业认证
     * @date 2023/3/6 14:40
     **/
    CompanyAuthVo saveCompanyApply(SaveCompanyApplyDTO companyAuthDTO);

    /**
     * <AUTHOR>
     * @description 根据关键字查询支行列表
     * @date 2023/3/6 15:40
     **/
    List<String> getBranchBankList(QueryBranchBankDTO queryBranchBankDTO);

    /**
     * <AUTHOR>
     * @description 保存企业银行卡
     * @date 2023/3/6 16:20
     **/
    void saveCompanyBank(SaveCompanyBankDTO saveCompanyBankDTO);

    /**
     * <AUTHOR>
     * @description 企业银行卡金额校验
     * @date 2023/3/6 16:47
     **/
    void verifyCompanyBankAmount(VerifyCompanyBankAmountDTO verifyCompanyBankAmountDTO);

    /**
     * <AUTHOR>
     * @description 查询企业金额打款状态
     * @date 2023/3/7 11:28
     **/
    Integer getCompanyPayState(String merchantNo);

    /**
     * <AUTHOR>
     * @description 向公共crm推送通知
     * @date 2023/3/7 18:10
     **/
    void pushFaceAuthNotifyMessage(JSONObject notifyMessage);

    /**
     * <AUTHOR>
     * @description 向公共crm推送企业打款通知
     * @date 2023/3/8 9:30
     **/
    void pushCompanyBankMoneyNotify(JSONObject notifyMessage);

    /**
     * <AUTHOR>
     * @description 重新发起企业认证
     * @date 2023/3/13 17:42
     **/
    void reCompanyAuth(String merchantNo);

    /**
     * <AUTHOR>
     * @description 合同签署回调
     * @date 2023/3/17 14:12
     **/
    void pushContractNotify(JSONObject jsonObject);

    /**
     * <AUTHOR>
     * @description 提交补齐证件照
     * @date 2023/4/4 11:23
     **/
    void submitCustomerAttach(CardAttachDTO cardAttachDTO);

    /**
     * <AUTHOR>
     * @description 校验客户唯一性
     * @date 2023/5/13 9:39
     **/
    void validateCustomerUnique(ValidateCustomerUniqueDTO validateCustomerUniqueDTO);

    /**
     * <AUTHOR>
     * @description 获取客户名
     * @date 2023/6/16 11:16
     **/
    String getCustomerName(String merchantNo);
    /**
     * <AUTHOR>
     * @description 校验是否客户已经实名认证
     * @date 2023/8/4 14:46
     **/
    Integer checkCustomerRealAuth(String no);
    /**
     * <AUTHOR>
     * @description 发起补齐资料
     * @date 2023/8/7 18:13
     **/
    void completeData(String userCode,String userName);
    /**
     * <AUTHOR>
     * @description 变更客户名
     * @date 2023/9/5 15:26
     **/
    void changeCustomerName(ChangeCustomerNameDTO changeCustomerNameDTO);
    /**
     * <AUTHOR>
     * @description 获取变更客户名信息
     * @date 2023/9/6 10:47
     **/
    ChangeCustomerNameVo getCustomerChangeInfo(String userCode);
    /**
     * @author: dinghy 
     * @createTime: 2024/4/3 9:27
     * @description: 查询客户的认证状态
     */
    CustomerAuthStateVo getAuthState(String userCode);
    /**
     * @author: dinghy
     * @createTime: 2024/6/5 15:04
     * @description: 发起补齐证件照
     */
    Integer completeAttach(String userCode, String loginName);
    /**
     * @author: dinghy
     * @createTime: 2024/7/10 15:50
     * @description: 判断是否已经实名认证
     */
    boolean checkRealAuth(String userCode);
}
