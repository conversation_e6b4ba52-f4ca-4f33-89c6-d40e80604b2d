package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/21 16:16
 */
@NoArgsConstructor
@Data
public class QueryUnBillDTO {
    private String opCode= BillOpCodeEnum.UN_BILLED_BILL.value();
    private Integer businessType;
    private String userCode;
    private Integer currentPage;
    private Integer pageSize;
    private String startCalcTime;
    private String endCalcTime;
    private List<String> waybillNumbers;
    private String merchantCode;
    private Integer type=0;

    public void toExport(){
        setOpCode(BillOpCodeEnum.EXPORT_UNBILLED_DETAIL.value());
    }

    public void toExportTax(){
        setOpCode(BillOpCodeEnum.EXPORT_UNBILLED_TAX_DETAIL.value());
    }

    public String build() {
        StringBuffer sb = new StringBuffer();
        sb.append("?")
                .append("merchantCode=").append(merchantCode).append("&")
                .append("opCode=").append(opCode);
        if (StringUtils.isNotBlank(startCalcTime) && StringUtils.isNotBlank(endCalcTime)) {
            sb.append("&").append("startCalcTime=").append(startCalcTime).append("&")
                    .append("endCalcTime=").append(endCalcTime);
        }
        if (!CollectionUtils.isEmpty(waybillNumbers)) {
            for (String waybillNumber : waybillNumbers) {
                sb.append("&").append("waybillNumbers=").append(waybillNumber);
            }
        }
        return sb.toString();
    }
}
