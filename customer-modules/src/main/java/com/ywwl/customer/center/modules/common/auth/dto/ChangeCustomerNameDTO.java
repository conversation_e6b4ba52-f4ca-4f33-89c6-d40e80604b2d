package com.ywwl.customer.center.modules.common.auth.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/9/5 15:10
 */
@Data
public class ChangeCustomerNameDTO {
    private String userCode;
    @NotNull(message = "新客户类型不能为空")
    private Integer customerTypeNew;
    @NotNull(message = "新客户区域不能为空")
    private Integer customerAreaNew;
    @NotBlank(message = "新客户名称不能为空")
    private String merchantNameNew;
    @NotBlank(message = "与原商户关系不能为空")
    private String relationShip;
    @NotBlank(message = "转让原因不能为空")
    private String transferReason;
    private String transferAttach;
    private Integer applyType=4;
    private Integer changeType=3;
    private String smsCode;
    @NotBlank(message = "证件号不能为空")
    private String cardNumber;
}
