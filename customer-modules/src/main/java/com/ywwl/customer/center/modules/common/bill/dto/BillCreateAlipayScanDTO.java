package com.ywwl.customer.center.modules.common.bill.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
@Data
public class BillCreateAlipayScanDTO {
    // 全局唯一流水号（用于识别是否为同一次交易）
    private String merchantSerialNumber;
    // 付款平台
    private String paymentType="alipay";
    // 结算公司
    private String terminalId;
    // 订单类型
    private String orderType="balance";
    // 订单标题
    private String orderSubject="余额充值";
    // 业务账号
    private String merchantCode;
    // 币种
    private String payCurrency="CNY";
    // 金额
    @NotBlank(message = "金额不能为空")
    private String orderAmount;
    // 操作人
    private String operatorId;
}
