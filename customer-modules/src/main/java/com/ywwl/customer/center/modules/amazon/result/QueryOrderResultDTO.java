package com.ywwl.customer.center.modules.amazon.result;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: dinghy
 * @date: 2022/9/6 10:30
 */
@NoArgsConstructor
@Data
public class QueryOrderResultDTO {

    /**
     * 成功true,失败false
     */
    @JSONField(name = "Success")
    private Boolean success;
    /**
     * 异常信息描述
     */
    @JSONField(name = "Message")
    private String message;
    /**
     * 当前页
     */
    @JSONField(name = "PageIndex")
    private Integer pageIndex;
    /**
     * 总条数
     */
    @JSONField(name = "TotalCount")
    private Integer totalCount;
    /**
     * 查询结果集合
     */
    @JSONField(name = "Data")
    private List<DataDTO> data;

    /**
     * 查询结果集合Item
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运输子状态
         */
        @JSONField(name = "SubStatus")
        private String subStatus;
        /**
         * 产品号
         */
        @JSONField(name = "ProductCode")
        private Integer productCode;
        /**
         * 申报品名，中文
         */
        @JSONField(name = "DescriptionName")
        private String descriptionName;
        /**
         * 申报品名，英文
         */
        @JSONField(name = "DescriptionEn")
        private String descriptionEn;
        /**
         * 收件人姓名
         */
        @JSONField(name = "ReceiverName")
        private String receiverName;
        /**
         * 订单时间
         */
        @JSONField(name = "OrderTime")
        private String orderTime;
        /**
         * 平台账号
         */
        @JSONField(name = "PlatfromCustomCode")
        private String platfromCustomCode;
        /**
         * 目的国二字码
         */
        @JSONField(name = "DestinationCountry")
        private String destinationCountry;
        /**
         * 运单号
         */
        @JSONField(name = "TrackingId")
        private String trackingId;
        /**
         * 打印状态
         */
        @JSONField(name = "PrintStatus")
        private Integer printStatus;
        /**
         * 订单号
         */
        @JSONField(name = "LogisticsOrderCode")
        private String logisticsOrderCode;
        /**
         * 城市
         */
        @JSONField(name = "ReceiverCity")
        private String receiverCity;
    }

}
