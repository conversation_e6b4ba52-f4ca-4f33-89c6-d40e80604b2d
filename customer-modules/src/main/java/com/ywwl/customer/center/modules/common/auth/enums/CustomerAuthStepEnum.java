package com.ywwl.customer.center.modules.common.auth.enums;

public enum CustomerAuthStepEnum {
    AUTH_SUCCESS("0", "认证成功"),
    AUTH_INIT("1", "进入客户认证类型选择"),
    PERSON_AUTH("2","个人认证页面"),
    COMPANY_AGENT_AUTH("3", "进入企业代办人认证页面"),
    COMPANY_AUTH_SUBMIT("4", "企业信息填写页面"),
    COMPANY_BANK_SUBMIT("5", "企业银行卡信息填写页面"),
    COMPANY_VERIFY_SUBMIT("6", "企业金额信息填写"),
    COMPANY_OVERSEA__SUBMIT("7","非大陆企业信息填写页面"),
    DATA_ERROR("8","数据异常"),
    PERSON_AUDITING("9","境外个人页面"),
    COMPANY_AUDITING("10","境外企业待审核"),
    COMPLETE_CARD_ATTACH("11","补齐证件照页面"),
    MAINLAND_COMPANY_AUDIT("12","大陆企业线下审核"),
    ;
    private String value;
    private String desc;

    CustomerAuthStepEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    public String value() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
