package com.ywwl.customer.center.modules.business.enums;

import java.util.Arrays;
import java.util.Objects;

public enum OrderFormStatusEnum {

	UNDOCUMENTED("0", "未制单"),
	BILLED("1", "制单成功"),
	ORDER_FAILED("2", "制单失败"),
	OTHER("3", "其他");

	/**
	 * 编码
	 */
	private String code;
	private String description;

	OrderFormStatusEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public static OrderFormStatusEnum getOrderFormStatusEnum(String statusCode) {
		return Arrays.stream(OrderFormStatusEnum.values()).filter(status -> Objects.equals(statusCode, status.getCode())).findFirst().orElse(OTHER);
	}

}
