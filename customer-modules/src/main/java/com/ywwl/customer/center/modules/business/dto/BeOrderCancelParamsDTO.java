package com.ywwl.customer.center.modules.business.dto;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 取消运单参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:18
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class BeOrderCancelParamsDTO extends BeOrderCancelParamDTO {

    /**
     * 运单号
     */
    @Size(min = 1, message = "运单号不能为空")
    private List<String> waybillNumbers;

    /**
     * 获取取消运单参数
     *
     * @return 取消运单参数
     */
    public List<BeOrderCancelParamDTO> getBeOrderCancelParamDTO() {
        if (CollectionUtil.isNotEmpty(waybillNumbers)) {
            return waybillNumbers
                    .stream()
                    .distinct()
                    .map(v -> BeOrderCancelParamDTO.builder()
                            .waybillNumber(v)
                            .userId(getUserId())
                            .note(getNote())
                            .platform(getPlatform())
                            .build())
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
