package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: dinghy
 * @date: 2023/3/6 16:43
 */
@Data
public class VerifyCompanyBankAmountDTO {
    private String no;
    @NotBlank(message = "[打款金额]不能为空")
    private String amount;
    private String userCode;
    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;
}
