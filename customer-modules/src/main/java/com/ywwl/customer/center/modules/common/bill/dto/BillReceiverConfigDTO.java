package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.common.bill.enums.WaybillApiEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@NoArgsConstructor
@Data
public class BillReceiverConfigDTO {
    private String code= WaybillApiEnum.MERCHANT_ACCEPT_BILL_APPLY.value();
    // 商户号
    private String merchantCode;
    // 商户名称
    private String merchantName;
    @NotBlank(message = "生效日期不能为空")
    private String effectDate;
    @NotBlank(message = "请求地址不能为空")
    private String sendUrl;
    @NotBlank(message = "秘钥不能为空")
    private String secretkey;
    // 数据类型,目前支持TC01，TC02
    private List<String> transTypeJson;
    // 不需要表头认证，该参数不传
    private String headJson;
    // fat:调试 模式 pro: 正式模式
    private String model;
    @JSONField(serialize = false)
    private UserAgent currentUser;
    private String versionCode;
}
