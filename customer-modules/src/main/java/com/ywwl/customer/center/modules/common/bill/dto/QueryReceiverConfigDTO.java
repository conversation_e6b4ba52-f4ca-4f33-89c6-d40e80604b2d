package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.WaybillApiEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@NoArgsConstructor
@Data
public class QueryReceiverConfigDTO {
    private String opCode= WaybillApiEnum.QUERY_BILL_CONFIG.value();
    private String merchantCode;
    @NotBlank(message = "参数不能为空")
    private String model;
}
