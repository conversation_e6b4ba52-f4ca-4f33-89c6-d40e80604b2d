package com.ywwl.customer.center.modules.business.service;

import com.ywwl.customer.center.modules.business.dto.*;

import java.util.List;

/**
 * 商业快递服务类
 *
 * <AUTHOR>
 * @since 2023/10/10 15:03
 **/
public interface BusinessService {

    /**
     * 商业快递列表查询
     *
     * @param param 参数
     * @return 商业快递列表
     */
    InnerBeOrderPortalGetListFilterDTO getBusiness(InnerBeOrderPortalGetListFilterParamDTO param);


    /**
     * 获取国家列表
     *
     * @param warehouseCode 仓库号
     * @param countryId     国家id
     * @return 国家列表
     */
    BusinessChannelDTO getBaseChannel(String userId, String warehouseCode, String countryId);

    /**
     * 创建运单
     *
     * @param param 参数
     * @return 运单号
     */
    BeOrderCreateDTO createBusinessOrder(BusinessOrderDTO param);

    /**
     * 查询产品列表-根据燕文仓库编码、国家Id
     *
     * @param param 参数
     * @return 产品
     */
    InnerBeChannelGetListDTO getBaseChannel(InnerBeChannelGetListParamDTO param);

    /**
     * 取消运单
     *
     * @param param 参数
     * @return 取消运单结果
     */
    GeneralDTO cancelBusinessOrder(BeOrderCancelParamDTO param);

    /**
     * 运单详细
     *
     * @param waybillNumber 参数
     * @return 运单详细结果
     */
    BeOrderGetDTO getBusinessOrderDetail(String userId, String waybillNumber);

    /**
     * 打印标签
     *
     * @param param 参数
     * @return 打印标签结果
     */
    BeOrderLabelGetDTO businessOrderPrintLabel(BeOrderLabelGetParamDTO param);

    /**
     * 运单信息修改
     *
     * @param param 参数
     * @return 运单信息修改结果
     */
    GeneralDTO editBusinessOrder(BusinessOrderDTO param);

    /**
     * 运单状态统计
     *
     * @param param 参数
     * @return 运单状态统计结果
     */
    InnerBeOrderStatusGetListFilterDTO getBusinessOrderStatistics(InnerBeOrderPortalGetListFilterParamDTO param);

    /**
     * 确认发货
     *
     * @param waybillNumber 参数
     * @return 确认发货结果
     */
    GeneralDTO businessOrderConfirmDelivery(String userId ,String waybillNumber);

    /**
     * 取消确认发货
     *
     * @param param 参数
     * @return 取消确认发货结果
     */
    GeneralDTO businessOrderCancelDelivery(InnerBeOrderAcknowledgeCancelParamDTO param);

    /**
     * 运单详细-批量
     *
     * @param param 参数
     * @return 批量结果
     */
    BeOrderGetListDTO getBusinessOrderDetailBatch(InnerBeExpressOrderGetListParamDTO param);

    /**
     * 打印标签
     *
     * @param param 参数
     * @return 批量结果
     */
    InnerBeOrderLabelGetListDTO businessOrderPrintLabelBatch(InnerBeOrderLabelGetListParamDTO param);

    /**
     * 查询运单取消记录
     *
     * @param userId         制单账号
     * @param waybillNumbers 运单号
     * @return 查询运单取消记录结果
     */
    InnerBeOrderCancelRecordGetListDTO cancelBusinessOrderInfo(String userId, List<String> waybillNumbers);

    /**
     * 导入订单
     *
     * @param userId 参数
     * @param param 参数
     * @return 导入订单结果
     */
    InnerBeDraftOrderImportDTO importBusinessOrder(String userId, List<BusinessOrderDTO> param);

    /**
     * 草稿订单修改
     *
     * @param param 参数
     * @return 草稿订单修改结果
     */
    GeneralDTO editBusinessDraft(BusinessOrderDTO param);

    /**
     * 订单状态统计
     *
     * @param param 参数
     * @return 订单状态统计结果
     */
    InnerBeDraftOrderStatusGetListDTO getBusinessDraftStatistics(InnerBeDraftOrderGetListFilterParamDTO param);

    /**
     * 订单生成运单
     *
     * @param userId 参数
     * @param id     参数
     * @return 订单生成运单结果
     */
    InnerBeDraftOrderGenerateDTO businessDraftToOrder(String userId, String id);

    /**
     * 删除订单
     *
     * @param userId 参数
     * @param ids    参数
     * @return 删除订单结果
     */
    GeneralDTO deleteBusinessDraft(String userId, List<String> ids);

    /**
     * 订单列表查询
     *
     * @param param 参数
     * @return 订单列表查询结果
     */
    InnerBeDraftOrderGetListFilterDTO getBusinessDraft(InnerBeDraftOrderGetListFilterParamDTO param);

    /**
     * 订单详细
     *
     * @param userId 参数
     * @param id     参数
     * @return 订单详细结果
     */
    BeOrderGetDTO getBusinessDraftDetail(String userId, String id);

    /**
     * 已存在订单号查询
     *
     * @param param 参数
     * @return 已存在订单号查询结果
     */
    InnerBeOrderNumberGetListDTO getExistBusinessDraftNumber(InnerBeOrderNumberGetListParamDTO param);

    /**
     * 运价试算
     *
     * @param param 参数
     * @return 运价试算结果
     */
    ManyPieceDTO manyPiece(ManyPieceParamDTO param);
    /**
     * @author: dinghy
     * @createTime: 2025/1/23 10:29
     * @description: 根据产品查询国家
     */
    List<PLMCountryDTO.DataDTO> getCountryByProductCode(String code);
}
