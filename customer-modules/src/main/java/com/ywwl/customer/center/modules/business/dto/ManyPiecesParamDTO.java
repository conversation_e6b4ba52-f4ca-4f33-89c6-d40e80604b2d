package com.ywwl.customer.center.modules.business.dto;

import com.ywwl.customer.center.modules.business.annotation.CountryVerify;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商业快递多产品费用试算参数
 *
 * <AUTHOR>
 * @since 2023/10/12 09:59
 **/
@NoArgsConstructor
@Data
public class ManyPiecesParamDTO {
    /**
     * 揽收仓
     */
    private String companyCode;
    /**
     * 目的国id
     */
    @NotBlank(message = "目的国家不能为空")
    @CountryVerify(message = "目的国家不存在", groups = Input.class)
    private String countryId;
    /**
     * 货品属性
     */
    private String productAttributes;
    /**
     * 目的国邮编
     */
    private String postCode;
    /**
     * 小件参数
     */
    @Size(min = 1, message = "包裹参数不能为空")
    private List<ItemsDTO> items;

    /**
     * 小件参数
     */
    @NoArgsConstructor
    @Data
    public static class ItemsDTO {
        /**
         * 长（cm 单件）
         */
        @NotNull(message = "长不能为空")
        private BigDecimal length;
        /**
         * 宽（cm 单件）
         */
        @NotNull(message = "宽不能为空")
        private BigDecimal width;
        /**
         * 高（cm 单件）
         */
        @NotNull(message = "高不能为空")
        private BigDecimal height;
        /**
         * 计费实重(单件)
         */
        @NotNull(message = "实重不能为空")
        private BigDecimal weight;
        /**
         * 包裹数量
         */
        private Integer count;
        /**
         * 重量单位（g、kg、oz、lb、ct）
         */
        private String unit;
    }
}
