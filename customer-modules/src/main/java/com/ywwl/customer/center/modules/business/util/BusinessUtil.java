package com.ywwl.customer.center.modules.business.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.ImmutableMap;
import com.ywwl.customer.center.common.domain.RequestParam;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.modules.business.dto.*;
import com.ywwl.customer.center.modules.common.account.service.AccountService;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;
import com.ywwl.customer.center.modules.ejf.util.EJFUrl;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 商业快递URL
 *
 * <AUTHOR>
 * @since 2023/10/10 10:08
 **/
@Component
public class BusinessUtil {

    /**
     * 发货账号服务类
     */
    private static AccountService accountService;

    /**
     * 业务API
     */
    private static String business;

    @Autowired
    public BusinessUtil(AccountService accountService, @Value("${business.api}") String business) {
        BusinessUtil.business = business + "/api/order?";
        BusinessUtil.accountService = accountService;
    }

    /**
     * 创建运单
     */
    public static Request<BeOrderCreateDTO> beOrderCreate() {
        return Request.<BeOrderCreateDTO>builder()
                .clazz(BeOrderCreateDTO.class)
                .open(true)
                .api("be.order.create")
                .build();
    }

    /**
     * 取消运单
     */
    public static Request<GeneralDTO> beOrderCancel() {
        return Request.<GeneralDTO>builder()
                .clazz(GeneralDTO.class)
                .open(true)
                .api("be.order.cancel")
                .build();
    }

    /**
     * 运单详细
     */
    public static Request<BeOrderGetDTO> beOrderGet() {
        return Request.<BeOrderGetDTO>builder()
                .api("be.order.get")
                .open(true)
                .clazz(BeOrderGetDTO.class)
                .build();
    }

    /**
     * 运单详细-批量
     */
    public static Request<BeOrderGetListDTO> beOrderGetList() {
        return Request.<BeOrderGetListDTO>builder()
                .api("be.order.getlist")
                .open(true)
                .clazz(BeOrderGetListDTO.class)
                .build();
    }

    /**
     * 打印标签
     */
    public static Request<BeOrderLabelGetDTO> beOrderLabelGet() {
        return Request.<BeOrderLabelGetDTO>builder()
                .api("be.order.label.get")
                .open(true)
                .clazz(BeOrderLabelGetDTO.class)
                .build();
    }

    /**
     * 国家开通列表
     */
    public static Request<BeCountryGetListDTO> beCountryGetList() {
        return Request.<BeCountryGetListDTO>builder()
                .api("be.country.getlist")
                .open(true)
                .clazz(BeCountryGetListDTO.class)
                .build();
    }


    /**
     * 查询产品列表-根据燕文仓库编码、国家Id
     */
    public static Request<InnerBeChannelGetListDTO> innerBeChannelGetList() {
        return Request.<InnerBeChannelGetListDTO>builder()
                .api("inner_be.channel.getlist")
                .clazz(InnerBeChannelGetListDTO.class)
                .build();
    }

    /**
     * 查询通达国家-根据产品Id
     */
    public static Request<GeneralDTO> innerBeCountryGetList() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.country.getlist")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 运单信息修改
     */
    public static Request<GeneralDTO> innerBeOrderEdit() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.edit")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 查询运单信息修改记录
     */
    public static Request<GeneralDTO> innerBeOrderEditRecordGetList() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.edit_record.getlist")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 运单列表-通过运单号查询
     */
    public static Request<InnerBeOrderPortalGetListDTO> innerBeOrderPortalGetList() {
        return Request.<InnerBeOrderPortalGetListDTO>builder()
                .api("inner_be.order.portal.getlist")
                .clazz(InnerBeOrderPortalGetListDTO.class)
                .build();
    }

    /**
     * 运单列表
     */
    public static Request<InnerBeOrderPortalGetListFilterDTO> innerBeOrderPortalGetListFilter() {
        return Request.<InnerBeOrderPortalGetListFilterDTO>builder()
                .api("inner_be.order.portal.getlist.filter")
                .clazz(InnerBeOrderPortalGetListFilterDTO.class)
                .build();
    }

    /**
     * 运单状态统计-通过运单号查询
     */
    public static Request<GeneralDTO> innerBeOrderStatusGetList() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.status.getlist")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 运单状态统计
     */
    public static Request<InnerBeOrderStatusGetListFilterDTO> innerBeOrderStatusGetListFilter() {
        return Request.<InnerBeOrderStatusGetListFilterDTO>builder()
                .api("inner_be.order.status.getlist.filter")
                .clazz(InnerBeOrderStatusGetListFilterDTO.class)
                .build();
    }

    /**
     * 运单-确认发货
     */
    public static Request<GeneralDTO> innerBeOrderAcknowledge() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.acknowledge")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 运单-取消确认发货
     */
    public static Request<GeneralDTO> innerBeOrderAcknowledgeCancel() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order_acknowledge.cancel")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 截留运单恢复
     */
    public static Request<GeneralDTO> innerBeOrderInterceptRecover() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.intercept.recover")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 运单详细-批量
     */
    public static Request<BeOrderGetListDTO> innerBeOrderGetList() {
        return Request.<BeOrderGetListDTO>builder()
                .api("inner_be.order.getlist")
                .clazz(BeOrderGetListDTO.class)
                .logRsp(false)
                .build();
    }

    /**
     * 打印标签
     */
    public static Request<InnerBeOrderLabelGetListDTO> innerBeOrderLabelGetList() {
        return Request.<InnerBeOrderLabelGetListDTO>builder()
                .api("be.order.label.get")
                .open(true)
                .clazz(InnerBeOrderLabelGetListDTO.class)
                .build();
    }

    /**
     * 取消运单-超期
     */
    public static Request<GeneralDTO> innerBeOrderClose() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.close")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 查询运单取消记录
     */
    public static Request<InnerBeOrderCancelRecordGetListDTO> innerBeOrderCancelRecordGetList() {
        return Request.<InnerBeOrderCancelRecordGetListDTO>builder()
                .api("inner_be.order.cancel_record.getlist")
                .clazz(InnerBeOrderCancelRecordGetListDTO.class)
                .build();
    }

    /**
     * 导入订单
     */
    public static Request<InnerBeDraftOrderImportDTO> innerBeDraftOrderImport() {
        return Request.<InnerBeDraftOrderImportDTO>builder()
                .api("inner_be.draft_order.import")
                .clazz(InnerBeDraftOrderImportDTO.class)
                .build();
    }

    /**
     * 草稿订单修改
     */
    public static Request<InnerBeDraftOrderEditDTO> innerBeDraftOrderEdit() {
        return Request.<InnerBeDraftOrderEditDTO>builder()
                .api("inner_be.draft_order.edit")
                .clazz(InnerBeDraftOrderEditDTO.class)
                .build();
    }

    /**
     * 订单状态统计
     */
    public static Request<InnerBeDraftOrderStatusGetListDTO> innerBeDraftOrderStatusGetList() {
        return Request.<InnerBeDraftOrderStatusGetListDTO>builder()
                .api("inner_be.draft_order.status.getlist")
                .clazz(InnerBeDraftOrderStatusGetListDTO.class)
                .build();
    }

    /**
     * 订单生成运单
     */
    public static Request<InnerBeDraftOrderGenerateDTO> innerBeDraftOrderGenerate() {
        return Request.<InnerBeDraftOrderGenerateDTO>builder()
                .api("inner_be.draft_order.generate")
                .clazz(InnerBeDraftOrderGenerateDTO.class)
                .build();
    }

    /**
     * 删除订单
     */
    public static Request<GeneralDTO> innerBeDraftOrderDelete() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.draft_order.delete")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 订单列表查询
     */
    public static Request<InnerBeDraftOrderGetListFilterDTO> innerBeDraftOrderGetListFilter() {
        return Request.<InnerBeDraftOrderGetListFilterDTO>builder()
                .api("inner_be.draft_order.getlist.filter")
                .clazz(InnerBeDraftOrderGetListFilterDTO.class)
                .build();
    }

    /**
     * 订单详细
     */
    public static Request<BeOrderGetDTO> innerBeDraftOrderGet() {
        return Request.<BeOrderGetDTO>builder()
                .api("inner_be.draft_order.get")
                .clazz(BeOrderGetDTO.class)
                .build();
    }

    /**
     * 已存在订单号查询
     */
    public static Request<InnerBeOrderNumberGetListDTO> innerBeOrderNumberGetList() {
        return Request.<InnerBeOrderNumberGetListDTO>builder()
                .api("inner_be.order_number.getlist")
                .clazz(InnerBeOrderNumberGetListDTO.class)
                .build();
    }

    /**
     * 获取主单信息和包裹信息
     */
    public static Request<GeneralDTO> innerBeOrderGet() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.get")
                .clazz(GeneralDTO.class)
                .build();
    }

    /**
     * 获取运单包裹序号
     */
    public static Request<GeneralDTO> innerBeOrderExpressPackNumberGetList() {
        return Request.<GeneralDTO>builder()
                .api("inner_be.order.express_pack_number_getlist")
                .clazz(GeneralDTO.class)
                .build();
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @SuperBuilder
    public static class Request<T> extends RequestParam<T> {

        /**
         * 请求节点
         */
        private String api;

        /**
         * 令牌
         */
        private String token;

        /**
         * 是否开放
         */
        private Boolean open;

        /**
         * 发货账号
         */
        private String customerCode;

        /**
         * 用户
         */
        private UserAgent user;

        @Override
        public T get() {
            return post();
        }

        @Override
        public T request() {
            return post();
        }

        @Override
        public T post() {
            // 没有customerCode，则设置默认customerCode
            if (StringUtils.isBlank(getCustomerCode())) {
                setCustomerCode(EJFUrl.CUSTOMER_CODE);
            }
            // 没有token，则设置基础token
            if (StringUtils.isBlank(getToken())) {
                token(getBaseToken(getCustomerCode(), user));
            }
            // 设置URL
            String url = Boolean.TRUE.equals(open) ?
                    EJFUrl.getUrl(business, getCustomerCode(), getApi(), getBody(), getToken()) :
                    EJFUrl.getUrl(business, getCustomerCode(), getApi());
            this.url(url)
                    .header(ImmutableMap.of("Authorization", StringUtils.join("basic ", getToken())))
                    .body(getBody());
            return super.post();
        }

        /**
         * 设置token
         *
         * @param token token
         * @return this
         */
        public Request<T> token(String token) {
            this.token = token;
            return this;
        }

        /**
         * 设置user
         *
         * @param user user
         * @return this
         */
        public Request<T> user(UserAgent user) {
            this.user = user;
            return this;
        }

        /**
         * 设置customerCode
         *
         * @param customerCode customerCode
         * @return this
         */
        public Request<T> customerCode(String customerCode) {
            this.customerCode = customerCode;
            return this;
        }
    }


    /**
     * 构建MapEntry
     *
     * @param key   键
     * @param value 值
     * @return MapEntry
     */
    public static <T> Map.Entry<String, T> entry(String key, T value) {
        if (value instanceof String) {
            if (StringUtils.isBlank((String) value)) {
                value = null;
            }
        }
        if (value instanceof Collection) {
            if (CollectionUtil.isEmpty((Collection<?>) value)) {
                value = null;
            }
        }
        return MapUtil.entry(key, value);
    }

    /**
     * 根据发货账号获取token
     */
    private static String getBaseToken(String customerCode, UserAgent user) {
        // 使用瞬时缓存
        return CacheUtil.ehCache().<String>getValueAndCache(CacheKeyEnum.API_TOKEN, customerCode, () -> {
            final AccountGetResVO account = accountService.getAccount(customerCode, user);
            Assert.notBlank(account.getApiToken(), ResponseCode.PORTAL_5021::getError);
            return account.getApiToken();
        });
    }

    /**
     * 转换
     *
     * @param dto 业务订单
     * @return 转换后的数据
     */
    public static List<BusinessExcelPropertyDTO> to(BusinessOrderDTO dto) {
        final List<BusinessOrderDTO.ProductInfoDTO> productInfos = dto.getProductInfo();
        final List<BusinessOrderDTO.ParcelInfoDTO> parcelInfos = dto.getParcelInfo();
        return IntStream.range(0, NumberUtil.max(1, parcelInfos.size(), productInfos.size())).mapToObj(i -> {
            // 设置产品
            BusinessOrderDTO.ProductInfoDTO productInfo = null;
            // 设置包裹
            BusinessOrderDTO.ParcelInfoDTO parcelInfo = null;
            if (productInfos.size() - 1 >= i) {
                productInfo = productInfos.get(i);
            }
            if (parcelInfos.size() - 1 >= i) {
                parcelInfo = parcelInfos.get(i);
            }
            final BusinessExcelPropertyDTO.BusinessExcelPropertyDTOBuilder excelPropertyBuilder = BusinessExcelPropertyDTO.builder();
            final BusinessExcelPropertyDTO excelProperty;
            if (i == 0) {
                final BusinessOrderDTO.CustomInfosDTO customsInfo = dto.getCustomsInfo();
                final BusinessOrderDTO.SenderInfoDTO senderInfo = dto.getSenderInfo();
                final BusinessOrderDTO.ReceiverInfoDTO receiverInfo = dto.getReceiverInfo();
                excelProperty = excelPropertyBuilder
                        .status(dto.getStatusName())
                        .trackingGMT(dto.getTrackingGMT())
                        .createTime(dto.getCreateTime())
                        .waybillNumber(dto.getWaybillNumber())
                        .companyCode(dto.getCompanyCode())
                        .channelId(dto.getChannelId())
                        .orderNumber(dto.getOrderNumber())
                        .packageType(dto.getPackageType())
                        .goodsType(dto.getGoodsType())
                        .batteryType(dto.getBatteryType())
                        .dutyType(dto.getDutyType())
                        .shippingMethod(dto.getShippingMethod())
                        .domesticLogisticsCompany(dto.getDomesticLogisticsCompany())
                        .domesticTrackingNo(dto.getDomesticTrackingNo())
                        .remark(dto.getRemark())
                        .returnExpressCompanyName(dto.getReturnExpressCompanyName())
                        .returnTrackingNumber(dto.getReturnTrackingNumber())
                        .build();
                if (Objects.nonNull(customsInfo)) {
                    excelProperty.setIoss(customsInfo.getIoss());
                    excelProperty.setEori(customsInfo.getEori());
                    excelProperty.setCurrency(customsInfo.getCurrency());
                    excelProperty.setPackFee(customsInfo.getPackFee());
                    excelProperty.setInsurance(customsInfo.getInsurance());
                    excelProperty.setOtherFee(customsInfo.getOtherFee());
                    excelProperty.setPrice(customsInfo.getPrice());
                    excelProperty.setIsSignature(customsInfo.getIsSignature());
                    excelProperty.setIsInsurance(customsInfo.getIsInsurance());
                    excelProperty.setIsCustomsService(customsInfo.getIsCustomsService());
                }
                if (Objects.nonNull(senderInfo)) {
                    excelProperty.setSenderName(senderInfo.getName());
                    excelProperty.setSenderCompany(senderInfo.getCompany());
                    excelProperty.setSenderCity(senderInfo.getCity());
                    excelProperty.setSenderDistrict(senderInfo.getDistrict());
                    excelProperty.setSenderState(senderInfo.getState());
                    excelProperty.setSenderZipCode(senderInfo.getZipCode());
                    excelProperty.setSenderAddress(senderInfo.getAddress());
                    excelProperty.setSenderPhone(senderInfo.getPhone());
                    excelProperty.setSenderEmail(senderInfo.getEmail());
                    excelProperty.setSenderTaxNumber(senderInfo.getTaxNumber());
                }
                if (Objects.nonNull(receiverInfo)) {
                    excelProperty.setCountryId(receiverInfo.getCountryId());
                    excelProperty.setReceiverName(receiverInfo.getName());
                    excelProperty.setReceiverCompany(receiverInfo.getCompany());
                    excelProperty.setReceiverCity(receiverInfo.getCity());
                    excelProperty.setReceiverOutskirts(receiverInfo.getOutskirts());
                    excelProperty.setReceiverState(receiverInfo.getState());
                    excelProperty.setReceiverZipCode(receiverInfo.getZipCode());
                    excelProperty.setReceiverPhone(receiverInfo.getPhone());
                    excelProperty.setReceiverMobile(receiverInfo.getMobile());
                    excelProperty.setReceiverEmail(receiverInfo.getEmail());
                    excelProperty.setReceiverTaxType(receiverInfo.getTaxType());
                    excelProperty.setReceiverTaxNumber(receiverInfo.getTaxNumber());
                    excelProperty.setReceiverAddress1(receiverInfo.getAddress1());
                    excelProperty.setReceiverAddress2(receiverInfo.getAddress2());
                    excelProperty.setReceiverAddress3(receiverInfo.getAddress3());
                }
            } else {
                excelProperty = excelPropertyBuilder.build();
            }
            // 设置包裹信息
            if (Objects.nonNull(parcelInfo)) {
                excelProperty.setLength(parcelInfo.getLength());
                excelProperty.setWidth(parcelInfo.getWidth());
                excelProperty.setHeight(parcelInfo.getHeight());
                excelProperty.setWeight(parcelInfo.getWeight());
            }
            // 设置商品信息
            if (Objects.nonNull(productInfo)) {
                excelProperty.setGoodsNameEn(productInfo.getGoodsNameEn());
                excelProperty.setGoodsNameCh(productInfo.getGoodsNameCh());
                excelProperty.setProductPrice(productInfo.getPrice());
                excelProperty.setQuantity(productInfo.getQuantity());
                excelProperty.setHscode(productInfo.getHscode());
                excelProperty.setMaterial(productInfo.getMaterial());
                excelProperty.setBrand(productInfo.getBrand());
                excelProperty.setModel(productInfo.getModel());
                excelProperty.setUse(productInfo.getUse());
            }
            return excelProperty;
        }).collect(Collectors.toList());
    }

}
