package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 获取产品参数
 *
 * <AUTHOR>
 * @since 2023/12/01 10:34
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class InnerBeChannelGetListParamDTO {

    /**
     * userId
     */
    @NotBlank(message = "userId不能为空")
    private String userId;
    /**
     * 揽收仓
     */
    private String warehouseCode;
    /**
     * 国家
     */
    private String countryId;
}
