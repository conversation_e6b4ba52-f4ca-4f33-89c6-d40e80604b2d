package com.ywwl.customer.center.modules.common.bill.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@Data
public class CreateScanOrderDTO {
    // 币种
    private String payCurrency="CNY";
    // 金额
    @NotBlank(message = "金额不能为空")
    private String orderAmount;
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;
}
