package com.ywwl.customer.center.modules.business.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.ywwl.customer.center.modules.business.enums.SearchStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单列表查询参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:57
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchParamDTO extends InnerBeDraftOrderGetListFilterParamDTO {

    /**
     * 状态
     */
    @JsonDeserialize(using = SearchStatus.SearchStatusSerializer.class)
    private SearchStatus status;

}
