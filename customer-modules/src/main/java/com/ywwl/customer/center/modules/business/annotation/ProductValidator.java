package com.ywwl.customer.center.modules.business.annotation;

import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.plm.dto.ProductResultDTO;
import com.ywwl.customer.center.modules.general.plm.enums.PLMPlatformEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class ProductValidator implements ConstraintValidator<ProductVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<ProductResultDTO.DataDTO> products = EJFUtil.plmService.getProducts(PLMPlatformEnum.EJF);
        // 仅Code校验
        return products.stream()
                .anyMatch(v -> Objects.equals(v.getProductNumber(), value));
    }

}
