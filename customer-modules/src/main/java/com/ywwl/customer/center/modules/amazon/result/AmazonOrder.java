
package com.ywwl.customer.center.modules.amazon.result;

import lombok.Data;

/**
 * @author: dinghy
 * @date: 2022/9/6 9:58
 */
@Data
public class AmazonOrder {

    /**
     * <AUTHOR>
     * @description 序号
     * @date 2022/9/6 10:02
     **/
    private Integer id;
    /**
     * <AUTHOR>
     * @description 订单编号
     * @date 2022/9/6 10:02
     **/
    private String orderNumber;
    /**
     * <AUTHOR>
     * @description 平台编码
     * @date 2022/9/7 17:03
     **/
    private String platformCode;
    /**
     * <AUTHOR>
     * @description 运单号
     * @date 2022/9/6 10:02
     **/
    private String trackNumber;
    /**
     * <AUTHOR>
     * @description 产品名称
     * @date 2022/9/6 10:02
     **/
    private String productName;
    /**
     * <AUTHOR>
     * @description 申报品名
     * @date 2022/9/6 10:02
     **/
    private String goodsName;
    /**
     * <AUTHOR>
     * @description 英文申报品名
     * @date 2022/9/6 10:02
     **/
    private String goodsNameEn;
    /**
     * <AUTHOR>
     * @description 城市
     * @date 2022/9/6 10:02
     **/
    private String receiverCity;
    /**
     * <AUTHOR>
     * @description 目的国
     * @date 2022/9/6 10:03
     **/
    private String destinationCountry;
    /**
     * <AUTHOR>
     * @description 收件人
     * @date 2022/9/6 10:03
     **/
    private String receiverName;
    /**
     * <AUTHOR>
     * @description 制单时间
     * @date 2022/9/6 10:03
     **/
    private String orderTime;
    /**
     * <AUTHOR>
     * @description 子状态
     * @date 2022/9/6 10:03
     **/
    private String subStatus;
    /**
     * <AUTHOR>
     * @description 打印状态
     * @date 2022/9/6 10:03
     **/
    private Integer printStatus;

}
