package com.ywwl.customer.center.modules.common.auth.enums;


import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import lombok.val;

/**
 * 客户实名认证 状态枚举类
 */
public enum CustomerStatusEnum {

	/** 0, "未提交" */
	UN_COMMIT(0, "未提交"),

	/** 1, "暂存" */
	STAGING(1, "暂存"),

	/** 2, "待审核" */
	WAIT_AUDIT(2, "待审核"),

	/** 3, "审核通过" */
	AUDIT_SUCCESS(3, "审核通过"),

	/** 4, "审核拒绝" */
	AUDIT_FAIL(4, "审核拒绝");

	/*** 状态id */
	private Integer status;

	/** 描述 */
	private String desc;

	private CustomerStatusEnum(Integer status, String desc) {
		this.status = status;
		this.desc = desc;
	}

	public Integer status() {
		return status;
	}

	public String desc() {
		return desc;
	}

	/**
	 * 根据状态id获取状态名称
	 */
	public static CustomerStatusEnum getCustomerStatusEnum(Integer status) {
		for (val data : CustomerStatusEnum.values()) {
			if(data.status().equals(status)){
				return data;
			}
		}
		throw new BusinessException(ResponseCode.PORTAL_7004);
	}

}
