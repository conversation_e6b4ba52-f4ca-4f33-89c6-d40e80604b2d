package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: dinghy
 * @date: 2023/3/6 13:41
 */
@Data
public class CheckBankMobileDTO {
    private String no;
    @NotBlank(message = "[验证码]不能为空")
    private String code;
    private String userCode;
    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;
}
