package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 已经存在订单号查询参数
 *
 * <AUTHOR>
 * @since 2023/10/11 16:20
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class InnerBeOrderNumberGetListParamDTO {

    /**
     * 订单号
     */
    @NotNull(message = "订单号不能为空")
    @Size(min = 1, message = "订单号不能为空")
    private List<String> listOrderNumber;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 制单账号
     */
    @NotBlank(message = "制单账号不能为空")
    private String userId;
}
