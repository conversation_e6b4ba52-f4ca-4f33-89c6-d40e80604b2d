package com.ywwl.customer.center.modules.business.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ywwl.customer.center.modules.business.converter.CompanyConverter;
import lombok.Data;

/**
 * 商业快递列表信息DTO
 *
 * <AUTHOR>
 * @since 2023/10/10 15:22
 **/
@Data
public class BusinessSimpleOrderDTO {

    /**
     * 订单号
     */
    private String orderNumber;
    /**
     * 运单号
     */
    private String waybillNumber;
    /**
     * 转单号
     */
    private String referenceNumber;
    /**
     * 制单账号
     */
    private String userId;
    /**
     * 产品名称
     */
    private String channelName;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 揽收仓
     */
    @JsonSerialize(using = CompanyConverter.class)
    @JsonAlias(value = "companyCode")
    @JsonProperty(value = "companyName")
    private String companyCode;
    /**
     * 收件人名称
     */
    private String receiverName;
    /**
     * 是否打印
     */
    private String isPrint;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 追踪时间
     */
    private String trackingTime;
    /**
     * 追踪时间-GMT
     */
    private String trackingGMT;
    /**
     * 时区
     */
    private String trackingTimeZone;
    /**
     * 取消原因
     */
    private String cancelNote;
    /**
     * 取消类型
     */
    private Integer cancelType;
    /**
     * 取消时间
     */
    private String cancelTime;
    /**
     * 处理方式
     */
    private String howToHandle;
    /**
     * 截留状态
     */
    private String trappedState;
    /**
     * 新单号
     */
    private String newExpressCode;
    /**
     * 退件快递公司名称
     */
    private String expressCompanyName;
    /**
     * 退件快递单号
     */
    private String trackingNumber;

}
