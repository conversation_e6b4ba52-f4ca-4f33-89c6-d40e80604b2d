package com.ywwl.customer.center.modules.common.bill.enums;

public enum BillOpCodeEnum {
    QUERY_BALANCE("qrycustbalance", "查询账户余额"),
    ACCOUNT_PERIOD("qryhisperiodtranbill", "历史对账单分账期查询"),
    HISTORY("histransbill", "历史账单"),
    BILL_DETAIL ("billdetail","查看账单明细"),
    DETAIL_EXPORT("expbilldetail","导出账单明细"),
    UN_BILLED_BILL ("openbilldetail","未出账单明细查询"),
    UN_BILLED_TAX_BILL("openbilltaxdetail","未出账单税金查询"),
    EXPORT_UNBILLED_DETAIL ("expopenbilldetail","下载未出账单明细"),
    EXPORT_UNBILLED_TAX_DETAIL ("expopenbilltaxdetail","下载未出账单明细"),
    SEND_WING_WITHOUT_LIT ("hwqbalance","查询海外派预扣款"),
    EXPORT_SEND_WING_BILL ("exphwqbalance","海外派账单下载"),
    WITHOUT_BILL_DETAIL ("withholdingbilldetail","预扣款明细查询"),
    E_BILL("qrymerbillfile","查询电子账单"),
    OVERSEA_RECORD("customerdealrecord","海外派交易记录查询"),
    OVERSEA_CHECK_BILL("overseabilldetail","对账单查询接口"),

    ;
    private String value;
    private String desc;

    BillOpCodeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String value() {
        return value;
    }
}
