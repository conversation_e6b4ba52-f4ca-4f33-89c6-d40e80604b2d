package com.ywwl.customer.center.modules.business.annotation.entrance;

import com.ywwl.customer.center.modules.business.enums.BusinessFixedEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class ShippingMethodImportValidator implements ConstraintValidator<ShippingMethodImportVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        return BusinessFixedEnum.SHIPPING_METHOD.containsKey(StringUtils.strip(value));
    }

}
