package com.ywwl.customer.center.modules.amazon.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 敦煌打印标签结果
 *
 * <AUTHOR>
 * @date 2024/06/21 09:57
 **/
@NoArgsConstructor
@Data
public class DhBase64LabelDTO {

	/**
	 * 响应Code
	 */
	private Integer code;
	/**
	 * 运单号
	 */
	@JSONField(alternateNames = "tracking_id")
	private String trackingId;
	/**
	 * logisticsOrderCode
	 */
	private String logisticsOrderCode;
	/**
	 * 响应信息
	 */
	private String message;
	/**
	 * base64
	 */
	@JSONField(alternateNames = "label_base64")
	private String labelBase64;

}
