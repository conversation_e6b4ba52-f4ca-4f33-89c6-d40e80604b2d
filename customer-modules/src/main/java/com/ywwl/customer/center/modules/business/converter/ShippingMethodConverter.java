package com.ywwl.customer.center.modules.business.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 包裹类型转换器
 *
 * <AUTHOR>
 * @since 2023/10/20 15:17
 **/
public class ShippingMethodConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {

        return new WriteCellData<>(getValue(value));
    }

    /**
     * 获取枚举类型
     *
     * @param value 1:客户自送 2:燕文揽收 3:客户自寄
     * @return String
     */
    private static String getValue(String value) {
        // 1:客户自送 2:燕文揽收 3:客户自寄
        if (Objects.equals("1", value)) {
            return "客户自送";
        } else if (Objects.equals("2", value)) {
            return "燕文揽收";
        } else if (Objects.equals("3", value)) {
            return "客户自寄";
        }
        return value;
    }


}
