package com.ywwl.customer.center.modules.business.annotation;

import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class CurrentValidator implements ConstraintValidator<CurrentVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        return Objects.nonNull(EJFUtil.baseInfoService.getCurrencyById(value));
    }

}
