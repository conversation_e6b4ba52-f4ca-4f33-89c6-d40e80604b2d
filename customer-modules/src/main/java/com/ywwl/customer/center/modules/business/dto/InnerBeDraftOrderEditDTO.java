package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 草稿订单修改
 *
 * <AUTHOR>
 * @since 2023/10/11 15:51
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeDraftOrderEditDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * ID
         */
        private String sequenceNo;
        /**
         * 订单号
         */
        private String orderNumber;
        /**
         * 成功标志
         */
        private Boolean successX;
        /**
         * 错误信息
         */
        private String errorMessage;
    }

}
