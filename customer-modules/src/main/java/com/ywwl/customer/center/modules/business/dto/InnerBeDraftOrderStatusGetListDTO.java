package com.ywwl.customer.center.modules.business.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单状态统计
 *
 * <AUTHOR>
 * @since 2023/10/11 16:00
 **/
@NoArgsConstructor
@Data
public class InnerBeDraftOrderStatusGetListDTO extends GeneralDTO {
    /**
     * data
     */
    @JsonProperty("data")
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * list
         */
        @JsonProperty("list")
        private List<ExpressDTO> list;
        /**
         * total
         */
        @JsonProperty("total")
        private Integer total;

    }
}
