package com.ywwl.customer.center.modules.common.auth.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 个人认证类型
 * @date 2023/2/21 15:44
 **/
@Getter
public enum PersonalAuthTypeEnum {
    FACE(0,"人脸识别"),
    BANK(1,"银行卡认证"),
    AUDIT(2,"人工审核")
    ;

    @EnumValue
    @JsonValue
    private final Integer code;
    private String desc;

    PersonalAuthTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
