package com.ywwl.customer.center.modules.common.account.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ywwl.customer.center.modules.common.account.domain.SysOperationController;

/**
* <AUTHOR>
* @description 针对表【sys_operation_controller(用户操作控制)】的数据库操作Service
* @createDate 2023-12-22 16:07:58
*/
public interface SysOperationControllerService extends IService<SysOperationController> {

    /**
     * 是否需要展示
     * @param userId userId
     * @return 展示
     */
    Boolean doYouNeedToShow(Long userId, String type);

    /**
     * 查看完结
     * @param userId userId
     */
    void viewTheEnd(Long userId, String type);

}
