package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/3/6 16:12
 */
@Data
public class SaveCompanyBankDTO {
    private String no;
    @NotBlank(message = "[客户银行卡号]不能为空")
    private String bankCard;
    @NotBlank(message = "[客户支行名称]不能为空")
    private String branchBankName;
    private String userCode;
    /**
     * <AUTHOR>
     * @date 2023/10/12 10:41
     * @description 银行code
     */
    @NotNull(message = "[银行]不能为空")
    private String bankCode;
    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;
}
