package com.ywwl.customer.center.modules.amazon.result;


import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 打印标签参数类
 *
 * <AUTHOR>
 * @date 2022/09/05 16:51
 **/
@NoArgsConstructor
@XmlRootElement(name = "LabelTemplatePrintRequest")
public class PrintLabelsParam {



	private String templateCode;

	private ExtensionTypeDTO extensionType;

	private ExpressTypeDTO expressType;

	@XmlElement(name = "TemplateCode")
	public String getTemplateCode() {
		return templateCode;
	}

	public void setTemplateCode(String templateCode) {
		this.templateCode = templateCode;
	}

	@XmlElement(name = "ExtensionType")
	public ExtensionTypeDTO getExtensionType() {
		return extensionType;
	}

	public void setExtensionType(ExtensionTypeDTO extensionType) {
		this.extensionType = extensionType;
	}

	@XmlElement(name = "ExpressType")
	public ExpressTypeDTO getExpressType() {
		return expressType;
	}

	public void setExpressType(ExpressTypeDTO expressType) {
		this.expressType = expressType;
	}

	public static class ExtensionTypeDTO {

		private String field01;

		private String field02;

		private String field03;

		private String field04;

		private String field05;

		private String field06;

		private String field07;

		@XmlElement(name = "Field01")
		public String getField01() {
			return field01;
		}

		public void setField01(String field01) {
			this.field01 = field01;
		}

		@XmlElement(name = "Field02")
		public String getField02() {
			return field02;
		}

		public void setField02(String field02) {
			this.field02 = field02;
		}

		@XmlElement(name = "Field03")
		public String getField03() {
			return field03;
		}

		public void setField03(String field03) {
			this.field03 = field03;
		}

		@XmlElement(name = "Field04")
		public String getField04() {
			return field04;
		}

		public void setField04(String field04) {
			this.field04 = field04;
		}

		@XmlElement(name = "Field05")
		public String getField05() {
			return field05;
		}

		public void setField05(String field05) {
			this.field05 = field05;
		}

		@XmlElement(name = "Field06")
		public String getField06() {
			return field06;
		}

		public void setField06(String field06) {
			this.field06 = field06;
		}

		@XmlElement(name = "Field07")
		public String getField07() {
			return field07;
		}

		public void setField07(String field07) {
			this.field07 = field07;
		}
	}

	public static class ExpressTypeDTO {

		private String epcode;

		private String userid;

		private String channel;

		private String userOrderNumber;

		private String yanwenNumber;

		private String sendDate;

		private String channelType;

		private ReceiverDTO receiver;

		private SenderDTO sender;

		private String memo;

		private String quantity;

		private GoodsNameDTO goodsName;

		private String count;

		@XmlElement(name = "Epcode")
		public String getEpcode() {
			return epcode;
		}

		public void setEpcode(String epcode) {
			this.epcode = epcode;
		}

		@XmlElement(name = "Userid")
		public String getUserid() {
			return userid;
		}

		public void setUserid(String userid) {
			this.userid = userid;
		}

		@XmlElement(name = "Channel")
		public String getChannel() {
			return channel;
		}

		public void setChannel(String channel) {
			this.channel = channel;
		}

		@XmlElement(name = "UserOrderNumber")
		public String getUserOrderNumber() {
			return userOrderNumber;
		}

		public void setUserOrderNumber(String userOrderNumber) {
			this.userOrderNumber = userOrderNumber;
		}

		@XmlElement(name = "YanwenNumber")
		public String getYanwenNumber() {
			return yanwenNumber;
		}

		public void setYanwenNumber(String yanwenNumber) {
			this.yanwenNumber = yanwenNumber;
		}

		@XmlElement(name = "SendDate")
		public String getSendDate() {
			return sendDate;
		}

		public void setSendDate(String sendDate) {
			this.sendDate = sendDate;
		}

		@XmlElement(name = "ChannelType")
		public String getChannelType() {
			return channelType;
		}

		public void setChannelType(String channelType) {
			this.channelType = channelType;
		}

		@XmlElement(name = "Receiver")
		public ReceiverDTO getReceiver() {
			return receiver;
		}

		public void setReceiver(ReceiverDTO receiver) {
			this.receiver = receiver;
		}

		@XmlElement(name = "Sender")
		public SenderDTO getSender() {
			return sender;
		}

		public void setSender(SenderDTO sender) {
			this.sender = sender;
		}

		@XmlElement(name = "Memo")
		public String getMemo() {
			return memo;
		}

		public void setMemo(String memo) {
			this.memo = memo;
		}

		@XmlElement(name = "Quantity")
		public String getQuantity() {
			return quantity;
		}

		public void setQuantity(String quantity) {
			this.quantity = quantity;
		}

		@XmlElement(name = "GoodsName")
		public GoodsNameDTO getGoodsName() {
			return goodsName;
		}

		public void setGoodsName(GoodsNameDTO goodsName) {
			this.goodsName = goodsName;
		}

		@XmlElement(name = "Count")
		public String getCount() {
			return count;
		}

		public void setCount(String count) {
			this.count = count;
		}

		public static class ReceiverDTO {

			private String userid;

			private String name;

			private String phone;

			private String mobile;

			private String email;

			private String company;

			private String country;

			private String postcode;

			private String state;

			private String city;

			private String nationalId;

			private String address1;

			private String address2;

			@XmlElement(name = "Userid")
			public String getUserid() {
				return userid;
			}

			public void setUserid(String userid) {
				this.userid = userid;
			}

			@XmlElement(name = "Name")
			public String getName() {
				return name;
			}

			public void setName(String name) {
				this.name = name;
			}

			@XmlElement(name = "Phone")
			public String getPhone() {
				return phone;
			}

			public void setPhone(String phone) {
				this.phone = phone;
			}

			@XmlElement(name = "Mobile")
			public String getMobile() {
				return mobile;
			}

			public void setMobile(String mobile) {
				this.mobile = mobile;
			}

			@XmlElement(name = "Email")
			public String getEmail() {
				return email;
			}

			public void setEmail(String email) {
				this.email = email;
			}

			@XmlElement(name = "Company")
			public String getCompany() {
				return company;
			}

			public void setCompany(String company) {
				this.company = company;
			}

			@XmlElement(name = "Country")
			public String getCountry() {
				return country;
			}

			public void setCountry(String country) {
				this.country = country;
			}

			@XmlElement(name = "Postcode")
			public String getPostcode() {
				return postcode;
			}

			public void setPostcode(String postcode) {
				this.postcode = postcode;
			}

			@XmlElement(name = "District")
			public String getState() {
				return state;
			}

			public void setState(String state) {
				this.state = state;
			}

			@XmlElement(name = "City")
			public String getCity() {
				return city;
			}

			public void setCity(String city) {
				this.city = city;
			}

			@XmlElement(name = "NationalId")
			public String getNationalId() {
				return nationalId;
			}

			public void setNationalId(String nationalId) {
				this.nationalId = nationalId;
			}

			@XmlElement(name = "Address1")
			public String getAddress1() {
				return address1;
			}

			public void setAddress1(String address1) {
				this.address1 = address1;
			}

			@XmlElement(name = "Address2")
			public String getAddress2() {
				return address2;
			}

			public void setAddress2(String address2) {
				this.address2 = address2;
			}
		}

		public static class SenderDTO {

			private String taxNumber;

			private String senderName;

			@XmlElement(name = "TaxNumber")
			public String getTaxNumber() {
				return taxNumber;
			}

			public void setTaxNumber(String taxNumber) {
				this.taxNumber = taxNumber;
			}

			@XmlElement(name = "SenderName")
			public String getSenderName() {
				return senderName;
			}

			public void setSenderName(String senderName) {
				this.senderName = senderName;
			}
		}

		public static class GoodsNameDTO {

			private String userid;

			private String nameCh;

			private String nameEn;

			private String weight;

			private String declaredValue;

			private String declaredCurrency;

			private String moreGoodsName;

			private String hsCode;

			@XmlElement(name = "Userid")
			public String getUserid() {
				return userid;
			}

			public void setUserid(String userid) {
				this.userid = userid;
			}

			@XmlElement(name = "NameCh")
			public String getNameCh() {
				return nameCh;
			}

			public void setNameCh(String nameCh) {
				this.nameCh = nameCh;
			}

			@XmlElement(name = "NameEn")
			public String getNameEn() {
				return nameEn;
			}

			public void setNameEn(String nameEn) {
				this.nameEn = nameEn;
			}

			@XmlElement(name = "Weight")
			public String getWeight() {
				return weight;
			}

			public void setWeight(String weight) {
				this.weight = weight;
			}

			@XmlElement(name = "DeclaredValue")
			public String getDeclaredValue() {
				return declaredValue;
			}

			public void setDeclaredValue(String declaredValue) {
				this.declaredValue = declaredValue;
			}

			@XmlElement(name = "DeclaredCurrency")
			public String getDeclaredCurrency() {
				return declaredCurrency;
			}

			public void setDeclaredCurrency(String declaredCurrency) {
				this.declaredCurrency = declaredCurrency;
			}

			@XmlElement(name = "MoreGoodsName")
			public String getMoreGoodsName() {
				return moreGoodsName;
			}

			public void setMoreGoodsName(String moreGoodsName) {
				this.moreGoodsName = moreGoodsName;
			}

			@XmlElement(name = "HsCode")
			public String getHsCode() {
				return hsCode;
			}

			public void setHsCode(String hsCode) {
				this.hsCode = hsCode;
			}
		}
	}
}
