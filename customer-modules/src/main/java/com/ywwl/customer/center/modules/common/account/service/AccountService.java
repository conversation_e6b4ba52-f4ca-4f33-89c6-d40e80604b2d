// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.service;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.modules.common.account.dto.AccountEditReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountGetReqDTO;
import com.ywwl.customer.center.modules.common.account.dto.AccountInsertReqDTO;
import com.ywwl.customer.center.modules.common.account.vo.AccountGetResVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 10:48
 * @ModifyDate 2023/3/14 10:48
 * @Version 1.0
 */
public interface AccountService {
    /**
     * 保存制单账号
     * @param accountInsertReqDTO
     * @return
     */
    JsonResult saveAccount(AccountInsertReqDTO accountInsertReqDTO);


	/**
	 * 获取制单账号
	 * @param accountGetReqDTO	参数
	 * @return					制单账号结果类
	 */
    JsonResult<List<AccountGetResVO>> getAccountResults(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent);

	/**
	 * 获取制单账号
	 * @param accountGetReqDTO	参数
	 * @return					制单账号
	 */
	List<AccountGetResVO> getAccounts(AccountGetReqDTO accountGetReqDTO);


    /**
     * 获取制单账号
     * @param accountGetReqDTO	参数
     * @param userAgent         用户信息
     * @return					制单账号
     */
    List<AccountGetResVO> getAccounts(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent);

    /**
	 * 获取单个制单账号
	 * @param accountGetReqDTO  获取参数
	 * @return  制单账号信息
	 */
	AccountGetResVO getAccount(AccountGetReqDTO accountGetReqDTO);

	/**
	 * 获取单个制单账号
	 * @param accountGetReqDTO  获取参数
	 * @return  制单账号信息
	 */
	AccountGetResVO getAccount(AccountGetReqDTO accountGetReqDTO, UserAgent userAgent);

	/**
	 * 获取单个制单账号
	 * @param account  获取参数
	 * @return  制单账号信息
	 */
	AccountGetResVO getAccount(String account);
	/**
	 * 获取单个制单账号
	 * @param account  获取参数
	 * @return  制单账号信息
	 */
	AccountGetResVO getAccount(String account, UserAgent userAgent);

	/**
     * 编辑制单账号权限操作
     * @param accountEditReqDTO
     * @return
     */
    JsonResult editAccount(AccountEditReqDTO accountEditReqDTO);


    /**
     * 判断所传的制单账号是否都属于当前用户下面
     * @param accounts
     * @param userAgent
     * @return
     */
    boolean existAccount(List<String> accounts,UserAgent userAgent,Integer accountType);


    /**
     * 根据业务类型获取所有制单账号
     * @param businessType
     * @param userAgent
     * @param scene
     * @return
     */
    Map<Integer,List<AccountGetResVO>> getAccountForAll(List<Integer> businessType, UserAgent userAgent,Integer scene);


	boolean existAccount(List<String> accounts, String userCode, Integer accountType);

	boolean existAccount(List<String> accounts, String userCode);

	boolean existAccount(List<String> accounts);

	/**
	 * 制单账号验证是否有权限,无权限会直接异常
	 * @param accounts	制单账号
	 * @return 是否有权限
	 */
	void existAccountThrow(List<String> accounts);

	/**
	 * 制单账号验证是否有权限,无权限会直接异常
	 * @param accounts	制单账号
	 * @param accountType 类型
	 */
	void existAccountThrow(List<String> accounts, Integer accountType);

	/**
	 * 制单单笔账号验证是否有权限,无权限会直接异常
	 * @param account	制单账号
	 * @return 是否有权限
	 */
	void existAccountThrow(String account);

	void existAccountThrow(String account, Integer accountType);

	/**
	 * 获取当前用户特定场景下所有有权限制单账号
	 * @param accountType
	 * @param scene
	 * @return
	 */
	List<String> getAccountNosByPermission(Integer accountType,Integer scene);

	List<String> listAccountNoForChild(Long userId);

	/**
     * <AUTHOR>
     * @description 根据userCode查询出所有的制单账号,校验所传的账号是否存在
     * @date 2023/7/24 11:20
     **/
	void checkAllAccountCode(List<String> accounts,String userCode);
    /**
     * @author: dinghy
     * @createTime: 2024/7/17 18:06
     * @description: 查询默认交货仓
     */
	String getDefaultWarehouse(String accountCode);
    /**
     * @author: dinghy
     * @createTime: 2024/7/22 16:27
     * @description: 根据制单账号查询制单账号所有信息
     */
	AccountGetResVO getAccountByCustomerCode(String accountCode);
}
