package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 打印标签-批量参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:47
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@SuperBuilder(builderMethodName = "vBuilder")
@Data
public class InnerBeOrderLabelGetListDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运单号
         */
        private String waybillNumber;
        /**
         * 是否成功
         */
        private Boolean isSuccess;
        /**
         * 错误信息
         */
        private String errorMsg;
        /**
         * base64
         */
        private String base64String;
        /**
         * 标签类型
         */
        private String labelType;
    }

}
