package com.ywwl.customer.center.modules.common.auth.enums;

import com.ywwl.customer.center.framework.exceptions.BusinessException;

public enum CompanyAuthStatusEnum {
    // 账号初始化
    INIT(0),
    // 待提交银行卡信息
    SUBMIT_BANK(1),
    // 待金额打款校验
    AMOUNT_VERIFY(2),
    // 认证成功
    SUCCESS(3),
    // 认证失败
    FAIL(4),
    ;
    private Integer value;

    CompanyAuthStatusEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static CompanyAuthStatusEnum getCompanyAuthStatusEnum(Integer value){
        for (CompanyAuthStatusEnum companyAuthStatusEnum : CompanyAuthStatusEnum.values()) {
            if(companyAuthStatusEnum.getValue().equals(value)){
                return companyAuthStatusEnum;
            }
        }
        throw new BusinessException();
    }
}
