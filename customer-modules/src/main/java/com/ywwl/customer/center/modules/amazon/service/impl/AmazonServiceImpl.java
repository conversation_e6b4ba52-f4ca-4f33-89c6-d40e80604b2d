package com.ywwl.customer.center.modules.amazon.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.amazon.constant.AmazonOrderConstant;
import com.ywwl.customer.center.modules.amazon.dto.DhBase64LabelDTO;
import com.ywwl.customer.center.modules.amazon.result.*;
import com.ywwl.customer.center.modules.amazon.service.AmazonOrderService;
import com.ywwl.customer.center.modules.amazon.vo.AmazonOrderDetailVO;
import com.ywwl.customer.center.modules.amazon.vo.PageResultVO;
import com.ywwl.customer.center.modules.ejf.entity.Express;
import com.ywwl.customer.center.modules.ejf.enums.SearchStatus;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import com.ywwl.customer.center.modules.general.plm.dto.ProductResultDTO;
import com.ywwl.customer.center.modules.general.plm.service.PLMService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.xml.bind.JAXB;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 亚马逊订单服务
 *
 * <AUTHOR>
 * @date 2022/1/27
 */
@Service
@Slf4j
public class AmazonServiceImpl implements AmazonOrderService {

    private static final String SEARCH_URL = "/customer/platform/search";
    private static final String PRINT_LABELS = "/service/manage/LabelTemplatePrint";
    @Value("${crm.oldApi}")
    private String crmApi;
    @Value("${part-net.api}")
    private String partNetApi;
    /**
     * 列表分页查询
     */
    static final String LIST="/ejf/v2/amazon/SelectAmazonPaging";
    /**
     * 更新打印状态
     */
    static final String UPDATE_PRINT_STATUS="/ejf/v2/amazon/UpdateAmazonPrintStatus";
    /**
     * 列表数量统计
     */
    static final String LIST_COUNT="/ejf/v2/amazon/AmazonSubStatusGroup";
    /**
     * 运单详情
     */
    static final String DETAIL="/ejf/v2/amazon/SelectOrderInfo?trackingNumber={}";
    @Value("${ejf.host}")
    private String ejf;
    @Resource
    private PLMService service;
    @Value("${part-net.template-code.amazon}")
    private String ejfLabelTemplateAmazon;

    /**
     * 同于获取绑定的亚马逊账号
     *
     * @param param 参数
     * @return
     * @throws IOException
     */
    @Override
    public List<QueryPlatformAccountResult.DataDTO> queryPlatformAccount(QueryPlatformAccountParam param) throws IOException {
        QueryPlatformAccountResult result = HttpUtil.doPost(crmApi.concat(SEARCH_URL), param, QueryPlatformAccountResult.class);
        if (Objects.nonNull(result)) {
            if (result.getResult()) {
                return result.getData();
            }
            throw error(result.getMessage());
        }
        throw error("平台账号获取失败");
    }

    /**
     * 更新打印状态
     *
     * @param trackNumbers 运单号
     * @return 结果
     */
    @Override
    public UpdatePrintStatusResultDTO updatePrintStatus(List<String> trackNumbers) {
        return HttpUtil.clazz(UpdatePrintStatusResultDTO.class)
                        .body(trackNumbers)
                        .url(partNetApi.concat(UPDATE_PRINT_STATUS))
                        .post();
    }

    /**
     * 获取敦煌标签
     *
     * @param customerCode 制单账号
     * @param trackNumber  运单号
     * @param orderNumber  订单号
     * @return  敦煌标签
     */
    @Override
    public String getDhLabelVase64String(String customerCode, String trackNumber, String orderNumber) {
        final String url = partNetApi + "/ejf/v2/dh/label";
        final DhBase64LabelDTO result = HttpUtil.clazz(DhBase64LabelDTO.class)
                .url(url)
                .body(ImmutableMap.of("tracking_number", trackNumber, "order_number", orderNumber))
                .post();
        if (result.getCode() != 0 || StrUtil.isBlank(result.getLabelBase64())) {
            log.error("打印敦煌标签失败 {} {} {}", trackNumber, orderNumber, JSON.toJSONString(result));
            throw BusinessException.msg(result.getMessage());
        }
        return result.getLabelBase64();
    }

    /**
     * 打印标签
     *
     * @param trackNumber 参数
     * @return
     * @throws IOException
     */
    @Override
    public String getLabelBase64String(String customerCode, String trackNumber) {
        // 根据运单号查询运单详情
        AmazonOrderDetail detail = queryOrderDetail(trackNumber);
        AmazonOrderDetailVO amazonOrderDetail = new AmazonOrderDetailVO(detail);
        PrintLabelsParam param = new PrintLabelsParam();
        param.setTemplateCode(ejfLabelTemplateAmazon);
        PrintLabelsParam.ExtensionTypeDTO extensionTypeDTO = new PrintLabelsParam.ExtensionTypeDTO();
        extensionTypeDTO.setField01("AL");
        extensionTypeDTO.setField02("A4");
        extensionTypeDTO.setField03("grg");
        param.setExtensionType(extensionTypeDTO);
        // 订单信息
        PrintLabelsParam.ExpressTypeDTO expressTypeDTO = new PrintLabelsParam.ExpressTypeDTO();
        AmazonOrderDetailVO.OrderDetailDTO orderDetail = amazonOrderDetail.getOrderDetail();
        expressTypeDTO.setUserid(customerCode);
        expressTypeDTO.setUserOrderNumber(orderDetail.getTrackingNumber());
        expressTypeDTO.setYanwenNumber(orderDetail.getTrackingNumber());
        expressTypeDTO.setChannel(orderDetail.getProductCode());
        expressTypeDTO.setEpcode(orderDetail.getWaybillNumber());
        List<AmazonOrderDetailVO.ParcelInfoDTO.GoodsInfosDTO> goodsInfos = amazonOrderDetail.getParcelInfo().getGoodsInfos();
        StringBuilder goodNames = new StringBuilder();
        for (AmazonOrderDetailVO.ParcelInfoDTO.GoodsInfosDTO goodsInfo : goodsInfos) {
            goodNames.append(goodsInfo.getGoodsNameCN()).append("*").append(goodsInfo.getQuantity()).append(";");
        }
        expressTypeDTO.setMemo(goodNames.toString());
        // 收件人信息
        PrintLabelsParam.ExpressTypeDTO.ReceiverDTO receiverDTO = new PrintLabelsParam.ExpressTypeDTO.ReceiverDTO();
        AmazonOrderDetailVO.ReceiverInfoDTO receiverInfo = amazonOrderDetail.getReceiverInfo();
        receiverDTO.setName(receiverInfo.getName());
        receiverDTO.setCountry(receiverInfo.getCountryCode());
        receiverDTO.setState(receiverInfo.getProvince());
        receiverDTO.setCity(receiverInfo.getCity());
        receiverDTO.setAddress1(receiverInfo.getDetailAddress());
        receiverDTO.setPhone(receiverInfo.getPhone());
        receiverDTO.setPostcode(receiverInfo.getZipCode());
        expressTypeDTO.setReceiver(receiverDTO);
        expressTypeDTO.setGoodsName(new PrintLabelsParam.ExpressTypeDTO.GoodsNameDTO());
        param.setExpressType(expressTypeDTO);
        try(Response response = HttpUtil.doPost(ejf.concat(PRINT_LABELS), EJFUtil.getXmlStr(param), Response.class, MediaType.APPLICATION_XML)) {
            // 获取结果类
            PrintLabelsResult result = JAXB.unmarshal(response.body().byteStream(), PrintLabelsResult.class);
            if (Objects.nonNull(result.getIsSuccess())) {
                if (result.getIsSuccess()) {
                    return result.getBase64String();
                } else {
                    throw new BusinessException(result.getErrorMsg());
                }
            }
            throw new BusinessException(AmazonOrderConstant.EJF_LABEL_ERROR);
        }
    }


    /**
     * 抛错
     *
     * @param message 错误信息
     * @return
     */
    private BusinessException error(String message) {
        return BusinessException.msg(message);
    }

    @Override
    public PageResultVO<AmazonOrder, Express> queryOrders(QueryOrderParam queryOrderParam) {

        // 设置对应状态
        Collection<Integer> orderState = SearchStatus
                .getSearchStatus(queryOrderParam.getStatus())
                .getWaybillEnumKey().stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        queryOrderParam.setStatus(null);
        queryOrderParam.setSubStatus(orderState);
        // 列表查询
        QueryOrderResultDTO queryOrderResultDTO = getAmazonList(queryOrderParam);
        // 列表状态统计查询
        QueryOrderCountResultDTO queryOrderCountResultDTO =
                HttpUtil.doPost(partNetApi.concat(LIST_COUNT),
                        queryOrderParam,
                        QueryOrderCountResultDTO.class);
        if (Objects.isNull(queryOrderResultDTO) || Objects.isNull(queryOrderCountResultDTO)) {
            throw new BusinessException(AmazonOrderConstant.OMS_ERROR);
        }
        // 响应页面对象初始化
        PageResultVO<AmazonOrder, Express> pageResultVO = PageResultVO.<AmazonOrder, Express>builder()
                .totalPages(0)
                .totalCount(0)
                .records(Collections.emptyList())
                .build();
        // 获取实际返回数据
        if (queryOrderResultDTO.getSuccess()) {
            List<AmazonOrder> amazonOrders = new LinkedList<>();
            // 转换为本地亚马逊对象并响应前端
            for (QueryOrderResultDTO.DataDTO o : queryOrderResultDTO.getData()) {
                amazonOrders.add(amazonInterfaceToLocal(o));
            }
            // 总条数除以每页条数再向上取整
            int totalPages = NumberUtil.ceilDiv(queryOrderResultDTO.getTotalCount(), queryOrderParam.getPageSize());
            pageResultVO = PageResultVO.<AmazonOrder, Express>builder()
                    .totalPages(totalPages)
                    .totalCount(queryOrderResultDTO.getTotalCount())
                    .records(amazonOrders)
                    .build();
        }
        if (queryOrderCountResultDTO.getSuccess()) {
            pageResultVO.setStatusCounts(getExpress(queryOrderCountResultDTO.getData()));
        }else {
            pageResultVO.setStatusCounts(getExpress(Collections.emptyList()));
        }
        return pageResultVO;
    }

    /**
     * 转换为本地亚马逊对象
     *
     * @param o 远程亚马逊对象
     */
    @Override
    public AmazonOrder amazonInterfaceToLocal(QueryOrderResultDTO.DataDTO o) {
        AmazonOrder amazonOrder = new AmazonOrder();
        amazonOrder.setOrderNumber(o.getLogisticsOrderCode());
        amazonOrder.setTrackNumber(o.getTrackingId());
        amazonOrder.setGoodsName(o.getDescriptionName());
        amazonOrder.setSubStatus(o.getSubStatus());
        amazonOrder.setGoodsNameEn(o.getDescriptionEn());
        amazonOrder.setReceiverCity(o.getReceiverCity());
        amazonOrder.setPrintStatus(o.getPrintStatus());
        final CountryResultDTO.RegionListDTO country = service.getCountryByCode(o.getDestinationCountry());
        if (Objects.nonNull(country)) {
            amazonOrder.setDestinationCountry(country.getChinesename());
        }
        amazonOrder.setReceiverName(o.getReceiverName());
        amazonOrder.setOrderTime(o.getOrderTime());
        // 设置平台账号
        amazonOrder.setPlatformCode(o.getPlatfromCustomCode());
        String productCode = String.valueOf(o.getProductCode());
        final ProductResultDTO.DataDTO product = service.getProductByCode(productCode);
        if (Objects.nonNull(product)) {
            amazonOrder.setProductName(product.getProductCnName());
        }
        return amazonOrder;
    }

    /**
     * 列表查询
     * @param queryOrderParam  查询参数
     * @return 查询结果
     */
    @Override
    public QueryOrderResultDTO getAmazonList(QueryOrderParam queryOrderParam) {
        // 列表查询
        return HttpUtil.doPost(partNetApi.concat(LIST),
                queryOrderParam,
                QueryOrderResultDTO.class);
    }

    /**
     * 获取数量统计信息
     * @param queryOrderCountResultDTO  数据
     * @return  统计信息
     */
    private static Collection<Express> getExpress(List<QueryOrderCountResultDTO.DataDTO> queryOrderCountResultDTO) {
        // 获取总数量枚举类
        Map<SearchStatus, Express> expressMap = getBaseExpress();
        for (SearchStatus searchStatus : expressMap.keySet()) {
            // 获取汇总数量类
            Express gather = expressMap.get(searchStatus);
            Set<String> waybillEnumKey = searchStatus.getWaybillEnumKey();
            // 循环进行汇总订单数量
            if (!CollectionUtils.isEmpty(queryOrderCountResultDTO)) {
                // 循环进行汇总订单数量
                for (QueryOrderCountResultDTO.DataDTO express : queryOrderCountResultDTO) {
                    if (waybillEnumKey.contains(String.valueOf(express.getSubStatus()))) {
                        gather.setQuantity(gather.getQuantity() + express.getCount());
                    }
                }
            }
        }
        final Collection<Express> values = expressMap.values();
        // 统计所有运单信息
        final Express all = Express.builder()
                .status(SearchStatus.ALL_ORDER.getStatus())
                .description(SearchStatus.ALL_ORDER.getMsg())
                .quantity(values.stream().mapToInt(Express::getQuantity).sum())
                .build();
        expressMap.put(SearchStatus.ALL_ORDER, all);
        return expressMap.values();
    }


    /**
     * 获取基础数量统计实体类
     *
     * @return 数量统计实体类
     */
    public static Map<SearchStatus, Express> getBaseExpress() {
        return Arrays.stream(SearchStatus.values())
                .filter(x -> !ImmutableSet.of("-1","-2","9","10","11","12").contains(x.getStatus()))
                .collect(Collectors.toMap(
                        k -> k,
                        v -> Express.builder()
                                .status(v.getStatus())
                                .description(v.getMsg())
                                .quantity(0).build(),
                        (v1, v2) -> v1)
                );
    }

    @Override
    public AmazonOrderDetail queryOrderDetail(String trackNumber) {
        // 获取详情信息
        String url = partNetApi.concat(StrUtil.format(DETAIL, trackNumber));
        AmazonOrderDetail detail = HttpUtil.doGet(url, AmazonOrderDetail.class);
        if (Objects.isNull(detail)) {
            throw new BusinessException(AmazonOrderConstant.OMS_ERROR);
        }
        if (!detail.getHasError()) {
            List<AmazonOrderDetail.DataDTO> detailData = detail.getData();
            if (CollectionUtils.isEmpty(detailData)) {
                throw new BusinessException("无此运单信息");
            }
            AmazonOrderDetail.DataDTO dataDTO = detailData.get(0);
            String productCode = String.valueOf(dataDTO.getStype());
            final ProductResultDTO.DataDTO product = service.getProductByCode(productCode);
            if (Objects.nonNull(product)) {
                dataDTO.setProductName(product.getProductCnName());
            }
            final CountryResultDTO.RegionListDTO country = service.getCountryByCode(dataDTO.getReceiverCountryCode());
            if (Objects.nonNull(country)) {
                dataDTO.setCountryName(country.getChinesename());
            }
            return detail;
        }
        throw new BusinessException(detail.getMessage());
    }

    /**
     * 获取详情信息，不做处理
     * @param trackNumber   运单号
     * @return 详情信息
     */
    @Override
    public AmazonOrderDetail getAmazonOrderDetail(String trackNumber) {
        // 获取详情信息
        String url = partNetApi.concat(StrUtil.format(DETAIL, trackNumber));
        return HttpUtil.clazz(AmazonOrderDetail.class).url(url).get();
    }

    /**
     * 打印标签
     *
     * @param customerCode      发货账号
     * @param amazonOrderDetail 详情
     * @return 打印标签
     */
    @Override
    public String getLabelBase64String(String customerCode, AmazonOrderDetailVO amazonOrderDetail) {
        PrintLabelsParam param = new PrintLabelsParam();
        param.setTemplateCode(ejfLabelTemplateAmazon);
        PrintLabelsParam.ExtensionTypeDTO extensionTypeDTO = new PrintLabelsParam.ExtensionTypeDTO();
        extensionTypeDTO.setField01("AL");
        extensionTypeDTO.setField02("A4");
        extensionTypeDTO.setField03("grg");
        param.setExtensionType(extensionTypeDTO);
        // 订单信息
        PrintLabelsParam.ExpressTypeDTO expressTypeDTO = new PrintLabelsParam.ExpressTypeDTO();
        AmazonOrderDetailVO.OrderDetailDTO orderDetail = amazonOrderDetail.getOrderDetail();
        expressTypeDTO.setUserid(customerCode);
        expressTypeDTO.setUserOrderNumber(orderDetail.getTrackingNumber());
        expressTypeDTO.setYanwenNumber(orderDetail.getTrackingNumber());
        expressTypeDTO.setChannel(orderDetail.getProductCode());
        expressTypeDTO.setEpcode(orderDetail.getWaybillNumber());
        List<AmazonOrderDetailVO.ParcelInfoDTO.GoodsInfosDTO> goodsInfos = amazonOrderDetail.getParcelInfo().getGoodsInfos();
        StringBuilder goodNames = new StringBuilder();
        for (AmazonOrderDetailVO.ParcelInfoDTO.GoodsInfosDTO goodsInfo : goodsInfos) {
            goodNames.append(goodsInfo.getGoodsNameCN()).append("*").append(goodsInfo.getQuantity()).append(";");
        }
        expressTypeDTO.setMemo(goodNames.toString());
        // 收件人信息
        PrintLabelsParam.ExpressTypeDTO.ReceiverDTO receiverDTO = new PrintLabelsParam.ExpressTypeDTO.ReceiverDTO();
        AmazonOrderDetailVO.ReceiverInfoDTO receiverInfo = amazonOrderDetail.getReceiverInfo();
        receiverDTO.setName(receiverInfo.getName());
        receiverDTO.setCountry(receiverInfo.getCountryCode());
        receiverDTO.setState(receiverInfo.getProvince());
        receiverDTO.setCity(receiverInfo.getCity());
        receiverDTO.setAddress1(receiverInfo.getDetailAddress());
        receiverDTO.setPhone(receiverInfo.getPhone());
        receiverDTO.setPostcode(receiverInfo.getZipCode());
        expressTypeDTO.setReceiver(receiverDTO);
        expressTypeDTO.setGoodsName(new PrintLabelsParam.ExpressTypeDTO.GoodsNameDTO());
        param.setExpressType(expressTypeDTO);
        try(Response response = HttpUtil.post(ejf.concat(PRINT_LABELS), Collections.emptyMap(), com.google.common.net.MediaType.APPLICATION_XML_UTF_8, EJFUtil.getXmlStr(param));) {
            // 获取结果类
            PrintLabelsResult result = JAXB.unmarshal(response.body().byteStream(), PrintLabelsResult.class);
            if (Objects.nonNull(result.getIsSuccess())) {
                if (result.getIsSuccess()) {
                    return result.getBase64String();
                } else {
                    throw new BusinessException(result.getErrorMsg());
                }
            }
            throw new BusinessException(AmazonOrderConstant.EJF_LABEL_ERROR);
        }

    }

}
