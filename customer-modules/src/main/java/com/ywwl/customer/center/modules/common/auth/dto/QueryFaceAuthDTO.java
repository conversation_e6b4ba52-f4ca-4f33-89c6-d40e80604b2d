package com.ywwl.customer.center.modules.common.auth.dto;

import com.ywwl.customer.center.modules.common.auth.constant.CommonCrmConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/3/6 9:54
 */
@Data
public class QueryFaceAuthDTO {
    private String no;
    /**
     * <AUTHOR>
     * @description 1腾讯刷脸, 2支付宝刷脸
     * @date 2023/3/6 9:55
     **/
    @NotNull(message = "[认证类型]不能为空")
    private Integer faceAuthType;

    private String userCode;
    /**
     * <AUTHOR>
     * @description 来源类型, 提供给公共crm
     * @date 2023/3/10 17:10
     **/
    private Integer sourceType = CommonCrmConstant.PORTAL_SOURCE_TYPE;
}
