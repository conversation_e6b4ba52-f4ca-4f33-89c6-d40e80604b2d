package com.ywwl.customer.center.modules.common.bill.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;

/**
 * @author: dinghy
 * @date: 2023/4/20 15:06
 */
@Data
public class QueryHistoryDTO {
    private String opCode = BillOpCodeEnum.HISTORY.value();
    private Integer businessType;
    @JSONField(serialize = false)
    private String userCode;
    /**
     * 记账开始时间
     */
    private String startRefDate;
    /**
     * 记账结束时间
     */
    private String endRefDate;
    /**
     * 交易类型
     * 帐单: billType 收款：receType 帐单+收款：allType
     */
    private String transType;

    private String billType;

    private String merchantCode;

    private Integer pageSize = 10;
    private Integer currentPage = 1;

}
