package com.ywwl.customer.center.modules.business.converter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * 揽收仓名称转换器
 *
 * <AUTHOR>
 * @since 2023/10/13 11:08
 **/
@Component
public class CompanyConverter extends JsonSerializer<String> implements Converter<String> {

    /**
     * 揽收仓名称转换器
     *
     * @param value              揽收仓编码
     * @param jsonGenerator      json生成器
     * @param serializerProvider 序列化提供者
     * @throws IOException
     */
    @Override
    public void serialize(String value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (StringUtils.isNotBlank(value)) {
            // 从PLM获取揽收仓名称
            final WareHouseVo warehouse = EJFUtil.cmccService.getWarehouseById(value);
            if (Objects.nonNull(warehouse)) {
                // 揽收仓名称
                jsonGenerator.writeString(warehouse.getName());
            }
        }
    }

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        final WareHouseVo warehouse = EJFUtil.cmccService.getWarehouseById(value);
        if (Objects.nonNull(warehouse)) {
            return new WriteCellData<>(StrUtil.format("{}-{}", value, warehouse.getName()));
        }
        return new WriteCellData<>(value);
    }

}
