package com.ywwl.customer.center.modules.common.auth.enums;

public enum BusinessProcessStateEnum {
    UN_SUBMIT_INFO(0,"信息未提交"),
    SUBMIT_INFO(1,"信息提交,待审核"),
    AUDIT_FAIL(2,"信息提交,审核失败"),
    AUDIT_SUCCESS(3,"信息提交,审核成功,待签约"),
    SIGN_SUCCESS(4,"签约成功"),

    ;
    private Integer value;
    private String desc;

    BusinessProcessStateEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer value() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
