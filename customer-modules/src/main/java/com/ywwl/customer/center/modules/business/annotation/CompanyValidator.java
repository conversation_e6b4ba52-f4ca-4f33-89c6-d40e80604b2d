package com.ywwl.customer.center.modules.business.annotation;

import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Objects;

/**
 * 揽收仓校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class CompanyValidator implements ConstraintValidator<CompanyVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final WareHouseVo warehouse = EJFUtil.cmccService.getWarehouseById(StringUtils.strip(value));
        // 其他校验，仅Code校验
        return Objects.nonNull(warehouse);
    }

}
