package com.ywwl.customer.center.modules.business.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 包裹类型转换器
 *
 * <AUTHOR>
 * @since 2023/10/20 15:17
 **/
public class DutyTypeConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(getValue(value));
    }

    /**
     * 获取值
     *
     * @param value value
     * @return com.alibaba.excel.metadata.data.WriteCellData<java.lang.Object>
     */
    public static String getValue(String value) {
        if (Objects.equals("DDU", value)) {
            return "DDU(收件人支付关税)";
        } else if (Objects.equals("DDP", value)) {
            return "DDP(寄件人支付关税)";
        }
        return value;
    }


}
