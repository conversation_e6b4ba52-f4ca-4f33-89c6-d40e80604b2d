package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单列表查询
 *
 * <AUTHOR>
 * @since 2023/10/11 16:11
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeDraftOrderGetListFilterDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 页码
         */
        private Integer pageNum;
        /**
         * 页大小
         */
        private Integer pageSize;
        /**
         * records
         */
        private List<BusinessSimpleDraftDTO> records;
        /**
         * 总条数
         */
        private Integer totalRecord;
        /**
         * 总页数
         */
        private Integer totalPages;

    }
}
