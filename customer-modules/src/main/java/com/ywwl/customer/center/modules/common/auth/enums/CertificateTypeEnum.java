package com.ywwl.customer.center.modules.common.auth.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum CertificateTypeEnum {
    ID_CARD(0, "CRED_PSN_CH_IDCARD", "大陆身份证"),
    HONG_KONG_ID_CARD(1, "", "香港身份证"),
    AO_MEN_ID_CARD(2, "", "澳门身份证"),
    TAI_WAN_ID_CARD(3, "", "台湾身份证"),
    HONG_HONG_PASSPORT(4, "CRED_PSN_CH_HONGKONG", "香港来往大陆通行证"),
    AO_MEN_PASSPORT(5, "CRED_PSN_CH_MACAO", "澳门来往大陆通行证"),
    TAI_WAN_PASSPORT(6, "CRED_PSN_CH_TWCARD", "台湾来往大陆通行证"),
    PASSPORT(7, "CRED_PSN_PASSPORT", "护照"),
    ;
    @EnumValue
    @JsonValue
    private final Integer code;
    private String typeCode;
    private String desc;

    CertificateTypeEnum(Integer code, String typeCode, String desc) {
        this.code = code;
        this.desc = desc;
        this.typeCode = typeCode;
    }

    public static String getTypeCode(Integer code) {
        for (CertificateTypeEnum value : CertificateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getTypeCode();
            }
        }
        return "";

    }
}
