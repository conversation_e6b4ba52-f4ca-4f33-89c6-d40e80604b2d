package com.ywwl.customer.center.modules.common.auth.enums;



import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;

public enum CustomerTypeEnum {
    INDIVIDUAL(0, "个人"), ENTERPRISE(1, "企业");

    CustomerTypeEnum(Integer value, String desc) {
        this.value = value;
		this.desc = desc;
	}
	
	private Integer value;
	private String desc;


    public Integer value() {
		return value;
	}

    public String desc() {
		return desc;
	}

	public static CustomerTypeEnum getCustomerTypeEnum(Integer value){
		for (CustomerTypeEnum customerTypeEnum : CustomerTypeEnum.values()) {
			if(customerTypeEnum.value().equals(value)){
				return customerTypeEnum;
			}
		}
		throw new BusinessException(ResponseCode.PORTAL_7003);
	}
}
