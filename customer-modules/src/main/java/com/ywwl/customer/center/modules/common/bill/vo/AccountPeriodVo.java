package com.ywwl.customer.center.modules.common.bill.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/20 10:55
 */
@Data
public class AccountPeriodVo {
    /**
     * <AUTHOR>
     * @description 账单总金额
     * @date 2023/4/20 11:31
     **/
    @JSONField(alternateNames = "sumBillAmount")
    private BigDecimal sumBillAmount;
    @JSONField(alternateNames = "sumBalAmout")
    private BigDecimal sumBalAmount;
    @JSONField(alternateNames = "sumBalingAmout")
    private BigDecimal sumBalingAmount;
    @JSONField(alternateNames = "returnlist")
    private List<BillVO> list;
    @JSONField(serialize = false)
    private Boolean result;
    @JSONField(serialize = false)
    private String message;

    @Data
    public static class BillVO {
        /**
         * @description 账单金额
         * <AUTHOR>
         * @date 2022/3/28 14:47
         */
        private BigDecimal billAmount;
        /**
         * @description 核销金额
         * <AUTHOR>
         * @date 2022/3/28 14:47
         */
        private BigDecimal balAmount;
        /**
         * @description 未清金额
         * <AUTHOR>
         * @date 2022/3/28 14:50
         */
        private BigDecimal balingAmount;
        /**
         * @description 账期
         * <AUTHOR>
         * @date 2022/3/28 14:47
         */
        private String billPeriod;
        /**
         * @description 核销状态, 1是全部核销, 2部分核销, 3是全部
         * <AUTHOR>
         * @date 2022/3/28 14:48
         */
        private Integer chkStatus;
        /**
         * <AUTHOR>
         * @description 付款到期日
         * @date 2023/7/17 15:22
         **/
        private LocalDate dueDate;
        /**
         * EJF流水号
         */
        private String yanwenOrderNumber;
    }
}
