package com.ywwl.customer.center.modules.business.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商业快递国家
 *
 * <AUTHOR>
 * @since 2023/10/18 14:14
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class BusinessChannelDTO extends GeneralDTO {

    /**
     * data
     */
    private List<DataDTO> data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 产品ID
         */
        private String productNumber;
        /**
         * 产品名称
         */
        private String productCnName;
        /**
         * 英文名称
         */
        private String productEnName;
        /**
         * 是否推荐
         */
        private Boolean hasRecommend;
        /**
         * 主类
         */
        private String primaryClassify;
        /**
         * 二级分类
         */
        private String secondaryClassify;
        /**
         * 排序
         */
        @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
        private Integer displayOrder;
    }

}
