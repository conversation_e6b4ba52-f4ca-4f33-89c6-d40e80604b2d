package com.ywwl.customer.center.modules.business.annotation.entrance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.modules.general.cmcc.dto.BusinessOrderEnumDTO.DataDTO.ValueDTO;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

import static com.ywwl.customer.center.modules.ejf.util.EJFUtil.cmccService;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class BusinessTypeImportValidator implements ConstraintValidator<BusinessTypeImportVerify, String> {

    /**
     * 商业快递枚举类型
     */
    private CmccItemEnum item;

    @Override
    public void initialize(BusinessTypeImportVerify constraintAnnotation) {
        item = constraintAnnotation.item();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<ValueDTO> orderTypeEnum =
                cmccService.getBusinessOrderTypeEnum(item, StringUtils.strip(value), ValueDTO::getCode, ValueDTO::getValue);
        // 导入校验
        if (CollectionUtil.isEmpty(orderTypeEnum)) {
            return false;
        }
        if (orderTypeEnum.size() > 1) {
            setError(StrUtil.format("有多个匹配项 {}", orderTypeEnum), constraintValidatorContext);
            return false;
        }
        // 获取枚举类型
        return true;
    }

    /**
     * 设置错误信息
     *
     * @param msg 错误信息
     */
    private void setError(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg)
                .addConstraintViolation();
    }

}
