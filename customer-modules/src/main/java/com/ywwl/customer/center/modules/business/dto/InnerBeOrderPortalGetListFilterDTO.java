package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运单列表
 *
 * <AUTHOR>
 * @since 2023/10/10 15:18
 **/
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class InnerBeOrderPortalGetListFilterDTO extends GeneralDTO {
    
    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 页码
         */
        private Integer pageNum;
        /**
         * 页大小
         */
        private Integer pageSize;
        /**
         * records
         */
        private List<BusinessSimpleOrderDTO> records;
        /**
         * 总条数
         */
        private Integer totalRecord;
        /**
         * 总页数
         */
        private Integer totalPages;
    }

}
