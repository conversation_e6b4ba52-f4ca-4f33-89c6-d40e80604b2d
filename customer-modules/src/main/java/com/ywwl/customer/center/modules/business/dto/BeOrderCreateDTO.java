package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 创建订单
 *
 * <AUTHOR>
 * @since 2023/10/10 11:32
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class BeOrderCreateDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运单号
         */
        private String waybillNumber;
        /**
         * 订单号
         */
        private String orderNumber;
    }

}
