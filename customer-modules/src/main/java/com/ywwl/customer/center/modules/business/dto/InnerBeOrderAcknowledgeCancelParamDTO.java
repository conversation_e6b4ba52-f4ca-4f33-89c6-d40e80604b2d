package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 取消确认发货参数
 *
 * <AUTHOR>
 * @since 2023/10/11 15:39
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class InnerBeOrderAcknowledgeCancelParamDTO {

    /**
     * 制单账号
     */
    private String userId;
    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String waybillNumber;
    /**
     * 平台
     */
    private String platform;

}
