package com.ywwl.customer.center.modules.common.bill.enums;


public enum PayCycleEnum  {
    PRE_PAY(1, "预付款"),
    ONE_WEEK(2, "周结"),
    TWO_WEEK(3, "双周结"),
    FOUR_WEEK(4, "四周结"),
    MONTH_PAY(5, "月结"),
    //    ONE_WEEK_DENIED(6, "周结"),
//    TWO_WEEK_DENIED(7, "双周"),
//    FOUR_WEEK_DENIED(8, "四周"),
    MONTH_PAY_DENIED(9, "月结"),
    HALF_MONTH_DENIED(10, "半月结"),
    // 海外派客户结算方式
    SPOT_PAYMENT(11, "现付"),
    ;

    private Integer value;
    private String desc;

    PayCycleEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        for(PayCycleEnum enums:PayCycleEnum.values()){
            if(enums.value() == code){
                return enums.desc();
            }
        }
        return "";
    }

    public Integer value() {
        return value;
    }


    public String desc() {
        return desc;
    }
}
