package com.ywwl.customer.center.modules.amazon.vo;

import com.ywwl.customer.center.modules.amazon.result.AmazonOrderDetail;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: dinghy
 * @date: 2022/9/7 13:48
 */
@NoArgsConstructor
@Data
public class AmazonOrderDetailVO {
    private OrderDetailDTO orderDetail;
    private SenderInfoDTO senderInfo;
    private ReceiverInfoDTO receiverInfo;
    private ParcelInfoDTO parcelInfo;

    /**
     * 用于匹配新亚马逊接口的转换方法
     * @param detail    详情
     */
    public AmazonOrderDetailVO(AmazonOrderDetail detail) {

        final AmazonOrderDetail.DataDTO data = detail.getData().get(0);

        orderDetail = new OrderDetailDTO();
        orderDetail.setYwOrderCode(data.getLogisticsOrderCode());
        orderDetail.setPlatform(11);
        orderDetail.setTrackingNumber(data.getLogisticsOrderCode());
        orderDetail.setWaybillNumber(data.getTrackingId());
        orderDetail.setCustomerCode(data.getPaymentAccountUserid());
        orderDetail.setProductCode(String.valueOf(data.getStype()));
        orderDetail.setDestinationCountry(data.getReceiverCountryCode());
        orderDetail.setProductName(data.getProductName());
        orderDetail.setCountryName(data.getCountryName());


        senderInfo = new SenderInfoDTO();
        senderInfo.setName(data.getSenderAddressEnName());
        senderInfo.setCompany("");
        senderInfo.setPhone(data.getSenderPhone());
        senderInfo.setEmail(data.getSenderEmail());
        senderInfo.setCountryCode(data.getSenderCountryCode());
        senderInfo.setProvince(data.getSenderAddressEnProvince());
        senderInfo.setCity(data.getSenderAddressEnCity());
        senderInfo.setDistict("");
        senderInfo.setZipCode(data.getSenderZipcode());
        senderInfo.setDetailAddress(data.getSenderAddressEnStreetAddress1()+data.getSenderAddressEnStreetAddress2());
        senderInfo.setTaxId(data.getSenderTaxNumber());

        receiverInfo = new ReceiverInfoDTO();
        receiverInfo.setName(data.getReceiverAddressEnName());
        receiverInfo.setCompany("");
        receiverInfo.setPhone(data.getReceiverPhone());
        receiverInfo.setEmail(data.getReceiverEmail());
        receiverInfo.setCountryCode(data.getReceiverCountryCode());
        receiverInfo.setProvince(data.getReceiverAddressEnProvince());
        receiverInfo.setCity(data.getReceiverAddressEnCity());
        receiverInfo.setDistict("");
        receiverInfo.setZipCode(data.getReceiverZipcode());
        receiverInfo.setDetailAddress(data.getReceiverAddressEnStreetAddress1()+data.getReceiverAddressEnStreetAddress2());
        receiverInfo.setTaxId(data.getReceiverTaxNumber());

        parcelInfo = new ParcelInfoDTO();
        parcelInfo.setPackageId(null);
        parcelInfo.setProductNameCN(data.getParcelDescriptionLocal());
        parcelInfo.setProductNameEN(data.getParcelDescriptionEn());
        parcelInfo.setWeight(String.valueOf(data.getParcelWeight()));
        parcelInfo.setWeightUnit(data.getParcelWeightUnit());
        parcelInfo.setLength(null);
        parcelInfo.setWidth(null);
        parcelInfo.setHeight(null);
        parcelInfo.setDimensionUnit(null);
        parcelInfo.setDeclareValue(data.getParcelDeclareValue());
        parcelInfo.setDeclareCurrency(data.getParcelPriceCurrency());
        parcelInfo.setQuantity(data.getProductList().size());
        parcelInfo.setHasBattery(data.getParcelHasBattery());

        final List<ParcelInfoDTO.GoodsInfosDTO> goodLists = data.getProductList().stream().map(g -> {
            ParcelInfoDTO.GoodsInfosDTO good = new ParcelInfoDTO.GoodsInfosDTO();
            good.setPackageId(Long.valueOf(g.getProductId()));
            good.setSkuId(g.getSku());
            good.setGoodsNameCN(g.getDescriptionLocal());
            good.setGoodsNameEN(g.getDescriptionEn());
            good.setDeclarePrice(String.valueOf(g.getValue()));
            good.setPriceCurrency(data.getParcelPriceCurrency());
            good.setDeclareWeight(g.getWeight());
            good.setQuantity(String.valueOf(g.getQuantity()));
            good.setHsCode(g.getHsCode());
            good.setProductUrl(g.getProductUrl());
            good.setImageUrl(g.getProductUrl());
            return good;
        }).collect(Collectors.toList());
        parcelInfo.setGoodsInfos(goodLists);
    }

    @NoArgsConstructor
    @Data
    public static class OrderDetailDTO {
        private String ywOrderCode;
        private Integer platform;
        /**
         * <AUTHOR>
         * @description 订单号
         * @date 2022/9/7 14:10
         **/
        private String trackingNumber;
        private String waybillNumber;
        private String customerCode;
        private String productCode;
        private String destinationCountry;
        private String productName;
        private String countryName;
    }

    @NoArgsConstructor
    @Data
    public static class SenderInfoDTO {
        private String name;
        private Object company;
        private String phone;
        private Object email;
        private String countryCode;
        private String province;
        private String city;
        private Object distict;
        private String zipCode;
        private String detailAddress;
        private Object taxId;
    }

    @NoArgsConstructor
    @Data
    public static class ReceiverInfoDTO {
        private String name;
        private Object company;
        private String phone;
        private Object email;
        private String countryCode;
        private String province;
        private String city;
        private Object distict;
        private String zipCode;
        private String detailAddress;
        private String taxId;
    }

    @NoArgsConstructor
    @Data
    public static class ParcelInfoDTO {
        private Long packageId;
        private Object productNameCN;
        private Object productNameEN;
        private String weight;
        private String weightUnit;
        private Object length;
        private Object width;
        private Object height;
        private String dimensionUnit;
        private String declareValue;
        private String declareCurrency;
        private Integer quantity;
        private Boolean hasBattery;
        private List<GoodsInfosDTO> goodsInfos;

        @NoArgsConstructor
        @Data
        public static class GoodsInfosDTO {
            private Long packageId;
            private Object skuId;
            private String goodsNameCN;
            private String goodsNameEN;
            private String declarePrice;
            private String priceCurrency;
            private Object declareWeight;
            private String quantity;
            private String hsCode;
            private Object productUrl;
            private Object imageUrl;
        }
    }

}
