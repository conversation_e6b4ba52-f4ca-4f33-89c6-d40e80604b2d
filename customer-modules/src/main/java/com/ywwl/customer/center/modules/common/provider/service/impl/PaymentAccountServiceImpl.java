package com.ywwl.customer.center.modules.common.provider.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.domain.UserAgent;
import com.ywwl.customer.center.common.enums.BusinessTypeEnum;
import com.ywwl.customer.center.common.enums.OPCodeEnum;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.common.utils.PrivacyDimmer;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.modules.common.auth.dto.QueryContractDTO;
import com.ywwl.customer.center.modules.common.auth.enums.CertificateTypeEnum;
import com.ywwl.customer.center.modules.common.auth.enums.CustomerTypeEnum;
import com.ywwl.customer.center.modules.common.auth.service.CustomerAuthService;
import com.ywwl.customer.center.modules.common.auth.vo.CompanyAuthVo;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerAuthVo;
import com.ywwl.customer.center.modules.common.auth.vo.CustomerVo;
import com.ywwl.customer.center.modules.common.auth.vo.PersonAuthVo;
import com.ywwl.customer.center.modules.common.provider.constant.PaymentConstant;
import com.ywwl.customer.center.modules.common.provider.dao.PaymentAccountDao;
import com.ywwl.customer.center.modules.common.provider.domain.AlipayPaymentOrder;
import com.ywwl.customer.center.modules.common.provider.domain.PaymentAccount;
import com.ywwl.customer.center.modules.common.provider.dto.*;
import com.ywwl.customer.center.modules.common.provider.enums.ContractTypeEnum;
import com.ywwl.customer.center.modules.common.provider.enums.PayTypeEnum;
import com.ywwl.customer.center.modules.common.provider.enums.PaymentApplyTypeEnum;
import com.ywwl.customer.center.modules.common.provider.enums.PaymentRecordEnum;
import com.ywwl.customer.center.modules.common.provider.reponse.CrmBody;
import com.ywwl.customer.center.modules.common.provider.reponse.CrmPlatformBody;
import com.ywwl.customer.center.modules.common.provider.reponse.RecordBody;
import com.ywwl.customer.center.modules.common.provider.reponse.VerificationJsonBody;
import com.ywwl.customer.center.modules.common.provider.service.AlipayPaymentOrderService;
import com.ywwl.customer.center.modules.common.provider.service.BankService;
import com.ywwl.customer.center.modules.common.provider.service.PaymentAccountService;
import com.ywwl.customer.center.modules.common.provider.service.VerificationService;
import com.ywwl.customer.center.modules.common.provider.vo.*;
import com.ywwl.customer.center.modules.fba.constant.FbaConstant;
import com.ywwl.customer.center.modules.fba.enums.FbaApiEnum;
import com.ywwl.customer.center.modules.fba.service.FbaApplyService;
import com.ywwl.customer.center.modules.fba.vo.FbaApplyInfoVO;
import com.ywwl.customer.center.modules.general.crm.enums.CrmApiEnum;
import com.ywwl.customer.center.modules.general.crm.service.CommonCrmService;
import com.ywwl.customer.center.modules.general.crm.vo.SignContractVO;
import com.ywwl.customer.center.modules.international.constant.AbnormalConstant;
import com.ywwl.customer.center.modules.international.service.PacketBusinessApplyService;
import com.ywwl.customer.center.modules.international.service.StraightCrmService;
import com.ywwl.customer.center.modules.international.vo.PacketApplyInfoVo;
import com.ywwl.customer.center.modules.overseas.service.YWEOverseaService;
import com.ywwl.customer.center.modules.overseas.vo.YWEApplyInfoVo;
import com.ywwl.customer.center.system.request.ResBody;
import com.ywwl.customer.center.system.service.FileService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ywwl.customer.center.modules.common.provider.enums.PaymentApplyTypeEnum.*;

/**
 * //
 *
 * <AUTHOR>
 * @date 2023/5/4
 */
@Service
@Slf4j
@DS(value = "old")
public class PaymentAccountServiceImpl extends ServiceImpl<PaymentAccountDao, PaymentAccount> implements PaymentAccountService {
    @Value("${crm.host}")
    private String crmApi;
    @Value("${acc.alipay-record-url}")
    private String alipayRecordUrl;
    @Value("${common-crm.host}")
    private String crmCommonApi;
    @Resource
    private PacketBusinessApplyService packetBusinessApplyService;
    @Resource
    private FileService fileService;
    @Resource
    private VerificationService verificationService;
    @Resource
    private AlipayPaymentOrderService alipayPaymentOrderService;
    @Resource
    private CommonCrmService commonCrmService;
    @Resource
    private PaymentAccountDao paymentAccountDao;
    @Resource
    private FbaApplyService fbaApplyService;
    @Resource
    private CustomerAuthService customerAuthService;
    @Resource
    private BankService bankService;
    @Value("${fba.url}")
    private String fbaUrl;
    @Resource
    private StraightCrmService straightCrmService;
    @Resource
    private YWEOverseaService ywEOverseaService;

    @Override
    public ResBody alipayRecord(PaymentRecordDTO paymentRecordDTO) {
        ResBody resBody = HttpUtil.doPost(crmCommonApi + PaymentRecordEnum.PAYMENT_RECORD_BANK.value(), paymentRecordDTO, ResBody.class);
        error(resBody);
        return resBody;
    }

    @Override
    public ResBody bankCardRecord(PaymentRecordDTO paymentRecordDTO) {
        log.info("银行账号备案参数:{}", paymentRecordDTO);
        ResBody resBody = HttpUtil.doPost(crmCommonApi + PaymentRecordEnum.PAYMENT_RECORD_BANK.value(), paymentRecordDTO, ResBody.class);
        log.info("银行账号备案结果:{},{}", paymentRecordDTO, resBody);
        error(resBody);
        if (!resBody.getResult()) {
            DingTalkClient.sendMessage("银行卡备案失败:" + JSONObject.toJSONString(paymentRecordDTO));
        }
        return resBody;
    }

    /**
     * <AUTHOR>
     * @description 非商户主体直接下发到账务记账
     * @date 2023/5/10 16:47
     **/
    public void pushAlipayOrder(AlipayOrderPaymentDTO alipayOrderPaymentDTO) {
        try {
            log.info("支付宝扫码非商户主体下发记账:参数{}", alipayOrderPaymentDTO);
            JSONObject jsonObject = HttpUtil.doPost(alipayRecordUrl, alipayOrderPaymentDTO);
            log.info("支付宝扫码非商户主体下发记账:结果{}", jsonObject);
            if (Objects.isNull(jsonObject)) {
                DingTalkClient.sendMessage("支付宝扫码非商户主体下发记账异常,支付宝流水号" + alipayOrderPaymentDTO.getTradeNo());
            }
        } catch (Exception e) {
            log.error("支付宝扫码非商户主体下发记账异常,支付宝流水号:{},原因:{}", alipayOrderPaymentDTO.getTradeNo(), e.getMessage());
            DingTalkClient.sendMessage("支付宝扫码非商户主体下发记账异常,支付宝流水号" + alipayOrderPaymentDTO.getTradeNo());
        }
    }

    @Override
    public List<PaymentAccount> thirdPartyList(String merchantCode, String businessType) {
        ThirdPartyDTO thirdPartyDTO = new ThirdPartyDTO();
        thirdPartyDTO.setMerchantCode(merchantCode);
        thirdPartyDTO.setAccountType(businessType);
        JSONObject jsonObject = HttpUtil.doPost(crmApi + OPCodeEnum.SEARCH_PAYMENT_RECORD.value(), thirdPartyDTO, JSONObject.class);
        log.info("第三方账号查询结果:{},{}", jsonObject, merchantCode);
        error(jsonObject);
        List<PaymentAccount> list = new ArrayList<>();
        boolean parseBoolean = Boolean.parseBoolean(jsonObject.getString(AbnormalConstant.RESULT_NAME));
        if (parseBoolean) {
            JSONArray jsonArray = jsonObject.getJSONArray(AbnormalConstant.DATA_NAME);
            for (int i = 0; i < jsonArray.size(); i++) {
                ThirdPartyVo thirdPartyVo = jsonArray.getJSONObject(i).to(ThirdPartyVo.class);
                PaymentAccount transition = transition(thirdPartyVo);
                list.add(transition);
            }
        }
        return list;
    }

    @Override
    public JsonResult<PaymentAccountNum> paymentList(String merchantCode, String status, String businessType) {
        //查询当前流程未走完且有效的数据
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setMerchantCode(merchantCode);
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setEffectivity(PaymentConstant.VALID_CODE);
        if(String.valueOf(BusinessTypeEnum.OVERSEA.getValue()).equals(businessType)||String.valueOf(BusinessTypeEnum.CHINA_WAREHOUSE.getValue()).equals(businessType)){
            businessType=String.valueOf(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        }
        paymentAccount.setBusinessType(Integer.valueOf(businessType));
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(paymentAccount);
        List<PaymentAccount> accounts = list(wrapper);
        SearchCrmPaymentDTO dto = new SearchCrmPaymentDTO();
        dto.setMerchantCode(merchantCode);
        List<PaymentAccount> paymentAccounts = crmPaymentList(dto);
        accounts.addAll(paymentAccounts);
        // 获取第三方账户列表账号关系
        List<PaymentAccount> thirdPartyAccounts = thirdPartyList(merchantCode, businessType);
        Map<String, PaymentAccount> thirdPartyMap = thirdPartyAccounts.stream().collect(Collectors.toMap(PaymentAccount::getBankCardId, Function.identity()));
        // 对accounts列表进行分块并行处理
        accounts.addAll(thirdPartyAccounts);
        String finalBusinessType = businessType;
        Lists.partition(accounts, PaymentConstant.THREAD_SIZE).parallelStream().forEach(list -> {
            for (PaymentAccount account : list) {
                account.setBusinessType(Integer.valueOf(finalBusinessType));
                PaymentAccount thirdPartyAccount = thirdPartyMap.get(account.getBankCardId());
                if (null != thirdPartyAccount) {
                    account.setRelationShip(thirdPartyAccount.getRelationShip());
                    account.setAttachStatus(thirdPartyAccount.getAttachStatus());
                    account.setPaymentTopicType(thirdPartyAccount.getPaymentTopicType());
                    account.setAttach(thirdPartyAccount.getAttach());
                }
            }
        });
        //获取集合中的不同数据的统计数量
        PaymentAccountNum paymentAccountNum = new PaymentAccountNum();
        //单独状态数据
        List<PaymentAccount> paymentAccountList = statusSwitching(accounts, status, paymentAccountNum);
        paymentAccountNum.setPaymentAccounts(paymentAccountList);
        return JsonResult.success(paymentAccountNum);
    }

    private void paymentAccountListNums(List<PaymentAccount> accounts, PaymentAccountNum paymentAccountNum) {
        paymentAccountNum.setAllNum(accounts.size());
        paymentAccountNum.setSuccessNum(accounts.stream().filter(account -> account.getExist() != null && account.getExist()).count());
        paymentAccountNum.setCheckNum(accounts.stream().filter(account -> PROCESSSTATUS_CODE8.value().equals(account.getProcessStatus())).count());
        paymentAccountNum.setPayNum(accounts.stream().filter(account -> PROCESSSTATUS_CODE8.value().equals(account.getProcessStatus())).count());
        paymentAccountNum.setLoseNum(accounts.stream().filter(account -> PROCESSSTATUS_CODE4.value().equals(account.getProcessStatus())).count());
        paymentAccountNum.setRefuseNum(accounts.stream().filter(account -> PROCESSSTATUS_CODE10.value().equals(account.getProcessStatus())).count());
        paymentAccountNum.setVerifyNum(accounts.stream().filter(account -> APPLYTYPE_CODE3.value().equals(account.getApplicationType()) || APPLYTYPE_CODE4.value().equals(account.getApplicationType())).count());

    }

    private List<PaymentAccount> statusSwitching(List<PaymentAccount> accounts, String status, PaymentAccountNum paymentAccountNum) {
        if (!accounts.isEmpty()) {
            try {
                accounts.removeIf(account -> account.getImportSign() != null && account.getImportSign() == 5);
                Set<String> uniqueKeys = new HashSet<>();
                List<PaymentAccount> filteredAccounts = new ArrayList<>();
                for (PaymentAccount account : accounts) {
                    String key = account.getBankCard() + account.getAccountName() + account.getPaymentType()
                            + (PaymentConstant.CODE_2.equals(account.getPaymentType()) ? account.getPlatformName() : account.getDepositBank());

                    if (!uniqueKeys.contains(key)) {
                        uniqueKeys.add(key);
                        filteredAccounts.add(account);
                    } else {
                        PaymentAccount existingAccount = filteredAccounts.stream()
                                .filter(a -> key.equals(a.getBankCard() + a.getAccountName() + a.getPaymentType()
                                        + (PaymentConstant.CODE_2.equals(a.getPaymentType()) ? a.getPlatformName() : a.getDepositBank())))
                                .findFirst()
                                .orElse(null);
                        if (existingAccount != null && existingAccount.getId() == null && account.getId() != null) {
                            existingAccount.setId(account.getId());
                        }
                    }
                }

                filteredAccounts.forEach(account -> {
                    if (account.getDigitalReceipt() != null) {
                        account.setDigitalReceipt(fileService.appendSignature(account.getDigitalReceipt()));
                    }
                    if (account.getPlatformAgreement() != null) {
                        account.setPlatformAgreement(fileService.appendSignature(account.getPlatformAgreement()));
                    }
                    // 脱敏
                    String maskedBankCard = PayTypeEnum.ALIPAY.value().equals(account.getPaymentType()) ?
                            PrivacyDimmer.maskApliPay(account.getBankCard()) : PrivacyDimmer.maskBankCard(account.getBankCard());
                    account.setBankCard(maskedBankCard);
                });

                accounts = filteredAccounts;
            } catch (Exception e) {
                log.error("付款账号处理异常:{}", e.getMessage());
            }

        }
        paymentAccountListNums(accounts, paymentAccountNum);
        if (Objects.isNull(status) || accounts.isEmpty()) {
            return accounts;
        }
        if (PROCESSSTATUS_CODE3.value().equals(status)) {
            return accounts.stream()
                    .filter(account -> account.getExist() != null && account.getExist())
                    .collect(Collectors.toList());
        } else if (PROCESSSTATUS_CODE4.value().equals(status)
                || PROCESSSTATUS_CODE8.value().equals(status) || PROCESSSTATUS_CODE10.value().equals(status)) {
            return accounts.stream()
                    .filter(account -> status.equals(account.getProcessStatus()))
                    .collect(Collectors.toList());
        } else if (PROCESSSTATUS_CODE1.value().equals(status)) {
            return accounts.stream()
                    .filter(account -> status.equals(account.getProcessStatus()) || PROCESSSTATUS_CODE11.value().equals(account.getProcessStatus()))
                    .collect(Collectors.toList());
        } else if (PROCESSSTATUS_CODE7.value().equals(status)) {
            return accounts.stream()
                    .filter(account -> APPLYTYPE_CODE3.value().equals(account.getApplicationType()) || APPLYTYPE_CODE4.value().equals(account.getApplicationType()))
                    .collect(Collectors.toList());
        } else {
            return accounts;
        }

    }

    public String getMerchant(PaymentAccount paymentAccount, String userCode, Integer type) {
        paymentAccount.setUserCode(userCode);
        String merchantName;
        CustomerAuthVo customerInfoByUserCode = customerAuthService.getCustomerInfoByUserCode(userCode);
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerInfoByUserCode.getCustomerType())) {
            merchantName = customerInfoByUserCode.getPersonName();
        } else {
            merchantName = customerInfoByUserCode.getCompanyName();
        }
        if (BusinessTypeEnum.STRAIGHT.getValue().equals(type)) {
            PacketApplyInfoVo packetApplyInfoVo = getMerchantCode(userCode);
            paymentAccount.setSourceType(PaymentConstant.CODE_2);
            paymentAccount.setMerchantCode(packetApplyInfoVo.getMerchantCode());
            paymentAccount.setSettleCompanyCode(packetApplyInfoVo.getSettleCompanyCode());
            paymentAccount.setMyself(merchantName.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        } else if (BusinessTypeEnum.FBA.getValue().equals(type)) {
            FbaApplyInfoVO fbaApplySimpleInfo = fbaApplyService.getFbaApplySimpleInfo(userCode);
            paymentAccount.setSourceType(PaymentConstant.CODE_2);
            paymentAccount.setMerchantCode(fbaApplySimpleInfo.getAccountCode());
            paymentAccount.setSettleCompanyCode("921");
            paymentAccount.setMyself(merchantName.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        } else if (BusinessTypeEnum.OVERSEA.getValue().equals(type)|| BusinessTypeEnum.YWE_WAREHOUSE.getValue().equals(type)) {
            YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(userCode);
            paymentAccount.setSourceType(PaymentConstant.CODE_2);
            paymentAccount.setMerchantCode(yweApplyInfoVo.getMerchantCode());
            paymentAccount.setSettleCompanyCode(yweApplyInfoVo.getSettleCompanyCode());
            paymentAccount.setMyself(merchantName.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        }
        return merchantName;
    }

    public void corporateBankCard(PaymentAccount paymentAccount) {
        //提交银行卡验证一次即可
        PaymentAccount account = new PaymentAccount();
        account.setUserCode(paymentAccount.getUserCode());
        account.setFlowId(paymentAccount.getFlowId());
        account.setStatus(PaymentConstant.CODE_0);
        List<PaymentAccount> list = list(new QueryWrapper<>(account));
        if (CollectionUtils.isEmpty(list)) {
            EnterpriseBanKCardDTO enterpriseBanKCardDTO = new EnterpriseBanKCardDTO();
            enterpriseBanKCardDTO.setBankCard(paymentAccount.getBankCard());
            enterpriseBanKCardDTO.setBankBranchName(paymentAccount.getBankBranchName());
            enterpriseBanKCardDTO.setFlowId(paymentAccount.getFlowId());
            CommonCrmVo commonCrmVo = bankCardSubmit(enterpriseBanKCardDTO);
            if (!Boolean.parseBoolean(commonCrmVo.getResult())) {
                throw new BusinessException(commonCrmVo.getMessage());
            }
        }
        paymentAccount.setProcessStatus(PROCESSSTATUS_CODE11.value());
        //提交企业银行卡验证后保存到数据库
        if (PaymentApplyTypeEnum.APPLYTYPE_CODE1.value().equals(paymentAccount.getApplicationType()) || PaymentApplyTypeEnum.APPLYTYPE_CODE2.value().equals(paymentAccount.getApplicationType())) {
            save(paymentAccount);
        } else {
            updateById(paymentAccount);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> createPaymentAccount(PaymentAccount paymentAccount, String userCode) {
        log.info("创建支付账号:{},{}", paymentAccount, userCode);
        paymentAccount.setSourceType(PaymentConstant.CODE_2);
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setApplicationType(APPLYTYPE_CODE2.value());
        paymentAccount.setUserCode(userCode);
        boolean flag = true;
        for (Integer type : paymentAccount.getBusinessTypes()) {
            {
                paymentAccount.setId(null);
                paymentAccount.setBusinessType(type);
                if(BusinessTypeEnum.OVERSEA.getValue().equals(type)||BusinessTypeEnum.YWE_WAREHOUSE.getValue().equals(type)){
                    paymentAccount.setBusinessType(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
                }
                String merchantName = getMerchant(paymentAccount, userCode, type);
                paymentAccount.setRealName(merchantName);
                //从本地查询新增信息是否重复
                duplicateCheck(paymentAccount);
                //校验信息;个人银行卡/企业工商信息/企业银行卡/个人身份证
                if (flag) {
                    JsonResult<Object> verify = paymentVerify(paymentAccount);
                    if (verify.getCode().equals("1")) {
                        // 如果是个人四要素第一次提交
                        return verify;
                    }
                }
                //企业银行卡需走打款验证校验信息
                if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()))) {
                    paymentAccount.setBankCardId(soleCodeFake(paymentAccount));
                    corporateBankCard(paymentAccount);
                } else {
                    //新增本人的直接备案
                    if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) && merchantName.equals(paymentAccount.getAccountName())) {
                        paymentAccount.setBankCardId(soleCodeFake(paymentAccount));
                        JsonResult<Object> result = personalRecord(paymentAccount);
                        if (!result.getSuccess()) {
                            throw new BusinessException(ResponseCode.PORTAL_4004.getMessage());
                        }
                    } else if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) && !paymentAccount.getAccountName().equals(merchantName)) {
                        //暂时的唯一编码(此时未备案)
                        paymentAccount.setBankCardId(soleCodeFake(paymentAccount));
                        //新增非本人银行类型时提交到CRM进行非商户主题审核
                        JsonResult<Object> result = paymentAudit(paymentAccount);
                        if (!result.getSuccess()) {
                            throw new BusinessException(result.getMessage());
                        }
                    } else if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType())) {
                        //支付宝类型需进行扫码,更新为待支付后续进行扫码，本人扫码后直接备案，非本人同样需要进行非商户主题审核
                        paymentAccount.setBankCardId(soleCodeFake(paymentAccount));
                        paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE1.value());
                        save(paymentAccount);
                    } else {
                        //第三方平台账号无论是否本人都需要签订委托书,先推送到CRM审核（但不是非商户主题审核）
                        paymentAccount.setBankCardId(soleCode(paymentAccount));
                        paymentAccount.setSignTime(dataDime());
                        JsonResult<Object> jsonResult = commissionCallback(paymentAccount);
                        synAccount(paymentAccount);
                        paymentAccount.setProcessStatus(jsonResult.getSuccess() ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE8.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
                        paymentAccount.setRemark(jsonResult.getSuccess() ? null : jsonResult.getMessage());
                        save(paymentAccount);
                        if (!jsonResult.getSuccess()) {
                            throw new BusinessException(jsonResult.getMessage());
                        }
                    }
                }
            }
        }
        return JsonResult.success(paymentAccount.getId());
    }

    /**
     * //本人备案
     *
     * @date 2023/5/6
     */
    private JsonResult<Object> personalRecord(PaymentAccount paymentAccount) {
        PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
        paymentRecordDTO.setMerchantCode(paymentAccount.getMerchantCode());
        paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
        paymentRecordDTO.setAccountType(paymentAccount.getBusinessType());
        paymentRecordDTO.setBankCard(paymentAccount.getBankCard());
        paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
        paymentRecordDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
        paymentRecordDTO.setBankCode(paymentAccount.getBankCode());
        paymentRecordDTO.setUserCode(paymentAccount.getUserCode());
        paymentRecordDTO.setPayName(paymentAccount.getAccountName());
        paymentRecordDTO.setBankName(paymentAccount.getDepositBank());
        paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
        ResBody resBody = bankCardRecord(paymentRecordDTO);
        if (resBody.getResult()) {
            paymentAccount.setStatus(PaymentConstant.VALID_CODE);
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value());
            synBankId(paymentAccount, resBody.getData().toString());
            paymentAccount.setBankCardId(resBody.getData().toString());
            if (null != paymentAccount.getId()) updateById(paymentAccount);
            else save(paymentAccount);
            return JsonResult.success();
        } else {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            paymentAccount.setRemark(resBody.getMessage());
            if (null != paymentAccount.getId()) updateById(paymentAccount);
            else save(paymentAccount);
            return JsonResult.error(resBody.getMessage());
        }

    }


    @Override
    public String soleCode(PaymentAccount paymentAccount) {
        // ① 备案银行、支付宝以:商户号+createDate+ companyCode + paymentType为Id
        StringBuilder builder = new StringBuilder();
        try {
            if (!paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2)) {
                builder.append(paymentAccount.getMerchantCode());
                SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
                paymentDTO.setMerchantCode(paymentAccount.getMerchantCode());
                List<PaymentAccount> paymentAccounts = crmPaymentList(paymentDTO);
                for (PaymentAccount customerPayment : paymentAccounts) {
                    boolean success = (customerPayment.getBankCard() + customerPayment.getAccountName()).equals(paymentAccount.getBankCard() + paymentAccount.getAccountName());
                    if (success) {
                        return customerPayment.getBankCardId();
                    }
                }
            } else {
                //第三方账户以:商户号+ 年月日时分秒 + 两位随机数为Id + 固定值2
                String time = time(LocalDateTime.now());
                Random random = new Random();
                var i = random.nextInt(90) + 10;
                builder.append(paymentAccount.getMerchantCode()).append(time).append(i).append("2");
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("生成唯一编码异常:{}", e.getMessage());
            return paymentAccount.getMerchantCode() + paymentAccount.getBankCard() + paymentAccount.getPaymentType();
        }
    }

    @Override
    public JsonResult<Object> commissionCallback(PaymentAccount paymentAccount) {
        CallBackCrmDTO callBackCrmDTO = new CallBackCrmDTO();
        callBackCrmDTO.setUserCode(paymentAccount.getUserCode());
        callBackCrmDTO.setMerchantName(paymentAccount.getRealName());
        callBackCrmDTO.setBankCardId(paymentAccount.getBankCardId());
        callBackCrmDTO.setMerchantCode(paymentAccount.getMerchantCode());
        callBackCrmDTO.setBankCard(paymentAccount.getBankCard());
        callBackCrmDTO.setRelationShip(paymentAccount.getRelationShip());
        callBackCrmDTO.setReceiptType(paymentAccount.getPaymentTopicType());
        callBackCrmDTO.setYwAccount(paymentAccount.getAccountsReceivable());
        callBackCrmDTO.setBankAccount(paymentAccount.getAccountName());
        if (paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2) || paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE1)) {
            callBackCrmDTO.setBankName(paymentAccount.getPlatformName());
            //平台类型,编码默认为100
            callBackCrmDTO.setBankCode(PaymentConstant.BANK_CODE);
        } else {
            callBackCrmDTO.setBankName(paymentAccount.getDepositBank());
            callBackCrmDTO.setBankCode(paymentAccount.getBankCode());
        }
        callBackCrmDTO.setViewUrl(paymentAccount.getViewUrl());
        callBackCrmDTO.setDownloadUrl(paymentAccount.getDownloadUrl());
        callBackCrmDTO.setSignTime(paymentAccount.getSignTime());
        callBackCrmDTO.setIdCard(paymentAccount.getIdCard());
        if (StringUtils.isBlank(callBackCrmDTO.getIdCard())) {
            callBackCrmDTO.setIdCard(paymentAccount.getBusinessLicence());
        }
        //crm接收付款类型只有银行或者平台;0代表银行 1代表平台
        if (paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE0)) {
            callBackCrmDTO.setPaymentType(paymentAccount.getPaymentType());
        } else {
            callBackCrmDTO.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE1);
            callBackCrmDTO.setThirdAgreement(paymentAccount.getPlatformAgreement());
            callBackCrmDTO.setDigitalReceipt(paymentAccount.getDigitalReceipt());
        }
        if (paymentAccount.getApplicationType().equals(PaymentApplyTypeEnum.APPLYTYPE_CODE1.value()) || paymentAccount.getApplicationType().equals(APPLYTYPE_CODE2.value())) {
            callBackCrmDTO.setSource(PaymentConstant.PORTAL);
        } else {
            callBackCrmDTO.setSource(PaymentConstant.CRM);
        }
        callBackCrmDTO.setAccountType(paymentAccount.getBusinessType());
        log.info("付款委托书回调参数:{}", callBackCrmDTO);
        CrmBody crmBody = HttpUtil.doPost(crmApi + OPCodeEnum.COMMISSION_CALLBACK.value(), null, callBackCrmDTO, CrmBody.class, null);
        log.info("付款委托书回调结果:{},{}", crmBody, paymentAccount.getBankCard());
        if (Objects.isNull(crmBody)) {
            return JsonResult.error(ResponseCode.PORTAL_5015.getMessage());
        }
        if (Boolean.parseBoolean(crmBody.getResult())) {
            return JsonResult.success();
        } else {
            return JsonResult.error(crmBody.getMessage());
        }
    }

    @Override
    public String soleCodeFake(PaymentAccount paymentAccount) {
        StringBuilder builder = new StringBuilder();
        try {
            LocalDateTime now = LocalDateTime.now();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern(PaymentConstant.TIME_FORMAT_1);
            String time = now.format(fmt);
            Random random = new Random();
            var i = random.nextInt(90) + 10;
            builder.append(paymentAccount.getMerchantCode()).append(time).append(i).append(paymentAccount.getPaymentType());
            return builder.toString();
        } catch (Exception e) {
            log.error("生成唯一编码异常:{}", e.getMessage());
            return paymentAccount.getMerchantCode() + paymentAccount.getBankCard() + paymentAccount.getPaymentType();
        }
    }

    @Override
    public JsonResult<Object> paymentAudit(PaymentAccount paymentAccount) {
        PaymentAuditDTO paymentAuditDTO = new PaymentAuditDTO();
        paymentAuditDTO.setBusinessType(paymentAccount.getBusinessType());
        paymentAuditDTO.setId(paymentAccount.getBankCardId());
        paymentAuditDTO.setMerchantCode(paymentAccount.getMerchantCode());
        paymentAuditDTO.setPaymentType(paymentAccount.getPaymentType());
        paymentAuditDTO.setBankCard(paymentAccount.getBankCard());
        paymentAuditDTO.setBankAccount(paymentAccount.getAccountName());
        paymentAuditDTO.setReceiptType(paymentAccount.getPaymentTopicType());
        paymentAuditDTO.setRelationShip(paymentAccount.getRelationShip());
        paymentAuditDTO.setAccountType(paymentAccount.getBusinessType());
        paymentAuditDTO.setUserCode(paymentAccount.getUserCode());
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType())) {
            paymentAuditDTO.setBankName(paymentAccount.getDepositBank());
        } else {
            paymentAuditDTO.setBankName(PaymentConstant.ALIPAY_NAME);
            paymentAuditDTO.setAlipayAccount(paymentAccount.getAlipayAccount());
            paymentAuditDTO.setAlipayCode(paymentAccount.getAlipayCode());
        }
        log.info("非商户主题审核参数:{}", paymentAuditDTO);
        CrmBody crmBody = HttpUtil.doPost(crmApi + OPCodeEnum.PAYMENT_AUDIT_SNY.value(), null, null, paymentAuditDTO, CrmBody.class, null);
        log.info("非商户主题审核结果:{},{}", crmBody, paymentAccount.getBankCard());
        paymentAccount.setProcessStatus(Objects.isNull(crmBody) ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value() : Boolean.parseBoolean(crmBody.getResult()) ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE8.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
        if (APPLYTYPE_CODE2.value().equals(paymentAccount.getApplicationType())) {
            if (!Objects.isNull(paymentAccount.getId())) {
                updateById(paymentAccount);
            } else {
                save(paymentAccount);
            }
        } else {
            updateById(paymentAccount);
        }
        return Objects.isNull(crmBody) ? JsonResult.error(ResponseCode.PORTAL_5015) : Boolean.parseBoolean(crmBody.getResult()) ? JsonResult.success() : JsonResult.error(crmBody.getMessage());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> clientPolishing(PaymentAccount paymentAccount, String userCode) {
        log.info("客户补齐委托书:{}", paymentAccount);
        Map<String, Object> hashMap = new HashMap<>(4);
        String merchant = getMerchant(paymentAccount, userCode, paymentAccount.getBusinessType());
        paymentAccount.setApplicationType(PaymentApplyTypeEnum.APPLYTYPE_CODE1.value());
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setContractCode(" ");
        paymentAccount.setSignTime(dataDime());
        paymentAccount.setSourceType(PaymentConstant.CODE_2);
        paymentAccount.setUserCode(userCode);
        paymentAccount.setMyself(merchant.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        //校验信息;个人银行卡/企业工商信息/企业银行卡/个人身份证
        paymentRealAuthentication(paymentAccount);
        if (paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2) || paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE1)) {
            paymentAccount.setDepositBank(paymentAccount.getPlatformName());
            //平台类型,编码默认为100
            paymentAccount.setBankCode(PaymentConstant.BANK_CODE);
        }
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()))) {
            corporateBankCard(paymentAccount);
        } else {
            //从本地查询信息
            PaymentAccount paymentAccountOne = getPaymentAccountOne(paymentAccount);
            //第三方单独处理
            if (PaymentConstant.CODE_2.equals(paymentAccount.getPaymentType())) {
                paymentAccount.setRealName(merchant);
                synAccount(paymentAccount);
                return executeCommissionCallback(paymentAccount, paymentAccountOne, merchant);
            } else {
                //银行或支付宝账户名更改时需要重新备案
                if (!paymentAccount.getAccountName().equals(paymentAccount.getOriginalAccountName())) {
                    JsonResult<Object> jsonResult = accountUpdateRecord(paymentAccount, paymentAccountOne);
                    synAccount(paymentAccount);
                    if (!jsonResult.getSuccess()) {
                        return jsonResult;
                    }
                }
                paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE7.value());
                if (null == paymentAccountOne) {
                    save(paymentAccount);
                    hashMap.put("id", paymentAccount.getId());
                } else {
                    paymentAccount.setId(paymentAccountOne.getId());
                    updateById(paymentAccount);
                    hashMap.put("id", paymentAccountOne.getId());
                }
                synAccount(paymentAccount);
                log.info("客户补齐委托书更新{}", paymentAccount);
            }
        }
        return JsonResult.success(hashMap);

    }

    /**
     * // 第三方处理逻辑
     *
     * @date 2023/5/9
     */
    private JsonResult<Object> executeCommissionCallback(PaymentAccount paymentAccount, PaymentAccount paymentAccountOne, String name) {
        paymentAccount.setMyself(paymentAccount.getAccountName().equals(name) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        JsonResult<Object> jsonResult = commissionCallback(paymentAccount);
        Boolean success = jsonResult.getSuccess();
        paymentAccount.setProcessStatus(success ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE8.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
        paymentAccount.setRemark(success ? null : jsonResult.getMessage().replace("500:", ""));
        if (null == paymentAccountOne) {
            save(paymentAccount);
        } else {
            paymentAccount.setId(paymentAccountOne.getId());
            updateById(paymentAccount);
        }
        return success ? JsonResult.success() : jsonResult;
    }

    /**
     * // 更新备案信息
     *
     * @date 2023/5/8
     */
    JsonResult<Object> accountUpdateRecord(PaymentAccount paymentAccount, PaymentAccount one) {
        UpdateRecordDTO updateRecordDTO = new UpdateRecordDTO();
        updateRecordDTO.setPayName(paymentAccount.getAccountName());
        updateRecordDTO.setBankId(paymentAccount.getBankCardId());
        updateRecordDTO.setBankCode(paymentAccount.getBankCode());
        ResBody resBody = updateRecord(updateRecordDTO);
        if (!resBody.getResult()) {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            if (one == null) {
                save(paymentAccount);
            } else {
                paymentAccount.setId(one.getId());
                updateById(paymentAccount);
            }
            return JsonResult.error(resBody.getMessage());
        }
        return JsonResult.success();
    }

    private void synAccount(PaymentAccount paymentAccount) {
        PaymentAttachStatusDTO paymentAttachStatusDTO = new PaymentAttachStatusDTO();
        paymentAttachStatusDTO.setBankCardId(paymentAccount.getBankCardId());
        paymentAttachStatusDTO.setAttachStatus(paymentAccount.getApplicationType());
        synPaymentAttachStatus(paymentAttachStatusDTO);
    }

    @Override
    public ResBody updateRecord(UpdateRecordDTO updateRecordDTO) {
        log.info("更新备案信息:{}", updateRecordDTO);
        ResBody resBody = HttpUtil.doPost(crmCommonApi + PaymentRecordEnum.PAYMENT_CHANGEPAYNAME.value(), updateRecordDTO, ResBody.class);
        log.info("更新备案信息结果:{},{}", resBody, updateRecordDTO.getBankId());
        error(resBody);
        return resBody;
    }

    @Override
    public ResBody synPaymentAttachStatus(PaymentAttachStatusDTO paymentAttachStatusDTO) {
        log.info("同步Portal付款委托书状态到CRM:{}", paymentAttachStatusDTO);
        ResBody resBody = HttpUtil.doPost(crmApi + OPCodeEnum.PAYMENT_ATTACH_STATUS.value(), paymentAttachStatusDTO, ResBody.class);
        log.info("同步Portal付款委托书状态到CRM结果:{},{}", resBody, paymentAttachStatusDTO.getBankCardId());
        return resBody;
    }

    @Override
    public JsonResult<Object> confirmPaymentList(String userCode) {
        JsonResult<Object> paymentList = getPaymentList(userCode, APPLYTYPE_CODE4);
        if (paymentList.isEmpty()) {
            return JsonResult.error(PaymentConstant.BANKCARD);
        }
        return JsonResult.success(paymentList.get(0));
    }

    @Override
    public JsonResult<Object> servicePaymentList(String userCode) {
        JsonResult<Object> paymentList = getPaymentList(userCode, APPLYTYPE_CODE4);
        if (paymentList.isEmpty()) {
            return JsonResult.error(PaymentConstant.BANKCARD);
        }
        return JsonResult.success(paymentList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> confirmSubmit(PaymentAccount paymentAccount, String userCode) {
        log.info("确认付款账号提交：{}", paymentAccount);

        String merchant = getMerchant(paymentAccount, userCode, paymentAccount.getBusinessType());
        paymentAccount.setUserCode(userCode);
        //校验信息;个人银行卡/企业工商信息/企业银行卡/个人身份证
        paymentRealAuthentication(paymentAccount);
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()))) {
            corporateBankCard(paymentAccount);
        } else {
            PaymentAccount paymentAccountOne = getPaymentAccountOne(paymentAccount);
            paymentAccount.setMyself(merchant.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
            //第三方单独处理
            if (PaymentConstant.CODE_2.equals(paymentAccount.getPaymentType())) {
                return executeCommissionCallback(paymentAccount, paymentAccountOne, merchant);
            } else {
                //通过卡号对比账务是否存在备案
                comparison(paymentAccount);
                //本人直接备案
                if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) && merchant.equals(paymentAccount.getAccountName())) {
                    JsonResult<Object> jsonResult = personalRecord(paymentAccount);
                    if (jsonResult.getSuccess()) {
                        paymentAccount.setRealName(merchant);
                        JsonResult<Object> result = commissionCallback(paymentAccount);
                        paymentAccount.setProcessStatus(result.getSuccess() ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
                        paymentAccount.setRemark(jsonResult.getSuccess() ? null : jsonResult.getMessage());
                        paymentAccount.setStatus(result.getSuccess() ? PaymentConstant.MYSELF_CODE1 : PaymentConstant.CODE_0);
                        updateById(paymentAccount);
                        return result.getSuccess() ? JsonResult.success() : result;
                    } else {
                        return jsonResult;
                    }
                } else if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) && !merchant.equals(paymentAccount.getAccountName())) {
                    //非商户主题审核
                    JsonResult<Object> jsonResult = paymentAudit(paymentAccount);
                    paymentAccount.setProcessStatus(jsonResult.getSuccess() ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE8.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
                    paymentAccount.setRemark(jsonResult.getSuccess() ? null : ResponseCode.PORTAL_5063.getMessage());
                    updateById(paymentAccount);
                    return jsonResult;
                } else if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType())) {
                    paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE1.value());
                    paymentAccount.setAlipayAccount("");
                    paymentAccount.setAlipayCode("");
                    paymentAccount.setAlipayNumber("");
                    updateById(paymentAccount);
                }
                log.info("确认付款账号提交完成:{}", JSON.toJSONString(paymentAccount));
            }
        }
        return JsonResult.success(paymentAccount.getId());

    }


    /**
     * // 查询信息
     *
     * @date 2023/5/9
     */
    public PaymentAccount getPaymentAccountOne(PaymentAccount paymentAccount) {
        PaymentAccount account = new PaymentAccount();
        account.setBankCardId(paymentAccount.getBankCardId());
        account.setEffectivity(PaymentConstant.MYSELF_CODE1);
        account.setMerchantCode(paymentAccount.getMerchantCode());
        account.setStatus(PaymentConstant.CODE_0);
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(account);
        return getOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> serviceSubmit(PaymentAccount paymentAccount, UserAgent currentUser) {

        paymentAccount.setUserCode(currentUser.getUserCode());
        paymentAccount.setNo(currentUser.getMerchantNo());
        String merchant = getMerchant(paymentAccount, currentUser.getUserCode(), paymentAccount.getBusinessType());
        paymentAccount.setMyself(merchant.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        //校验信息;个人银行卡/企业工商信息/企业银行卡/个人身份证
        paymentRealAuthentication(paymentAccount);
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()))) {
            corporateBankCard(paymentAccount);
        } else {
            PaymentAccount paymentAccountOne = getPaymentAccountOne(paymentAccount);
            //第三方单独处理
            if (PaymentConstant.CODE_2.equals(paymentAccount.getPaymentType())) {
                return executeCommissionCallback(paymentAccount, paymentAccountOne, merchant);
            } else {
                //银行或支付宝账户名更改时需要重新备案
                if (!paymentAccount.getAccountName().equals(paymentAccount.getOriginalAccountName())) {
                    JsonResult<Object> jsonResult = accountUpdateRecord(paymentAccount, paymentAccountOne);
                    if (!jsonResult.getSuccess()) {
                        return jsonResult;
                    }
                }
                paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE7.value());
                if (null == paymentAccountOne) {
                    save(paymentAccount);
                } else {
                    paymentAccount.setId(paymentAccountOne.getId());
                    updateById(paymentAccount);
                }
                log.info("客服补齐委托书信息更新{}", paymentAccount);
            }
        }
        return JsonResult.success(ImmutableMap.of("id", paymentAccount.getId()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> deleteAccount(PaymentAccount paymentAccount) {
        if (Objects.isNull(paymentAccount.getId())) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        log.info("用户无效账号信息:{}", paymentAccount.getId());
        paymentAccount.setStatus(PaymentConstant.MYSELF_CODE1);
        paymentAccount.setEffectivity(PaymentConstant.CODE_0);
        paymentAccount.setRemark(PaymentConstant.DELETE_MESSAGE);
        updateById(paymentAccount);
        return JsonResult.success();
    }

    @Override
    public JsonResult<Object> reverseDesensitization(PaymentAccount paymentAccount, String userCode) {
        String merchantCode = getMerchantCode(userCode, paymentAccount.getBusinessType());
        List<PaymentAccount> paymentAccounts;
        SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
        paymentDTO.setMerchantCode(merchantCode);
        switch (paymentAccount.getSource()) {
            case PaymentConstant.INT_CODE_1:
                PaymentAccount paymentAccountOne = getById(paymentAccount.getId());
                return JsonResult.success(paymentAccountOne);
            case PaymentConstant.INT_CODE_2:
                paymentAccounts = crmPaymentList(paymentDTO);
                break;
            case PaymentConstant.INT_CODE_3:
                paymentAccounts = thirdPartyList(merchantCode, String.valueOf(paymentAccount.getBusinessType()));
                break;
            default:
                return JsonResult.error();
        }
        String matchValue = paymentAccount.getBankCardId();
        PaymentAccount matchedAccount = paymentAccounts.stream()
                .filter(account -> matchValue.equals(account.getBankCardId()))
                .findFirst()
                .orElse(null);
        return null == matchedAccount ? JsonResult.error() : JsonResult.success(matchedAccount);
    }


    /**
     * // 客服/确认付款信息查询
     *
     * @date 2023/5/9
     */
    public JsonResult<Object> getPaymentList(String userCode, PaymentApplyTypeEnum applyType) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setUserCode(userCode);
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setEffectivity(PaymentConstant.MYSELF_CODE1);
        paymentAccount.setApplicationType(applyType.value());
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(paymentAccount);
        List<PaymentAccount> list = list(wrapper);
        list.forEach(account -> {
            account.setPlatformAgreement(fileService.appendSignature(account.getPlatformAgreement()));
            account.setDigitalReceipt(fileService.appendSignature(account.getDigitalReceipt()));
        });
        return JsonResult.success(list);
    }

    public String time(LocalDateTime createTime) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(PaymentConstant.TIME_FORMAT_1);
        return createTime.format(fmt);
    }

    public String dataDime() {
        Date data = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat(PaymentConstant.TIME_FORMAT_2);
        return sdf.format(data);
    }

    /**
     * // //校验:银行卡不允许卡号重复,支付宝只允许存在一个流程中数据,第三那方同卡号需不同平台
     *
     * @date 2023/5/5
     */
    private void duplicateCheck(PaymentAccount paymentAccount) {
        if (PaymentConstant.CODE_2.equals(paymentAccount.getPaymentType())) {
            //对比已经备案的第三方平台账号
            platformAccountVerify(paymentAccount);
        }

        LocalDateTime now = LocalDateTime.now();
        String format = now.format(DateTimeFormatter.ofPattern(PaymentConstant.TIME_FORMAT));
        paymentAccount.setAccountTime(format);
        PaymentAccount param = new PaymentAccount();
        param.setMerchantCode(paymentAccount.getMerchantCode());
        param.setStatus(PaymentConstant.CODE_0);
        param.setEffectivity(PaymentConstant.MYSELF_CODE1);
        param.setBusinessType(paymentAccount.getBusinessType());
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(param);
        List<PaymentAccount> list = list(wrapper);
        for (PaymentAccount data : list) {
            String bankCard = data.getBankCard();
            if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) && bankCard.equals(paymentAccount.getBankCard())) {
                throw ResponseCode.PORTAL_4001.getError();
            } else if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType()) && PaymentConstant.PAYMENTTYPE_CODE1.equals(data.getPaymentType())) {
                throw ResponseCode.PORTAL_4002.getError();
            } else if (PaymentConstant.PAYMENTTYPE_CODE2.equals(paymentAccount.getPaymentType())
                    && data.getBankCard().equals(paymentAccount.getBankCard())
                    && paymentAccount.getPlatformName().equals(data.getPlatformName())) {
                paymentAccount.setId(data.getBankCardId().equals(paymentAccount.getBankCardId()) ? data.getId() : null);
                throw ResponseCode.PORTAL_4003.getError();
            }
        }
        //通过卡号对比账务是否存在备案
        comparison(paymentAccount);
    }

    //对比已备案数据
    public void comparison(PaymentAccount paymentAccount) {
        SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
        paymentDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
        paymentDTO.setAccountId(PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) ? paymentAccount.getBankCard() : paymentAccount.getAlipayCode());
        List<PaymentAccount> paymentAccounts = crmPaymentList(paymentDTO);
        if (paymentAccounts.stream().anyMatch(account -> paymentAccount.getBankCard().equals(account.getBankCard()) && account.getBusinessType().equals(paymentAccount.getBusinessType()))) {
            throw ResponseCode.PORTAL_4004.getError();
        }
    }

    /**
     * // 去账务对比支付宝2088备案失败单独提示
     *
     * @date 2023/5/11
     */
    public boolean alipayAccount(PaymentAccount paymentAccount) {
        SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
        paymentDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
        paymentDTO.setAccountId(paymentAccount.getAlipayCode());
        paymentDTO.setAccountType(paymentAccount.getBusinessType());
        List<PaymentAccount> paymentAccounts = crmPaymentList(paymentDTO);
        return !paymentAccounts.isEmpty();
    }

    /**
     * // 本地对比2088
     *
     * @date 2023/5/11
     */
    public JsonResult<Object> alipayLocality(PaymentAccount paymentAccount) {
        String message = "备案失败，请把您的支付宝user ID【" + paymentAccount.getAlipayCode() + "】反馈给客服退款!";
        PaymentAccount account = new PaymentAccount();
        account.setMerchantCode(paymentAccount.getMerchantCode());
        account.setAlipayCode(paymentAccount.getAlipayCode());
        account.setStatus(PaymentConstant.CODE_0);
        account.setEffectivity(PaymentConstant.MYSELF_CODE1);
        account.setBusinessType(paymentAccount.getBusinessType());
        List<PaymentAccount> paymentAccountList = paymentAccountDao.getPaymentAccountList(account);
        if (!paymentAccountList.isEmpty()) {
            if (paymentAccountList.size() > 1) {
                account.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
                account.setRemark(message);
                updateById(account);
                return JsonResult.error(message);
            }
        }
        return JsonResult.success();
    }


    /**
     * // 第三方已备案的平台信息
     *
     * @date 2023/5/6
     */
    public void platformAccountVerify(PaymentAccount paymentAccount) {
        String parm = paymentAccount.getMerchantCode() + PaymentConstant.URL_NAME + paymentAccount.getBusinessType();
        CrmPlatformBody crmPlatformBody = HttpUtil.doPost(crmApi + OPCodeEnum.SEARCH_ALL_PAYMENT.value() + parm, null, null, CrmPlatformBody.class, null);
        log.info("查询商户所有非[审核拒绝]且有效 的平台账号结果:{},{}", crmPlatformBody, paymentAccount.getMerchantCode());
        if (!Objects.isNull(crmPlatformBody) && Boolean.parseBoolean(crmPlatformBody.getResult())) {
            List<CrmPlatformBody.DataDTO> data = crmPlatformBody.getData();
            for (CrmPlatformBody.DataDTO datum : data) {
                if (paymentAccount.getBankCard().equals(datum.getBankCard()) && paymentAccount.getPlatformName().equals(datum.getBankName())) {
                    throw ResponseCode.PORTAL_4003.getError();
                }
            }
        }
    }

    public PaymentAccount transition(ThirdPartyVo thirdPartyVo) {
        if (ObjectUtils.isNotEmpty(thirdPartyVo)) {
            PaymentAccount paymentAccount = new PaymentAccount();
            if (PaymentConstant.PAYMENTTYPE_CODE0.equals(thirdPartyVo.getPaymentType())) {
                paymentAccount.setPaymentType(thirdPartyVo.getPaymentType());
            } else {
                paymentAccount.setPaymentType(PaymentConstant.ALIPAY_NAME.equals(thirdPartyVo.getBankName()) ? PaymentConstant.CODE_1 : PaymentConstant.CODE_2);
            }
            paymentAccount.setMerchantCode(thirdPartyVo.getMerchantCode());
            paymentAccount.setAccountName(thirdPartyVo.getBankAccount());
            paymentAccount.setBankCard(thirdPartyVo.getBankCard());
            paymentAccount.setRelationShip(thirdPartyVo.getRelationShip());
            paymentAccount.setPaymentTopicType(thirdPartyVo.getReceiptType());
            paymentAccount.setBankCardId(thirdPartyVo.getId());
            paymentAccount.setAttachStatus(thirdPartyVo.getAttachStatus());
            paymentAccount.setAttach(thirdPartyVo.getAttach());
            paymentAccount.setPaymentTopicType(thirdPartyVo.getReceiptType());
            paymentAccount.setBankCode(thirdPartyVo.getBankCode());
            paymentAccount.setDigitalReceipt(thirdPartyVo.getThirdAgreement());
            paymentAccount.setExist(true);
            paymentAccount.setAuditStatus(thirdPartyVo.getAuditStatus());
            paymentAccount.setPlatformName(thirdPartyVo.getBankName());
            paymentAccount.setSource(PaymentConstant.CODE_3);
            paymentAccount.setImportSign(thirdPartyVo.getImportSign());
            return paymentAccount;
        }
        return null;
    }

    public void error(ResBody resBody) {
        if (Objects.isNull(resBody)) {
            throw ResponseCode.PORTAL_3001.getError();
        }
    }

    public void error(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            throw ResponseCode.PORTAL_5015.getError();
        }
    }

    /***
     * //TODO 唯一编号
     * <AUTHOR>
     * @date 2021/12/13 15:56
     * @param paymentRecord 备案信息
     * @return java.lang.String
     */
    public String serialNumber(String code, RecordBody.PaymentRecordData
            paymentRecord, List<RecordBody.PaymentRecordData> bankRecordArray) {
        try {
            StringBuilder builder = new StringBuilder();
            builder.append(code);
            //账务中心创建时间相同时特殊处理
            Boolean alipay = alipay(paymentRecord, bankRecordArray);
            if (alipay) {
                builder.append(paymentRecord.getAccount()).append(paymentRecord.getCompanyCode()).append(paymentRecord.getPaymentType());
                return builder.toString();
            }
            if (null != paymentRecord.getCreateDate()) {
                LocalDateTime createTime = paymentRecord.getCreateDate();
                DateTimeFormatter fmt = DateTimeFormatter.ofPattern(PaymentConstant.TIME_FORMAT_1);
                String time = createTime.format(fmt);
                builder.append(time);
            }
            builder.append(paymentRecord.getCompanyCode()).append(paymentRecord.getPaymentType());
            return builder.toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * // 支付宝类型数据时间相同的特殊处理
     *
     * @date 2023/5/4
     */
    public Boolean alipay(RecordBody.PaymentRecordData
                                  paymentRecord, List<RecordBody.PaymentRecordData> bankRecordArray) {
        boolean condition = false;
        for (RecordBody.PaymentRecordData record : bankRecordArray) {
            if (null != record.getCreateDate()) {
                if (record.getPaymentType().equals(PayTypeEnum.ALIPAY.value())) {
                    if (paymentRecord.getCreateDate().equals(record.getCreateDate()) && !record.getAlipayAccount().equals(paymentRecord.getAlipayAccount())) {
                        condition = true;
                    }
                } else {
                    if (paymentRecord.getCreateDate().equals(record.getCreateDate()) && !record.getAccount().equals(paymentRecord.getAccount())) {
                        condition = true;
                    }
                }

            }
        }
        return condition;
    }

    public PacketApplyInfoVo getMerchantCode(String userCode) {
        PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
        if (Objects.isNull(packetApplyInfo)) {
            throw new BusinessException(ResponseCode.PORTAL_6103);
        }
        return packetApplyInfo;
    }

    public String getMerchantCode(String userCode, Integer businessType) {
        String merchantCode = null;
        if (BusinessTypeEnum.STRAIGHT.getValue().equals(businessType)) {
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
            if (Objects.isNull(packetApplyInfo) || StringUtils.isEmpty(packetApplyInfo.getMerchantCode())) {
                throw new BusinessException(ResponseCode.PORTAL_6103);
            }
            merchantCode = packetApplyInfo.getMerchantCode();
        } else if (BusinessTypeEnum.FBA.getValue().equals(businessType)) {
            FbaApplyInfoVO fbaApplySimpleInfo = fbaApplyService.getFbaApplySimpleInfo(userCode);
            if (Objects.isNull(fbaApplySimpleInfo) || StringUtils.isEmpty(fbaApplySimpleInfo.getAccountCode())) {
                throw new BusinessException(ResponseCode.PORTAL_6103);
            }
            merchantCode = fbaApplySimpleInfo.getAccountCode();
        }
        return merchantCode;
    }

    public List<PaymentAccount> getAccPaymentAccount(RecordBody resBody, String merchantCode) {
        log.info("获取账务中心数据结果:{},{}", resBody, merchantCode);
        error(resBody);
        List<PaymentAccount> list = new ArrayList<>();
        if (!resBody.getResult()) {
            return list;
        }
        List<RecordBody.PaymentRecordData> bankRecordArray = resBody.getBankRecordArray();
        if (CollectionUtils.isNotEmpty(bankRecordArray)) {
            for (RecordBody.PaymentRecordData paymentRecordData : bankRecordArray) {
                if (PaymentConstant.VALID_CODE.equals(paymentRecordData.getValidFlag())) {
                    PaymentAccount paymentAccount = new PaymentAccount();
                    paymentAccount.setExist(true);
                    String number = serialNumber(merchantCode, paymentRecordData, bankRecordArray);
                    paymentAccount.setBankCardId(number);
                    paymentAccount.setMerchantCode(merchantCode);
                    paymentAccount.setPaymentType(paymentRecordData.getPaymentType());
                    paymentAccount.setSource(PaymentConstant.CODE_2);
                    paymentAccount.setCreateTime(paymentRecordData.getCreateDate());
                    paymentAccount.setSettleCompanyCode(paymentRecordData.getCompanyCode());
                    if (paymentAccount.getPaymentType().equals(PayTypeEnum.BANK.value())) {
                        paymentAccount.setBankCode(paymentRecordData.getBankCode());
                        paymentAccount.setDepositBank(paymentRecordData.getBankName());
                        paymentAccount.setBankCard(paymentRecordData.getAccount());
                        paymentAccount.setAccountName(paymentRecordData.getPayName());
                        paymentAccount.setDepositBank(paymentRecordData.getBankAddr());
                    } else {
                        paymentAccount.setBankCard(paymentRecordData.getAlipayAccount());
                        paymentAccount.setAccountName(paymentRecordData.getPayName());
                        paymentAccount.setAlipayCode(paymentRecordData.getAccount());
                    }
                    list.add(paymentAccount);
                }

            }
        }
        return list;
    }


    public JsonResult<Object> paymentVerify(PaymentAccount paymentAccount) {
        VerificationJsonBody verificationJsonBody = new VerificationJsonBody();
        if (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType())) {
            if (Arrays.asList(PaymentConstant.CODE_1, PaymentConstant.CODE_2).contains(paymentAccount.getPaymentType())) {
                BusinessVerificationDTO businessVerificationDTO = new BusinessVerificationDTO();
                businessVerificationDTO.setName(paymentAccount.getAccountName());
                businessVerificationDTO.setLegalRepName(paymentAccount.getLegalPersonName());
                businessVerificationDTO.setOrgCode(paymentAccount.getBusinessLicence());
                verificationJsonBody = verificationService.businessVerification(businessVerificationDTO);
            }
        } else if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_2.equals(paymentAccount.getPaymentTopicType())) {
            PersonalCertificateDTO personalCertificateDTO = new PersonalCertificateDTO();
            personalCertificateDTO.setName(paymentAccount.getAccountName());
            personalCertificateDTO.setCardNo(paymentAccount.getBankCard());
            personalCertificateDTO.setIdNo(paymentAccount.getIdCard());
            if (Arrays.asList(PaymentConstant.CODE_1, PaymentConstant.CODE_2).contains(paymentAccount.getPaymentType())) {
                verificationJsonBody = verificationService.personalCertificate(personalCertificateDTO);
            } else {
                Integer certificateType = paymentAccount.getCertificateType();
                if (certificateType == null) {
                    throw new BusinessException("证件类型不能为空");
                }
                personalCertificateDTO.setCertificateType(certificateType);
                // 如果是大陆身份证类型直接进行三要素校验
                if (CertificateTypeEnum.ID_CARD.getCode().equals(certificateType)) {
                    verificationJsonBody = verificationService.personalBankCard(personalCertificateDTO);
                } else {
                    // 如果是港澳台，护照类型，先判断是否有flowId，没有，说明还未进行发起认证，需要先发起认证
                    String flowId = paymentAccount.getFlowId();
                    if (StringUtils.isBlank(flowId)) {
                        personalCertificateDTO.setMobile(paymentAccount.getMobile());
                        verificationJsonBody = verificationService.verifyBankFour(personalCertificateDTO);
                        if (Boolean.parseBoolean(verificationJsonBody.getResult())) {
                            Object data = verificationJsonBody.getData();
                            if (data == null) {
                                throw new BusinessException("银行卡校验数据异常");
                            }
                            JSONObject data1 = (JSONObject) data;
                            String flow = data1.getString("flowId");
                            // 校验成功
                            JsonResult jsonResult = JsonResult.success();
                            jsonResult.setCode("1");
                            jsonResult.setData(flow);
                            return jsonResult;
                        } else {
                            // 校验失败
                            throw new BusinessException(verificationJsonBody.getMessage());
                        }
                    } else {
                        // 有flowId，还要有手机验证码，去认证是否成功
                        String smsCode = paymentAccount.getSmsCode();
                        if (StringUtils.isBlank(smsCode)) {
                            throw new BusinessException("手机验证码缺失");
                        }
                        verificationJsonBody = verificationService.verifyBankMobileCode(flowId, smsCode);
                    }

                }

            }
        }
        boolean b = PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()));
        if (!b & !Boolean.parseBoolean(verificationJsonBody.getResult())) {
            throw new BusinessException(verificationJsonBody.getMessage());
        }
        // 按照之前的逻辑正常返回
        return JsonResult.success();
    }

    /**
     * // 付款账号实名校验
     *
     * @date 2023/5/9
     */

    public void paymentRealAuthentication(PaymentAccount paymentAccount) {
        VerificationJsonBody verificationJsonBody = new VerificationJsonBody();
        if (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType())) {
            if (Arrays.asList(PaymentConstant.CODE_1, PaymentConstant.CODE_2).contains(paymentAccount.getPaymentType())) {
                BusinessVerificationDTO businessVerificationDTO = new BusinessVerificationDTO();
                businessVerificationDTO.setName(paymentAccount.getAccountName());
                businessVerificationDTO.setLegalRepName(paymentAccount.getLegalPersonName());
                businessVerificationDTO.setOrgCode(paymentAccount.getBusinessLicence());
                verificationJsonBody = verificationService.businessVerification(businessVerificationDTO);
            }
        } else if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_2.equals(paymentAccount.getPaymentTopicType())) {
            PersonalCertificateDTO personalCertificateDTO = new PersonalCertificateDTO();
            personalCertificateDTO.setName(paymentAccount.getAccountName());
            personalCertificateDTO.setCardNo(paymentAccount.getBankCard());
            personalCertificateDTO.setIdNo(paymentAccount.getIdCard());
            verificationJsonBody = Arrays.asList(PaymentConstant.CODE_1, PaymentConstant.CODE_2).contains(paymentAccount.getPaymentType())
                    ? verificationService.personalCertificate(personalCertificateDTO)
                    : verificationService.personalBankCard(personalCertificateDTO);
        }
        boolean b = PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType()) & (PaymentConstant.CODE_1.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType()));
        if (!b & !Boolean.parseBoolean(verificationJsonBody.getResult())) {
            throw new BusinessException(verificationJsonBody.getMessage());
        }
    }

    @Override
    public JsonResult getQrCode(Integer id, String userCode) {
        PaymentAccount paymentAccount = this.getById(id);
        Assert.notNull(paymentAccount, "未查询到付款账号信息");
        Assert.isTrue(PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType()), "仅支持支付宝扫码");
        Integer businessType = paymentAccount.getBusinessType();
        String settleCompany = "";
        if (BusinessTypeEnum.STRAIGHT.getValue().equals(businessType)) {
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
            Assert.notNull(packetApplyInfo, "未查询到业务信息");
            settleCompany = packetApplyInfo.getSettleCompanyCode();
        } else if (BusinessTypeEnum.FBA.getValue().equals(businessType)) {
            settleCompany = FbaConstant.FBA_ACC_SETTLE_COMPANY;
        } else if(BusinessTypeEnum.YWE_WAREHOUSE.getValue().equals(businessType)){
            YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(userCode);
            settleCompany = yweApplyInfoVo.getSettleCompanyCode();
        } else {
            throw new BusinessException("该业务类型不支持支付宝扫码");
        }
        JsonResult jsonResult = alipayPaymentOrderService.getQrCodeUrl(paymentAccount.getAlipayNumber(), settleCompany, paymentAccount.getMerchantCode());
        if (!jsonResult.getSuccess()) {
            return jsonResult;
        }
        Map<String, String> data = (Map<String, String>) jsonResult.getData();
        String orderNumber = data.get("orderNumber");
        String qrCode = data.get("qrCode");
        if (!orderNumber.equals(paymentAccount.getAlipayNumber())) {
            paymentAccount.setAlipayNumber(orderNumber);
            this.updateById(paymentAccount);
        }
        jsonResult.setData(qrCode);
        jsonResult.setSuccess(true);
        return jsonResult;
    }

    @Override
    public JsonResult<Object> getSignPaymentBookUrl(Integer id, UserAgent currentUser,Boolean viewContract) {
        PaymentAccount paymentAccount = this.getById(id);
        Assert.notNull(paymentAccount, "未查询到付款账号信息");
        Assert.isTrue(paymentAccount.getSourceType() == 2, "请登录https://portal.yw56.com.cn/进行操作");
        Assert.isTrue(PaymentApplyTypeEnum.PROCESSSTATUS_CODE7.value().equals(paymentAccount.getProcessStatus()), "付款账号状态不是确认付款委托书状态");
        Integer businessType = paymentAccount.getBusinessType();
        Assert.isTrue(businessType != null, "业务类型不能为空");
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(businessType);
        String merchantCode = "";
        String settleCompanyCode = "";
        Integer contractType = null;
        switch (businessTypeEnum) {
            case STRAIGHT:
                PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(currentUser.getUserCode());
                Assert.notNull(packetApplyInfo, "未查询到小包业务信息");
                merchantCode = packetApplyInfo.getMerchantCode();
                settleCompanyCode = packetApplyInfo.getSettleCompanyCode();
                contractType = ContractTypeEnum.PAYMENT.value();
                break;
            case FBA:
                FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(currentUser.getUserCode());
                Assert.notNull(fbaApplyInfo, "未查询到fba业务信息");
                merchantCode = fbaApplyInfo.getAccountCode();
                settleCompanyCode = fbaApplyInfo.getSettleCompanyCode();
                contractType = ContractTypeEnum.FBA_PAYMENT.value();
                break;
            case YWE_WAREHOUSE:
                YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(currentUser.getUserCode());
                Assert.notNull(yweApplyInfoVo, "未查询到海外派业务信息");
                merchantCode =yweApplyInfoVo.getMerchantCode();
                settleCompanyCode = yweApplyInfoVo.getSettleCompanyCode();
                contractType = ContractTypeEnum.YWE_PAYMENT.value();
                break;
            default:
                Assert.isTrue(false, "业务类型异常");
        }
        QueryContractDTO queryContractDTO = new QueryContractDTO();
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        queryContractDTO.setContractType(contractType);
        // 付款委托书合同数据
        PaymentContractDTO extraData = new PaymentContractDTO();
        extraData.setAccountTime(LocalDate.parse(paymentAccount.getAccountTime()));
        extraData.setMerchantCode(merchantCode);
        extraData.setSettleCompanyCode(settleCompanyCode);
        extraData.setRelationShip(paymentAccount.getRelationShip());
        extraData.setAccountName(paymentAccount.getAccountName());
        extraData.setPaymentType(paymentAccount.getPaymentType());
        if (paymentAccount.getPaymentTopicType() == 0 || paymentAccount.getPaymentTopicType() == 2) {
            extraData.setCardNumber(paymentAccount.getIdCard());
        } else {
            extraData.setCardNumber(paymentAccount.getBusinessLicence());
        }
        extraData.setBankCard(paymentAccount.getBankCard());
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType())) {
            extraData.setBankName(paymentAccount.getDepositBank());
        } else {
            extraData.setBankName(paymentAccount.getPlatformName());
        }
        extraData.setReceiveAccount(paymentAccount.getAccountsReceivable());
        if (PaymentConstant.PAYMENTTYPE_CODE2.equals(paymentAccount.getPaymentType())) {
            // 如果协议为空,需要在补齐委托书时候添加承诺函
            extraData.setIfNeedProtocol(StringUtils.isBlank(paymentAccount.getPlatformAgreement()));
        }
        queryContractDTO.setExtraData(JSONObject.toJSONString(extraData));
        queryContractDTO.setViewContract(viewContract);
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        paymentAccount.setContractCode(signContractVO.getContractCode());
        this.updateById(paymentAccount);
        Map<String,String> resMap=new HashMap<>();
        resMap.put("signUrl", signContractVO.getUrl());
        resMap.put("contractName", signContractVO.getContractName());
        return JsonResult.success(resMap);
    }

    @Override
    public JsonResult getAlipayOrderStatus(String id) {
        JsonResult jsonResult = new JsonResult();
        jsonResult.setSuccess(false);
        PaymentAccount paymentAccount = this.getById(id);
        if (Objects.nonNull(paymentAccount)) {
            String alipayNumber = paymentAccount.getAlipayNumber();
            if (StringUtils.isNotBlank(alipayNumber)) {
                Boolean res = alipayPaymentOrderService.getOrderPayStatus(alipayNumber);
                if (res) {
                    jsonResult.setSuccess(true);
                    jsonResult.setMessage(paymentAccount.getRemark());
                }
            }
        }
        return jsonResult;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult alipayRecordHandle(AlipayPaymentOrder alipayPaymentOrder) {
        log.info("支付宝回调参数：{}", alipayPaymentOrder);
        JsonResult jsonResult = new JsonResult();
        PaymentAccount param = new PaymentAccount();
        param.setEffectivity("1");
        param.setAlipayNumber(alipayPaymentOrder.getOrderNumber());
        PaymentAccount account = paymentAccountDao.selectOne(new QueryWrapper<>(param));
        CustomerAuthVo customerInfoByUserCode = customerAuthService.getCustomerInfoByUserCode(account.getUserCode());
        if (account == null) {
            DingTalkClient.sendMessage("支付完成后更新portal_payment_account表数据不存在,订单号为" + alipayPaymentOrder.getOrderNumber());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("回调数据异常");
            return jsonResult;
        }
        // 设置支付宝支付账号用户id信息
        account.setAlipayAccount(alipayPaymentOrder.getBuyerLogonId());
        account.setAlipayCode(alipayPaymentOrder.getBuyerId());
        account.setRemark(null);
        //已存在数据没有批次号进行填充
        if (StringUtils.isBlank(account.getPayBatchId())) {
            account.setPayBatchId(IdWorker.getIdStr());
        }
        // 0是本人,1是非本人
        String myself = account.getMyself();
        if (StringUtils.isBlank(myself)) {
            DingTalkClient.sendMessage("支付完成后更新portal_payment_account表myself字段为空,订单号为" + alipayPaymentOrder.getOrderNumber());
            jsonResult.setSuccess(false);
            jsonResult.setMessage("回调数据异常");
            return jsonResult;
        }
        // 如果客户支付成功，并且备案的是非商户主体，下发到账务
        // JIRA-58363    客户中心支付宝备案扫码付入账,支持商户主体下发账务
        // 下发到账务
        AlipayOrderPaymentDTO alipayOrderPaymentDTO = new AlipayOrderPaymentDTO();
        alipayOrderPaymentDTO.setBuyerAccount(alipayPaymentOrder.getBuyerId());
        alipayOrderPaymentDTO.setAmount(alipayPaymentOrder.getTotalAmount().toString());
        alipayOrderPaymentDTO.setCompanyCode(alipayPaymentOrder.getCompanyCode());
        alipayOrderPaymentDTO.setMerchantCode(account.getMerchantCode());
        alipayOrderPaymentDTO.setTradeNo(alipayPaymentOrder.getTradeNo());
        alipayOrderPaymentDTO.setSellerId(alipayPaymentOrder.getSellId());
        alipayOrderPaymentDTO.setOutTradeNo(alipayPaymentOrder.getOrderNumber());
        pushAlipayOrder(alipayOrderPaymentDTO);


        // 对比支付宝账号信息
        JsonResult validateResult = validateAccount(alipayPaymentOrder, account);
        if (!validateResult.getSuccess()) {
            account.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            account.setRemark(validateResult.getMessage());
            updateById(account);
            return JsonResult.error(validateResult.getMessage());
        }
        //比对本地和账务是否存在2088备案信息
        String message = "备案失败，请把您的支付宝user ID【" + alipayPaymentOrder.getBuyerId() + "】反馈给客服退款!";
        JsonResult<Object> result = alipayLocality(account);
        if (!result.getSuccess()) {
            return result;
        }
        boolean resultAccount = alipayAccount(account);
        if (resultAccount) {
            account.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            account.setRemark(message);
            updateById(account);
            return JsonResult.error(message);
        }

        if (APPLYTYPE_CODE4.value().equals(account.getApplicationType())) {
            if (PaymentConstant.CODE_0.equals(myself)) {
                JsonResult<Object> record = alipayRecord(account);
                if (record.getSuccess()) {
                    if (null != customerInfoByUserCode) {
                        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerInfoByUserCode.getCustomerType())) {
                            account.setRealName(customerInfoByUserCode.getPersonName());
                        } else {
                            account.setRealName(customerInfoByUserCode.getCompanyName());
                        }
                    }
                    //确认付款类型账号备案完后推送到CRM；CRM接收到后改变商户状态
                    JsonResult<Object> commissionCallback = commissionCallback(account);
                    account.setProcessStatus(commissionCallback.getSuccess() ? PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value() : PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
                    account.setStatus(commissionCallback.getSuccess() ? PaymentConstant.MYSELF_CODE1 : PaymentConstant.CODE_0);
                    account.setRemark(commissionCallback.getSuccess() ? null : commissionCallback.getMessage());
                    updateById(account);
                    return commissionCallback.getSuccess() ? JsonResult.success() : commissionCallback;
                }
                return record;
            } else {
                return paymentAudit(account);
            }
        } else {
            //用户类型新增本人直接备案非本人推送到CRM审核
            return PaymentConstant.CODE_0.equals(myself) ? alipayRecord(account) : paymentAudit(account);
        }

    }

    // 对比账号信息
    public JsonResult validateAccount(AlipayPaymentOrder alipayPaymentOrder, PaymentAccount paymentAccount) {
        String account = alipayPaymentOrderService.getAccountOrder(alipayPaymentOrder);
        if (account == null || paymentAccount == null) {
            return JsonResult.success();
        }
        Integer paymentTopicType = paymentAccount.getPaymentTopicType();
        // 定义正则表达式模式
        Pattern pattern = Pattern.compile("(.+)\\(([^()]+)\\)$");
        Matcher matcher = pattern.matcher(account);
        if (!matcher.find()) {
            return JsonResult.success();
        }
        String name = matcher.group(1);
        String accountNo = matcher.group(2);
        String bankCard = paymentAccount.getBankCard();
        String accountName = paymentAccount.getAccountName();
        String message = "您填写的账户名【%s】跟扫码账户名称【%s】不一致【扫码支付宝账户名:%s，账号：%s】，请核对后重新提交";
        message = String.format(message, accountName, name, name, accountNo);
        // 企业
        if (paymentTopicType == 1 || paymentTopicType == 3) {
            // 企业名称是否相等
            if (!accountName.equals(name)) {
                return JsonResult.error(message);
            }
        } else {
            // 个人
            // 名字位数是否相等
            if (name.length() != accountName.length()) {
                return JsonResult.error(message);
            }
            // 最后一位是否相同
            if (name.charAt(name.length() - 1) != accountName.charAt(accountName.length() - 1)) {
                return JsonResult.error(message);
            }
        }
        if (compareFirstThreeChars(bankCard, accountNo)) {
            return JsonResult.success();
        }
        return JsonResult.error(message);
    }

    public boolean compareFirstThreeChars(String str1, String str2) {
        // 检查字符串长度是否至少为3
        if (str1.length() < 3 || str2.length() < 3) {
            return false;
        }
        // 获取前3位
        String firstThreeStr1 = str1.substring(0, 3);
        String firstThreeStr2 = str2.substring(0, 3);
        // 比较前3位
        return firstThreeStr1.equals(firstThreeStr2);
    }

    public JsonResult<Object> alipayRecord(PaymentAccount paymentAccount) {
        PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
        paymentRecordDTO.setMerchantCode(paymentAccount.getMerchantCode());
        paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
        paymentRecordDTO.setAccountType(paymentAccount.getBusinessType());
        paymentRecordDTO.setBankCard(paymentAccount.getBankCard());
        paymentRecordDTO.setPaymentType(PayTypeEnum.ALIPAY.value());
        paymentRecordDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
        paymentRecordDTO.setBankCode(PaymentConstant.BANK_CODE);
        paymentRecordDTO.setUserCode(paymentAccount.getUserCode());
        paymentRecordDTO.setPayName(paymentAccount.getAccountName());
        paymentRecordDTO.setBankName(PaymentConstant.ALIPAY_NAME);
        paymentRecordDTO.setAccountId(paymentAccount.getAlipayCode());
        ResBody resBody = HttpUtil.doPost(crmCommonApi + PaymentRecordEnum.PAYMENT_RECORD_BANK.value(), paymentRecordDTO, ResBody.class);
        log.info("支付宝备案结果：{},{}", resBody, JSON.toJSON(paymentRecordDTO));
        String message = "备案失败，请把您的支付宝user ID【" + paymentAccount.getAlipayCode() + "】反馈给客服退款!";
        if (Objects.isNull(resBody)) {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            paymentAccount.setRemark(message);
            updateById(paymentAccount);
            JsonResult.error(ResponseCode.PORTAL_3005);
        }
        if (resBody.getResult()) {
            synBankId(paymentAccount, resBody.getData().toString());
            paymentAccount.setBankCardId(resBody.getData().toString());
            updateById(paymentAccount);
            //备案成功后相同卡号的支付宝账号置为无效
            PaymentAccount account = new PaymentAccount();
            account.setStatus(PaymentConstant.CODE_0);
            account.setEffectivity(PaymentConstant.MYSELF_CODE1);
            account.setBankCard(paymentAccount.getBankCard());
            account.setMerchantCode(paymentAccount.getMerchantCode());
            account.setBusinessType(paymentAccount.getBusinessType());
            List<PaymentAccount> paymentAccountList = paymentAccountDao.getPaymentAccountList(account);
            for (PaymentAccount account1 : paymentAccountList) {
                account1.setStatus(PaymentConstant.MYSELF_CODE1);
                account1.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value());
                updateById(account1);
            }
            return JsonResult.success();
        } else {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
            paymentAccount.setRemark(message);
            updateById(paymentAccount);
            return JsonResult.error(message);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> editAgain(PaymentAccount paymentAccount, String userCode) {
        getMerchant(paymentAccount, userCode, paymentAccount.getBusinessType());
        if (!PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType())) {
            paymentAccount.setEffectivity(PaymentConstant.CODE_0);
            paymentAccount.setStatus(PaymentConstant.VALID_CODE);
            updateById(paymentAccount);
            log.info("非支付宝类型数据重新编辑:{}", paymentAccount);
        } else {
            paymentAccount.setEffectivity(PaymentConstant.CODE_0);
            paymentAccount.setStatus(PaymentConstant.VALID_CODE);
            PaymentAccount select = paymentAccountDao.selectById(paymentAccount.getId());
            //支付宝类型重新编辑时相同卡号数据全部置为无效
            PaymentAccount account = new PaymentAccount();
            account.setMerchantCode(select.getMerchantCode());
            account.setBankCard(select.getBankCard());
            account.setPaymentType(select.getPaymentType());
            account.setBusinessType(select.getBusinessType());
            List<PaymentAccount> paymentAccountList = paymentAccountDao.getPaymentAccountList(account);
            for (PaymentAccount paymentAccount1 : paymentAccountList) {
                paymentAccount1.setStatus(PaymentConstant.VALID_CODE);
                paymentAccount1.setEffectivity(PaymentConstant.CODE_0);
                updateById(paymentAccount1);
            }
            log.info("支付宝类型数据重新编辑:{}", paymentAccount);
        }
        paymentAccount.setEffectivity(PaymentConstant.MYSELF_CODE1);
        paymentAccount.setId(null);
        log.info("重编编辑提交付款账号信息:{},{}", paymentAccount, userCode);
        return createPaymentAccount(paymentAccount, userCode);
    }

    @Override
    public void synBankId(PaymentAccount paymentAccount, String code) {
        SynBankDTO synBankDTO = new SynBankDTO();
        synBankDTO.setNewBankCardId(code);
        synBankDTO.setOldBankCardId(paymentAccount.getBankCardId());
        synBankDTO.setMerchantCode(paymentAccount.getMerchantCode());
        JSONObject jsonObject = HttpUtil.doPost(crmApi + OPCodeEnum.PAYMENT_BANK_ID.value(), null, synBankDTO, JSONObject.class, null);
        log.info("同步bankId到CRM结果:{},{},{}", jsonObject, paymentAccount, code);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> serviceCommission(PaymentAccountCrmDTO paymentAccountCrmDTO) {
        List<PaymentAccount> paymentAccounts = paymentAccountCrmDTO.getPaymentAccounts();
        //流程未走完
        ArrayList<PaymentAccount> list = new ArrayList<>();
        //流程走完
        ArrayList<PaymentAccount> endList = new ArrayList<>();

        for (PaymentAccount paymentAccount : paymentAccounts) {
            String merchant = getMerchant(paymentAccount, paymentAccount.getUserCode(), paymentAccount.getBusinessType());
            paymentAccount.setEffectivity(PaymentConstant.MYSELF_CODE1);
            paymentAccount.setStatus(PaymentConstant.CODE_0);
            paymentAccount.setApplicationType(PaymentApplyTypeEnum.APPLYTYPE_CODE3.value());
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE0.value());
            paymentAccount.setOriginalAccountName(paymentAccount.getAccountName());
            if (StringUtils.isBlank(paymentAccount.getBankCard()) || StringUtils.isBlank(paymentAccount.getMerchantCode())
                    || StringUtils.isBlank(paymentAccount.getAccountName()) || Objects.isNull(paymentAccount.getPaymentType())) {
                return JsonResult.error(ResponseCode.PORTAL_5010);
            }

            //平台类型支付宝时需要填充2088id
            if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType())) {
                if (PaymentConstant.ALIPAY_NAME.equals(paymentAccount.getPlatformName())) {
                    SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
                    paymentDTO.setMerchantCode(paymentAccount.getMerchantCode());
                    crmPaymentList(paymentDTO).stream()
                            .filter(account -> paymentAccount.getBankCard().equals(account.getBankCard()))
                            .map(PaymentAccount::getAlipayCode)
                            .findAny()
                            .ifPresent(paymentAccount::setAlipayCode);
                    paymentAccount.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE1);
                } else {
                    paymentAccount.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE2);
                }
            }
            //非第三方账号本人数据不允许操作
            if (!paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2) && paymentAccount.getAccountName().equals(merchant)) {
                return JsonResult.error(PaymentConstant.NO_SELF);
            }
            PaymentAccount condition = new PaymentAccount();
            condition.setBankCardId(paymentAccount.getBankCardId());
            condition.setEffectivity(PaymentConstant.MYSELF_CODE1);
            condition.setStatus(PaymentConstant.CODE_0);
            QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(condition);
            PaymentAccount account = getOne(wrapper);
            Optional.ofNullable(account).ifPresent(a -> {
                if (a.getStatus().equals(PaymentConstant.CODE_0) && !a.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2)) {
                    list.add(a);
                } else if (a.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2)) {
                    if (paymentAccount.getBankCardId().equals(a.getBankCardId()) && a.getStatus().equals(PaymentConstant.VALID_CODE)) {
                        endList.add(a);
                    } else {
                        list.add(a);
                    }
                } else {
                    endList.add(a);
                }
            });

        }
        list.stream().findAny().ifPresent(a -> {
            throw new BusinessException(PaymentConstant.INFORMATION_PROCESSING);
        });

        endList.forEach(this::removeById);
        saveBatch(paymentAccounts);
        return JsonResult.success();
    }


    @Override
    public JsonResult<Object> handlePaymentCallback(String contractCode, String downloadUrl) {
        PaymentAccount param = new PaymentAccount();
        param.setContractCode(contractCode);
        PaymentAccount paymentAccount = this.getOne(new QueryWrapper<>(param));
        if (paymentAccount == null) {
            return JsonResult.error("根据合同编号未查询到付款账号信息");
        }
        CustomerAuthVo customerInfoByUserCode = customerAuthService.getCustomerInfoByUserCode(paymentAccount.getUserCode());
        if (null != customerInfoByUserCode) {
            if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerInfoByUserCode.getCustomerType())) {
                paymentAccount.setRealName(customerInfoByUserCode.getPersonName());
            } else {
                paymentAccount.setRealName(customerInfoByUserCode.getCompanyName());
            }
        }
        // 只有新增和确认付款账号时候才到账务中心进行备案
        String applicationType = paymentAccount.getApplicationType();
        Integer paymentType = paymentAccount.getPaymentType();
        // 第三方不需要备案
        boolean isThree = !PaymentConstant.PAYMENTTYPE_CODE2.equals(paymentType);
        boolean applyTypeFlag = PaymentApplyTypeEnum.APPLYTYPE_CODE2.value().equals(applicationType) || PaymentApplyTypeEnum.APPLYTYPE_CODE4.value().equals(applicationType);
        if (applyTypeFlag && isThree) {
            // 支付宝
            if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentAccount.getPaymentType())) {
                PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                paymentRecordDTO.setMerchantCode(paymentAccount.getMerchantCode());
                paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
                paymentRecordDTO.setAccountType(paymentAccount.getBusinessType());
                paymentRecordDTO.setBankCard(paymentAccount.getBankCard());
                paymentRecordDTO.setPaymentType(PayTypeEnum.ALIPAY.value());
                paymentRecordDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
                paymentRecordDTO.setBankCode(PaymentConstant.BANK_CODE);
                paymentRecordDTO.setUserCode(paymentAccount.getUserCode());
                paymentRecordDTO.setPayName(paymentAccount.getAccountName());
                paymentRecordDTO.setBankName(PaymentConstant.ALIPAY_NAME);
                paymentRecordDTO.setAccountId(paymentAccount.getAlipayCode());
                ResBody resBody = alipayRecord(paymentRecordDTO);
                if (!resBody.getResult()) {
                    String message = "备案失败，请把您的支付宝user ID【" + paymentAccount.getAlipayCode() + "】反馈给客服退款!";
                    paymentAccount.setRemark(message);
                    paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
                    this.updateById(paymentAccount);
                    return JsonResult.error(resBody.getMessage());
                }
                synBankId(paymentAccount, resBody.getData().toString());
                paymentAccount.setBankCardId(resBody.getData().toString());
                PaymentAccount paramAccount = new PaymentAccount();
                paramAccount.setMerchantCode(paymentAccount.getMerchantCode());
                paramAccount.setBankCard(paymentAccount.getBankCard());
                paramAccount.setBusinessType(paymentAccount.getBusinessType());
                List<PaymentAccount> paymentAccountList = paymentAccountDao.getPaymentAccountList(paramAccount);
                for (PaymentAccount temp : paymentAccountList) {
                    temp.setEffectivity("0");
                    if (null != paymentAccount.getPayBatchId()) {
                        temp.setPayBatchId(paymentAccount.getPayBatchId());
                    } else {
                        temp.setPayBatchId(IdWorker.getIdStr());
                    }
                    temp.setStatus("1");
                }

                this.updateBatchById(paymentAccountList);
            }
            String code = "";
            if (PaymentConstant.PAYMENTTYPE_CODE0.equals(paymentAccount.getPaymentType())) {
                PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                paymentRecordDTO.setMerchantCode(paymentAccount.getMerchantCode());
                paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
                paymentRecordDTO.setAccountType(paymentAccount.getBusinessType());
                paymentRecordDTO.setBankCard(paymentAccount.getBankCard());
                paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
                paymentRecordDTO.setCompanyCode(paymentAccount.getSettleCompanyCode());
                paymentRecordDTO.setBankCode(paymentAccount.getBankCode());
                paymentRecordDTO.setUserCode(paymentAccount.getUserCode());
                paymentRecordDTO.setPayName(paymentAccount.getAccountName());
                paymentRecordDTO.setBankName(paymentAccount.getDepositBank());
                paymentRecordDTO.setAccountId(paymentAccount.getBankCard());
                ResBody resBody = bankCardRecord(paymentRecordDTO);
                if (!resBody.getResult()) {
                    paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value());
                    this.updateById(paymentAccount);
                    return JsonResult.error(resBody.getMessage());
                }
                code = resBody.getData().toString();
                synBankId(paymentAccount, code);
                paymentAccount.setBankCardId(code);
            }
            updateById(paymentAccount);

        }

        String format = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        paymentAccount.setSignTime(format);
        paymentAccount.setDownloadUrl(downloadUrl);
        JsonResult<Object> jsonResult = commissionCallback(paymentAccount);
        if (jsonResult.getSuccess()) {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value());
            paymentAccount.setStatus("1");
        } else {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE6.value());
            paymentAccount.setRemark(jsonResult.getMessage());
        }
        updateById(paymentAccount);
        return jsonResult;
    }


    @Override
    public JsonResult<Object> thirdPartyAudit(ThirdPartyAuditDTO thirdPartyAuditDTO) {
        String name = null;
        PaymentAccount param = new PaymentAccount();
        param.setBankCardId(thirdPartyAuditDTO.getBankCardId());
        param.setBusinessType(thirdPartyAuditDTO.getBusinessType());
        param.setStatus(PaymentConstant.CODE_0);
        param.setEffectivity(PaymentConstant.VALID_CODE);
        //客户认证名称
        CustomerAuthVo customerAuthInfo = customerAuthService.getCustomerInfoByUserCode(thirdPartyAuditDTO.getUserCode());
        if (null != customerAuthInfo) {
            name = CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthInfo.getCustomerType()) ? customerAuthInfo.getPersonName() : customerAuthInfo.getCompanyName();
        }
        if (PaymentConstant.PAYMENTTYPE_CODE0.equals(thirdPartyAuditDTO.getBusinessType())) {
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(thirdPartyAuditDTO.getUserCode());
            if (packetApplyInfo == null) {
                return JsonResult.error(ResponseCode.PORTAL_6207);
            }
            param.setMerchantCode(packetApplyInfo.getMerchantCode());
        } else if (PaymentConstant.CODE_1.equals(thirdPartyAuditDTO.getBusinessType())) {
            FbaApplyInfoVO fbaInfo = getFbaInfo(thirdPartyAuditDTO.getUserCode());
            if (fbaInfo == null) {
                return JsonResult.error(PaymentConstant.EXCEPTION_INFORMATION);
            }
            param.setMerchantCode(fbaInfo.getAccountCode());
        }
        PaymentAccount paymentAccount = getOne(new QueryWrapper<>(param));
        if (paymentAccount == null) {
            return JsonResult.error(PaymentConstant.BANKCARD);
        }
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        boolean reviewStatus = Boolean.parseBoolean(thirdPartyAuditDTO.getReviewStatus());
        //是否本人
        paymentAccount.setMyself(paymentAccount.getAccountName().equals(name) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        //审核通过
        if (reviewStatus) {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE7.value());
        } else {
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE10.value());
            paymentAccount.setRemark(thirdPartyAuditDTO.getRemark());
        }
        paymentAccountDao.updateById(paymentAccount);
        return JsonResult.success();
    }

    @Override
    public JsonResult<Object> cancelAccount(AnnulAccountDTO annulAccountDTO) {
        PaymentAccount param = new PaymentAccount();
        param.setBankCardId(annulAccountDTO.getBankCardId());
        param.setMerchantCode(annulAccountDTO.getMerchantCode());
        param.setStatus(PaymentConstant.CODE_0);
        param.setEffectivity(PaymentConstant.VALID_CODE);
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(param);
        PaymentAccount paymentAccount = getOne(wrapper);
        if (paymentAccount == null) {
            return JsonResult.success();
        }
        paymentAccount.setStatus(PaymentConstant.VALID_CODE);
        paymentAccount.setEffectivity(PaymentConstant.CODE_0);
        updateById(paymentAccount);
        return JsonResult.success();
    }

    @Override
    public JsonResult<Object> getPaymentAccountStatus(String userCode, String businessType) {
        if(String.valueOf(BusinessTypeEnum.OVERSEA.getValue()).equals(businessType)||String.valueOf(BusinessTypeEnum.YWE_WAREHOUSE.getValue()).equals(businessType)){
            businessType=String.valueOf(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
        }
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("user_code", userCode)
                .eq("status", PaymentConstant.CODE_0)
                .eq("effectivity", PaymentConstant.VALID_CODE)
                .eq("process_status", PROCESSSTATUS_CODE7.value())
                .eq("business_type", businessType)
                .eq("source_type", 2)
        ;
        List<PaymentAccount> list = list(wrapper);
        if (!list.isEmpty()) {
            return JsonResult.success(list.get(0).getId());
        }
        return JsonResult.success();
    }

    public List<PaymentAccount> crmPaymentList(SearchCrmPaymentDTO searchCrmPaymentDTO) {
        CrmPaymentListVo resBody = HttpUtil.doPost(crmCommonApi + PaymentRecordEnum.SEARCH_RECORD_BANK.value(), searchCrmPaymentDTO, CrmPaymentListVo.class);
        if (resBody == null) {
            throw ResponseCode.PORTAL_3001.getError();
        }
        List<PaymentAccount> list = new ArrayList<>();
        if (Boolean.parseBoolean(resBody.getResult())) {
            List<CrmPaymentListVo.DataDTO> data = resBody.getData();
            if (data != null) {
                data.stream().filter(dataDTO -> !dataDTO.getDeleteFlag()).map(this::createPaymentAccountFromData).forEach(list::add);
            }
        }
        return list;
    }

    @Override
    public JsonResult<Object> businessVerification(EnterpriseVerifyDTO enterpriseVerify) {
        CommonCrmVo crmBody = HttpUtil.doPost(crmCommonApi + CrmApiEnum.SUBMIT_COMPANYAUTH.value(), enterpriseVerify, CommonCrmVo.class);
        error(crmBody);
        boolean parseBoolean = Boolean.parseBoolean(crmBody.getResult());
        if (parseBoolean) {
            return JsonResult.success(crmBody.getData());
        } else {
            return JsonResult.error(crmBody.getMessage());
        }
    }

    @Override
    public CommonCrmVo bankCardSubmit(EnterpriseBanKCardDTO enterpriseBanKCard) {
        CommonCrmVo commonCrmVo = HttpUtil.doPost(crmCommonApi + CrmApiEnum.SUBMIT_COMPANY_BANKAUTH.value(), enterpriseBanKCard, CommonCrmVo.class);
        error(commonCrmVo);
        return commonCrmVo;
    }

    @Override
    public JsonResult<Object> getBranchBankList(String flowId, String keyword) {
        BankCardBranchVo bankCardBranchVo = HttpUtil.doPost(crmCommonApi + CrmApiEnum.BRANCH_BANK_LIST.value(), null, ImmutableMap.of("flowId", flowId, "keyword", keyword), BankCardBranchVo.class);
        error(bankCardBranchVo);
        boolean parseBoolean = Boolean.parseBoolean(bankCardBranchVo.getResult());
        if (parseBoolean) {
            return JsonResult.success(bankCardBranchVo.getData());
        } else {
            return JsonResult.error(bankCardBranchVo.getMessage());
        }
    }

    @Override
    public JsonResult<Object> getCompanyBankAuthInfo(String flowId, Integer businessType) {
        BankCardVerifyVo bankCardVerifyVo = getBankCardVerifyVo(flowId);
        error(bankCardVerifyVo);
        boolean parseBoolean = Boolean.parseBoolean(bankCardVerifyVo.getResult());
        if (parseBoolean) {
            PaymentAccount account = new PaymentAccount();
            account.setFlowId(flowId);
            account.setBusinessType(businessType);
            account.setStatus(PaymentConstant.CODE_0);
            account.setEffectivity(PaymentConstant.VALID_CODE);
            PaymentAccount one = getOne(new QueryWrapper<>(account));
            //校验失败后需要校验次数,到达三次后重置信息
            Integer counter = bankCardVerifyVo.getData().getCounter();
            if (PaymentConstant.CODE_3.equals(counter)) {
                String resubmit = resubmit(bankCardVerifyVo, flowId);
                one.setFlowId(resubmit);
            }
            return JsonResult.success(ImmutableMap.of("bankCardVerifyVo", bankCardVerifyVo.getData(), "paymentAccount", one));
        } else {
            return JsonResult.error(bankCardVerifyVo.getMessage());
        }
    }

    public BankCardVerifyVo getBankCardVerifyVo(String flowId) {
        return HttpUtil.doGet(crmCommonApi + CrmApiEnum.GET_COMPANY_BANKAUTH_INFO.value() + flowId, BankCardVerifyVo.class);

    }

    @Override
    public JsonResult<Object> verifyTransferAmount(String flowId, String amount) {
        CommonCrmVo commonCrmVo = HttpUtil.doPost(crmCommonApi + CrmApiEnum.VERIFY_TRANSFERAMOUNT.value(), null, ImmutableMap.of("flowId", flowId, "amount", amount), CommonCrmVo.class);
        error(commonCrmVo);
        boolean parseBoolean = Boolean.parseBoolean(commonCrmVo.getResult());
        if (parseBoolean) {
            PaymentAccount account = new PaymentAccount();
            account.setFlowId(flowId);
            account.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE0);
            account.setStatus(PaymentConstant.CODE_0);
            account.setEffectivity(PaymentConstant.VALID_CODE);
            List<PaymentAccount> list = list(new QueryWrapper<>(account));
            for (PaymentAccount paymentAccount : list) {
                if (APPLYTYPE_CODE2.value().equals(paymentAccount.getApplicationType()) || APPLYTYPE_CODE4.value().equals(paymentAccount.getApplicationType())) {
                    //新增本人的直接备案
                    if (PaymentConstant.CODE_0.equals(paymentAccount.getMyself())) {
                        paymentAccount.setBankCardId(soleCodeFake(paymentAccount));
                        JsonResult<Object> result = personalRecord(paymentAccount);
                        if (!result.getSuccess()) {
                            throw new BusinessException(ResponseCode.PORTAL_4004.getMessage());
                        }
                    } else {
                        JsonResult<Object> result = paymentAudit(paymentAccount);
                        if (!result.getSuccess()) {
                            throw new BusinessException(result.getMessage());
                        }
                    }
                } else {
                    //账户名更改时需要重新备案
                    if (!paymentAccount.getAccountName().equals(paymentAccount.getOriginalAccountName())) {
                        JsonResult<Object> jsonResult = accountUpdateRecord(paymentAccount, paymentAccount);
                        if (!jsonResult.getSuccess()) {
                            return jsonResult;
                        }
                        // 如果是企业商户，校验是否是本商户操作
                        if(completeCompanyPayment(paymentAccount)){
                            return JsonResult.success();
                        }
                        synAccount(paymentAccount);
                    }
                    paymentAccount.setProcessStatus(PROCESSSTATUS_CODE7.value());
                    updateById(paymentAccount);
                }
            }
            return JsonResult.success();
        }
        return JsonResult.error(commonCrmVo.getMessage());
    }

    public boolean completeCompanyPayment(PaymentAccount paymentAccount){
        // 银行卡类型
        try {
            if(paymentAccount.getPaymentType()==0&&paymentAccount.getPaymentTopicType()==3){
                CustomerAuthVo customerAuthVo = customerAuthService.getCustomerInfoByUserCode(paymentAccount.getUserCode());
                if(customerAuthVo!=null){
                    if(CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthVo.getCustomerType())){
                        if(customerAuthVo.getCompanyName().equals(paymentAccount.getAccountName())){
                            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value());
                            paymentAccount.setStatus("1");
                            paymentAccount.setEffectivity("0");
                            updateById(paymentAccount);
                            return true;
                        }
                    }
                }
            }
        } catch (Exception e) {
           log.error("与企业名称相同付款账号补齐处理失败",e);
        }
        return false;
    }

    @Override
    public JsonResult<Object> alipayCheck(String bankCard, UserAgent currentUser, Integer businessType, String id) {
        PaymentAccount account = new PaymentAccount();
        account.setNo(currentUser.getMerchantNo());
        getMerchant(account, currentUser.getUserCode(), businessType);
        SearchCrmPaymentDTO dto = new SearchCrmPaymentDTO();
        dto.setMerchantCode(account.getMerchantCode());
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setMerchantCode(account.getMerchantCode());
        paymentAccount.setBusinessType(businessType);
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setEffectivity(PaymentConstant.MYSELF_CODE1);
        paymentAccount.setPaymentType(PaymentConstant.CODE_1);
        QueryWrapper<PaymentAccount> wrapper = new QueryWrapper<>(paymentAccount);
        List<PaymentAccount> accounts = list(wrapper);
        if (!accounts.isEmpty()) {
            if (StringUtils.isBlank(id) || accounts.size() > 1) {
                return JsonResult.error(PaymentConstant.ALIPAY_ERROR, null, "-1");
            }
        }
        List<PaymentAccount> paymentAccounts = crmPaymentList(dto);
        if (paymentAccounts.stream().anyMatch(payment ->
                businessType.equals(payment.getBusinessType())
                        && PaymentConstant.PAYMENTTYPE_CODE1.equals(payment.getPaymentType())
                        && bankCard.equals(payment.getBankCard()))) {
            return JsonResult.error(PaymentConstant.ALIPAY_DUPLICATE, null, "-2");
        }

        return JsonResult.success();
    }

    @Override
    public JsonResult<Object> revalidateBank(PaymentAccount paymentAccount, UserAgent currentUser) {
        if (null == paymentAccount.getId()) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        PaymentAccount account = getById(paymentAccount.getId());
        String flowId = account.getFlowId();
        //从本地查询新增信息是否重复
        paymentVerification(paymentAccount, account.getBankCard());
        //提交企业银行卡
        EnterpriseBanKCardDTO enterpriseBanKCardDTO = new EnterpriseBanKCardDTO();
        enterpriseBanKCardDTO.setBankCard(paymentAccount.getBankCard());
        enterpriseBanKCardDTO.setBankBranchName(paymentAccount.getBankBranchName());
        enterpriseBanKCardDTO.setFlowId(paymentAccount.getFlowId());
        CommonCrmVo commonCrmVo = bankCardSubmit(enterpriseBanKCardDTO);
        if (!Boolean.parseBoolean(commonCrmVo.getResult())) {
            throw new BusinessException(commonCrmVo.getMessage());
        }
        PaymentAccount parameter = new PaymentAccount();
        parameter.setMerchantCode(account.getMerchantCode());
        parameter.setFlowId(flowId);
        parameter.setStatus(PaymentConstant.CODE_0);
        parameter.setEffectivity(PaymentConstant.MYSELF_CODE1);
        List<PaymentAccount> list = list(new QueryWrapper<>(parameter));
        for (PaymentAccount paymentAccount1 : list) {
            //更新信息
            paymentAccount1.setProcessStatus(PROCESSSTATUS_CODE11.value());
            paymentAccount1.setBankCard(paymentAccount.getBankCard());
            paymentAccount1.setBankCode(paymentAccount.getBankCode());
            paymentAccount1.setDepositBank(paymentAccount.getDepositBank());
            paymentAccount1.setFlowId(paymentAccount.getFlowId());
            updateById(paymentAccount1);
        }
        return JsonResult.success();
    }

    @Override
    public JsonResult<Object> recordSigning(PaymentRecordDTO paymentRecordDTO) {
        paymentRecordDTO.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE0);
        String userCode = paymentRecordDTO.getUserCode();
        String merchantCode;
        String companyCode;
        if (PaymentConstant.PAYMENTTYPE_CODE1.equals(paymentRecordDTO.getAccountType())) {
            FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(userCode);
            merchantCode = fbaApplyInfo.getAccountCode();
            companyCode = FbaConstant.FBA_ACC_SETTLE_COMPANY;
        } else {
            PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
            merchantCode = packetApplyInfo.getMerchantCode();
            companyCode = packetApplyInfo.getSettleCompanyCode();
        }
        paymentRecordDTO.setMerchantCode(merchantCode);
        paymentRecordDTO.setCompanyCode(companyCode);
        paymentRecordDTO.setBankName(bankService.getBankName(paymentRecordDTO.getBankCode()));
        SearchCrmPaymentDTO dto = new SearchCrmPaymentDTO();
        dto.setMerchantCode(paymentRecordDTO.getMerchantCode());
        List<PaymentAccount> paymentAccounts = crmPaymentList(dto);
        if (paymentAccounts.stream().anyMatch(payment ->
                paymentRecordDTO.getAccountType().equals(payment.getBusinessType())
                        && PaymentConstant.PAYMENTTYPE_CODE0.equals(payment.getPaymentType())
                        && paymentRecordDTO.getBankCard().equals(payment.getBankCard()))) {
            log.error("当前付款账号在当前客户号下已存在备案:{}", paymentRecordDTO);
            return JsonResult.success();
        }
        //相同结算公司是否存在
        SearchCrmPaymentDTO paymentDTO = new SearchCrmPaymentDTO();
        paymentDTO.setCompanyCode(paymentRecordDTO.getCompanyCode());
        paymentDTO.setAccountId(paymentRecordDTO.getBankCard());
        List<PaymentAccount> paymentAccountList = crmPaymentList(paymentDTO);
        if (!paymentAccountList.isEmpty()) {
            //已存在则插入备案失败状态
            saveRecordSigning(paymentRecordDTO);
            log.error("当前付款账号相同结算公司已存在备案:{}", paymentRecordDTO);
            return JsonResult.success();
        }
        paymentRecordDTO.setAccountId(paymentRecordDTO.getBankCard());
        ResBody resBody = bankCardRecord(paymentRecordDTO);
        if (!resBody.getResult()) {
            //备案失败则插入
            saveRecordSigning(paymentRecordDTO);
            log.error("备案失败:{}", paymentRecordDTO);
            DingTalkClient.sendMessage("签约回调银行卡备案失败:" + resBody.getMessage() + "备案信息" + paymentRecordDTO);
            return JsonResult.success();
        }
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JsonResult<Object> thawPayment(PaymentAccount paymentAccount) {
        if (paymentAccount.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE1)) {
            paymentAccount.setPayBatchId(IdWorker.getIdStr());
            paymentAccount.setPaymentType(paymentAccount.getPlatformName().equals(PaymentConstant.ALIPAY_NAME) ? PaymentConstant.PAYMENTTYPE_CODE1 : PaymentConstant.PAYMENTTYPE_CODE2);
        }
        paymentAccount.setEffectivity(PaymentConstant.VALID_CODE);
        paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE0.value());
        paymentAccount.setApplicationType(PaymentApplyTypeEnum.APPLYTYPE_CODE4.value());
        paymentAccount.setOriginalAccountName(paymentAccount.getAccountName());
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setProcessStatus(PROCESSSTATUS_CODE0.value());
        //客户认证名称
        CustomerAuthVo customerAuthInfo = customerAuthService.getCustomerInfoByUserCode(paymentAccount.getUserCode());
        String merchantName = CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthInfo.getCustomerType()) ? customerAuthInfo.getPersonName() : customerAuthInfo.getCompanyName();
        //非本人
        if (!merchantName.equals(paymentAccount.getAccountName())) {
            if (paymentAccount.getPaymentTopicType().equals(PaymentConstant.PAYMENTTYPE_CODE0) || paymentAccount.getPaymentTopicType().equals(PaymentConstant.CODE_1)) {
                if (CustomerTypeEnum.ENTERPRISE.value().equals(customerAuthInfo.getCustomerType())) {
                    return JsonResult.error(ResponseCode.PORTAL_5010);
                }
            } else if (PaymentConstant.CODE_2.equals(paymentAccount.getPaymentTopicType()) || PaymentConstant.CODE_3.equals(paymentAccount.getPaymentTopicType())) {
                if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerAuthInfo.getCustomerType())) {
                    return JsonResult.error(ResponseCode.PORTAL_5010);
                }
            }
        }
        paymentAccount.setMyself(merchantName.equals(paymentAccount.getAccountName()) ? PaymentConstant.MYSELF_CODE0 : PaymentConstant.MYSELF_CODE1);
        PaymentAccount condition = new PaymentAccount();
        condition.setMerchantCode(paymentAccount.getMerchantCode());
        condition.setBankCard(paymentAccount.getBankCard());
        condition.setEffectivity(PaymentConstant.VALID_CODE);
        List<PaymentAccount> paymentAccountList = paymentAccountDao.getPaymentAccountList(condition);
        for (PaymentAccount account : paymentAccountList) {
            if (PaymentConstant.CODE_0.equals(account.getStatus())) {
                //第三方
                if (account.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2) && account.getBankCardId().equals(paymentAccount.getBankCardId())) {
                    return JsonResult.error(PaymentConstant.INFORMATION_PROCESSING);
                    //银行或者支付宝备案失败不参与校验
                } else if (!account.getPaymentType().equals(PaymentConstant.PAYMENTTYPE_CODE2)) {
                    if (account.getApplicationType().equals(PaymentApplyTypeEnum.APPLYTYPE_CODE2.value()) && !account.getProcessStatus().equals(PaymentApplyTypeEnum.PROCESSSTATUS_CODE4.value())) {
                        return JsonResult.error(PaymentConstant.INFORMATION_PROCESSING);
                    }
                }
            } else {
                removeById(account.getId());
            }
        }
        save(paymentAccount);
        return JsonResult.success();

    }

    @Override
    public JsonResult<Object> queryAccountStatus(JSONObject jsonObject) {
        log.info("查看委托书状态CRM传参:{}", jsonObject);
        JSONArray data = jsonObject.getJSONArray("data");
        //委托书的状态
        String status = jsonObject.getString("status");
        if (null == data && status == null) {
            return JsonResult.error(ResponseCode.PORTAL_5010);
        }
        QueryWrapper<PaymentAccount> queryWrapper = new QueryWrapper<>();
        if (data != null && !data.isEmpty()) {
            String[] merchantCode = new String[data.size() + 1];
            String[] bankCard = new String[data.size() + 1];
            for (int i = 0; i < data.size(); i++) {
                merchantCode[i] = data.getJSONObject(i).getString("merchantCode");
                bankCard[i] = data.getJSONObject(i).getString("bankCard");
            }
            queryWrapper.in("merchant_code", (Object) merchantCode)
                    .in("bank_card", (Object) bankCard);
        }
        if (StringUtils.isNotBlank(status) && !PaymentConstant.CRM_CODE.equals(status)) {
            queryWrapper.eq("application_type", status);
        }
        if (PaymentConstant.CRM_CODE.equals(status)) {
            //委托书的四种申请类型
            queryWrapper.in("application_type", APPLYTYPE_CODE1.value(), APPLYTYPE_CODE2.value(), APPLYTYPE_CODE3.value(), APPLYTYPE_CODE4.value());
        }
        queryWrapper.eq("status", PaymentConstant.CODE_0)
                .orderBy(false, false, "update_time");
        List<PaymentAccount> paymentAccounts = list(queryWrapper);
        List<Map<String, Object>> list = paymentAccounts.parallelStream()
                .map(paymentAccount -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("applicationType", paymentAccount.getApplicationType());
                    map.put("bankCard", paymentAccount.getBankCard());
                    map.put("merchantCode", paymentAccount.getMerchantCode());
                    return map;
                })
                .collect(Collectors.toList());
        return JsonResult.success(list);
    }

    @Override
    public JsonResult<Object> repealAccount(AnnulAccountDTO annulAccountVo) {
        PaymentAccount param = new PaymentAccount();
        param.setBankCardId(annulAccountVo.getBankCardId());
        param.setMerchantCode(annulAccountVo.getMerchantCode());
        param.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE7.value());
        param.setEffectivity(PaymentConstant.VALID_CODE);
        List<PaymentAccount> paymentAccounts = list(new QueryWrapper<>(param));
        if (paymentAccounts.isEmpty()) {
            return JsonResult.success();
        }
        for (PaymentAccount paymentAccount : paymentAccounts) {
            String applicationType = paymentAccount.getApplicationType();
            if (PaymentApplyTypeEnum.APPLYTYPE_CODE3.value().equals(applicationType) || PaymentApplyTypeEnum.APPLYTYPE_CODE4.value().equals(applicationType)) {
                throw new BusinessException("当前账号类型为客服发起暂不支持撤销!");
            }
            paymentAccount.setRemark(PaymentConstant.CRM_MESSAGE);
            paymentAccount.setStatus(PaymentConstant.VALID_CODE);
            paymentAccount.setEffectivity(PaymentConstant.INVALID_CODE);
            paymentAccount.setProcessStatus(PaymentApplyTypeEnum.PROCESSSTATUS_CODE3.value());
            updateById(paymentAccount);

        }
        return JsonResult.success();
    }

    public void error(Object o) {
        if (null == o) {
            throw new BusinessException(ResponseCode.PORTAL_3001);
        }
    }

    public void saveRecordSigning(PaymentRecordDTO paymentRecordDTO) {
        PaymentAccount paymentAccount = new PaymentAccount();
        paymentAccount.setMerchantCode(paymentRecordDTO.getMerchantCode());
        paymentAccount.setStatus(PaymentConstant.CODE_0);
        paymentAccount.setEffectivity(PaymentConstant.VALID_CODE);
        paymentAccount.setBusinessType(paymentRecordDTO.getAccountType());
        paymentAccount.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE0);
        List<PaymentAccount> list = list(new QueryWrapper<>(paymentAccount));
        list.stream()
                .filter(account -> paymentRecordDTO.getBankCard().equals(account.getBankCard()))
                .forEach(account -> {
                    account.setProcessStatus(PROCESSSTATUS_CODE4.value());
                    updateById(account);
                });

        if (list.isEmpty()) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            PaymentAccount account = new PaymentAccount();
            account.setMerchantCode(paymentRecordDTO.getMerchantCode());
            account.setStatus(PaymentConstant.CODE_0);
            account.setEffectivity(PaymentConstant.VALID_CODE);
            account.setBusinessType(paymentRecordDTO.getAccountType());
            account.setPaymentType(PaymentConstant.PAYMENTTYPE_CODE0);
            account.setProcessStatus(PROCESSSTATUS_CODE4.value());
            account.setApplicationType(APPLYTYPE_CODE2.value());
            account.setMyself(PaymentConstant.MYSELF_CODE0);
            account.setBankCard(paymentRecordDTO.getBankCard());
            account.setAccountName(paymentRecordDTO.getPayName());
            account.setBankCode(paymentRecordDTO.getBankCode());
            account.setDepositBank(paymentRecordDTO.getBankName());
            account.setOriginalAccountName(paymentRecordDTO.getPayName());
            account.setSourceType(PaymentConstant.CODE_2);
            account.setUserCode(paymentRecordDTO.getUserCode());
            account.setAccountTime(LocalDate.now().format(formatter));
            save(account);
        }
    }

    private PaymentAccount createPaymentAccountFromData(CrmPaymentListVo.DataDTO datum) {
        PaymentAccount account = new PaymentAccount();
        account.setMerchantCode(datum.getMerchantCode());
        account.setPayment(datum.getPaymentType());
        account.setPayment(datum.getPaymentType());
        account.setPaymentType(datum.getPaymentType());
        account.setBankCardId(datum.getId());
        account.setAccountName(datum.getPayName());
        account.setDepositBank(datum.getBankName());
        account.setBankCode(datum.getBankCode());
        account.setAlipayCode(datum.getAccountId());
        account.setBusinessType(datum.getAccountType());
        account.setSettleCompanyCode(datum.getCompanyCode());
        account.setBankCard(datum.getBankCard());
        account.setExist(true);
        account.setSource(2);
        return account;
    }

    public String resubmit(BankCardVerifyVo bankCardVerifyVo, String flowId) {
        BankCardVerifyVo.DataDTO data = bankCardVerifyVo.getData();
        EnterpriseVerifyDTO enterpriseVerify = new EnterpriseVerifyDTO();
        enterpriseVerify.setCompanyName(data.getCompanyName());
        enterpriseVerify.setBusinessLicense(data.getBusinessLicense());
        enterpriseVerify.setLegalName(data.getLegalName());
        JSONObject jsonObject = HttpUtil.doPost(crmCommonApi + CrmApiEnum.SUBMIT_COMPANYAUTH.value(), enterpriseVerify, JSONObject.class);
        error(jsonObject);
        String id = jsonObject.getJSONObject("data").getString("flowId");
        EnterpriseBanKCardDTO enterpriseBanKCardDTO = new EnterpriseBanKCardDTO();
        enterpriseBanKCardDTO.setFlowId(id);
        enterpriseBanKCardDTO.setBankCard(data.getBankCard());
        enterpriseBanKCardDTO.setBankBranchName(data.getBankBranchName());
        CommonCrmVo commonCrmVo = bankCardSubmit(enterpriseBanKCardDTO);
        boolean parseBoolean = Boolean.parseBoolean(commonCrmVo.getResult());
        //重置后更改流程id
        if (parseBoolean) {
            //更新查询条件
            PaymentAccount parameter = new PaymentAccount();
            parameter.setFlowId(flowId);
            //更新的id
            PaymentAccount paymentAccount = new PaymentAccount();
            paymentAccount.setFlowId(id);
            //替换id
            update(paymentAccount, new QueryWrapper<>(parameter));
        }
        return id;
    }

    public void paymentVerification(PaymentAccount paymentAccount, String bankCard) {
        if (!bankCard.equals(paymentAccount.getBankCard())) {
            PaymentAccount account = new PaymentAccount();
            account.setMerchantCode(paymentAccount.getMerchantCode());
            account.setBankCard(paymentAccount.getBankCard());
            account.setStatus(PaymentConstant.CODE_0);
            account.setEffectivity(PaymentConstant.MYSELF_CODE1);
            List<PaymentAccount> list = list(new QueryWrapper<>(account));
            if (!list.isEmpty()) {
                throw new BusinessException(PaymentConstant.INFORMATION_PROCESSING);
            }
            comparison(paymentAccount);
        }
    }

    private FbaApplyInfoVO getFbaInfo(String userCode) {
        JSONObject jsonObject = HttpUtil.doGet(fbaUrl + FbaApiEnum.GET_APPLY_INFO.value() + userCode);
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        String hasError1 = jsonObject.getString("HasError");
        boolean parseBoolean1 = Boolean.parseBoolean(hasError1);
        if (parseBoolean1) {
            return null;
        }

        String code = jsonObject.getJSONObject("Data").getString("AccountCode");
        JSONObject object = HttpUtil.doGet(fbaUrl + FbaApiEnum.GET_FBA_SALE_INFO.value() + code);
        String hasError2 = object.getString("HasError");
        boolean parseBoolean2 = Boolean.parseBoolean(hasError2);
        if (parseBoolean2) {
            return null;
        }
        FbaApplyInfoVO fbaApplyInfoVO = new FbaApplyInfoVO();
        String merchantName = object.getJSONObject("Data").getString("MerchantName");
        fbaApplyInfoVO.setAccountCode(code);
        fbaApplyInfoVO.setContactName(merchantName);
        return fbaApplyInfoVO;
    }

    @Override
    public JsonResult contractAndRecord(UserAgent userAgent, Integer businessType, Boolean viewContract) {
        String userCode = userAgent.getUserCode();
        Map<String, Object> resMap = new HashMap<>(6);
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(businessType);
        String merchantCode = "";
        String settleCompanyCode = "";
        Integer contractType = null;
        switch (businessTypeEnum) {
            case STRAIGHT:
                PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
                Assert.notNull(packetApplyInfo, "未查询到小包业务信息");
                merchantCode = packetApplyInfo.getMerchantCode();
                settleCompanyCode = packetApplyInfo.getSettleCompanyCode();
                contractType = ContractTypeEnum.CONTRACT.value();
                break;
            case FBA:
                FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(userCode);
                Assert.notNull(fbaApplyInfo, "未查询到fba业务信息");
                merchantCode = fbaApplyInfo.getAccountCode();
                settleCompanyCode = FbaConstant.FBA_ACC_SETTLE_COMPANY;
                contractType = ContractTypeEnum.FBA_CONTRACT.value();
                break;
            case OVERSEA:
                YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(userCode);
                Assert.notNull(yweApplyInfoVo, "未查询到YWE海外派业务信息");
                merchantCode = yweApplyInfoVo.getMerchantCode();
                contractType = ContractTypeEnum.YWE_CONTRACT.value();
                businessType= BusinessTypeEnum.YWE_WAREHOUSE.getValue();
                settleCompanyCode = yweApplyInfoVo.getSettleCompanyCode();
                break;
            default:
                Assert.isTrue(false, "业务类型异常");
        }
        JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
        Assert.isTrue(Objects.nonNull(data), "网络异常,未查询到客户信息");
        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        Integer customerType = customer.getCustomerType();
        String customerName;
        if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
            customerName = customer.getPersonName();
        } else {
            customerName = customer.getCompanyName();
        }
        SearchCrmPaymentDTO dto = new SearchCrmPaymentDTO();
        dto.setMerchantCode(merchantCode);
        CrmPaymentListVo resBody = straightCrmService.getPayment(dto);
        Optional<CrmPaymentListVo.DataDTO> first = resBody.getData().stream().filter(entity -> !entity.getDeleteFlag() && PaymentConstant.PAYMENTTYPE_CODE0.equals(entity.getPaymentType()) && customerName.equals(entity.getPayName())).findFirst();
        // 银行付款账号信息存在
        if (first.isPresent()) {
            CrmPaymentListVo.DataDTO dataDTO = first.get();
            // 去签署合同
            SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, dataDTO.getPayName(), dataDTO.getBankCard(), dataDTO.getBankName(), viewContract);
            resMap.put("status", 0);
            resMap.put("signUrl", signContractVO.getUrl());
            resMap.put("contractName", signContractVO.getContractName());
            return JsonResult.success(resMap);
        } else {
            if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
                PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                if (personAuthVo == null || !Integer.valueOf(1).equals(personAuthVo.getAuthResult())) {
                    throw new BusinessException(ResponseCode.PORTAL_4013);
                }
                if (Integer.valueOf(1).equals(personAuthVo.getAuthType()) && StringUtils.isNotBlank(personAuthVo.getBankCard())) {
                    // 没有银行编码的怎么处理
                    if (StringUtils.isNotBlank(personAuthVo.getBankCode())) {
                        // 去备案
                        PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                        paymentRecordDTO.setMerchantCode(merchantCode);
                        paymentRecordDTO.setAccountId(personAuthVo.getBankCard());
                        paymentRecordDTO.setAccountType(businessType);
                        paymentRecordDTO.setBankCard(personAuthVo.getBankCard());
                        paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
                        paymentRecordDTO.setCompanyCode(settleCompanyCode);
                        paymentRecordDTO.setBankCode(personAuthVo.getBankCode());
                        paymentRecordDTO.setUserCode(userCode);
                        paymentRecordDTO.setPayName(customerName);
                        paymentRecordDTO.setBankName(bankService.getBankName(paymentRecordDTO.getBankCode()));
                        paymentRecordDTO.setAccountId(personAuthVo.getBankCard());
                        bankCardRecord(paymentRecordDTO);
                        // 去签署合同
                        SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, paymentRecordDTO.getPayName(), paymentRecordDTO.getBankCard(), paymentRecordDTO.getBankName(), viewContract);
                        resMap.put("status", 0);
                        resMap.put("signUrl", signContractVO.getUrl());
                        resMap.put("contractName", signContractVO.getContractName());
                        return JsonResult.success(resMap);
                    } else {
                        // 去处理个人银行卡信息
                        resMap.put("status", 3);
                        resMap.put("customerName", customerName);
                        resMap.put("idCard", customer.getPersonCard());
                        resMap.put("customerType", customerType);
                        resMap.put("bankCard", personAuthVo.getBankCard());
                        return JsonResult.success(resMap);
                    }

                }
                // 去处理个人银行卡信息
                resMap.put("status", 1);
                resMap.put("customerName", customerName);
                resMap.put("idCard", customer.getPersonCard());
                return JsonResult.success(resMap);
            } else {
                // 大陆企业
                CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
                // 如果是法人认证不需要银行卡信息
                PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                if (personAuthVo.getIdNumber().equals(companyAuthVo.getLegalIdCard()) && StringUtils.isBlank(companyAuthVo.getBankCard())) {
                    // 去签署合同
                    SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, null, null, null, viewContract);
                    resMap.put("status", 0);
                    resMap.put("signUrl", signContractVO.getUrl());
                    resMap.put("contractName", signContractVO.getContractName());
                    return JsonResult.success(resMap);
                }
                if (companyAuthVo != null && Integer.valueOf(3).equals(companyAuthVo.getVerifyStatus()) && StringUtils.isNotBlank(companyAuthVo.getBankCard())) {
                    // 没有银行编码的怎么处理
                    if (StringUtils.isNotBlank(companyAuthVo.getBankCode())) {
                        // 去备案
                        PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                        paymentRecordDTO.setMerchantCode(merchantCode);
                        paymentRecordDTO.setAccountId(companyAuthVo.getBankCard());
                        paymentRecordDTO.setAccountType(businessType);
                        paymentRecordDTO.setBankCard(companyAuthVo.getBankCard());
                        paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
                        paymentRecordDTO.setCompanyCode(settleCompanyCode);
                        paymentRecordDTO.setBankCode(companyAuthVo.getBankCode());
                        paymentRecordDTO.setUserCode(userCode);
                        paymentRecordDTO.setPayName(customerName);
                        paymentRecordDTO.setBankName(bankService.getBankName(paymentRecordDTO.getBankCode()));
                        paymentRecordDTO.setAccountId(companyAuthVo.getBankCard());
                        bankCardRecord(paymentRecordDTO);
                        // 去签署合同
                        SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, paymentRecordDTO.getPayName(), paymentRecordDTO.getBankCard(), paymentRecordDTO.getBankName(), viewContract);
                        resMap.put("status", 0);
                        resMap.put("signUrl", signContractVO.getUrl());
                        resMap.put("contractName", signContractVO.getContractName());
                        return JsonResult.success(resMap);
                    } else {
                        // 去处理个人银行卡信息
                        resMap.put("status", 3);
                        resMap.put("customerName", customerName);
                        resMap.put("customerType", customerType);
                        resMap.put("bankCard", companyAuthVo.getBankCard());
                        return JsonResult.success(resMap);
                    }

                } else {
                    resMap.put("status", 2);
                    return JsonResult.success(resMap);
                }
            }
        }
    }


    public SignContractVO getContractSignUrl(UserAgent currentUser, Integer contractType, String bankAccountName, String bankCard, String bankName, Boolean viewContract) {
        QueryContractDTO queryContractDTO = new QueryContractDTO();
        queryContractDTO.setNo(currentUser.getMerchantNo());
        queryContractDTO.setContractType(contractType);
        queryContractDTO.setUserCode(currentUser.getUserCode());
        queryContractDTO.setAdminPhone(currentUser.getPhone().trim());
        queryContractDTO.setViewContract(viewContract);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bankAccountName", bankAccountName);
        jsonObject.put("bankCard", bankCard);
        jsonObject.put("bankName", bankName);
        queryContractDTO.setExtraData(jsonObject.toString());
        SignContractVO signContractVO = commonCrmService.getSignUrl(queryContractDTO);
        return signContractVO;
    }

    @Override
    public JsonResult getRecordSign(UserAgent userAgent, RecordSignDTO recordSignDTO) {
        String userCode = userAgent.getUserCode();
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getBusinessTypeEnum(recordSignDTO.getBusinessType());
        String merchantCode = "";
        String settleCompanyCode = "";
        Integer contractType = null;
        switch (businessTypeEnum) {
            case STRAIGHT:
                PacketApplyInfoVo packetApplyInfo = packetBusinessApplyService.getPacketApplyInfo(userCode);
                Assert.notNull(packetApplyInfo, "未查询到小包业务信息");
                merchantCode = packetApplyInfo.getMerchantCode();
                settleCompanyCode = packetApplyInfo.getSettleCompanyCode();
                contractType = ContractTypeEnum.CONTRACT.value();
                break;
            case FBA:
                FbaApplyInfoVO fbaApplyInfo = fbaApplyService.getFbaApplyInfo(userCode);
                Assert.notNull(fbaApplyInfo, "未查询到fba业务信息");
                merchantCode = fbaApplyInfo.getAccountCode();
                settleCompanyCode = FbaConstant.FBA_ACC_SETTLE_COMPANY;
                contractType = ContractTypeEnum.FBA_CONTRACT.value();
                break;
            case OVERSEA:
                YWEApplyInfoVo yweApplyInfoVo = ywEOverseaService.getYWEApplyInfoVo(userCode);
                Assert.notNull(yweApplyInfoVo, "未查询到YWE海外派业务信息");
                merchantCode = yweApplyInfoVo.getMerchantCode();
                contractType = ContractTypeEnum.YWE_CONTRACT.value();
                settleCompanyCode = yweApplyInfoVo.getSettleCompanyCode();
                recordSignDTO.setBusinessType(BusinessTypeEnum.YWE_WAREHOUSE.getValue());
                break;
            default:
                Assert.isTrue(false, "业务类型异常");
        }
        JSONObject data = commonCrmService.getCustomerAuthInoByUserCode(userCode);
        Assert.isTrue(Objects.nonNull(data), "网络异常,未查询到客户信息");

        CustomerVo customer = data.getObject("customer", CustomerVo.class);
        // 如果为3说明是缺少银行名称
        Integer customerType = customer.getCustomerType();
        if (recordSignDTO.getStatus() == 3) {
            if (CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
                PersonAuthVo personAuthVo = data.getObject("personAuthVo", PersonAuthVo.class);
                if (personAuthVo == null || !Integer.valueOf(1).equals(personAuthVo.getAuthResult())) {
                    throw new BusinessException(ResponseCode.PORTAL_4013);
                }
                if (Integer.valueOf(1).equals(personAuthVo.getAuthType()) && StringUtils.isNotBlank(personAuthVo.getBankCard())) {
                    // 去备案
                    PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                    paymentRecordDTO.setMerchantCode(merchantCode);
                    paymentRecordDTO.setAccountId(personAuthVo.getBankCard());
                    paymentRecordDTO.setAccountType(recordSignDTO.getBusinessType());
                    paymentRecordDTO.setBankCard(personAuthVo.getBankCard());
                    paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
                    paymentRecordDTO.setCompanyCode(settleCompanyCode);
                    paymentRecordDTO.setBankCode(recordSignDTO.getBankCode());
                    paymentRecordDTO.setUserCode(userCode);
                    paymentRecordDTO.setPayName(customer.getPersonName());
                    paymentRecordDTO.setBankName(recordSignDTO.getBankName());
                    paymentRecordDTO.setAccountId(personAuthVo.getBankCard());
                    bankCardRecord(paymentRecordDTO);
                    // 去签署合同
                    SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, paymentRecordDTO.getPayName(), paymentRecordDTO.getBankCard(), paymentRecordDTO.getBankName(), null);
                    return JsonResult.success(signContractVO.getUrl());
                } else {
                    return JsonResult.error(ResponseCode.PORTAL_4013);
                }
            } else {
                // 大陆企业
                CompanyAuthVo companyAuthVo = data.getObject("companyAuthVo", CompanyAuthVo.class);
                if (companyAuthVo != null && Integer.valueOf(3).equals(companyAuthVo.getVerifyStatus()) && StringUtils.isNotBlank(companyAuthVo.getBankCard())) {
                    // 没有银行编码的怎么处理
                    // 去备案
                    PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
                    paymentRecordDTO.setMerchantCode(merchantCode);
                    paymentRecordDTO.setAccountId(companyAuthVo.getBankCard());
                    paymentRecordDTO.setAccountType(recordSignDTO.getBusinessType());
                    paymentRecordDTO.setBankCard(companyAuthVo.getBankCard());
                    paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
                    paymentRecordDTO.setCompanyCode(settleCompanyCode);
                    paymentRecordDTO.setBankCode(recordSignDTO.getBankCode());
                    paymentRecordDTO.setUserCode(userCode);
                    paymentRecordDTO.setPayName(customer.getCompanyName());
                    paymentRecordDTO.setBankName(recordSignDTO.getBankName());
                    paymentRecordDTO.setAccountId(companyAuthVo.getBankCard());
                    bankCardRecord(paymentRecordDTO);
                    // 去签署合同
                    SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, paymentRecordDTO.getPayName(), paymentRecordDTO.getBankCard(), paymentRecordDTO.getBankName(), null);
                    return JsonResult.success(signContractVO.getUrl());

                } else {
                    return JsonResult.error(ResponseCode.PORTAL_4013);
                }
            }

        } else {
            if (!CustomerTypeEnum.INDIVIDUAL.value().equals(customerType)) {
                throw new BusinessException("仅支持个人客户");
            }
            String personName = customer.getPersonName();
            String personCard = customer.getPersonCard();
            if (StringUtils.isBlank(personCard) || StringUtils.isBlank(personName)) {
                throw new BusinessException("信息异常");
            }
            PersonalCertificateDTO personalCertificateDTO = new PersonalCertificateDTO();
            personalCertificateDTO.setName(personName);
            personalCertificateDTO.setCardNo(recordSignDTO.getBankCard());
            personalCertificateDTO.setIdNo(personCard);
            VerificationJsonBody verificationJsonBody = verificationService.personalBankCard(personalCertificateDTO);
            if (!Boolean.parseBoolean(verificationJsonBody.getResult())) {
                throw new BusinessException(verificationJsonBody.getMessage());
            }
            // 去备案
            PaymentRecordDTO paymentRecordDTO = new PaymentRecordDTO();
            paymentRecordDTO.setMerchantCode(merchantCode);
            paymentRecordDTO.setAccountId(recordSignDTO.getBankCard());
            paymentRecordDTO.setAccountType(recordSignDTO.getBusinessType());
            paymentRecordDTO.setBankCard(recordSignDTO.getBankCard());
            paymentRecordDTO.setPaymentType(PayTypeEnum.BANK.value());
            paymentRecordDTO.setCompanyCode(settleCompanyCode);
            paymentRecordDTO.setBankCode(recordSignDTO.getBankCode());
            paymentRecordDTO.setUserCode(userCode);
            paymentRecordDTO.setPayName(personName);
            paymentRecordDTO.setBankName(recordSignDTO.getBankName());
            paymentRecordDTO.setAccountId(recordSignDTO.getBankCard());
            ResBody recordResult = bankCardRecord(paymentRecordDTO);
            if (recordResult.getResult()) {
                // 去签署合同
                SignContractVO signContractVO = getContractSignUrl(userAgent, contractType, paymentRecordDTO.getPayName(), paymentRecordDTO.getBankCard(), paymentRecordDTO.getBankName(), null);
                return JsonResult.success(signContractVO.getUrl());
            } else {
                throw new BusinessException(ResponseCode.PORTAL_4010);
            }
        }
    }
}

