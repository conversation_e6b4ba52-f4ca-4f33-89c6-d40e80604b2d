package com.ywwl.customer.center.modules.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 通用响应
 *
 * <AUTHOR>
 * @since 2023/10/10 13:42
 **/
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class GeneralDTO {

    /**
     * 响应结果
     */
    private Boolean success;
    /**
     * 响应码
     */
    private String code;
    /**
     * 响应信息
     */
    private String message;

    /**
     * 获取数据
     *
     * @return 数据
     */
    public Object getData() {
        return null;
    }

}
