package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;

import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/28 10:22
 */
@Data
public class QueryWithoutBillDTO {
    private String opCode= BillOpCodeEnum.WITHOUT_BILL_DETAIL.value();
    private Integer businessType;
    private String userCode;
    private String merchantCode;
    private Integer pageSize = 10;
    private Integer currentPage = 1;
    /**
     * 账单开始时间
     */
    private String startCalcTime;
    /**
     * 账单结束日期
     */
    private String endCalcTime;
    /**
     * 运单号
     */
    private List<String> waybillNumbers;

}
