// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/3/14 10:49
 * @ModifyDate 2023/3/14 10:49
 * @Version 1.0
 */
@Data
public class AccountInsertReqDTO {
    /**
     * 用户代码
     */
    @NotNull(message = "用戶代码不能为空")
    private String userCode;
    /**
     * 账号类型
     * 0 直发业务
     * 1 FBA业务
     * 2 海外仓
     * 3 中国仓
     */
    @NotNull(message = "账号类型不能为空")
    private Integer accountType;
    /**
     * 揽收仓编码
     * 非必填
     */
    private String warehouseCode;
    /**
     * 制单账号
     * 非必填
     */
    private String accountCode;
    /**
     * 业务账号
     * 非必填
     */
    private String merchantCode;
    /**
     * 数据来源类型
     */
    private String sourceType;
}
