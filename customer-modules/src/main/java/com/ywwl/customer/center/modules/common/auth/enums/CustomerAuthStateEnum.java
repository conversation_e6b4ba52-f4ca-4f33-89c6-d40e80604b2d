package com.ywwl.customer.center.modules.common.auth.enums;

public enum CustomerAuthStateEnum {
    NEW(0,"新商户"),
    SUCCESS(1,"申请完成"),
    COMPLETE_ATTACH(2,"补齐证件照"),
    COMPLETE_DATA(3,"强制补齐资料状态"),
    COMPLETE_UN_LIMIT(4,"补齐资料不受限"),

    ;
    private Integer value;
    private String desc;

    CustomerAuthStateEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
