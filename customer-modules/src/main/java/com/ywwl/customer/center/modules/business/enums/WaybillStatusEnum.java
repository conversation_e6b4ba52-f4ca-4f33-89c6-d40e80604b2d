package com.ywwl.customer.center.modules.business.enums;

import java.util.Arrays;
import java.util.Objects;
public enum WaybillStatusEnum {

	OLD("-1", "旧EJF数据"),
	ALREADY_SAVED("0", "已制单"),
	DELIVERED("1", "待确认账单"),
	RECEIVED("2", "已收货"),
	IN_TRANSIT("3", "运输中"),
	COMPLETED("4", "已妥投"),
	CANCEL("5", "已取消"),
	INTERCEPTED("6", "已截留"),
	DELIVERY_FAILURE("7", "投递失败"),
	WAREHOUSE_EXCEPTION("8", "仓内异常"),
	WAREHOUSE_RETURN("9", "仓内退件"),
	SIGN_FOR_RETURN("10", "退件签收"),
	TRANSPORT_EXCEPTION("11", "转运异常"),
	IN_DELIVERY("12", "派送中"),
	WAIT_EXTRACTED("13", "待提取"),
	UNISSUED_DOCUMENT("14", "未制单"),
	END_OF_TRACE("15", "追踪结束"),
	FOREIGN_RETURN("17", "国外退件"),
	OTHER("999", "其他");

	private String code;
	private String description;

	@Override
	public String toString() {
		return code;
	}

	WaybillStatusEnum(String code, String description) {
		this.code = code;
		this.description = description;
	}

	public static WaybillStatusEnum getWaybillStatusEnum(String statusCode) {
		return Arrays.stream(WaybillStatusEnum.values()).filter(status -> Objects.equals(statusCode, status.getCode())).findFirst().orElse(OTHER);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}