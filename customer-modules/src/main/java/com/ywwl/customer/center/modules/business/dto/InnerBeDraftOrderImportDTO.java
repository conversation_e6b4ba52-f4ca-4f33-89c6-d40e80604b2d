package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 导入响应
 *
 * <AUTHOR>
 * @since 2023/10/17 15:40
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class InnerBeDraftOrderImportDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 总数量
         */
        private Integer totalRecord;
        /**
         * 成功数量
         */
        private Integer successRecord;
        /**
         * 失败数量
         */
        private Integer failuresRecord;
        /**
         * records
         */
        private List<RecordsDTO> records;

        /**
         * RecordsDTO
         */
        @NoArgsConstructor
        @Data
        public static class RecordsDTO {
            /**
             * 序号
             */
            private Integer sequenceNo;
            /**
             * 订单号
             */
            private String orderNumber;
            /**
             * 是否成功
             */
            private Boolean success;
            /**
             * 错误信息
             */
            private String errorMessage;
        }
    }

}
