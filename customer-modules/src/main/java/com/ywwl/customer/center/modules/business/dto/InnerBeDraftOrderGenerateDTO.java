package com.ywwl.customer.center.modules.business.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 订单生成运单
 *
 * <AUTHOR>
 * @since 2023/10/11 16:07
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(builderMethodName = "vBuilder")
@Data
public class InnerBeDraftOrderGenerateDTO extends GeneralDTO {

    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运单号
         */
        private String waybillNumber;
        /**
         * 订单号
         */
        private String orderNumber;
        /**
         * 燕文单号
         */
        private String yanwenNumber;
    }

}
