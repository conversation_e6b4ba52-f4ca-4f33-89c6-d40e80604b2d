package com.ywwl.customer.center.modules.common.auth.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @author: dinghy
 * @date: 2023/4/4 11:22
 * <p>
 *     提交证件照信息
 * </p>
 */
@NoArgsConstructor
@Data
public class CardAttachDTO {

    /**
     * 用户代码
     */
    private String userCode;
    /**
     * 证件正面照/营业执照证件照
     */
    @NotBlank(message = "证件信息缺失")
    private String cardAttach;
    /**
     * 证件反面照
     */
    private String cardBackAttach;
    /**
     * 证件背面照
     */
    private String cardHoldAttach;
    /**
     * 个人证件有效截止日期/企业商户证件照有效期
     */
    private String cardValidityPeriod;
    /**
     * @author: dinghy 
     * @createTime: 2024/1/15 10:28
     * @description: 营业执照号,境外企业须填写
     */
    private String registerNumber;

    // 通行证号码
    private String passCard;
    // 通行证件正面
    private String passAttach;
    // 通行证件反面
    private String passBackAttach;
    // 通行证件有效期
    private LocalDateTime passValidityPeriod;
    private Integer customerArea;
    private Integer customerType;
    private String homeAttach;

    /*** 境外个人-本国身份证反面照 */
    private String homeBackAttach;

    /*** 境外个人-本国身份证有效期 */
    private LocalDateTime homeValidityPeriod;
}
