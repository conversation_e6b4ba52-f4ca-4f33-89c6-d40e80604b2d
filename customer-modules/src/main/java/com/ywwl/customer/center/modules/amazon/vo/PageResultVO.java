package com.ywwl.customer.center.modules.amazon.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

/**
 * @author: dinghy
 * @date: 2022/9/6 14:06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResultVO<T,K> {

    /**
     * 总条数
     */
    private Integer totalCount;
    /**
     * 总页数
     */
    private Integer totalPages;
    /**
     * 数据
     */
    private Collection<T> records;
    /**
     * 数据状态计数
     */
    private Collection<K> statusCounts;

}
