package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 商业快递列表信息DTO
 *
 * <AUTHOR>
 * @since 2023/10/10 15:22
 **/
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class BusinessSimpleDraftDTO extends BusinessSimpleOrderDTO {

    /**
     * 产品ID
     */
    private String channelId;
    /**
     * 国家ID
     */
    private String countryId;
    /**
     * 导入时间
     */
    private String importDate;
    /**
     * 错误信息
     */
    private Object errorMessage;
    /**
     * 草稿ID
     */
    private String id;

}
