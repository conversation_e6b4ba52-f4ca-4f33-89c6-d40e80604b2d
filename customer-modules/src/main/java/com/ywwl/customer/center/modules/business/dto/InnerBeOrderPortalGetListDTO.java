package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 运单列表-通过运单号查询
 *
 * <AUTHOR>
 * @since 2023/10/10 14:49
 **/
@NoArgsConstructor
@Data
public class InnerBeOrderPortalGetListDTO extends GeneralDTO{


    /**
     * data
     */
    private DataDTO data;

    /**
     * DataDTO
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 页码
         */
        private Integer pageNum;
        /**
         * 页大小
         */
        private Integer pageSize;
        /**
         * records
         */
        private List<BusinessSimpleOrderDTO> records;
        /**
         * 总条数
         */
        private Integer totalRecord;
        /**
         * 总页数
         */
        private Integer totalPages;
    }
}
