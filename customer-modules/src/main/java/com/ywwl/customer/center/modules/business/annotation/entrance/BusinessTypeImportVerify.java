package com.ywwl.customer.center.modules.business.annotation.entrance;

import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/

@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = BusinessTypeImportValidator.class)
public @interface BusinessTypeImportVerify {

    /**
     * 错误信息
     *
     * @return String
     */
    String message();

    /**
     * 快递类型
     *
     * @return CmccItemEnum
     */
    CmccItemEnum item();

    /**
     * 校验组
     *
     * @return Class
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     *
     * @return Class
     */
    Class<? extends Payload>[] payload() default {};

}
