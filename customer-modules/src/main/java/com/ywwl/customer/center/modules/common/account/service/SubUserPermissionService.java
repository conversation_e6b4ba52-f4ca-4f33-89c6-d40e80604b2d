// Copyright 2016-2101 Pica.
package com.ywwl.customer.center.modules.common.account.service;

import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.modules.common.account.dto.SubUserAccountReqDTO;

/**
 * <AUTHOR>
 * @Description 涉及到子用户权限信息服务
 * @Date 2023/3/15 10:11
 * @ModifyDate 2023/3/15 10:11
 * @Version 1.0
 */
public interface SubUserPermissionService{

    JsonResult<?> getAccountByChildUserId(SubUserAccountReqDTO subUserAccountReqDTO);
}
