package com.ywwl.customer.center.modules.common.auth.enums;

import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.framework.exceptions.BusinessException;

/**
 * 实名认证 - 认证状态枚举类
 */
public enum CustomerAuthStatusEnum {

    /**
     * 0, "待认证"
     */
    WAIT_AUTH(0, "待认证"),

    /**
     * 1, "已认证"
     */
    AUTH_SUCCESS(1, "已认证");

    /*** value */
    private Integer value;

    /**
     * 描述
     */
    private String desc;

    CustomerAuthStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer value() {
        return value;
    }

    public String desc() {
        return desc;
    }

    public static CustomerAuthStatusEnum getCustomerAuthStatusEnum(Integer value) {
        for (CustomerAuthStatusEnum customerStatusEnum : CustomerAuthStatusEnum.values()) {
            if (customerStatusEnum.value().equals(value)) {
                return customerStatusEnum;
            }
        }
        throw new BusinessException(ResponseCode.PORTAL_7001);
    }


}
