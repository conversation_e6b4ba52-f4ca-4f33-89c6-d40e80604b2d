package com.ywwl.customer.center.modules.common.auth.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: dinghy
 * @date: 2023/5/13 9:22
 */
@Data
public class ValidateCustomerUniqueDTO {
    /**
     * <AUTHOR>
     * @description 客户类型
     * @date 2023/5/13 9:24
     **/
    @NotNull(message = "客户类型不能为空")
    private Integer customerType;
    private String personCard;
    private String registerNumber;
    private String userCode;
    private String companyName;
}
