package com.ywwl.customer.center.modules.common.auth.vo;

import com.ywwl.customer.center.modules.common.auth.enums.CustomerAuthStateEnum;
import lombok.Data;

@Data
public class CustomerAuthStateVo {
    /**
     * @author: dinghy
     * @createTime: 2024/4/3 13:42
     * @description: {@link CustomerAuthStateEnum}
     */
    private Integer authState;
    private String unLimitTime;
    /**
     * @author: dinghy
     * @createTime: 2024/4/8 17:02
     * @description: 变更类型
     */
    private String changTypeName;
    // 补齐证件照提示错误信息
    private String completeAttachMessage;
}
