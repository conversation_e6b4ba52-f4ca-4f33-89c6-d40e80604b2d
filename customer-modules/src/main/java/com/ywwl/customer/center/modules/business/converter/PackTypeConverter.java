package com.ywwl.customer.center.modules.business.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.dto.BusinessOrderEnumDTO.DataDTO.ValueDTO;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;

import java.util.List;

/**
 * 包裹类型转换器
 *
 * <AUTHOR>
 * @since 2023/10/20 15:17
 **/
public class PackTypeConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(getValue(value));
    }

    /**
     * 获取枚举类型
     *
     * @param value 值
     * @return String
     */
    public static String getValue(String value) {
        final List<ValueDTO> typeEnum = EJFUtil.cmccService.getBusinessOrderTypeEnum(CmccItemEnum.PACK_TYPE, value, ValueDTO::getCode);
        if (CollectionUtil.isNotEmpty(typeEnum)) {
            return typeEnum.get(0).getValue();
        }
        return value;
    }


}
