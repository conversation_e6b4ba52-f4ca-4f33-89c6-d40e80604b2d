package com.ywwl.customer.center.modules.business.dto;

import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ywwl.customer.center.modules.business.annotation.*;
import com.ywwl.customer.center.modules.business.annotation.entrance.*;
import com.ywwl.customer.center.modules.business.converter.*;
import com.ywwl.customer.center.modules.business.enums.OrderFormStatusEnum;
import com.ywwl.customer.center.modules.business.enums.WaybillStatusEnum;
import com.ywwl.customer.center.modules.ejf.annotation.Import;
import com.ywwl.customer.center.modules.ejf.annotation.Input;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccItemEnum;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import com.ywwl.customer.center.modules.general.plm.dto.ProductResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 商业快递运单信息DTO
 *
 * <AUTHOR>
 * @since 2023/10/10 14:39
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BusinessOrderDTO {
    /**
     * 序号
     */
    private Integer sequenceNo;
    /**
     * 草稿ID
     */
    private String id;
    /**
     * 运单号
     */
    private String waybillNumber;
    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    @Length(max = 50, message = "订单号长度不能超过50")
    private String orderNumber;
    /**
     * 追踪单号
     */
    private String trackingNumber;
    /**
     * 燕文单号(唯一)
     */
    private String yanwenNumber;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新节点时间
     */
    private String trackingGMT;
    /**
     * 产品编号(产品id)
     */
    @NotBlank(message = "产品不能为空")
    @ProductImportVerify(message = "产品不存在", groups = Import.class)
    @ProductVerify(message = "产品不存在", groups = Input.class)
    private String channelId;
    /**
     * 揽收仓
     */
    @CompanyImportVerify(message = "揽收仓不存在", groups = Import.class)
    @CompanyVerify(message = "揽收仓不存在", groups = Input.class)
    private String companyCode;

    /**
     * 获取仓库名称
     *
     * @return 仓库名称
     */
    @JSONField(serialize = false)
    public String getCompanyName() {
        final WareHouseVo warehouse = EJFUtil.cmccService.getWarehouseById(StringUtils.strip(getCompanyCode()));
        if (Objects.nonNull(warehouse)) {
            return warehouse.getName();
        }
        return getCompanyCode();
    }

    /**
     * 发货方式 1:客户自送 2:燕文揽收 3:客户自寄； 默认燕文揽收
     */
    @NotBlank(message = "发货方式不能为空")
    @ShippingMethodVerify(message = "发货方式不存在", groups = Input.class)
    @ShippingMethodImportVerify(message = "发货方式不存在", groups = Import.class)
    private String shippingMethod;
    /**
     * 国内头程承运商名称
     */
    private String domesticLogisticsCompany;
    /**
     * 国内头程运单号
     */
    private String domesticTrackingNo;
    /**
     * 包裹类型(WPX(包裹类)/DOC(文件类)/Park(袋子)) 默认WPX(包裹类)
     */
    @NotBlank(message = "包裹类型不能为空")
    @BusinessTypeImportVerify(message = "包裹类型不存在", item = CmccItemEnum.PACK_TYPE, groups = Import.class)
    @BusinessTypeVerify(message = "包裹类型不存在", item = CmccItemEnum.PACK_TYPE, groups = Input.class)
    private String packageType;

    /**
     * 获取包裹类型名称
     *
     * @return 包裹类型名称
     */
    @JSONField(serialize = false)
    public String getPackageTypeName() {
        return PackTypeConverter.getValue(getPackageType());
    }

    /**
     * 商品类型（普货/带电/成人用品/膏状化妆品/品牌/木箱-有合页/带磁）默认普货
     */
    @NotBlank(message = "商品类型不能为空")
    @BusinessTypeImportVerify(message = "商品类型不存在", item = CmccItemEnum.COMMODITY_TYPE, groups = Import.class)
    @BusinessTypeVerify(message = "商品类型不存在", item = CmccItemEnum.COMMODITY_TYPE, groups = Input.class)
    private String goodsType;

    /**
     * 获取商品类型名称
     *
     * @return 商品类型名称
     */
    @JSONField(serialize = false)
    public String getGoodsTypeName() {
        return GoodsTypeConverter.getValue(getGoodsType());
    }

    /**
     * 电池类型 PI965整箱锂离子电池 PI968整箱锂金属电池 PI966配套锂离子电池 PI969配套锂金属电池 PI967置锂离子电池 PI970内置锂金属电池
     */
    @BusinessTypeImportVerify(message = "电池类型不存在", item = CmccItemEnum.BATTERY_TYPE, groups = Import.class)
    @BusinessTypeVerify(message = "电池类型不存在", item = CmccItemEnum.BATTERY_TYPE, groups = Input.class)
    private String batteryType;

    /**
     * 获取电池类型名称
     *
     * @return 电池类型名称
     */
    @JSONField(serialize = false)
    public String getBatteryTypeName() {
        return BatteryTypeConverter.getValue(getBatteryType());
    }

    /**
     * 关税类型 DDU(收件人支付关税)/DDP(寄件人支付关税) 默认DDU
     */
    @NotBlank(message = "关税类型不能为空")
    @DutyTypeImportVerify(message = "关税类型不存在", groups = Import.class)
    @DutyTypeVerify(message = "关税类型不存在", groups = Input.class)
    private String dutyType;

    /**
     * 获取关税类型名称
     *
     * @return 关税类型名称
     */
    @JSONField(serialize = false)
    public String getDutyTypeName() {
        return DutyTypeConverter.getValue(getDutyType());
    }

    /**
     * 拣货单信息
     */
    private String remark;
    /**
     * 备注
     */
    private String memo;
    /**
     * 订单来源
     */
    private String orderSource;
    /**
     * 制单账号
     */
    @NotBlank(message = "制单账号不能为空")
    private String userId;
    /**
     * 状态
     */
    private String status;

    /**
     * 获取状态名称
     *
     * @return 状态名称
     */
    public String getStatusName() {
        if (StringUtils.isBlank(getId())) {
            return EnumUtil.getBy(WaybillStatusEnum::getCode, getStatus(), WaybillStatusEnum.OTHER).getDescription();
        }
        return EnumUtil.getBy(OrderFormStatusEnum::getCode, getStatus(), OrderFormStatusEnum.OTHER).getDescription();
    }

    /**
     * 收件人信息
     */
    @Valid
    @NotNull(message = "收件人信息不能为空")
    private ReceiverInfoDTO receiverInfo;
    /**
     * 发件人信息
     */
    private SenderInfoDTO senderInfo;
    /**
     * 申报信息
     */
    @Valid
    @NotNull(message = "申报信息不能为空")
    private CustomInfosDTO customsInfo;
    /**
     * 包裹信息
     */
    @Valid
    @NotNull(message = "包裹信息不能为空")
    @Size(min = 1, message = "包裹信息不能为空")
    private List<ParcelInfoDTO> parcelInfo;
    /**
     * 商品信息
     */
    @Valid
    @Size(min = 1, message = "商品信息不能为空")
    @NotNull(message = "商品信息不能为空")
    private List<ProductInfoDTO> productInfo;
    /**
     * 订单附件信息
     */
    @Valid
    private List<AttachmentsInfoDTO> attachmentsInfo;

    /**
     * 退件国内快递公司
     */
    private String returnExpressCompanyName;
    /**
     * 退件国内快递单号
     */
    private String returnTrackingNumber;

    /**
     * 收件人信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class ReceiverInfoDTO {
        /**
         * 收件人姓名
         */
        @NotBlank(message = "收件人姓名不能为空")
        private String name;
        /**
         * 收件人电话
         */
        private String phone;
        /**
         * 收件人手机
         */
        @NotBlank(message = "收件人手机不能为空")
        private String mobile;
        /**
         * 收件人邮箱
         */
        private String email;
        /**
         * 收件人公司
         */
        private String company;
        /**
         * 目的国
         */
        @NotBlank(message = "目的国不能为空")
        @CountryImportVerify(message = "目的国不存在", groups = Import.class)
        @CountryVerify(message = "目的国不存在", groups = Input.class)
        // 用于接口调用
        @JSONField(name = "country", alternateNames = "countryId")
        private String countryId;
        /**
         * 省
         */
        private String state;
        /**
         * 郊区
         */
        // 用于接口调用
        @JSONField(name = "suburbs", alternateNames = "outskirts")
        private String outskirts;
        /**
         * 收件人城市
         */
        private String city;
        /**
         * 收件人邮编
         */
        private String zipCode;
        /**
         * 收件人地址1
         */
        @NotBlank(message = "收件人地址1不能为空")
        private String address1;
        /**
         * 收件人地址2
         */
        private String address2;
        /**
         * 收件人地址3
         */
        private String address3;
        /**
         * 收件人门牌号
         */
        private String houseNumber;
        /**
         * 税号类型
         */
        @BusinessTypeImportVerify(message = "税号类型不存在", item = CmccItemEnum.TAX_TYPE, groups = Import.class)
        @BusinessTypeVerify(message = "税号类型不存在", item = CmccItemEnum.TAX_TYPE, groups = Input.class)
        private String taxType;

        /**
         * 获取税号类型名称
         *
         * @return 税号类型名称
         */
        @JSONField(serialize = false)
        public String getTaxTypeName() {
            return TaxTypeConverter.getValue(getTaxType());
        }

        /**
         * 收件人税号
         */
        private String taxNumber;

        /**
         * 获取国家名称
         *
         * @return 国家名称
         */
        @JSONField(serialize = false)
        public String getCountryName() {
            final CountryResultDTO.RegionListDTO country = EJFUtil.plmService.getCountryById(getCountryId());
            if (Objects.nonNull(country)) {
                return country.getChinesename();
            }
            return null;
        }
    }

    /**
     * 发件人信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class SenderInfoDTO {
        /**
         * 发件人姓名
         */
        private String name;
        /**
         * 发件人电话
         */
        private String phone;
        /**
         * 发件人邮箱
         */
        private String email;
        /**
         * 发件人公司
         */
        private String company;
        /**
         * 国家
         */
        // 用于接口调用
        @JSONField(name = "country", alternateNames = "countryId")
        private String countryId;
        /**
         * 省
         */
        private String state;
        /**
         * 城市
         */
        private String city;
        /**
         * 区
         */
        private String district;
        /**
         * 发件人邮编
         */
        private String zipCode;
        /**
         * 发件人税号
         */
        private String taxNumber;
        /**
         * 发件人地址
         */
        private String address;

        /**
         * 获取国家名称
         *
         * @return 国家名称
         */
        @JSONField(serialize = false)
        public String getCountryName() {
            final CountryResultDTO.RegionListDTO country = EJFUtil.plmService.getCountryById(getCountryId());
            if (Objects.nonNull(country)) {
                return country.getChinesename();
            }
            return null;
        }

    }

    /**
     * 申报信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class CustomInfosDTO {
        /**
         * 是否正式报关(1:报关 0:不报关 默认0不报关)
         */
        @NotBlank(message = "是否正式报关不能为空")
        private String isCustomsService;
        /**
         * 是否面签服务(1：是；0：否)
         */
        @NotBlank(message = "是否面签服务不能为空")
        private String isSignature;
        /**
         * 是否保险服务(1：是；0：否)
         */
        @NotBlank(message = "是否保险服务不能为空")
        private String isInsurance;
        /**
         * 其他费用
         */
        //@Pattern(regexp = "^\\d{1,5}(\\.\\d{1,2})?$", message = "【其他费用】最大值99999且最大两位小数")
        private String otherFee;
        /**
         * 申报币种
         */
        @NotBlank(message = "申报币种不能为空")
        @CurrentVerify(message = "申报币种不存在", groups = Input.class)
        @CurrentImportVerify(message = "申报币种不存在", groups = Import.class)
        private String currency;
        /**
         * 保险费用
         */
        //@Pattern(regexp = "^\\d{1,5}(\\.\\d{1,2})?$", message = "【保险费用】最大值99999且最大两位小数")
        private String insurance;
        /**
         * 申报运费
         */
        //@Pattern(regexp = "^\\d{1,5}(\\.\\d{1,2})?$", message = "【申报运费】最大值99999且最大两位小数")
        private String packFee;
        /**
         * 申报总金额
         */
        // @Pattern(regexp = "^\\d{1,6}(\\.\\d{1,2})?$", message = "【申报总金额】最大值999999且最大两位小数")
        // @NotBlank(message = "申报总金额不能为空")
        private String price;
        /**
         * IOSS税号
         */
        private String ioss;
        /**
         * eori
         */
        private String eori;
        /**
         * 单票件数/箱数（一票多件的数量（外箱数量））
         */
        private Integer totalQuantity;
        /**
         * 商品申报总货值
         */
        private BigDecimal totalProductPrice;
    }

    /**
     * 包裹信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class ParcelInfoDTO {
        /**
         * 包号
         */
        private String packNumber;
        /**
         * 高
         */
        @NotBlank(message = "高不能为空")
        @Pattern(regexp = "^\\d{1,3}$", message = "【高】最大值999且无小数")
        private String height;
        /**
         * 宽
         */
        @NotBlank(message = "宽不能为空")
        @Pattern(regexp = "^\\d{1,3}$", message = "【宽】最大值999且无小数")
        private String width;
        /**
         * 长
         */
        @NotBlank(message = "长不能为空")
        @Pattern(regexp = "^\\d{1,3}$", message = "【长】最大值999且无小数")
        private String length;
        /**
         * 实重
         */
        @NotBlank(message = "实重不能为空")
        @Pattern(regexp = "^\\d{1,3}(\\.\\d{1,2})?$", message = "【实重】最大值999且最大两位小数")
        private String weight;
    }

    /**
     * 商品信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class ProductInfoDTO {
        /**
         * 中文品名
         */
        @NotBlank(message = "中文品名不能为空")
        private String goodsNameCh;
        /**
         * 英文品名
         */
        @NotBlank(message = "英文品名不能为空")
        private String goodsNameEn;
        /**
         * 申报单价
         */
        @NotBlank(message = "申报单价不能为空")
        @Pattern(regexp = "^\\d{1,5}(\\.\\d{1,2})?$", message = "【申报单价】最大值99999且最大两位小数")
        private String price;
        /**
         * 商品数量
         */
        @NotBlank(message = "商品数量不能为空")
        @Pattern(regexp = "^\\d{1,3}$", message = "【商品数量】最大值999且无小数")
        private String quantity;
        /**
         * 海关编码
         */
        private String hscode;
        /**
         * 材质
         */
        private String material;
        /**
         * 品牌
         */
        private String brand;
        /**
         * 型号
         */
        private String model;
        /**
         * 用途
         */
        // @NotBlank(message = "用途不能为空")
        private String use;
        /**
         * 商品销售链接
         */
        private String url;
    }

    /**
     * 订单附件信息
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @Data
    public static class AttachmentsInfoDTO {
        /**
         * 文件url
         */
        private String fileUrl;
        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 文件类型 1.报关资料 2:商业发票 3:交易证明 4:其他资料
         */
        private String attachmentType;
    }

    /**
     * 获取产品名称
     *
     * @return 产品名称
     */
    @JSONField(serialize = false)
    public String getChannelName() {
        final ProductResultDTO.DataDTO product = EJFUtil.plmService.getProductByCode(getChannelId());
        if (Objects.nonNull(product)) {
            return product.getProductCnName();
        }
        return null;
    }

    /**
     * 导出数据备份
     */
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private List<Map<Integer, String>> source;

}
