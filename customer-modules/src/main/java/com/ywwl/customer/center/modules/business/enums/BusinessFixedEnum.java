package com.ywwl.customer.center.modules.business.enums;

import cn.hutool.core.map.MapUtil;

import java.util.Map;

/**
 * 商业快递固定枚举类
 *
 * <AUTHOR>
 * @since 2023/10/30 14:09
 **/
public class BusinessFixedEnum {

    /**
     * 税号类型
     */
    public static final Map<String, String> DUTY_TYPE =
            MapUtil.ofEntries(
                    MapUtil.entry("DDU(收件人支付关税)", "DDU"),
                    MapUtil.entry("收件人支付关税", "DDU"),
                    MapUtil.entry("DDU", "DDU"),
                    MapUtil.entry("DDP(寄件人支付关税)", "DDP"),
                    MapUtil.entry("寄件人支付关税", "DDP"),
                    MapUtil.entry("DDP", "DDP")
            );

    /**
     * 发货方式
     */
    public static final Map<String, String> SHIPPING_METHOD =
            MapUtil.ofEntries(
                    MapUtil.entry("1", "1"),
                    MapUtil.entry("2", "2"),
                    MapUtil.entry("3", "3"),
                    MapUtil.entry("客户自送", "1"),
                    MapUtil.entry("燕文揽收", "2"),
                    MapUtil.entry("客户自寄", "3")
            );

}
