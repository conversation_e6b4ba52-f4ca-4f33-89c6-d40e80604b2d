package com.ywwl.customer.center.modules.business.enums;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ywwl.customer.center.modules.business.enums.WaybillStatusEnum.*;

/**
 * 查询类型ENUM
 *
 * <AUTHOR>
 * @since 2022/08/15 17:05
 **/
public enum SearchStatus {

    ALL_ORDER("-2", "所有运单", Collections.emptyList(), Collections.emptyList()),
    ALL_DRAFT("-1", "所有订单", Arrays.asList(
            OrderFormStatusEnum.UNDOCUMENTED,
            OrderFormStatusEnum.ORDER_FAILED),
            Collections.emptyList()),
    BILLED("0", "已制单", Collections.emptyList(), Collections.singletonList(ALREADY_SAVED)),
    SHIPMENT_CONFIRMED("1", "待确认账单", Collections.emptyList(), Collections.singletonList(DELIVERED)),
    RECEIVED("2", "已收货", Collections.emptyList(), Collections.singletonList(WaybillStatusEnum.RECEIVED)),
    SHIPPED("3", "运输途中", Collections.emptyList(), Arrays.asList(
            IN_TRANSIT,
            IN_DELIVERY
    )),
    HAS_BEEN_PLACED("4", "已妥投", Collections.emptyList(), Collections.singletonList(COMPLETED)),
    CANCELLED("5", "已取消", Collections.emptyList(), Collections.singletonList(CANCEL)),
    RETAINED("6", "已截留", Collections.emptyList(), Collections.singletonList(INTERCEPTED)),
    END_OF_TRACE("7", "追踪结束", Collections.emptyList(), Collections.singletonList(WaybillStatusEnum.END_OF_TRACE)),
    RETURN_EXCEPTION("8", "退件/异常", Collections.emptyList(), Arrays.asList(
            DELIVERY_FAILURE,
            WAREHOUSE_EXCEPTION,
            WAREHOUSE_RETURN,
            SIGN_FOR_RETURN,
            TRANSPORT_EXCEPTION,
            WAIT_EXTRACTED,
            FOREIGN_RETURN)),
    UNDOCUMENTED("9", "未制单", Collections.singletonList(OrderFormStatusEnum.UNDOCUMENTED), Collections.emptyList()),
    ORDER_FAILED("10", "制单失败", Collections.singletonList(OrderFormStatusEnum.ORDER_FAILED), Collections.emptyList()),
    BILLED_DRAFT("11", "制单成功", Collections.singletonList(OrderFormStatusEnum.BILLED), Collections.emptyList()),
    OTHER("999", "其他/未知", Collections.emptyList(), Collections.emptyList()),
    ;

    /**
     * 状态
     */
    final String status;
    /**
     * 描述
     */
    final String msg;
    /**
     * 原生订单状态
     */
    final List<OrderFormStatusEnum> orderEnum;
    /**
     * 原生运单状态
     */
    final List<WaybillStatusEnum> waybillEnum;

    SearchStatus(String status, String msg, List<OrderFormStatusEnum> orderEnum, List<WaybillStatusEnum> waybillEnum) {
        this.status = status;
        this.msg = msg;
        this.orderEnum = orderEnum;
        this.waybillEnum = waybillEnum;
    }

    public String getStatus() {
        return status;
    }

    public List<String> getOrderEnumKey() {
        return orderEnum.stream().map(OrderFormStatusEnum::getCode).distinct().collect(Collectors.toList());
    }

    public List<String> getWaybillEnumKey() {
        return waybillEnum.stream().map(WaybillStatusEnum::getCode).distinct().collect(Collectors.toList());
    }

    public String getMsg() {
        return msg;
    }

    public static SearchStatus getSearchStatus(String statusCode) {
        return Arrays.stream(SearchStatus.values()).filter(status -> Objects.equals(statusCode, status.getStatus())).findFirst().orElse(ALL_ORDER);
    }

    @Override
    public String toString() {
        return "SearchStatus{" +
                "status='" + status + '\'' +
                ", msg='" + msg + '\'' +
                '}';
    }

    /**
     * 反序列化器
     */
    public static class SearchStatusSerializer extends JsonDeserializer<SearchStatus> {
        @Override
        public SearchStatus deserialize(JsonParser jsonParser,
                                        DeserializationContext deserializationContext) throws IOException, JsonProcessingException {
            return SearchStatus.getSearchStatus(jsonParser.getText());
        }
    }

}
