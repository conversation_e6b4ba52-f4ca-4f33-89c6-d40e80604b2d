package com.ywwl.customer.center.modules.common.bill.dto;

import com.ywwl.customer.center.modules.common.bill.enums.BillOpCodeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/4/18 10:07
 */
@NoArgsConstructor
@Data
public class QueryBillBalanceDTO {
    private String opCode= BillOpCodeEnum.QUERY_BALANCE.value();
    private List<MerchantCodeListDTO> merchantCodeList;

    @NoArgsConstructor
    @Data
    public static class MerchantCodeListDTO {
        private String merchantCode;
    }

    public void fillBillBalanceDTO(String code){
        MerchantCodeListDTO merchantCodeListDTO = new MerchantCodeListDTO();
        merchantCodeListDTO.setMerchantCode(code);
        List<MerchantCodeListDTO> merchantCodeList=new ArrayList<>(2);
        merchantCodeList.add(merchantCodeListDTO);
        this.setMerchantCodeList(merchantCodeList);
    }
}
