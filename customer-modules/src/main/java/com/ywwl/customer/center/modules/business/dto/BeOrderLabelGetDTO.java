package com.ywwl.customer.center.modules.business.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 打印标签DTO
 *
 * <AUTHOR>
 * @since 2023/10/10 14:40
 **/
@NoArgsConstructor
@Data
public class BeOrderLabelGetDTO extends GeneralDTO{

    /**
     * data
     */
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        /**
         * 运单号
         */
        private String waybillNumber;
        /**
         * 是否成功
         */
        private Boolean isSuccess;
        /**
         * 错误信息
         */
        private String errorMsg;
        /**
         * base64
         */
        private String base64String;
        /**
         * 标签尺寸类型
         */
        private String labelType;
    }


}
