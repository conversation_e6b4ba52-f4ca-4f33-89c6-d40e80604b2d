package com.ywwl.customer.center.modules.business.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentFontStyle;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.ywwl.customer.center.modules.business.converter.*;
import com.ywwl.customer.center.modules.ejf.util.CountryConverter;
import com.ywwl.customer.center.modules.ejf.util.ProductConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// 头部背景色为蓝色
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 42)
// 头部字体为11号加粗
@HeadFontStyle(fontHeightInPoints = 11, bold = BooleanEnum.TRUE, fontName = "Calibri")
// 内容字体为11号
@ContentFontStyle(fontHeightInPoints = 11, fontName = "Calibri")
public class BusinessExcelPropertyDTO {
    /**
     * {"基本信息", "状态"}
     */
    @ExcelProperty(value = {"基本信息", "状态"}, index = 0)
    private String status;
    /**
     * {"基本信息", "更新时间"}
     */
    @ExcelProperty(value = {"基本信息", "更新时间"}, index = 1)
    private String trackingGMT;
    /**
     * {"基本信息", "创建时间"}
     */
    @ExcelProperty(value = {"基本信息", "创建时间"}, index = 2)
    private String createTime;
    /**
     * {"基本信息", "运单号"}
     */
    @ExcelProperty(value = {"基本信息", "运单号"}, index = 3)
    private String waybillNumber;
    /**
     * {"基本信息", "揽收仓"}
     */
    @ExcelProperty(value = {"基本信息", "揽收仓"}, index = 4, converter = CompanyConverter.class)
    private String companyCode;
    /**
     * {"基本信息", "目的国"}
     */
    @ExcelProperty(value = {"基本信息", "目的国"}, index = 5, converter = CountryConverter.class)
    private String countryId;
    /**
     * {"基本信息", "产品名称"}
     */
    @ExcelProperty(value = {"基本信息", "产品名称"}, index = 6, converter = ProductConverter.class)
    private String channelId;
    /**
     * {"基本信息", "客户单号"}
     */
    @ExcelProperty(value = {"基本信息", "客户单号"}, index = 7)
    private String orderNumber;
    /**
     * {"基本信息", "包裹类型"}
     */
    @ExcelProperty(value = {"基本信息", "包裹类型"}, index = 8, converter = PackTypeConverter.class)
    private String packageType;
    /**
     * {"基本信息", "商品类型"}
     */
    @ExcelProperty(value = {"基本信息", "商品类型"}, index = 9, converter = GoodsTypeConverter.class)
    private String goodsType;
    /**
     * {"基本信息", "电池类型"}
     */
    @ExcelProperty(value = {"基本信息", "电池类型"}, index = 10, converter = BatteryTypeConverter.class)
    private String batteryType;
    /**
     * {"基本信息", "关税类型"}
     */
    @ExcelProperty(value = {"基本信息", "关税类型"}, index = 11, converter = DutyTypeConverter.class)
    private String dutyType;
    /**
     * {"基本信息", "发货方式"}
     */
    @ExcelProperty(value = {"基本信息", "发货方式"}, index = 12, converter = ShippingMethodConverter.class)
    private String shippingMethod;
    /**
     * {"基本信息", "国内头程承运商名称"}
     */
    @ExcelProperty(value = {"基本信息", "国内头程承运商名称"}, index = 13)
    private String domesticLogisticsCompany;
    /**
     * {"基本信息", "国内头程运单号"}
     */
    @ExcelProperty(value = {"基本信息", "国内头程运单号"}, index = 14)
    private String domesticTrackingNo;
    /**
     * IOSS税号
     */
    @ExcelProperty(value = {"申报信息", "IOSS税号"}, index = 15)
    private String ioss;
    /**
     * eori
     */
    @ExcelProperty(value = {"申报信息", "eori"}, index = 16)
    private String eori;
    /**
     * 币种
     */
    @ExcelProperty(value = {"申报信息", "币种"}, index = 17, converter = CurrencyConverter.class)
    private String currency;
    /**
     * {"申报信息", "申报运费"}
     */
    @ExcelProperty(value = {"申报信息", "申报运费"}, index = 18)
    private String packFee;
    /**
     * {"申报信息", "保险费用"}
     */
    @ExcelProperty(value = {"申报信息", "保险费用"}, index = 19)
    private String insurance;
    /**
     * {"申报信息", "其他费用"}
     */
    @ExcelProperty(value = {"申报信息", "其他费用"}, index = 20)
    private String otherFee;
    /**
     * {"申报信息", "申报总金额"}
     */
    @ExcelProperty(value = {"申报信息", "申报总金额"}, index = 21)
    private String price;
    /**
     * {"包裹信息", "长"}
     */
    @ExcelProperty(value = {"包裹信息", "长(cm)"}, index = 22)
    private String length;
    /**
     * {"包裹信息", "宽"}
     */
    @ExcelProperty(value = {"包裹信息", "宽(cm)"}, index = 23)
    private String width;
    /**
     * {"包裹信息", "高"}
     */
    @ExcelProperty(value = {"包裹信息", "高(cm)"}, index = 24)
    private String height;
    /**
     * {"包裹信息", "实重(kg)"}
     */
    @ExcelProperty(value = {"包裹信息", "实重(kg)"}, index = 25)
    private String weight;
    /**
     * {"寄运商品信息", "英文品名"}
     */
    @ExcelProperty(value = {"寄运商品信息", "英文品名"}, index = 26)
    private String goodsNameEn;
    /**
     * {"寄运商品信息", "中文品名"}
     */
    @ExcelProperty(value = {"寄运商品信息", "中文品名"}, index = 27)
    private String goodsNameCh;
    /**
     * {"寄运商品信息", "申报单价"}
     */
    @ExcelProperty(value = {"寄运商品信息", "申报单价"}, index = 28)
    private String productPrice;
    /**
     * {"寄运商品信息", "商品数量"}
     */
    @ExcelProperty(value = {"寄运商品信息", "商品数量"}, index = 29)
    private String quantity;
    /**
     * {"寄运商品信息", "海关编码"}
     */
    @ExcelProperty(value = {"寄运商品信息", "海关编码"}, index = 30)
    private String hscode;
    /**
     * {"寄运商品信息", "材质"}
     */
    @ExcelProperty(value = {"寄运商品信息", "材质"}, index = 31)
    private String material;
    /**
     * {"寄运商品信息", "品牌"}
     */
    @ExcelProperty(value = {"寄运商品信息", "品牌"}, index = 32)
    private String brand;
    /**
     * {"寄运商品信息", "型号"}
     */
    @ExcelProperty(value = {"寄运商品信息", "型号"}, index = 33)
    private String model;
    /**
     * {"寄运商品信息", "用途"}
     */
    @ExcelProperty(value = {"寄运商品信息", "用途"}, index = 34)
    private String use;
    /**
     * {"发件人信息", "省"}
     */
    @ExcelProperty(value = {"发件人信息", "省"}, index = 35)
    private String senderState;
    /**
     * {"发件人信息", "市"}
     */
    @ExcelProperty(value = {"发件人信息", "市"}, index = 36)
    private String senderCity;
    /**
     * {"发件人信息", "区"}
     */
    @ExcelProperty(value = {"发件人信息", "区"}, index = 37)
    private String senderDistrict;
    /**
     * {"发件人信息", "地址"}
     */
    @ExcelProperty(value = {"发件人信息", "地址"}, index = 38)
    private String senderAddress;
    /**
     * {"发件人信息", "邮编"}
     */
    @ExcelProperty(value = {"发件人信息", "邮编"}, index = 39)
    private String senderZipCode;
    /**
     * {"发件人信息", "公司"}
     */
    @ExcelProperty(value = {"发件人信息", "公司"}, index = 40)
    private String senderCompany;
    /**
     * {"发件人信息", "税号"}
     */
    @ExcelProperty(value = {"发件人信息", "税号"}, index = 41)
    private String senderTaxNumber;
    /**
     * {"发件人信息", "姓名"}
     */
    @ExcelProperty(value = {"发件人信息", "姓名"}, index = 42)
    private String senderName;
    /**
     * {"发件人信息", "电话"}
     */
    @ExcelProperty(value = {"发件人信息", "电话"}, index = 43)
    private String senderPhone;
    /**
     * {"发件人信息", "邮箱"}
     */
    @ExcelProperty(value = {"发件人信息", "邮箱"}, index = 44)
    private String senderEmail;
    /**
     * {"收件人信息", "省"}
     */
    @ExcelProperty(value = {"收件人信息", "省/州"}, index = 45)
    private String receiverState;
    /**
     * {"收件人信息", "市"}
     */
    @ExcelProperty(value = {"收件人信息", "城市"}, index = 46)
    private String receiverCity;
    /**
     * {"收件人信息", "区"}
     */
    @ExcelProperty(value = {"收件人信息", "郊区"}, index = 47)
    private String receiverOutskirts;
    /**
     * {"收件人信息", "公司"}
     */
    @ExcelProperty(value = {"收件人信息", "公司英文名"}, index = 48)
    private String receiverCompany;
    /**
     * {"收件人信息", "姓名"}
     */
    @ExcelProperty(value = {"收件人信息", "姓名"}, index = 49)
    private String receiverName;
    /**
     * {"收件人信息", "邮编"}
     */
    @ExcelProperty(value = {"收件人信息", "邮编"}, index = 50)
    private String receiverZipCode;
    /**
     * {"收件人信息", "手机"}
     */
    @ExcelProperty(value = {"收件人信息", "手机号码"}, index = 51)
    private String receiverMobile;
    /**
     * {"收件人信息", "电话"}
     */
    @ExcelProperty(value = {"收件人信息", "固话"}, index = 52)
    private String receiverPhone;
    /**
     * {"收件人信息", "邮箱"}
     */
    @ExcelProperty(value = {"收件人信息", "邮箱"}, index = 53)
    private String receiverEmail;
    /**
     * {"收件人信息", "税号类型"}
     */
    @ExcelProperty(value = {"收件人信息", "税号类型"}, index = 54, converter = TaxTypeConverter.class)
    private String receiverTaxType;
    /**
     * {"收件人信息", "税号"}
     */
    @ExcelProperty(value = {"收件人信息", "税号"}, index = 55)
    private String receiverTaxNumber;
    /**
     * {"收件人信息", "地址1"}
     */
    @ExcelProperty(value = {"收件人信息", "地址1"}, index = 56)
    private String receiverAddress1;
    /**
     * {"收件人信息", "地址2"}
     */
    @ExcelProperty(value = {"收件人信息", "地址2"}, index = 57)
    private String receiverAddress2;
    /**
     * {"收件人信息", "地址3"}
     */
    @ExcelProperty(value = {"收件人信息", "地址3"}, index = 58)
    private String receiverAddress3;
    /**
     * {"其他信息", "是否面签服务"}
     */
    @ExcelProperty(value = {"其他信息", "是否面签服务"}, index = 59, converter = BooleanConverter.class)
    private String isSignature;
    /**
     * {"其他信息", "是否保险服务"}
     */
    @ExcelProperty(value = {"其他信息", "是否保险服务"}, index = 60, converter = BooleanConverter.class)
    private String isInsurance;
    /**
     * {"其他信息", "是否正式报关"}
     */
    @ExcelProperty(value = {"其他信息", "是否正式报关"}, index = 61, converter = BooleanConverter.class)
    private String isCustomsService;
    /**
     * {"其他信息", "拣货单信息"}
     */
    @ExcelProperty(value = {"其他信息", "拣货单信息"}, index = 62)
    private String remark;
    /**
     * {"其他信息", "国内快递公司"}
     */
    @ExcelProperty(value = {"其他信息", "国内快递公司"}, index = 63)
    private String returnExpressCompanyName;
    /**
     * {"其他信息", "国内快递单号"}
     */
    @ExcelProperty(value = {"其他信息", "国内快递单号"}, index = 64)
    private String returnTrackingNumber;


}