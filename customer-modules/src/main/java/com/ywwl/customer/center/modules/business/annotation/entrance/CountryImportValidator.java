package com.ywwl.customer.center.modules.business.annotation.entrance;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ywwl.customer.center.modules.ejf.util.EJFUtil;
import com.ywwl.customer.center.modules.general.plm.dto.CountryResultDTO;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

/**
 * 商业快递枚举类型校验
 *
 * <AUTHOR>
 * @since 2023/10/18 09:47
 **/
public class CountryImportValidator implements ConstraintValidator<CountryImportVerify, String> {

    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        final List<CountryResultDTO.RegionListDTO> country = EJFUtil.plmService.getCountry(StringUtils.strip(value));
        // 导入校验
        if (CollectionUtil.isEmpty(country)) {
            return false;
        }
        if (country.size() > 1) {
            setError(StrUtil.format("国家有多个匹配项 {}", country), constraintValidatorContext);
            return false;
        }
        return true;
    }

    /**
     * 设置错误信息
     *
     * @param msg 错误信息
     */
    private void setError(String msg, ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(msg)
                .addConstraintViolation();
    }

}
