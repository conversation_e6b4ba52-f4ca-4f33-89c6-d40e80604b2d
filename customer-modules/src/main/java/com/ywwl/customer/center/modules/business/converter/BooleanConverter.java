package com.ywwl.customer.center.modules.business.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 包裹类型转换器
 *
 * <AUTHOR>
 * @since 2023/10/20 15:17
 **/
public class BooleanConverter implements Converter<String> {

    @Override
    public Class<String> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value,
                                               ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) {
        // 1:是 0:否
        if (Objects.equals("1", value)) {
            return new WriteCellData<>("是");
        } else if (Objects.equals("0", value)) {
            return new WriteCellData<>("否");
        }
        return new WriteCellData<>(value);
    }


}
