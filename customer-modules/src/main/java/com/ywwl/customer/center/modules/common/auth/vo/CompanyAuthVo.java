package com.ywwl.customer.center.modules.common.auth.vo;

import lombok.Data;

/**
 * @author: dinghy
 * @date: 2023/3/3 14:05
 */
@Data
public class CompanyAuthVo {
    /**
     * 企业名字
     */
    private String companyName;

    /**
     * 营业执照号
     */
    private String businessLicense;

    /**
     * 法人名字
     */
    private String legalName;

    /**
     * 法人身份证号
     */
    private String legalIdCard;

    /**
     * 对公账号开户行支行名称全称
     */
    private String bankBranchName;

    /**
     * 银行卡号
     */
    private String bankCard;


    /**
     * 0未打款,1是打款成功,2是打款失败,3是金额校验成功
     */
    private Integer verifyStatus;

    /**
     * 打款次数
     */
    private Integer counter;


    /**
     * 0未发起认证,1是待提交银行卡信息,2金额打款校验,3是认证成功,4是认证失败
     */
    private Integer authStatus;

    private String bankCode;

}
