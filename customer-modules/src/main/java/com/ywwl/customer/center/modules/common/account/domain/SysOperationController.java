package com.ywwl.customer.center.modules.common.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户操作控制
 *
 * @TableName sys_operation_controller
 */
@TableName(value = "sys_operation_controller")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class SysOperationController implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 操作类型
     */
    private String type;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}