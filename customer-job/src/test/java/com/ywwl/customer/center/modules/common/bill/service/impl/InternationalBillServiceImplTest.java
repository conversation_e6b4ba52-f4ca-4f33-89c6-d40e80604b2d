package com.ywwl.customer.center.modules.common.bill.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.config.BillConfig;
import com.ywwl.customer.center.modules.common.bill.dto.QueryBillWaybillDTO;
import com.ywwl.customer.center.modules.common.bill.enums.WaybillApiEnum;
import com.ywwl.customer.center.modules.common.bill.service.BillService;
import com.ywwl.customer.center.modules.common.bill.vo.WaybillVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class InternationalBillServiceImplTest {

    @InjectMocks
    private InternationalBillServiceImpl internationalBillService;

    @Mock
    private BillService billService;


    @Test
    public void testGetNumberList_Success() throws Exception {
        // Arrange
        QueryBillWaybillDTO queryBillWaybillDTO = new QueryBillWaybillDTO();
        queryBillWaybillDTO.setPage(1);
        queryBillWaybillDTO.setSize(10);
        queryBillWaybillDTO.setMerchantCode("20121687");
        queryBillWaybillDTO.setDateOfBillBegin("2024-12-30");
        queryBillWaybillDTO.setDateOfBillEnd("2025-01-05");

        String param = queryBillWaybillDTO.getPage() + "/" + queryBillWaybillDTO.getSize();
        String waybillUrl = "http://10.14.0.34:20010/";
        String url = waybillUrl + WaybillApiEnum.BILL_DETAIL.value() + param;

        JSONObject jsonObject = HttpUtil.doPost(url, queryBillWaybillDTO);

        WaybillVO waybillVO = jsonObject.to(WaybillVO.class);
        System.out.println(waybillVO);
    }

}