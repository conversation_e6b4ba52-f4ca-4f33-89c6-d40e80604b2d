package com.ywwl.customer.center.job.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.modules.general.cmcc.service.CmccService;
import com.ywwl.customer.center.modules.general.cmcc.vo.WareHouseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: dinghy
 * @date: 2023/7/12 14:53
 */
@Component
@Slf4j
public class CmccCacheTask {
    @Resource
    private CmccService cmccService;

    /**
     * <AUTHOR>
     * @description 缓存交货仓
     * @date 2023/7/12 14:55
     **/
    @XxlJob("warehouseCacheTask")
    public void warehouseCacheTask() {
        List<WareHouseVo> warehouses = cmccService.getWarehouses();
        if (warehouses.size() > 0) {
            CacheUtil.redis().setValue(CacheKeyEnum.WAREHOUSE, CacheKeyEnum.WAREHOUSE.key(), warehouses);
        }
    }

}
