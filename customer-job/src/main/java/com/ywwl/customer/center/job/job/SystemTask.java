package com.ywwl.customer.center.job.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.common.utils.DingTalkClient;
import com.ywwl.customer.center.job.service.CustomerCenterService;
import com.ywwl.customer.center.modules.common.provider.domain.SysUserOnline;
import com.ywwl.customer.center.modules.common.provider.service.SysUserOnlineService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @author: dinghy
 * @date: 2023/7/12 17:10
 */
@Component
public class SystemTask {
    @Resource
    private CustomerCenterService customerCenterService;
    @Resource
    private SysUserOnlineService sysUserOnlineService;

    /**
     * <AUTHOR>
     * @description 获取在线用户定时任务
     * @date 2023/7/12 17:14
     **/
    @XxlJob("userOnlineTask")
    public void userOnlineTask() {
        try {
            Integer onlineUserNum = customerCenterService.getOnlineUserNum();
            SysUserOnline userOnline = new SysUserOnline();
            userOnline.setOnlineNumber(onlineUserNum);
            sysUserOnlineService.save(userOnline);
        } catch (Exception e) {
            XxlJobHelper.log("获取在线用户定时任务失败:{}", e);
        }
        XxlJobHelper.log("获取在线用户定时任务执行成功--------------");
    }

    /**
     * <AUTHOR>
     * @description 省市区更新缓存
     * @date 2023/7/12 17:18
     **/
    @XxlJob("countryCacheUpdateTask")
    public void countryCacheUpdateTask(){
        customerCenterService.countryCacheUpdate();
        XxlJobHelper.log("省市区更新缓存执行成功--------------");
    }


    @XxlJob("sendSmsCountNotice")
    public void sendSmsCountNotice(){
        LocalDateTime now = LocalDateTime.now();
        String endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
        String startTime = now.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
        try {
            Integer sendSmsCount = customerCenterService.getSendSmsCount(startTime, endTime);
            DingTalkClient.sendDingNoticeToUser("93802","通知","昨天短信发送统计数量:"+sendSmsCount);
            DingTalkClient.sendDingNoticeToUser("1150045","通知","昨天短信发送统计数量:"+sendSmsCount);
        } catch (Exception e) {
            DingTalkClient.sendDingNoticeToUser("93802","通知","昨天短信发送统计数量异常:"+e.getMessage());
        }
    }
}
