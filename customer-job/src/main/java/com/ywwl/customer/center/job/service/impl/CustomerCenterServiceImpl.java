package com.ywwl.customer.center.job.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.domain.ResponseCode;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.framework.exceptions.BusinessException;
import com.ywwl.customer.center.job.service.CustomerCenterService;
import com.ywwl.customer.center.system.config.RequestConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: dinghy
 * @date: 2023/7/12 17:04
 */
@Service
public class CustomerCenterServiceImpl implements CustomerCenterService {
    @Value("${newPortal.url}")
    private String customerCenterUrl;
    @Resource
    private RequestConfig requestConfig;

    @Override
    public Integer getOnlineUserNum() {
        JSONObject jsonObject = HttpUtil.doPost(customerCenterUrl + "/api/system/sessionMonitor",null,30);
        JsonResult jsonResult = jsonObject.toJavaObject(JsonResult.class);
        if (!jsonResult.getSuccess()) {
            throw new BusinessException(jsonResult.getMessage());
        }
        Map<String, Object> data = (Map<String, Object>) jsonResult.getData();
        return (Integer) data.get("hasCurrentUser");
    }

    @Override
    public void countryCacheUpdate() {
        HttpUtil.doGet(customerCenterUrl + "/api/sys/updateCountryCache");
    }

    @Override
    public Integer getSendSmsCount(String startTime, String endTime) {
        Map<String,Object> param=new HashMap<>(2);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        JsonResult jsonResult = HttpUtil.doPost(requestConfig.getApiUrl() + "/sys/getSmsCount", param, JsonResult.class);
        if(jsonResult==null){
            throw ResponseCode.PORTAL_5008.getError();
        }
        if(!jsonResult.getSuccess()){
            throw new BusinessException(jsonResult.getMessage());
        }
        return (Integer) jsonResult.getData();
    }
}
