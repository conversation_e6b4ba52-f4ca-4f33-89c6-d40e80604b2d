package com.ywwl.customer.center.job.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.modules.common.provider.domain.SysDownloadTask;
import com.ywwl.customer.center.modules.common.provider.enums.DownloadTaskStatusEnum;
import com.ywwl.customer.center.modules.common.provider.service.SysDownloadTaskService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@RequestMapping("/api/downloadTask")
@Controller
public class DownloadTask {
    @Value("${download.path}")
    private String downloadPath;
    @Resource
    private SysDownloadTaskService sysDownloadTaskService;
    @XxlJob("downloadTask")
    public void downloadTask() {
        try {
            LambdaQueryWrapper<SysDownloadTask> lambdaQueryWrapper=new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(SysDownloadTask::getDeleteFlag,false)
                    .eq(SysDownloadTask::getStatus, DownloadTaskStatusEnum.LOADING.value())
                    ;
            List<SysDownloadTask> list = sysDownloadTaskService.list(lambdaQueryWrapper);
            for (SysDownloadTask sysDownloadTask : list) {
                sysDownloadTaskService.downloadFile(sysDownloadTask,downloadPath);
            }
        } catch (Exception e) {
            XxlJobHelper.log("下载中心定时任务失败:{}", e);
        }
        XxlJobHelper.log("下载中心定时任务执行成功--------------");
    }

    @XxlJob("deleteFilesTask")
    public void deleteFilesTask() {
        try {
            // 7天之前的数据删除
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime localDateTime = now.minusDays(7);
            List<Integer> statusList = Arrays.asList(DownloadTaskStatusEnum.DOWNLOAD_SUCCESS.value(), DownloadTaskStatusEnum.SUCCESS.value());
            LambdaQueryWrapper<SysDownloadTask> lambdaQueryWrapper=new LambdaQueryWrapper();
            lambdaQueryWrapper.eq(SysDownloadTask::getDeleteFlag,false)
                    .in(SysDownloadTask::getStatus, statusList)
                    .le(SysDownloadTask::getFinishTime,localDateTime)
            ;

            List<SysDownloadTask> list = sysDownloadTaskService.list(lambdaQueryWrapper);
            for (SysDownloadTask sysDownloadTask : list) {
                sysDownloadTaskService.deleteFile(sysDownloadTask);
            }
        } catch (Exception e) {
            XxlJobHelper.log("下载中心定时任务失败:{}", e);
        }
        XxlJobHelper.log("下载中心定时任务执行成功--------------");
    }

    /**
     * @author: dinghy
     * @createTime: 2024/3/5 15:22
     * @description: 下载文件
     */
    @RequestMapping("download")
    @ResponseBody
    public void download(@RequestParam String taskId, HttpServletResponse response){
        sysDownloadTaskService.fileDownload(taskId,response);
    }
}
