package com.ywwl.customer.center.job.job;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.common.domain.JsonResult;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.modules.common.provider.dto.AutowiredDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * //TODO 保存高德的key
 *
 * <AUTHOR>
 * @date 2023/2/3
 */
@RequestMapping("/api/map")
@Component
@Slf4j
public class AutoNaviTask {


    /***
     * //TODO  晚上12点执行重置标记
     * <AUTHOR>
     * @date 2021/12/7 17:21
     */
    @XxlJob("autoNaviTask")
    public void bankRead() {
        boolean succeed = false;
        //查询所有的高德信息
        List<AutowiredDTO> autoNavi = CacheUtil.redis().getValue(CacheKeyEnum.GD_NUMBER, "AutoNavi");
        //如果没有已用完的则不进行重置
        for (AutowiredDTO autowiredDTO : autoNavi) {
            if (!autowiredDTO.getStatus()) {
                succeed = true;
                autowiredDTO.setStatus(true);
            }
        }
        //每天重置信息
        if (succeed) {
            CacheUtil.redis().setValue(CacheKeyEnum.GD_NUMBER, "AutoNavi", autoNavi);
            XxlJobHelper.log("高德key更新成功==========!");
        }
    }

   /**
    * @author: dinghy
    * @createTime: 2024/4/10 17:14
    * @description: 设置高德地图mapKey
    */
    @PostMapping("setMapKey")
    @ResponseBody
    public JsonResult setMapKey(@RequestBody JSONObject jsonObject){
        JSONArray list = jsonObject.getJSONArray("list");
        if(list==null||list.isEmpty()){
            return JsonResult.error("参数异常");
        }
        List<AutowiredDTO> javaList = list.toJavaList(AutowiredDTO.class);
        CacheUtil.redis().setValue(CacheKeyEnum.GD_NUMBER,"AutoNavi",javaList);
        return JsonResult.success("操作成功");
    }
}
