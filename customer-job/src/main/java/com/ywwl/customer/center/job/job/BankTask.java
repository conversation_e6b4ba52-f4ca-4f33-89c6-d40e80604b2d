package com.ywwl.customer.center.job.job;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.common.enums.CacheKeyEnum;
import com.ywwl.customer.center.common.utils.CacheUtil;
import com.ywwl.customer.center.common.utils.HttpUtil;
import com.ywwl.customer.center.modules.common.provider.vo.BankVo;
import com.ywwl.customer.center.modules.general.cmcc.enums.CmccApiEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * //银行信息更新
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BankTask {
    @Value("${cmcc.host}")
    private String url;

    /***
     * //一个小时执行一次
     * <AUTHOR>
     * @date 2023/6/20 10:27

     */
    @XxlJob("bankTask")
    public void bank() {
        List<BankVo> bankList = new ArrayList<>();
        List<BankVo> thirdList = new ArrayList<>();
        Map<String, String> map = new HashMap<>(4);
        String fullUrl = url + CmccApiEnum.OPENINTERFACE.getValue();
        map.put("type", "paymentmode");

        JSONObject jsonObject = sendHttpPostRequest(fullUrl, map);
        if (jsonObject != null && Boolean.parseBoolean(jsonObject.getString("result"))) {
            map.put("param1", "1");
            JSONObject bankJson = sendHttpPostRequest(fullUrl, map);
            if (bankJson != null && Boolean.parseBoolean(bankJson.getString("result"))) {
                JSONArray paymentModes = bankJson.getJSONObject("data").getJSONArray("paymentmode");
                for (int i = 0; i < paymentModes.size(); i++) {
                    JSONObject json = paymentModes.getJSONObject(i);
                    bankList.add(json.toJavaObject(BankVo.class));
                }
                CacheUtil.redis().setValue(CacheKeyEnum.BANK, "bank", bankList);
            }

            map.put("param1", "2");
            JSONObject thirdPaymentJson = sendHttpPostRequest(fullUrl, map);
            if (thirdPaymentJson != null && Boolean.parseBoolean(thirdPaymentJson.getString("result"))) {
                JSONArray paymentModes = thirdPaymentJson.getJSONObject("data").getJSONArray("paymentmode");
                for (int i = 0; i < paymentModes.size(); i++) {
                    JSONObject json = paymentModes.getJSONObject(i);
                    thirdList.add(json.toJavaObject(BankVo.class));
                }
                CacheUtil.redis().setValue(CacheKeyEnum.THIRD_PAYMENT_LIST, "thirdPaymentList", thirdList);
            }
        }
    }

    private JSONObject sendHttpPostRequest(String url, Map<String, String> params) {
        return HttpUtil.doPost(url, params, JSONObject.class);
    }

}
