package com.ywwl.customer.center.job.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ywwl.customer.center.modules.international.service.ForecastDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: dinghy
 * @date: 2023/4/10 14:10
 */
@Component
@Slf4j
public class ForecastDataTask {
    @Resource
    private ForecastDetailService forecastDetailService;

    /**
     * 导入预报推送任务
     */
    @XxlJob("forecastDataTask")
    public void pushData() {
        XxlJobHelper.log("导入预报任务推送------>");
        try {
            forecastDetailService.pushForecastData();
        } catch (Exception e) {
            log.info("推送预报数据出现异常原因:{}", e.getMessage());
        }
    }

}
