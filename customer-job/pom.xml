<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ywwl.customer.center</groupId>
        <artifactId>customer-center</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <!-- 独立Jar包 -->
    <artifactId>customer-job</artifactId>
    <packaging>jar</packaging>
    <description>
        定时任务模块，使用xxl-job
    </description>
    <properties>
        <spring-boot-maven-plugin.version>2.5.14</spring-boot-maven-plugin.version>
    </properties>
    <dependencies>
        <!-- 任务中心 -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <!-- 系统模块 -->
        <dependency>
            <groupId>com.ywwl.customer.center</groupId>
            <artifactId>customer-modules</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
