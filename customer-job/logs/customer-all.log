2025-06-16 15:22:45.886 =>[main] == INFO  API:548 - 2b564ab1d7c54714b78fc1625f7f68cd -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:22:45.890 =>[main] == INFO  API:726 - 2b564ab1d7c54714b78fc1625f7f68cd -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:60)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 1ms
2025-06-16 15:23:26.292 =>[main] == INFO  API:548 - d49579ce4d0e4c2ca77caa1ac19814ac -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:23:26.296 =>[main] == INFO  API:726 - d49579ce4d0e4c2ca77caa1ac19814ac -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:60)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 0ms
2025-06-16 15:24:42.809 =>[main] == INFO  API:548 - 98a067fef1264a8c903f041670d26e20 -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:24:42.853 =>[main] == INFO  API:726 - 98a067fef1264a8c903f041670d26e20 -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:60)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 2ms
2025-06-16 15:25:03.200 =>[main] == INFO  API:548 - 5d7faa90f1184339a0f8983e5146aac2 -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:25:03.204 =>[main] == INFO  API:726 - 5d7faa90f1184339a0f8983e5146aac2 -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:60)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 0ms
2025-06-16 15:25:59.190 =>[main] == INFO  API:548 - e1e82358251245139bbe2f1219e81c13 -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:25:59.193 =>[main] == INFO  API:726 - e1e82358251245139bbe2f1219e81c13 -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:40)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 1ms
2025-06-16 15:26:50.691 =>[main] == INFO  API:548 - ab299e6613a44d0cb41f5822cbd99dad -REQUEST -X POST -H  -d {"dateOfBillBegin":"2024-12-30","dateOfBillEnd":"2025-01-05","merchantCode":"20121687","page":1,"size":10} http://**********:20010/acc-ar/bill-detail/search/1/10
2025-06-16 15:26:50.696 =>[main] == INFO  API:726 - ab299e6613a44d0cb41f5822cbd99dad -RESPONSE ERROR java.lang.NullPointerException: Cannot invoke "okhttp3.OkHttpClient.newCall(okhttp3.Request)" because "com.ywwl.customer.center.common.utils.HttpUtil.okHttpClient" is null
	at com.ywwl.customer.center.common.utils.HttpUtil.request(HttpUtil.java:660)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:554)
	at com.ywwl.customer.center.common.utils.HttpUtil.execute(HttpUtil.java:458)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:191)
	at com.ywwl.customer.center.common.utils.HttpUtil.doPost(HttpUtil.java:154)
	at com.ywwl.customer.center.modules.common.bill.service.impl.InternationalBillServiceImplTest.testGetNumberList_Success(InternationalBillServiceImplTest.java:40)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.mockito.internal.runners.DefaultInternalRunner$1$1.evaluate(DefaultInternalRunner.java:54)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.mockito.internal.runners.DefaultInternalRunner$1.run(DefaultInternalRunner.java:99)
	at org.mockito.internal.runners.DefaultInternalRunner.run(DefaultInternalRunner.java:105)
	at org.mockito.internal.runners.StrictRunner.run(StrictRunner.java:40)
	at org.mockito.junit.MockitoJUnitRunner.run(MockitoJUnitRunner.java:163)
	at org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.run(JUnit4TestReference.java:93)
	at org.eclipse.jdt.internal.junit.runner.TestExecution.run(TestExecution.java:40)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:520)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.runTests(RemoteTestRunner.java:748)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.run(RemoteTestRunner.java:443)
	at org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.main(RemoteTestRunner.java:211)
 http://**********:20010/acc-ar/bill-detail/search/1/10 elapsedTime 1ms
